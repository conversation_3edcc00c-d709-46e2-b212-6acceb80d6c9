/* ==========================================================================
   Portfolio Single navigation style - begin
   ========================================================================== */

.mkdf-ps-related-posts-holder {
	@include mkdfRelativeHolderLayout();
	margin: 80px 0 60px;
	clear: both;
	
	.mkdf-ps-related-posts {
		margin: 0 -15px;
	}
	
	.mkdf-ps-related-post {
		position: relative;
		display: inline-block;
		vertical-align: middle;
		width: 25%;
		float: left;
		padding: 0 15px;
		box-sizing: border-box;
		
		@include ipad-landscape {
			width: 50%;
			margin: 0 0 30px;
			
			&:nth-child(2n+1) {
				clear: both;
			}
		}
		
		@include phone-landscape {
			width: 100%;
		}
		.mkdf-pli-image-hover {
			@include mkdfAbsoluteHolderLayout();
			padding: 20px;
			opacity: 0;
			text-align: center;
			box-sizing: border-box;
			background-color: $first-main-color-yellow;
			color: #fff;
			font-size: 72px;
			@include mkdfTransition(opacity .2s ease-in-out);

			.mkdf-pli-image-hover-table{
				display: table;
				height: 100%;
				width: 100%;

				i{
					display: table-cell;
					vertical-align: middle;
				}
			}
		}
		&:hover{

			

				.mkdf-pli-image-hover{
					opacity: 1;
					cursor: pointer;
				}
			
		}
	}
	
	.mkdf-ps-related-image {
		@include mkdfRelativeHolderLayout();
		
		a, img {
			display: block;
		}
		overflow: hidden;
	}
	
	.mkdf-ps-related-text {
		@include mkdfRelativeHolderLayout();
		margin: 20px 0 0;
		
		.mkdf-ps-related-title {
			margin: 0;
		}
		
		.mkdf-ps-related-categories {
			margin: 6px 0 0;
		}
	}
}
/* ==========================================================================
   Portfolio Single navigation style - end
   ========================================================================== */