/* ==========================================================================
   Pricing Tables shortcode style - begin
   ========================================================================== */

.mkdf-pricing-tables {
    @include mkdfRelativeHolderLayout();
}

.mkdf-price-table {
    @media screen and (max-width: 680px){
        padding: 0 0 37px !important;

        &:last-child{
            padding-bottom: 0 !important;
        }
    }

    .mkdf-pt-inner {
        @include mkdfRelativeHolderLayout();
        border: 2px solid rgba(237,238,239,0.6);
        box-sizing: border-box;

        > ul {
            list-style: none;
            margin: 0;
            padding: 0;

            > li {
                margin: 0;
                text-align: center;
                box-sizing: border-box;

                &.mkdf-pt-title-holder {
                    padding: 10px 20px;
                    position: relative;
                    height: 203px;
                    color: $default-heading-color;
                    font-size: 18px;
                    line-height: 26px;
                    font-weight: 600;

                    .mkdf-pt-active-title{
                        display: block;
                        font-family: $default-text-font;
                        color: $first-main-color;
                        font-size: 14px;
                        font-weight: 700;
                        letter-spacing: .1em;
                        text-transform: uppercase;
                        padding: 15px 0 17px;
                    }
	                
                    .mkdf-pt-title {
                        display: block;
                        font-size: 36px;
                        font-family: $default-text-font;
                        letter-spacing: -.025em;
                        font-weight: 600;
	                    padding: 28px 0 22px;
	                    box-sizing: border-box;
                    }
                }

                &.mkdf-pt-content {

                    > ul {
                        list-style: none;
                        margin: 0;
                        padding: 0;
                        display: flex;
                        flex-direction: column;

                        > li{
                            padding: 20px;
                            line-height: 33px;
                            border-top: 2px solid $default-border-color;
                            font-size: 20px;
                            font-weight: 600;
                            color: $first-main-color-dark-blue;
                            letter-spacing: -.025em;

                            &:last-child{
                                border-bottom: 2px solid $default-border-color;
                            }
                        }
                    }
                }

                &.mkdf-pt-prices {
                    position: relative;
					padding: 33px 15px 28px;
                    border-bottom: 2px solid $default-border-color;
	                
                    .mkdf-pt-value {
                        position: relative;
                        display: inline-block;
                        vertical-align: middle;
                        font-size: 60px;
                        font-weight: 600;
                        line-height: 1em;
                        color: $default-heading-color;
                        font-family: $default-text-font;
                    }

                    .mkdf-pt-price {
                        position: relative;
                        display: inline-block;
                        vertical-align: middle;
                        font-size: 60px;
                        font-weight: 600;
                        line-height: 1em;
                        color: $default-heading-color;
                        font-family: $default-text-font;
                    }

                    .mkdf-pt-mark {
                        position: relative;
                        display: block;
                        font-size: 14px;
                        font-weight: 600;
                        line-height: 1em;
                        color: rgba(#ccc, 0.8);
                        font-family: $default-text-font;
                        letter-spacing: .1em;
                        margin: 12px 0 0;
                    }
                }

                &.mkdf-pt-button {
                    padding: 0;

                    a{
                        padding: 24px 0;
                        width: 100%;
                        border: none !important;
                        color: $first-main-color-dark-blue;

                        &.mkdf-btn-solid{
                            color: #fff !important;
                        }
                    }
                }
            }
        }
    }
}

.mkdf-pt-active-item.mkdf-price-table{
    @include mkdfTransform(translateY(-85px));
    z-index: 10;

    @media screen and (max-width: 680px){
        @include mkdfTransform(none);
    }

    .mkdf-pt-inner {
        border-color: transparent;
        border-left: 0;
        border-right: 0;
        border-bottom: 0;
        width: calc(100% + 6px);
        margin: 0 -3px;

        @media screen and (max-width: 680px){
            width: 100%;
            margin: 0;
        }

        > ul {

            > li {

                &.mkdf-pt-title-holder {
                    height: 203px + 85px;

                    .mkdf-pt-title{
                        padding: 0 0 22px;
                    }
                }

                &.mkdf-pt-button {
                    padding: 0;

                    a{
                        padding: 26px 0;
                        margin-top: -2px;
                    }
                }
            }
        }
    }
}

/* ==========================================================================
   Custom left menu style - start
   ========================================================================== */

   .mkdf-custom-opacity {
       
        -webkit-transition: opacity .22s cubic-bezier(0.23, 1, 0.32, 1), -webkit-transform 0s cubic-bezier(0.23, 1, 0.32, 1);
        transition: opacity .22s cubic-bezier(0.23, 1, 0.32, 1), transform 0s cubic-bezier(0.23, 1, 0.32, 1);

    h5 {
        @include mkdfTransition(all 0.3s ease-in-out);
        opacity: 0;
        -webkit-transition: -webkit-transform .6s cubic-bezier(0.18, 0.89, 0.32, 1), opacity .2s ease-out;
        transition: transform .4s cubic-bezier(0.18, 0.89, 0.32, 1), opacity .2s ease-out;

        

        &.mkdf-appeared {
            opacity: 1;
        }

        &:hover {
            opacity: 0.6;
        }
    }
   }

   /* ==========================================================================
   Custom left menu style - start
   ========================================================================== */
/* ==========================================================================
   Pricing Tables shortcode style - end
   ========================================================================== */