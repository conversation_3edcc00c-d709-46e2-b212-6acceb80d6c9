/* ==========================================================================
   Call To Action shortcode responsive style - begin
   ========================================================================== */


@media only screen and (min-width: $laptop-landscape-plus-pixel) and (max-width: 1300px) {
	
	.mkdf-call-to-action-holder {
		
		.mkdf-grid {
			width: 1100px;
		}
	}
}

@include laptop-landscape {
	
	.mkdf-call-to-action-holder {
		
		&.mkdf-three-quarters-columns,
		&.mkdf-four-fifths-columns {
			
			.mkdf-cta-text-holder {
				width: 66.66666666666667%;
			}
			
			.mkdf-cta-button-holder {
				width: 33.33333333333333%;
			}
		}
	}
}

@include ipad-portrait {
	
	.mkdf-call-to-action-holder {
		
		&.mkdf-normal-layout {
			
			.mkdf-cta-inner,
			.mkdf-cta-text-holder,
			.mkdf-cta-button-holder {
				display: block;
			}
			
			.mkdf-cta-button-holder {
				margin: 28px 0 0;
				text-align: initial;
			}
		}
		
		&.mkdf-two-halves-columns,
		&.mkdf-two-thirds-columns,
		&.mkdf-three-quarters-columns,
		&.mkdf-four-fifths-columns {
			
			.mkdf-cta-text-holder,
			.mkdf-cta-button-holder {
				width: 100%;
			}
		}
	}
}
/* ==========================================================================
   Call To Action shortcode responsive style - end
   ========================================================================== */