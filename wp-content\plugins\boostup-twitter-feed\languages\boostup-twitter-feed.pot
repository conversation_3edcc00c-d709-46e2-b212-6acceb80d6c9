# Copyright (C) 2023 Mikado Themes
# This file is distributed under the same license as the BoostUp Twitter Feed plugin.
msgid ""
msgstr ""
"Project-Id-Version: BoostUp Twitter Feed 1.1\n"
"Report-Msgid-Bugs-To: https://wordpress.org/support/plugin/boostup-twitter-feed\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"POT-Creation-Date: 2023-07-11T11:09:45+00:00\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"X-Generator: WP-CLI 2.6.0\n"
"X-Domain: boostup-twitter-feed\n"

#. Plugin Name of the plugin
msgid "BoostUp Twitter Feed"
msgstr ""

#. Description of the plugin
msgid "Plugin that adds Twitter feed functionality to our theme"
msgstr ""

#. Author of the plugin
msgid "Mikado Themes"
msgstr ""

#: lib/boostup-twitter-api.php:231
#: lib/boostup-twitter-api.php:298
#: lib/boostup-twitter-api.php:364
msgid "Internal WP error"
msgstr ""

#: lib/boostup-twitter-api.php:247
msgid "Redirect URL couldn\\t not be generated"
msgstr ""

#: lib/boostup-twitter-api.php:254
msgid "Couldn't connect with Twitter API"
msgstr ""

#: lib/boostup-twitter-api.php:310
msgid "Access token obtained"
msgstr ""

#: lib/boostup-twitter-api.php:315
msgid "Authorize token and it's secret were not obtainer"
msgstr ""

#: lib/boostup-twitter-api.php:374
msgid "Couldn't connect with Twitter"
msgstr ""

#: lib/boostup-twitter-api.php:379
msgid "It seams like you haven\\t connected with your Twitter account"
msgstr ""

#: lib/boostup-twitter-api.php:389
msgid "Couldn't retreive content from database"
msgstr ""

#: lib/boostup-twitter-helper.php:88
msgid "ago"
msgstr ""

#: shortcodes/twitter-list/holder.php:20
#: widgets/boostup-twitter-widget.php:203
msgid "It seams that you haven't connected with your Twitter account"
msgstr ""

#: shortcodes/twitter-list/twitter-list.php:24
msgid "Twitter List"
msgstr ""

#: shortcodes/twitter-list/twitter-list.php:26
msgid "by BOOSTUP"
msgstr ""

#: shortcodes/twitter-list/twitter-list.php:33
#: widgets/boostup-twitter-widget.php:40
msgid "User ID"
msgstr ""

#: shortcodes/twitter-list/twitter-list.php:39
msgid "Number of Columns"
msgstr ""

#: shortcodes/twitter-list/twitter-list.php:46
msgid "Space Between Columns"
msgstr ""

#: shortcodes/twitter-list/twitter-list.php:52
#: widgets/boostup-twitter-widget.php:45
msgid "Number of Tweets"
msgstr ""

#: shortcodes/twitter-list/twitter-list.php:57
#: widgets/boostup-twitter-widget.php:68
msgid "Tweets Cache Time"
msgstr ""

#: widgets/boostup-twitter-widget.php:12
msgid "BoostUp Twitter Widget"
msgstr ""

#: widgets/boostup-twitter-widget.php:14
msgid "Display your Twitter feed"
msgstr ""

#: widgets/boostup-twitter-widget.php:26
msgid "Title"
msgstr ""

#: widgets/boostup-twitter-widget.php:31
msgid "Type"
msgstr ""

#: widgets/boostup-twitter-widget.php:33
msgid "Standard"
msgstr ""

#: widgets/boostup-twitter-widget.php:34
msgid "Slider"
msgstr ""

#: widgets/boostup-twitter-widget.php:50
msgid "Show Tweet Icon"
msgstr ""

#: widgets/boostup-twitter-widget.php:52
#: widgets/boostup-twitter-widget.php:62
msgid "Yes"
msgstr ""

#: widgets/boostup-twitter-widget.php:53
#: widgets/boostup-twitter-widget.php:61
msgid "No"
msgstr ""

#: widgets/boostup-twitter-widget.php:59
msgid "Show Tweet Time"
msgstr ""
