/* ==========================================================================
   Image Gallery shortcode style - begin
   ========================================================================== */

.mkdf-image-gallery {
	@include mkdfRelativeHolderLayout();

	.owl-nav{

		@include phone-landscape {
			display: none;
		}

		.owl-prev, .owl-next{
			transition: opacity .2s ease;
			@include mkdfTransform(translateY(-50%) !important);

			&.disabled{
				opacity: 0;
			}

			span{
				font-size: 48px;
				color: #fff;
				width: 80px;
				height: 80px;
				line-height: 80px;
				background-color: $first-main-color;
				border-radius: 50%;
				-webkit-box-shadow: 0 -5px 10px rgba(234,61,86,.3);
				-moz-box-shadow: 0 -5px 10px rgba(234,61,86,.3);
				box-shadow: 0 -5px 10px rgba(234,61,86,.3);
			}
		}

		.owl-prev{
			left: 13%;

		}

		.owl-next{
			right: 13%;
		}
	}

	.owl-dots {

		display: none;

		@include phone-landscape {
			display: block;
		}
	}

	&.mkdf-ig-carousel-type {

		.owl-item:first-child {
			margin-left: -346px;

			@media screen and (max-width: 1366px) {
				margin-left: -247px;
			}

			@media screen and (max-width: 1024px) {
				margin-left: -82px;
			}

			@media screen and (max-width: 768px) {
				margin-left: 0;
			}
		}
	}

	&.mkdf-has-shadow {

		.mkdf-ig-image-inner {
			box-shadow: $default-box-shadow;
		}

		&.mkdf-ig-slider-type,
		&.mkdf-ig-carousel-type {

			.owl-stage-outer {
				padding: 0 0 20px;
			}

			.mkdf-ig-image {
				box-shadow: $default-box-shadow;
			}
		}
	}

	.mkdf-ig-image {

		a, img {
			position: relative;
			display: block;
		}
	}

	.mkdf-ig-image-inner {
		@include mkdfRelativeHolderLayout();
	}

	.mkdf-ig-slider {
		@include mkdfRelativeHolderLayout();
	}

	/***** Image Gallery Masonry Style - begin *****/

	&.mkdf-ig-masonry-type {

		.mkdf-ig-image {

			&.mkdf-fixed-masonry-item {

				.mkdf-ig-image-inner,
				a {
					height: 100%;
				}
			}
		}
	}

	/***** Image Gallery Masonry Style - end *****/

	/***** Custom Link Behavior Style - begin *****/

	&.mkdf-image-behavior-custom-link {

		.mkdf-ig-image {

			a {
				@include mkdfImageOverlayHoverStyle();

				img {
					border-radius: 10px;
				}

				&:after {
					border-radius: 10px;
				}
			}
		}
		&.mkdf-ig-carousel-type {
			.mkdf-ig-image {
				@include mkdfTransition(all 0.3s ease-in-out);
				a {
					&:after {
						display: none;
					}
				}

				&:hover {
					box-shadow: 6px 36px 67px -27px rgba(0,0,0,.1);
				}
			}

		}

		.owl-dot span {
			background:rgba(0,0,0,0.25);
			border: 0;

		}

		.owl-dot.active span {
			background:rgba(0, 0, 0, 0.5);
		}

	}

	/***** Custom Link Behavior Style - end *****/

	/***** Lightbox Behavior Style - begin *****/

	&.mkdf-image-behavior-lightbox {

		.mkdf-ig-image {

			position: relative;

			a {
				&:after {
		            @include mkdfAbsoluteHolderLayout();
		            content: '';
		            background-color: #74cccd;
		            opacity: 0;
		            @include mkdfTransition(opacity .2s ease-in-out);
		        }

		        &:before {
		        	
	        	    content: "\4c";
				    font-family: ElegantIcons;
				    position: absolute;
				    font-size: 72px;
				    color: #fff;
				    opacity: 0;
				    text-align: center;
				    box-sizing: border-box;
				    top: calc(50% - 36px);
				    left: calc(50% - 36px);
				    right: 0;
				    z-index: 9;
				   @include mkdfTransition(all .4s ease-in-out);
				    transform-origin: 50% 50%;
				    width: 72px;
				    height: 72px;
				    line-height: 72px;
				    display: block;


		        }
			}

			&:hover {

				a {
					&:after {
						opacity: 1;
					}
					&:before {
						opacity: 1;
						@include mkdfTransform (rotate(90deg));
						
						   
					}
				}
			}
		}
	}

	/***** Lightbox Behavior Style - end *****/

	/***** Zoom Behavior Style - begin *****/

	&.mkdf-image-behavior-zoom {

		.mkdf-ig-image {

			.touch & {
				cursor: pointer;
			}

			&:hover {

				img {
					@include mkdfTransform(scale(1.04));
				}
			}

			.mkdf-ig-image-inner {
				overflow: hidden;
			}

			img {
				@include mkdfTransform(scale(1));
				@include mkdfTransitionTransform(.3s ease-in-out);
			}
		}
	}

	/***** Zoom Behavior Style - end *****/

	/***** Grayscale Behavior Style - begin *****/

	&.mkdf-image-behavior-grayscale {

		.mkdf-ig-image {
			overflow: hidden;

			.touch & {
				cursor: pointer;
			}

			&:hover {

				img {
					-webkit-filter: grayscale(0);
					filter: none;
				}
			}

			img {
				filter: url('img/desaturate.svg#grayscale');
				-webkit-filter: grayscale(100%);
				-moz-filter: grayscale(100%);
				filter: gray;
				filter: grayscale(100%);
				@include mkdfTransition(all .3s ease-in-out);
			}
		}
	}

	/***** Grayscale Behavior Style - end *****/
}

/* ==========================================================================
   Image Gallery shortcode style - end
   ========================================================================== */