(function($) {
	'use strict';
	
	var workflow = {};
	mkdf.modules.workflow = workflow;

    workflow.mkdfWorkflow = mkdfWorkflow;


    workflow.mkdfOnDocumentReady = mkdfOnDocumentReady;
	
	$(document).ready(mkdfOnDocumentReady);
	
	/*
	 All functions to be called on $(document).ready() should be in this function
	 */
	function mkdfOnDocumentReady() {
        mkdfWorkflow();
        mkdfWorkflowAnimate();
	}

    function mkdfWorkflow() {
        var workflowShortcodes = $('.mkdf-workflow');
        if (workflowShortcodes.length) {
            workflowShortcodes.each(function () {
                var workflowShortcode = $(this);
                if (workflowShortcode.hasClass('mkdf-workflow-animate')) {
                    var workflowItems = workflowShortcode.find('.mkdf-workflow-item');

                    workflowShortcode.appear(function () {
                        workflowShortcode.addClass('mkdf-appeared');
                        setTimeout(function () {
                            workflowItems.each(function (i) {
                                var workflowItem = $(this);
                                setTimeout(function () {
                                    workflowItem.addClass('mkdf-appeared');
                                }, 250 * i);
                            });
                        }, 250);
                        $('.mkdf-workflow-item .line').each(function(i) {
                            var fadeInTime = 0.5 + i/5;

                            if($(this).hasClass('line-one'))  {
                                $(this).css({
                                    'height' : 'calc(50% - 10px)',
                                    'transition':'height .6s ease-in-out '+ fadeInTime +'s '
                                })
                            }

                            if($(this).hasClass('line-two'))  {
                                $(this).css({
                                    'height' : 'calc(50% - 10px)',
                                    'transition':'height .3s ease-in-out '+ fadeInTime +'s '
                                })
                            }
                        })
                    }, {accX: 0, accY: mkdfGlobalVars.vars.mkdfElementAppearAmount});

                }
            });
        }
    }
    function mkdfWorkflowAnimate() {
        var workflowShortcodes = $('.mkdf-workflow');
        if (workflowShortcodes.length) {
            workflowShortcodes.each(function () {
                var workflowShortcode = $(this);
                if (workflowShortcode.hasClass('mkdf-workflow-animate')) {
                    var workflowItems = workflowShortcode.find('.mkdf-workflow-item');

                    workflowShortcode.appear(function () {
                        workflowShortcode.addClass('mkdf-appeared');
                        setTimeout(function () {
                            workflowItems.each(function (i) {
                                var workflowItem = $(this);
                                setTimeout(function () {
                                    workflowItem.addClass('mkdf-appeared');
                                }, 350 * i);
                            });
                        }, 350);
                      
                    }, {accX: 0, accY: mkdfGlobalVars.vars.mkdfElementAppearAmount});

                }
            });
        }
    }
	
})(jQuery);