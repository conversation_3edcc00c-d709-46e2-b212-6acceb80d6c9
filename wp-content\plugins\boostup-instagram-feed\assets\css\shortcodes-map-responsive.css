/* ==========================================================================
   Global partials
   ========================================================================== */
@-webkit-keyframes animate-btn-line {
  0% {
    -webkit-transform-origin: 0 50%;
    transform-origin: 0 50%;
    -webkit-transform: scaleX(0);
    transform: scaleX(0);
  }
  100% {
    -webkit-transform-origin: 0 50%;
    transform-origin: 0 50%;
    -webkit-transform: scaleX(1);
    transform: scaleX(1);
  }
}
@keyframes animate-btn-line {
  0% {
    -webkit-transform-origin: 0 50%;
    transform-origin: 0 50%;
    -webkit-transform: scaleX(0);
    transform: scaleX(0);
  }
  100% {
    -webkit-transform-origin: 0 50%;
    transform-origin: 0 50%;
    -webkit-transform: scaleX(1);
    transform: scaleX(1);
  }
}

@-webkit-keyframes mkdfPulsebig {
  0% {
    -webkit-box-shadow: 0 0 0 0 currentColor;
    box-shadow: 0 0 0 0 currentColor;
  }
  70% {
    -webkit-box-shadow: 0 0 0 20px rgba(234, 61, 86, 0);
    box-shadow: 0 0 0 20px rgba(234, 61, 86, 0);
  }
  100% {
    -webkit-box-shadow: 0 0 0 0 rgba(234, 61, 86, 0);
    box-shadow: 0 0 0 0 rgba(234, 61, 86, 0);
  }
}

@keyframes mkdfPulsebig {
  0% {
    -webkit-box-shadow: 0 0 0 0 currentColor;
    box-shadow: 0 0 0 0 currentColor;
  }
  70% {
    -webkit-box-shadow: 0 0 0 20px rgba(234, 61, 86, 0);
    box-shadow: 0 0 0 20px rgba(234, 61, 86, 0);
  }
  100% {
    -webkit-box-shadow: 0 0 0 0 rgba(234, 61, 86, 0);
    box-shadow: 0 0 0 0 rgba(234, 61, 86, 0);
  }
}

@-webkit-keyframes mkdfPulsesmall {
  0% {
    -webkit-box-shadow: 0 0 0 0 currentColor;
  }
  70% {
    -webkit-box-shadow: 0 0 0 10px rgba(234, 61, 86, 0.15);
  }
  100% {
    -webkit-box-shadow: 0 0 0 0 rgba(234, 61, 86, 0);
  }
}

@keyframes mkdfPulsesmall {
  0% {
    -webkit-box-shadow: 0 0 0 0 currentColor;
    box-shadow: 0 0 0 0 currentColor;
  }
  70% {
    -webkit-box-shadow: 0 0 0 10px rgba(234, 61, 86, 0);
    box-shadow: 0 0 0 10px rgba(234, 61, 86, 0);
  }
  100% {
    -webkit-box-shadow: 0 0 0 0 rgba(234, 61, 86, 0);
    box-shadow: 0 0 0 0 rgba(234, 61, 86, 0);
  }
}

@-webkit-keyframes mkdfPulsesmallfirst {
  0% {
    -webkit-box-shadow: 0 0 0 0 #ea3d56;
  }
  70% {
    -webkit-box-shadow: 0 0 0 10px rgba(234, 61, 86, 0.15);
  }
  100% {
    -webkit-box-shadow: 0 0 0 0 rgba(234, 61, 86, 0);
  }
}

@keyframes mkdfPulsesmallfirst {
  0% {
    -webkit-box-shadow: 0 0 0 0 #ea3d56;
    box-shadow: 0 0 0 0 #ea3d56;
  }
  70% {
    -webkit-box-shadow: 0 0 0 10px rgba(234, 61, 86, 0);
    box-shadow: 0 0 0 10px rgba(234, 61, 86, 0);
  }
  100% {
    -webkit-box-shadow: 0 0 0 0 rgba(234, 61, 86, 0);
    box-shadow: 0 0 0 0 rgba(234, 61, 86, 0);
  }
}

/* ==========================================================================
   Shortcodes responsive styles
   ========================================================================== */
@media only screen and (max-width: 1280px) {
  .mkdf-instagram-list-holder.mkdf-il-four-columns .mkdf-il-item {
    width: 33.33333%;
  }
}

@media only screen and (max-width: 1280px) and (min-width: 1025px) {
  .mkdf-instagram-list-holder.mkdf-il-four-columns .mkdf-il-item:nth-child(3n+1) {
    clear: both;
  }
}

@media only screen and (max-width: 1280px) {
  .mkdf-instagram-list-holder.mkdf-il-five-columns .mkdf-il-item {
    width: 33.33333%;
  }
}

@media only screen and (max-width: 1280px) and (min-width: 1025px) {
  .mkdf-instagram-list-holder.mkdf-il-five-columns .mkdf-il-item:nth-child(3n+1) {
    clear: both;
  }
}

@media only screen and (max-width: 1024px) {
  .mkdf-instagram-list-holder.mkdf-il-three-columns .mkdf-il-item {
    width: 50%;
  }
}

@media only screen and (max-width: 1024px) and (min-width: 681px) {
  .mkdf-instagram-list-holder.mkdf-il-three-columns .mkdf-il-item:nth-child(2n+1) {
    clear: both;
  }
}

@media only screen and (max-width: 1024px) {
  .mkdf-instagram-list-holder.mkdf-il-four-columns .mkdf-il-item {
    width: 50%;
  }
}

@media only screen and (max-width: 1024px) and (min-width: 681px) {
  .mkdf-instagram-list-holder.mkdf-il-four-columns .mkdf-il-item:nth-child(2n+1) {
    clear: both;
  }
}

@media only screen and (max-width: 1024px) {
  .mkdf-instagram-list-holder.mkdf-il-five-columns .mkdf-il-item {
    width: 50%;
  }
}

@media only screen and (max-width: 1024px) and (min-width: 681px) {
  .mkdf-instagram-list-holder.mkdf-il-five-columns .mkdf-il-item:nth-child(2n+1) {
    clear: both;
  }
}

@media only screen and (max-width: 680px) {
  .mkdf-instagram-list-holder .mkdf-il-item {
    width: 100% !important;
  }
}

/*# sourceMappingURL=../../../../plugins/boostup-instagram-feed/assets/css/shortcodes-map-responsive.css.map */
