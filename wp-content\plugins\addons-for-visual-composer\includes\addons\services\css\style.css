/* Style 1 */
.lvca-services.lvca-style1 .lvca-service .lvca-icon-wrapper span {
  display: block;
  text-align: center;
  font-size: 96px;
  line-height: 1;
  margin-bottom: 20px;
  -webkit-transition: color .4s ease-in-out 0s;
  transition: color .4s ease-in-out 0s; }
.lvca-services.lvca-style1 .lvca-service .lvca-image-wrapper img {
  display: block;
  max-width: 100%;
  text-align: center;
  margin: 0 auto 25px;
  -webkit-transition: all .4s ease-in-out 0s;
  transition: all .4s ease-in-out 0s; }
.lvca-services.lvca-style1 .lvca-service .lvca-service-text {
  text-align: center;
  max-width: 300px;
  margin: 0 auto; }
  .lvca-services.lvca-style1 .lvca-service .lvca-service-text .lvca-title {
    font-size: 18px;
    line-height: 26px;
    letter-spacing: 1px;
    font-weight: bold;
    color: #333;
    text-transform: uppercase;
    clear: none;
    margin-top: 0;
    margin-bottom: 10px;
    margin-bottom: 20px; }
.lvca-services.lvca-style1 .lvca-service:hover .lvca-image-wrapper img {
  -webkit-transform: scale(0.9, 0.9);
          transform: scale(0.9, 0.9); }

/* Style 2 */
.lvca-services.lvca-style2 .lvca-service .lvca-image-wrapper img, .lvca-services.lvca-style2 .lvca-service .lvca-icon-wrapper span {
  float: left;
  margin-right: 18px; }
.lvca-services.lvca-style2 .lvca-service .lvca-icon-wrapper span {
  font-size: 24px;
  line-height: 32px; }
.lvca-services.lvca-style2 .lvca-service .lvca-service-text .lvca-title {
  font-size: 18px;
  line-height: 26px;
  letter-spacing: 1px;
  font-weight: bold;
  color: #333;
  text-transform: uppercase;
  clear: none;
  margin-top: 0;
  margin-bottom: 10px;
  margin-bottom: 20px; }

/* Style 3 */
.lvca-services.lvca-style3 .lvca-service .lvca-icon-wrapper span {
  display: block;
  text-align: left;
  font-size: 80px;
  line-height: 1;
  margin-bottom: 25px;
  color: #555; }
  .lvca-dark-bg .lvca-services.lvca-style3 .lvca-service .lvca-icon-wrapper span {
    color: #c5c5c5; }
.lvca-services.lvca-style3 .lvca-service .lvca-image-wrapper img {
  display: block;
  max-width: 100%;
  text-align: left;
  margin-bottom: 25px; }
.lvca-services.lvca-style3 .lvca-service .lvca-service-text {
  text-align: left;
  max-width: 300px;
  margin: 0;
  font-size: 14px;
  line-height: 32px;
  color: #888; }
  .lvca-services.lvca-style3 .lvca-service .lvca-service-text ul.lvca-services-list {
    padding: 0;
    margin: 0;
    border: none; }
  .lvca-services.lvca-style3 .lvca-service .lvca-service-text ul.lvca-services-list li {
    border-bottom: 1px solid #eee;
    position: relative;
    padding: 0;
    margin: 0;
    list-style: none;
    line-height: 42px; }
    .lvca-services.lvca-style3 .lvca-service .lvca-service-text ul.lvca-services-list li:hover {
      padding: 0; }
    .lvca-dark-bg .lvca-services.lvca-style3 .lvca-service .lvca-service-text ul.lvca-services-list li {
      border-color: #333; }
  .lvca-services.lvca-style3 .lvca-service .lvca-service-text ul.lvca-services-list li:before {
    font-family: 'lvca-icomoon' !important;
    speak: none;
    font-style: normal;
    font-weight: normal;
    font-variant: normal;
    text-transform: none;
    line-height: 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    position: relative;
    display: inline-block;
    height: auto;
    width: auto;
    background: none;
    float: none;
    vertical-align: middle;
    margin: 0 15px 0 0;
    content: "\e913";
    color: #BBBBBB;
    font-size: 12px;
    line-height: 1; }
    .lvca-dark-bg .lvca-services.lvca-style3 .lvca-service .lvca-service-text ul.lvca-services-list li:before {
      color: #606060; }
  .lvca-services.lvca-style3 .lvca-service .lvca-service-text .lvca-title {
    font-size: 18px;
    line-height: 26px;
    letter-spacing: 1px;
    font-weight: bold;
    color: #333;
    text-transform: uppercase;
    clear: none;
    margin-top: 0;
    margin-bottom: 10px;
    margin-bottom: 20px; }

/* Style 4 */
.lvca-services.lvca-style4 .lvca-service {
  margin-bottom: 60px; }
  .lvca-services.lvca-style4 .lvca-service .lvca-image-wrapper img, .lvca-services.lvca-style4 .lvca-service .lvca-icon-wrapper span {
    display: block;
    margin-bottom: 20px;
    text-align: left; }
  .lvca-services.lvca-style4 .lvca-service .lvca-icon-wrapper span {
    font-size: 36px;
    line-height: 1;
    color: #888; }
  .lvca-services.lvca-style4 .lvca-service .lvca-service-text .lvca-title {
    font-size: 18px;
    line-height: 26px;
    letter-spacing: 1px;
    font-weight: bold;
    color: #333;
    text-transform: uppercase;
    clear: none;
    margin-top: 0;
    margin-bottom: 10px; }

/* Style 5 */
.lvca-services.lvca-style5 .lvca-service {
  margin-bottom: 80px; }
  @media only screen and (max-width: 767px) {
    .lvca-services.lvca-style5 .lvca-service {
      margin-bottom: 50px; } }
  .lvca-services.lvca-style5 .lvca-service .lvca-icon-wrapper span {
    display: block;
    text-align: center;
    font-size: 48px;
    line-height: 1;
    margin-bottom: 15px;
    color: #999;
    -webkit-transition: color .4s ease-in-out 0s;
    transition: color .4s ease-in-out 0s; }
  .lvca-services.lvca-style5 .lvca-service .lvca-image-wrapper img {
    display: block;
    max-width: 100%;
    text-align: center;
    margin: 0 auto 25px;
    -webkit-transition: all .4s ease-in-out 0s;
    transition: all .4s ease-in-out 0s; }
  .lvca-services.lvca-style5 .lvca-service .lvca-service-text {
    text-align: center;
    max-width: 300px;
    margin: 0 auto; }
    .lvca-services.lvca-style5 .lvca-service .lvca-service-text .lvca-title {
      font-size: 18px;
      line-height: 26px;
      letter-spacing: 1px;
      font-weight: bold;
      color: #333;
      text-transform: uppercase;
      clear: none;
      margin-top: 0;
      margin-bottom: 10px;
      font-size: 16px;
      line-height: 26px;
      margin-bottom: 10px; }
  .lvca-services.lvca-style5 .lvca-service:hover .lvca-image-wrapper img {
    -webkit-transform: scale(0.9, 0.9);
            transform: scale(0.9, 0.9); }

/* -------- General services -------- */
.lvca-services .lvca-service {
  margin-bottom: 50px; }
  .lvca-services .lvca-service .lvca-icon-wrapper span {
    -webkit-transition: color .4s ease-in-out 0s;
    transition: color .4s ease-in-out 0s; }
  .lvca-services .lvca-service .lvca-service-text {
    font-size: 15px;
    line-height: 24px; }
    .lvca-dark-bg .lvca-services .lvca-service .lvca-service-text {
      color: #909090; }
      .lvca-dark-bg .lvca-services .lvca-service .lvca-service-text .lvca-title {
        color: #e5e5e5; }

/*# sourceMappingURL=style.css.map */