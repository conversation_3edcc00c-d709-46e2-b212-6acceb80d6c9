jQuery(document).ready(function($){$(".inline-list").each(function(){$(this).find("li").each(function(i){$(this).click(function(){$(this).addClass("current").siblings().removeClass("current").parents("#wpbody").find("div.panel-left").removeClass("visible").end().find("div.panel-left:eq("+i+")").addClass("visible");return false})})});$(".anchor-nav a, .toc a").click(function(e){e.preventDefault();var href=$(this).attr("href");$("html, body").animate({scrollTop:$(href).offset().top-50},"slow","swing")});$(".livemesh-doc .panel-left h3").append($("<a class='back-to-top' href='#panel'><span class='dashicons dashicons-arrow-up-alt2'></span> Back to top</a>"));$("a[href*='cl.ly']:not(.direct-link)").each(function(){$(this).addClass("thickbox");var imgUrl=$(this).attr("href")+"?TB_iframe=true&width=1200&height=700";$(this).attr("href",imgUrl)})});