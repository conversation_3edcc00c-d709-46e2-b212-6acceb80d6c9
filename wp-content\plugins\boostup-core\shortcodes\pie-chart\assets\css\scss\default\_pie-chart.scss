/* ==========================================================================
   Pie Chart shortcode style - begin
   ========================================================================== */

.mkdf-pie-chart-holder {
    @include mkdfRelativeHolderLayout();
	opacity: 0;
	@include mkdfTransition(opacity .2s ease-in);
	
    .mkdf-pc-percentage {
        position: relative;
        display: block;
        height: 176px;
        width: 176px;
        line-height: 176px;
	    text-align: center;
	    margin: 0 auto;
    
        canvas {
            position: absolute;
            top: 0;
            left: 0;
        }
        
        .mkdf-pc-percent {
            position: relative;
            display: inline-block;
            vertical-align: middle;
            color: $default-heading-color;
	        font-size: 30px;
	        line-height: inherit;
	        font-weight: 600;
            font-family: $default-text-font;
    
            &:after {
                position: relative;
                content: '%';
	            font-size: 30px;
            }
        }
    }

    .mkdf-pc-text-holder {
        @include mkdfRelativeHolderLayout();
        text-align: center;
        margin: 38px 0 0;
     
	    
	    .mkdf-pc-title {
		    margin: 0;
	    }
	
	    .mkdf-pc-text {
		    margin: 7px 0 0;
            font-family: $default-text-font;
            font-size: 12px;
            font-weight: 700;
            letter-spacing: 0.09em;
	    }
    }
}
/* ==========================================================================
   Pie Chart shortcode style - end
   ========================================================================== */