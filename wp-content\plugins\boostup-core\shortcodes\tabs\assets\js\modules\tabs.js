(function($) {
	'use strict';
	
	var tabs = {};
	mkdf.modules.tabs = tabs;
	
	tabs.mkdfInitTabs = mkdfInitTabs;
	
	tabs.mkdfOnDocumentReady = mkdfOnDocumentReady;
	
	$(document).ready(mkdfOnDocumentReady);
	
	/*
	 All functions to be called on $(document).ready() should be in this function
	 */
	function mkdfOnDocumentReady() {
		mkdfInitTabs();
		mkdfTabsOnHover();
	}
	
	/*
	 **	Init tabs shortcode
	 */
	function mkdfInitTabs(){
		var tabs = $('.mkdf-tabs');
		
		if(tabs.length){
			tabs.each(function(){
				var thisTabs = $(this);
				
				thisTabs.children('.mkdf-tab-container').each(function(index){
					index = index + 1;
					var that = $(this),
						link = that.attr('id'),
						navItem = that.parent().find('.mkdf-tabs-nav li:nth-child('+index+') a'),
						navLink = navItem.attr('href');
					
					link = '#'+link;

					if(link.indexOf(navLink) > -1) {
						navItem.attr('href',link);
					}
				});
				
				thisTabs.tabs();

                $('.mkdf-tabs a.mkdf-external-link').off('click');
			});
		}
	}

	function mkdfTabsOnHover(){
        var tabs = $('.mkdf-tabs');

        if(tabs.length){
            tabs.each(function(){
                var thisTabs = $(this);

                thisTabs.find('li.ui-state-active .mkdf-tabs-underline').css("background", thisTabs.attr("data-title-bottom-color"));
                thisTabs.find('li.ui-state-active .mkdf-tabs-underline').css("width", "100%");

                var changeActiveColor = function(item) {
                    item.siblings().not('.ui-state-active').find('.mkdf-tabs-underline').css("background", "transparent");
                    item.siblings().not('.ui-state-active').find('.mkdf-tabs-underline').css("width", "0");
                    item.find('.mkdf-tabs-underline').css("background", thisTabs.attr("data-title-bottom-color"));
                    item.find('.mkdf-tabs-underline').css("width", "100%");
                }

                thisTabs.find( "li" ).on( "click", function() {
                    var activeItem = thisTabs.find('.ui-state-active');
                    changeActiveColor(activeItem);
                });

                thisTabs.find('li').on('mouseenter', function() {
                    var currentItem = $(this);
                    changeActiveColor(currentItem);
                });

                thisTabs.find('li').on('mouseleave', function() {
                    var currentItem = $(this);
                    currentItem.not('.ui-state-active').find('.mkdf-tabs-underline').css("background", "transparent");
                    currentItem.not('.ui-state-active').find('.mkdf-tabs-underline').css("width", "0");
                });
            });
        }
	}
	
})(jQuery);


