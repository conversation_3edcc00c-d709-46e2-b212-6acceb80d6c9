/* ==========================================================================
   Global partials
   ========================================================================== */
@-webkit-keyframes animate-btn-line {
  0% {
    -webkit-transform-origin: 0 50%;
    transform-origin: 0 50%;
    -webkit-transform: scaleX(0);
    transform: scaleX(0);
  }
  100% {
    -webkit-transform-origin: 0 50%;
    transform-origin: 0 50%;
    -webkit-transform: scaleX(1);
    transform: scaleX(1);
  }
}
@keyframes animate-btn-line {
  0% {
    -webkit-transform-origin: 0 50%;
    transform-origin: 0 50%;
    -webkit-transform: scaleX(0);
    transform: scaleX(0);
  }
  100% {
    -webkit-transform-origin: 0 50%;
    transform-origin: 0 50%;
    -webkit-transform: scaleX(1);
    transform: scaleX(1);
  }
}

@-webkit-keyframes mkdfPulsebig {
  0% {
    -webkit-box-shadow: 0 0 0 0 currentColor;
    box-shadow: 0 0 0 0 currentColor;
  }
  70% {
    -webkit-box-shadow: 0 0 0 20px rgba(234, 61, 86, 0);
    box-shadow: 0 0 0 20px rgba(234, 61, 86, 0);
  }
  100% {
    -webkit-box-shadow: 0 0 0 0 rgba(234, 61, 86, 0);
    box-shadow: 0 0 0 0 rgba(234, 61, 86, 0);
  }
}

@keyframes mkdfPulsebig {
  0% {
    -webkit-box-shadow: 0 0 0 0 currentColor;
    box-shadow: 0 0 0 0 currentColor;
  }
  70% {
    -webkit-box-shadow: 0 0 0 20px rgba(234, 61, 86, 0);
    box-shadow: 0 0 0 20px rgba(234, 61, 86, 0);
  }
  100% {
    -webkit-box-shadow: 0 0 0 0 rgba(234, 61, 86, 0);
    box-shadow: 0 0 0 0 rgba(234, 61, 86, 0);
  }
}

@-webkit-keyframes mkdfPulsesmall {
  0% {
    -webkit-box-shadow: 0 0 0 0 currentColor;
  }
  70% {
    -webkit-box-shadow: 0 0 0 10px rgba(234, 61, 86, 0.15);
  }
  100% {
    -webkit-box-shadow: 0 0 0 0 rgba(234, 61, 86, 0);
  }
}

@keyframes mkdfPulsesmall {
  0% {
    -webkit-box-shadow: 0 0 0 0 currentColor;
    box-shadow: 0 0 0 0 currentColor;
  }
  70% {
    -webkit-box-shadow: 0 0 0 10px rgba(234, 61, 86, 0);
    box-shadow: 0 0 0 10px rgba(234, 61, 86, 0);
  }
  100% {
    -webkit-box-shadow: 0 0 0 0 rgba(234, 61, 86, 0);
    box-shadow: 0 0 0 0 rgba(234, 61, 86, 0);
  }
}

@-webkit-keyframes mkdfPulsesmallfirst {
  0% {
    -webkit-box-shadow: 0 0 0 0 #ea3d56;
  }
  70% {
    -webkit-box-shadow: 0 0 0 10px rgba(234, 61, 86, 0.15);
  }
  100% {
    -webkit-box-shadow: 0 0 0 0 rgba(234, 61, 86, 0);
  }
}

@keyframes mkdfPulsesmallfirst {
  0% {
    -webkit-box-shadow: 0 0 0 0 #ea3d56;
    box-shadow: 0 0 0 0 #ea3d56;
  }
  70% {
    -webkit-box-shadow: 0 0 0 10px rgba(234, 61, 86, 0);
    box-shadow: 0 0 0 10px rgba(234, 61, 86, 0);
  }
  100% {
    -webkit-box-shadow: 0 0 0 0 rgba(234, 61, 86, 0);
    box-shadow: 0 0 0 0 rgba(234, 61, 86, 0);
  }
}

/* ==========================================================================
   Shortcodes styles
   ========================================================================== */
/* ==========================================================================
   Accordions shortcode style - begin
   ========================================================================== */
.mkdf-accordion-holder {
  position: relative;
  display: inline-block;
  width: 100%;
  vertical-align: middle;
}

.mkdf-accordion-holder .mkdf-accordion-title {
  position: relative;
  cursor: pointer;
  margin: 0;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  -webkit-transform: translateZ(0px);
  transform: translateZ(0px);
  border: 2px rgba(225, 225, 225, 0.3) solid;
}

.mkdf-accordion-holder .mkdf-accordion-title .mkdf-tab-title {
  display: block;
  line-height: inherit;
}

.mkdf-accordion-holder .mkdf-accordion-title .mkdf-accordion-mark {
  position: absolute;
  top: 50%;
  right: 0;
  width: 40px;
  height: 40px;
  margin: -1px 0 0;
  font-size: 40px;
  line-height: 40px;
  text-align: center;
  -webkit-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  transform: translateY(-50%);
  -webkit-transition: width 0.2s ease-in-out;
  transition: width 0.2s ease-in-out;
}

.mkdf-accordion-holder .mkdf-accordion-title .mkdf-accordion-mark span {
  position: absolute;
  display: block;
  width: 100%;
  height: 100%;
  font-size: inherit;
  line-height: inherit;
  -webkit-transition: opacity 0.2s ease-out;
  transition: opacity 0.2s ease-out;
}

.mkdf-accordion-holder .mkdf-accordion-title .mkdf-accordion-mark span:before {
  display: block;
  line-height: inherit;
}

.mkdf-accordion-holder .mkdf-accordion-title .mkdf-accordion-mark .mkdf-eye-line {
  display: block;
  -webkit-transform: rotate(-45deg);
  -ms-transform: rotate(-45deg);
  transform: rotate(-45deg);
}

.mkdf-accordion-holder .mkdf-accordion-title .mkdf-accordion-mark .mkdf-eye-line:after {
  content: '';
  display: block;
  height: 2px;
  width: 100%;
  position: absolute;
  left: 0;
  bottom: 19px;
  background-color: currentColor;
  -webkit-transition: width 0.2s ease-in-out;
  transition: width 0.2s ease-in-out;
}

.mkdf-accordion-holder .mkdf-accordion-title .mkdf-accordion-mark:hover .mkdf-eye-line:after {
  width: 100%;
}

.mkdf-accordion-holder .mkdf-accordion-title.ui-state-active .mkdf-accordion-mark .mkdf-eye-line:after, .mkdf-accordion-holder .mkdf-accordion-title.ui-state-hover .mkdf-accordion-mark .mkdf-eye-line:after {
  width: 0%;
}

.mkdf-accordion-holder .mkdf-accordion-content {
  margin: 0;
  border: 2px solid rgba(225, 225, 225, 0.3);
  border-top: none;
}

.mkdf-accordion-holder .mkdf-accordion-content p {
  margin: 0;
}

.mkdf-accordion-holder.mkdf-ac-boxed.mkdf-white-skin {
  color: #fff;
}

.mkdf-accordion-holder.mkdf-ac-boxed.mkdf-white-skin .mkdf-accordion-title {
  color: #fff;
}

.mkdf-accordion-holder.mkdf-ac-boxed .mkdf-accordion-title {
  margin: 23px 0 0;
  padding: 24px 70px 24px 33px;
}

.mkdf-accordion-holder.mkdf-ac-boxed .mkdf-accordion-title:first-child {
  margin: 0;
}

.mkdf-accordion-holder.mkdf-ac-boxed .mkdf-accordion-title .mkdf-accordion-mark {
  right: 28px;
}

.mkdf-accordion-holder.mkdf-ac-boxed .mkdf-accordion-title.ui-state-active {
  border-bottom: none;
}

.mkdf-accordion-holder.mkdf-ac-boxed .mkdf-accordion-content {
  padding: 11px 36px 35px 36px;
}

.mkdf-accordion-holder.mkdf-ac-simple {
  border-bottom: 1px solid rgba(225, 225, 225, 0.3);
}

.mkdf-accordion-holder.mkdf-ac-simple .mkdf-accordion-title {
  padding: 17px 0 17px 30px;
  border-top: 1px solid rgba(225, 225, 225, 0.3);
}

.mkdf-accordion-holder.mkdf-ac-simple .mkdf-accordion-content {
  border-top: 1px solid transparent;
}

.mkdf-accordion-holder.mkdf-ac-simple .mkdf-accordion-content.ui-accordion-content-active {
  border-color: rgba(225, 225, 225, 0.3);
}

.mkdf-accordion-holder.mkdf-ac-simple .mkdf-accordion-content {
  padding: 21px 0 16px;
}

/* ==========================================================================
   Accordions shortcode style - end
   ========================================================================== */
/* ==========================================================================
   Banner shortcode style - begin
   ========================================================================== */
.mkdf-banner-holder {
  position: relative;
  display: inline-block;
  width: 100%;
  vertical-align: middle;
}

.touch .mkdf-banner-holder {
  cursor: pointer;
}

.mkdf-banner-holder.mkdf-visible-on-hover:hover .mkdf-banner-text-holder {
  opacity: 1;
}

.mkdf-banner-holder.mkdf-visible-on-hover .mkdf-banner-text-holder {
  opacity: 0;
  -webkit-transition: opacity 0.2s ease-out;
  transition: opacity 0.2s ease-out;
}

.mkdf-banner-holder.mkdf-disabled .mkdf-banner-text-holder {
  display: none;
}

.mkdf-banner-holder.mkdf-banner-info-centered .mkdf-banner-text-holder {
  padding: 70px 20px;
  text-align: center;
}

.mkdf-banner-holder .mkdf-banner-image {
  position: relative;
  display: inline-block;
  width: 100%;
  vertical-align: middle;
}

.mkdf-banner-holder .mkdf-banner-image img {
  display: block;
}

.mkdf-banner-holder .mkdf-banner-text-holder {
  position: absolute;
  display: block;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  padding: 35px;
  background-color: rgba(27, 44, 88, 0.4);
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}

@media only screen and (max-width: 768px) {
  .mkdf-banner-holder .mkdf-banner-text-holder {
    padding: 25px;
  }
}

.mkdf-banner-holder .mkdf-banner-text-outer {
  position: relative;
  display: table;
  table-layout: fixed;
  height: 100%;
  width: 100%;
}

.mkdf-banner-holder .mkdf-banner-text-inner {
  position: relative;
  display: table-cell;
  height: 100%;
  width: 100%;
  vertical-align: bottom;
}

.mkdf-banner-holder .mkdf-banner-subtitle {
  margin: 0 0 4px;
  color: #fff;
}

.mkdf-banner-holder .mkdf-banner-title {
  margin: 0;
  color: #fff;
}

.mkdf-banner-holder .mkdf-banner-title .mkdf-banner-title-light {
  font-weight: 300;
}

.mkdf-banner-holder .mkdf-banner-link-text {
  position: relative;
  display: inline-block;
  vertical-align: top;
  margin: 11px 0 0;
  color: #fff;
  line-height: 1em;
  z-index: 2;
  -webkit-transform: translateZ(0);
  transform: translateZ(0);
}

.mkdf-banner-holder .mkdf-banner-link-text:hover .mkdf-banner-link-hover {
  width: 100%;
}

.mkdf-banner-holder .mkdf-banner-link-text .mkdf-banner-link-original {
  position: relative;
  display: inline-block;
  vertical-align: top;
  width: 100%;
}

.mkdf-banner-holder .mkdf-banner-link-text .mkdf-banner-link-original span {
  color: inherit;
}

.mkdf-banner-holder .mkdf-banner-link-text .mkdf-banner-link-hover {
  position: absolute;
  top: 0;
  left: 0;
  width: 0.1%;
  height: 100%;
  display: inline-block;
  vertical-align: top;
  white-space: nowrap;
  overflow: hidden;
  -webkit-transition: width 0.4s ease-in-out;
  transition: width 0.4s ease-in-out;
}

.mkdf-banner-holder .mkdf-banner-link-text .mkdf-banner-link-hover span {
  color: #ea3d56;
}

.mkdf-banner-holder .mkdf-banner-link-text .mkdf-banner-link-icon,
.mkdf-banner-holder .mkdf-banner-link-text .mkdf-banner-link-label {
  position: relative;
  display: inline-block;
  vertical-align: top;
}

.mkdf-banner-holder .mkdf-banner-link-text .mkdf-banner-link-icon {
  margin: 0 2px 0 0;
  font-size: 15px;
}

.mkdf-banner-holder .mkdf-banner-link-text .mkdf-banner-link-label {
  font-size: 14px;
  line-height: inherit;
}

.mkdf-banner-holder .mkdf-banner-link {
  position: absolute;
  display: block;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  z-index: 1;
}

/* ==========================================================================
   Banner shortcode style - end
   ========================================================================== */
/* ==========================================================================
   Button shortcode style - begin
   ========================================================================== */
.mkdf-btn {
  position: relative;
  display: inline-block;
  vertical-align: middle;
  width: auto;
  margin: 0;
  font-family: "Josefin Sans", sans-serif;
  font-size: 12px;
  line-height: 2em;
  letter-spacing: 0.16em;
  font-weight: 700;
  text-transform: uppercase;
  outline: none;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  -webkit-transition: color 0.2s ease-in-out, background-color 0.2s ease-in-out, border-color 0.2s ease-in-out;
  transition: color 0.2s ease-in-out, background-color 0.2s ease-in-out, border-color 0.2s ease-in-out;
  padding: 13px 34px 11px;
  cursor: pointer;
}

.mkdf-btn.mkdf-btn-simple {
  padding: 0 !important;
  color: #868890;
  background-color: transparent;
  border: 0;
  vertical-align: middle;
}

.mkdf-btn.mkdf-btn-simple .mkdf-btn-text {
  display: inline-block;
  vertical-align: middle;
  line-height: 50px;
}

.mkdf-btn.mkdf-btn-simple.mkdf-btn-icon > i {
  padding: 11px 9px;
}

.mkdf-btn.mkdf-btn-simple.mkdf-btn-icon .mkdf-btn-text {
  position: relative;
}

.mkdf-btn.mkdf-btn-simple.mkdf-btn-icon .mkdf-btn-text::after {
  content: '';
  position: relative;
  display: block;
  top: -18px;
  width: 100%;
  height: 1px;
  background-color: currentColor;
  -webkit-transform: scaleX(1);
  -ms-transform: scaleX(1);
  transform: scaleX(1);
  -webkit-transform-origin: 0 50%;
  -ms-transform-origin: 0 50%;
  transform-origin: 0 50%;
  -webkit-transition: -webkit-transform .4s ease-out;
  transition: -webkit-transform .4s ease-out;
  transition: transform .4s ease-out;
  transition: transform .4s ease-out, -webkit-transform .4s ease-out;
}

.mkdf-btn.mkdf-btn-simple:hover .mkdf-btn-text::after {
  -webkit-transform-origin: 100% 50%;
  -ms-transform-origin: 100% 50%;
  transform-origin: 100% 50%;
  -webkit-transform: scaleX(0);
  -ms-transform: scaleX(0);
  transform: scaleX(0);
  -webkit-animation: animate-btn-line 0.5s 0.2s forwards;
  animation: animate-btn-line 0.5s 0.2s forwards;
}

.mkdf-btn.mkdf-btn-solid {
  color: #fff;
  background-color: #ea3d56;
  border: 1px solid transparent;
  -webkit-transition: all 0.2s ease-out;
  transition: all 0.2s ease-out;
}

.mkdf-btn.mkdf-btn-solid:not(.mkdf-btn-custom-hover-color):hover {
  color: #fff !important;
}

.mkdf-btn.mkdf-btn-solid:not(.mkdf-btn-custom-hover-bg):hover {
  background-color: #ff4661 !important;
}

.mkdf-btn.mkdf-btn-solid:not(.mkdf-btn-custom-border-hover):hover {
  border-color: #ff4661 !important;
}

.mkdf-btn.mkdf-btn-solid:hover {
  -webkit-box-shadow: none !important;
  box-shadow: none !important;
}

.mkdf-btn.mkdf-btn-outline {
  color: #ea3d56;
  background-color: transparent;
  border: 1px solid #ea3d56;
}

.mkdf-btn.mkdf-btn-outline:not(.mkdf-btn-custom-hover-color):hover {
  color: #fff !important;
}

.mkdf-btn.mkdf-btn-outline:not(.mkdf-btn-custom-hover-bg):hover {
  background-color: #ea3d56 !important;
}

.mkdf-btn.mkdf-btn-outline:not(.mkdf-btn-custom-border-hover):hover {
  border-color: #ea3d56 !important;
}

.mkdf-btn.mkdf-btn-small {
  padding: 11px 24px;
}

.mkdf-btn.mkdf-btn-large {
  padding: 13px 43px 11px;
}

.mkdf-btn.mkdf-btn-huge {
  padding: 21px 60px 16px;
  font-size: 14px;
}

.mkdf-btn.mkdf-btn-icon > i,
.mkdf-btn.mkdf-btn-icon > span:not(.mkdf-btn-text) {
  position: relative;
  display: inline-block;
  vertical-align: middle;
  margin: 0 10px 0 0;
  font-size: 33px;
  line-height: inherit;
}

.mkdf-btn.mkdf-btn-icon > i:before,
.mkdf-btn.mkdf-btn-icon > span:not(.mkdf-btn-text):before {
  display: block;
  line-height: inherit;
}

/* ==========================================================================
   Button shortcode style - end
   ========================================================================== */
/* ==========================================================================
   Call To Action shortcode style - begin
   ========================================================================== */
.mkdf-call-to-action-holder {
  position: relative;
  display: inline-block;
  width: 100%;
  vertical-align: middle;
  /***** Layout Style - begin *****/
  /***** Layout Style - end *****/
  /***** Columns Space - begin *****/
  /***** Columns Space - end *****/
}

.mkdf-call-to-action-holder .mkdf-cta-text-holder,
.mkdf-call-to-action-holder .mkdf-cta-button-holder {
  position: relative;
  display: inline-block;
  vertical-align: middle;
}

.mkdf-call-to-action-holder .mkdf-cta-text-holder h1, .mkdf-call-to-action-holder .mkdf-cta-text-holder h2, .mkdf-call-to-action-holder .mkdf-cta-text-holder h3, .mkdf-call-to-action-holder .mkdf-cta-text-holder h4, .mkdf-call-to-action-holder .mkdf-cta-text-holder h5, .mkdf-call-to-action-holder .mkdf-cta-text-holder h6 {
  margin: 0;
}

.mkdf-call-to-action-holder .mkdf-cta-button-holder .mkdf-btn {
  white-space: nowrap;
}

.mkdf-call-to-action-holder.mkdf-normal-layout .mkdf-cta-inner {
  display: table;
}

.mkdf-call-to-action-holder.mkdf-normal-layout:not(.mkdf-content-in-grid) .mkdf-cta-inner {
  width: 100%;
}

.mkdf-call-to-action-holder.mkdf-normal-layout .mkdf-cta-text-holder,
.mkdf-call-to-action-holder.mkdf-normal-layout .mkdf-cta-button-holder {
  display: table-cell;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}

.mkdf-call-to-action-holder.mkdf-normal-layout .mkdf-cta-button-holder {
  text-align: right;
}

.mkdf-call-to-action-holder.mkdf-simple-layout .mkdf-cta-inner {
  text-align: center;
}

.mkdf-call-to-action-holder.mkdf-simple-layout .mkdf-cta-text-holder,
.mkdf-call-to-action-holder.mkdf-simple-layout .mkdf-cta-button-holder {
  width: 100%;
}

.mkdf-call-to-action-holder.mkdf-simple-layout .mkdf-cta-button-holder {
  margin: 28px 0 0;
}

.mkdf-call-to-action-holder.mkdf-two-halves-columns .mkdf-cta-text-holder,
.mkdf-call-to-action-holder.mkdf-two-halves-columns .mkdf-cta-button-holder {
  width: 50%;
}

.mkdf-call-to-action-holder.mkdf-two-thirds-columns .mkdf-cta-text-holder {
  width: 66.66666666666667%;
}

.mkdf-call-to-action-holder.mkdf-two-thirds-columns .mkdf-cta-button-holder {
  width: 33.33333333333333%;
}

.mkdf-call-to-action-holder.mkdf-three-quarters-columns .mkdf-cta-text-holder {
  width: 75%;
}

.mkdf-call-to-action-holder.mkdf-three-quarters-columns .mkdf-cta-button-holder {
  width: 25%;
}

.mkdf-call-to-action-holder.mkdf-four-fifths-columns .mkdf-cta-text-holder {
  width: 80%;
}

.mkdf-call-to-action-holder.mkdf-four-fifths-columns .mkdf-cta-button-holder {
  width: 20%;
}

/* ==========================================================================
   Call To Action shortcode style - end
   ========================================================================== */
/* ==========================================================================
   Clients Carousel shortcode style - begin
   ========================================================================== */
.mkdf-clients-carousel-holder {
  position: relative;
  display: inline-block;
  width: 100%;
  vertical-align: middle;
  /***** Hover Types - begin *****/
  /***** Hover Types - end *****/
}

.mkdf-clients-carousel-holder .mkdf-cc-inner {
  position: relative;
  display: inline-block;
  width: 100%;
  vertical-align: middle;
}

.mkdf-clients-carousel-holder .mkdf-cc-item {
  position: relative;
  display: inline-block;
  width: 100%;
  vertical-align: middle;
}

.touch .mkdf-clients-carousel-holder .mkdf-cc-item {
  cursor: pointer;
}

.mkdf-clients-carousel-holder .mkdf-cc-item .mkdf-cc-item {
  position: relative;
  display: block;
}

.mkdf-clients-carousel-holder .mkdf-cc-link {
  position: relative;
  display: inline-block;
  vertical-align: top;
}

.mkdf-clients-carousel-holder.mkdf-cc-hover-switch-images .mkdf-cc-item {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
}

.mkdf-clients-carousel-holder.mkdf-cc-hover-switch-images .mkdf-cc-item:hover .mkdf-cc-image {
  opacity: 0;
}

.mkdf-clients-carousel-holder.mkdf-cc-hover-switch-images .mkdf-cc-item:hover .mkdf-cc-hover-image {
  opacity: 1;
}

.mkdf-clients-carousel-holder.mkdf-cc-hover-switch-images .mkdf-cc-item .mkdf-cc-image {
  position: relative;
  display: block;
  width: auto;
  margin: 0 auto;
  opacity: 1;
  -webkit-transform: translateZ(0);
  -webkit-transition: opacity 0.15s ease-out;
  transition: opacity 0.15s ease-out;
}

.mkdf-clients-carousel-holder.mkdf-cc-hover-switch-images .mkdf-cc-item .mkdf-cc-hover-image {
  position: absolute;
  top: 0;
  left: auto;
  width: auto;
  opacity: 0;
  -webkit-transform: translateZ(0);
  -webkit-transition: opacity 0.15s ease-out;
  transition: opacity 0.15s ease-out;
}

.mkdf-clients-carousel-holder.mkdf-cc-hover-opacity-images .mkdf-cc-item {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
}

.mkdf-clients-carousel-holder.mkdf-cc-hover-opacity-images .mkdf-cc-item:hover .mkdf-cc-image {
  opacity: 0.6;
}

.mkdf-clients-carousel-holder.mkdf-cc-hover-opacity-images .mkdf-cc-item .mkdf-cc-image {
  position: relative;
  display: block;
  width: auto;
  margin: 0 auto;
  opacity: 1;
  -webkit-transform: translateZ(0);
  -webkit-transition: opacity 0.15s ease-out;
  transition: opacity 0.15s ease-out;
}

.mkdf-clients-carousel-holder.mkdf-cc-hover-opacity-images .mkdf-cc-item .mkdf-cc-hover-image {
  position: absolute;
  top: 0;
  left: auto;
  width: auto;
  opacity: 0;
  -webkit-transform: translateZ(0);
  -webkit-transition: opacity 0.15s ease-out;
  transition: opacity 0.15s ease-out;
}

.mkdf-clients-carousel-holder.mkdf-cc-hover-roll-over .mkdf-cc-item {
  overflow: hidden;
}

.mkdf-clients-carousel-holder.mkdf-cc-hover-roll-over .mkdf-cc-item:hover .mkdf-cc-image {
  -webkit-transform: translateY(-100%);
  -ms-transform: translateY(-100%);
  transform: translateY(-100%);
}

.mkdf-clients-carousel-holder.mkdf-cc-hover-roll-over .mkdf-cc-item:hover .mkdf-cc-hover-image {
  -webkit-transform: translate(-50%, 0);
  -ms-transform: translate(-50%, 0);
  transform: translate(-50%, 0);
}

.mkdf-clients-carousel-holder.mkdf-cc-hover-roll-over .mkdf-cc-item .mkdf-cc-image {
  position: relative;
  display: block;
  width: auto;
  margin: 0 auto;
  -webkit-transition: -webkit-transform 0.4s ease;
  transition: -webkit-transform 0.4s ease;
  transition: transform 0.4s ease;
  transition: transform 0.4s ease, -webkit-transform 0.4s ease;
}

.mkdf-clients-carousel-holder.mkdf-cc-hover-roll-over .mkdf-cc-item .mkdf-cc-hover-image {
  position: absolute;
  top: 0;
  left: 50%;
  width: auto;
  -webkit-transform: translate(-50%, 100%);
  -ms-transform: translate(-50%, 100%);
  transform: translate(-50%, 100%);
  -webkit-transition: -webkit-transform 0.4s ease;
  transition: -webkit-transform 0.4s ease;
  transition: transform 0.4s ease;
  transition: transform 0.4s ease, -webkit-transform 0.4s ease;
}

/* ==========================================================================
   Clients Carousel shortcode style - end
   ========================================================================== */
/* ==========================================================================
   clients Holder shortcode style - begin
   ========================================================================== */
.mkdf-clients-grid-holder {
  position: relative;
  display: inline-block;
  width: 100%;
  vertical-align: middle;
  text-align: center;
}

.mkdf-clients-grid-holder.mkdf-cg-alignment-left {
  text-align: left;
}

.mkdf-clients-grid-holder.mkdf-cg-alignment-right {
  text-align: right;
}

.mkdf-clients-grid-holder .mkdf-cc-link {
  position: relative;
  display: inline-block;
  vertical-align: top;
}

/* ==========================================================================
   clients Holder shortcode style - end
   ========================================================================== */
/* ==========================================================================
   Countdown shortcode style - begin
   ========================================================================== */
.mkdf-countdown {
  position: relative;
  display: inline-block;
  width: 100%;
  vertical-align: middle;
}

.mkdf-countdown.mkdf-light-skin .countdown-row .countdown-section .countdown-amount,
.mkdf-countdown.mkdf-light-skin .countdown-row .countdown-section .countdown-period {
  color: #fff;
}

.mkdf-countdown .countdown-rtl {
  direction: rtl;
}

.mkdf-countdown .countdown-row {
  position: relative;
  display: inline-block;
  width: 100%;
  vertical-align: middle;
  text-align: center;
  clear: both;
}

.mkdf-countdown .countdown-row.countdown-show1 .countdown-section {
  width: 100%;
}

.mkdf-countdown .countdown-row.countdown-show2 .countdown-section {
  width: 50%;
}

.mkdf-countdown .countdown-row.countdown-show3 .countdown-section {
  width: 33.33333%;
}

.mkdf-countdown .countdown-row.countdown-show4 .countdown-section {
  width: 25%;
}

.mkdf-countdown .countdown-row.countdown-show5 .countdown-section {
  width: 20%;
}

.mkdf-countdown .countdown-row.countdown-show6 .countdown-section {
  width: 16.66667%;
}

.mkdf-countdown .countdown-row .countdown-section {
  position: relative;
  display: inline-block;
  width: 100%;
  vertical-align: middle;
  padding: 0 5px;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}

.mkdf-countdown .countdown-row .countdown-section .countdown-amount {
  position: relative;
  display: block;
  color: #1b2c58;
  font-family: "Josefin Sans", sans-serif;
  letter-spacing: -0.025em;
  font-size: 72px;
  line-height: 1em;
  font-weight: 600;
}

.mkdf-countdown .countdown-row .countdown-section .countdown-period {
  display: block;
  font-family: "Josefin Sans", sans-serif;
  font-size: 15px;
  font-weight: 700;
  font-size: 14px;
  letter-spacing: .025em;
  text-transform: uppercase;
}

/* ==========================================================================
   Countdown shortcode style - end
   ========================================================================== */
/* ==========================================================================
   Counter shortcode style - begin
   ========================================================================== */
.mkdf-counter-holder {
  position: relative;
  display: inline-block;
  width: 100%;
  vertical-align: middle;
  opacity: 0;
  -webkit-transition: opacity 0.01s ease-in;
  transition: opacity 0.01s ease-in;
  text-align: center;
}

.mkdf-counter-holder .mkdf-counter-inner {
  position: relative;
  display: inline-block;
  vertical-align: middle;
}

.mkdf-counter-holder .mkdf-counter {
  height: 1em;
  display: inline-block !important;
  vertical-align: middle;
  color: #1b2c58;
  font-family: "Josefin Sans", sans-serif;
  font-size: 60px;
  letter-spacing: -0.025em;
  line-height: 1em;
  font-weight: 600;
  overflow: hidden;
}

.mkdf-counter-holder .mkdf-counter-title {
  margin: 13px 0 0;
  font-size: 14px;
  letter-spacing: 0.025em;
}

.mkdf-counter-holder .mkdf-counter-text {
  margin: 14px 0 0;
}

/* ==========================================================================
   Counter shortcode style - end
   ========================================================================== */
/* ==========================================================================
   Custom Font shortcode style - begin
   ========================================================================== */
.mkdf-custom-font-holder .mkdf-cf-typed-wrap {
  width: 0;
  white-space: nowrap;
}

.mkdf-custom-font-holder .mkdf-cf-typed {
  display: inline-block;
}

.mkdf-custom-font-holder .mkdf-cf-typed span {
  display: none;
}

.mkdf-custom-font-holder .mkdf-cf-typed ~ .typed-cursor {
  display: inline-block;
  opacity: 1;
  -webkit-animation: blink 0.7s infinite;
  animation: blink 0.7s infinite;
}

@-webkit-keyframes blink {
  0% {
    opacity: 1;
    filter: alpha(opacity=100);
  }
  50% {
    opacity: 0;
    filter: alpha(opacity=0);
  }
  100% {
    opacity: 1;
    filter: alpha(opacity=100);
  }
}

@keyframes blink {
  0% {
    opacity: 1;
    filter: alpha(opacity=100);
  }
  50% {
    opacity: 0;
    filter: alpha(opacity=0);
  }
  100% {
    opacity: 1;
    filter: alpha(opacity=100);
  }
}

/* ==========================================================================
   Custom Font shortcode style - end
   ========================================================================== */
/* ==========================================================================
   Dropcaps shortcode style - begin
   ========================================================================== */
.mkdf-dropcaps {
  position: relative;
  display: inline-block;
  vertical-align: top;
  float: left;
  font-family: "Josefin Sans", sans-serif;
  line-height: 60px;
  font-size: 55px;
  color: #999;
  font-weight: 600;
  text-align: center;
  margin: 0 20px 0 0;
}

.mkdf-dropcaps.mkdf-square, .mkdf-dropcaps.mkdf-circle {
  height: 50px;
  width: 50px;
  font-family: "Josefin Sans", sans-serif;
  font-size: 36px;
  line-height: 58px;
  font-weight: 600;
  color: #fff;
  background-color: #1b2c58;
  margin: 2px 15px 0 0;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}

.mkdf-dropcaps.mkdf-circle {
  border-radius: 3em;
}

/* ==========================================================================
   Dropcaps shortcode style - end
   ========================================================================== */
/* ==========================================================================
   Elements Holder shortcode style - begin
   ========================================================================== */
.mkdf-elements-holder {
  width: 100%;
  display: table;
  table-layout: fixed;
}

.mkdf-elements-holder.mkdf-eh-full-height {
  height: 100%;
}

.mkdf-elements-holder.mkdf-ehi-float .mkdf-eh-item {
  float: left;
}

.mkdf-elements-holder.mkdf-two-columns .mkdf-eh-item {
  width: 50%;
}

.mkdf-elements-holder.mkdf-three-columns .mkdf-eh-item {
  width: 33.33333%;
}

.mkdf-elements-holder.mkdf-four-columns .mkdf-eh-item {
  width: 25%;
}

.mkdf-elements-holder.mkdf-five-columns .mkdf-eh-item {
  width: 20%;
}

.mkdf-elements-holder.mkdf-six-columns .mkdf-eh-item {
  width: 16.66667%;
}

.mkdf-elements-holder .mkdf-eh-item {
  display: table-cell;
  vertical-align: middle;
  height: 100%;
  background-position: center;
  background-size: cover;
}

.mkdf-elements-holder .mkdf-eh-item.mkdf-vertical-alignment-top {
  vertical-align: top;
}

.mkdf-elements-holder .mkdf-eh-item.mkdf-vertical-alignment-bottom {
  vertical-align: bottom;
}

.mkdf-elements-holder .mkdf-eh-item.mkdf-horizontal-alignment-center {
  text-align: center;
}

.mkdf-elements-holder .mkdf-eh-item.mkdf-horizontal-alignment-right {
  text-align: right;
}

.mkdf-elements-holder .mkdf-eh-item .mkdf-elements-holder-item-inner {
  width: 100%;
}

.mkdf-elements-holder .mkdf-ehi-content {
  padding: 0 20px;
}

/* ==========================================================================
   Elements Holder shortcode style - end
   ========================================================================== */
/* ==========================================================================
   Google Map shortcode style - begin
   ========================================================================== */
.mkdf-google-map-holder {
  position: relative;
  display: inline-block;
  width: 100%;
  vertical-align: middle;
}

.mkdf-google-map-holder .mkdf-google-map-direction {
  position: absolute;
  top: 10px;
  left: 10px;
  padding: 0 8px;
  font-size: 13px;
  line-height: 24px;
  color: #868890;
  background-color: #fff;
  z-index: 999;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}

.mkdf-google-map-holder .mkdf-google-map-direction:hover {
  color: #1b2c58;
}

.mkdf-google-map-holder .mkdf-google-map {
  display: block;
  width: 100%;
  height: 300px;
}

.mkdf-google-map-holder .mkdf-google-map iframe,
.mkdf-google-map-holder .mkdf-google-map object,
.mkdf-google-map-holder .mkdf-google-map embed {
  width: 100%;
  display: block;
}

.mkdf-google-map-holder .mkdf-google-map img {
  max-width: none;
}

.mkdf-google-map-holder .mkdf-snazzy-map {
  display: none;
}

.mkdf-google-map-holder .mkdf-google-map-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: none;
  z-index: 1000;
}

/* ==========================================================================
   Google Map shortcode style - end
   ========================================================================== */
/* ==========================================================================
   Icon List Item shortcode style - begin
   ========================================================================== */
.mkdf-icon-list-holder {
  position: relative;
  display: table;
  table-layout: fixed;
  height: auto;
  width: 100%;
  margin-bottom: 8px;
}

.mkdf-icon-list-holder .mkdf-il-icon-holder,
.mkdf-icon-list-holder .mkdf-il-text {
  position: relative;
  display: table-cell;
  vertical-align: top;
}

.mkdf-icon-list-holder .mkdf-il-icon-holder {
  width: 1%;
}

.mkdf-icon-list-holder .mkdf-il-icon-holder > * {
  position: relative;
  display: inline-block;
  vertical-align: top;
  color: #1b2c58;
  font-size: 17px;
  line-height: inherit;
}

.mkdf-icon-list-holder .mkdf-il-icon-holder > *:before {
  display: block;
  line-height: inherit;
}

.mkdf-icon-list-holder .mkdf-il-text {
  width: 99%;
  padding: 0 0 0 13px;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}

/* ==========================================================================
   Icon List Item shortcode style - end
   ========================================================================== */
/* ==========================================================================
   Icon With Text shortcode style - begin
   ========================================================================== */
.mkdf-iwt {
  position: relative;
  display: inline-block;
  width: 100%;
  vertical-align: middle;
  -webkit-transition: all 0.2s ease-out;
  transition: all 0.2s ease-out;
}

.mkdf-iwt:hover {
  cursor: pointer;
}

.mkdf-iwt .mkdf-iwt-icon {
  -webkit-transition: all 0.3s ease-in-out;
  transition: all 0.3s ease-in-out;
}

.mkdf-iwt .mkdf-iwt-icon a {
  position: relative;
  display: inline-block;
  vertical-align: middle;
}

.mkdf-iwt .mkdf-iwt-icon .mkdf-icon-shortcode {
  line-height: 1;
}

.mkdf-iwt .mkdf-iwt-icon .mkdf-icon-shortcode.mkdf-circle, .mkdf-iwt .mkdf-iwt-icon .mkdf-icon-shortcode.mkdf-square, .mkdf-iwt .mkdf-iwt-icon .mkdf-icon-shortcode.mkdf-dropcaps.mkdf-circle {
  line-height: 2;
}

.mkdf-iwt .mkdf-iwt-title {
  margin: 0;
  line-height: 1.2em;
}

.mkdf-iwt .mkdf-iwt-title a {
  position: relative;
  display: inline-block;
  vertical-align: top;
}

.mkdf-iwt .mkdf-iwt-title-text {
  display: block;
}

.mkdf-iwt .mkdf-iwt-text {
  margin: 6px 0;
  font-weight: 500;
}

.mkdf-iwt .mkdf-iwt-text a:hover {
  color: #3745a5 !important;
}

.mkdf-iwt:hover .mkdf-iwt-icon {
  -webkit-transform: translate(0, -3px);
  -webkit-transform: translate(0, -3px);
  -ms-transform: translate(0, -3px);
  transform: translate(0, -3px);
}

.mkdf-iwt.mkdf-iwt-icon-left {
  width: auto;
}

.mkdf-iwt.mkdf-iwt-icon-left .mkdf-iwt-icon,
.mkdf-iwt.mkdf-iwt-icon-left .mkdf-iwt-content {
  display: table-cell;
  vertical-align: top;
}

.mkdf-iwt.mkdf-iwt-icon-left .mkdf-iwt-icon {
  position: relative;
  top: 1px;
  padding: 5px 0 0 8px;
}

.mkdf-iwt.mkdf-iwt-icon-left .mkdf-iwt-icon img {
  max-width: none;
}

.mkdf-iwt.mkdf-iwt-icon-left .mkdf-iwt-content {
  padding: 0 0 0 22px;
  vertical-align: middle;
}

.mkdf-iwt.mkdf-iwt-icon-left-from-title .mkdf-iwt-icon,
.mkdf-iwt.mkdf-iwt-icon-left-from-title .mkdf-iwt-title-text {
  position: relative;
  display: table-cell;
  vertical-align: middle;
}

.mkdf-iwt.mkdf-iwt-icon-left-from-title .mkdf-iwt-icon .mkdf-icon-element {
  -webkit-transition: none;
  transition: none;
}

.mkdf-iwt.mkdf-iwt-icon-left-from-title .mkdf-iwt-icon img {
  max-width: none;
}

.mkdf-iwt.mkdf-iwt-icon-left-from-title .mkdf-iwt-title-text {
  padding: 0 0 0 17px;
}

.mkdf-iwt.mkdf-iwt-icon-top {
  text-align: center;
}

.mkdf-iwt.mkdf-iwt-icon-top .mkdf-iwt-content {
  padding: 23px 0 0;
}

.mkdf-iwt.boxed-shadow {
  width: 100%;
  padding: 30px;
  border-radius: 8px;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  background: #fff;
  -webkit-transition: all 0.2s ease-out;
  transition: all 0.2s ease-out;
}

.mkdf-iwt.boxed-shadow .mkdf-iwt-text {
  font-size: 11px;
  line-height: 1em;
  letter-spacing: 0.075em;
}

.mkdf-iwt.boxed-hover-shadow {
  width: 100%;
  padding: 30px;
  border-radius: 8px;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  background: #fff;
}

@media only screen and (max-width: 1366px) {
  .mkdf-iwt.boxed-hover-shadow.mkdf-iwt-icon-medium {
    padding: 30px 30px 30px 20px !important;
  }
}

.mkdf-iwt.boxed-hover-shadow .mkdf-iwt-text {
  font-size: 11px;
  line-height: 1em;
  letter-spacing: 0.075em;
}

/* ==========================================================================
   Icon With Text shortcode style - end
   ========================================================================== */
/* ==========================================================================
   Icon shortcode style - begin
   ========================================================================== */
.mkdf-icon-shortcode {
  position: relative;
  display: inline-block;
  vertical-align: middle;
  line-height: 1.1em;
}

.mkdf-icon-shortcode.mkdf-circle, .mkdf-icon-shortcode.mkdf-square, .mkdf-icon-shortcode.mkdf-dropcaps.mkdf-circle {
  width: 2em;
  height: 2em;
  line-height: 2em;
  text-align: center;
  background-color: #ea3d56;
  -webkit-transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out;
  transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out;
}

.mkdf-icon-shortcode.mkdf-circle a, .mkdf-icon-shortcode.mkdf-square a, .mkdf-icon-shortcode.mkdf-dropcaps.mkdf-circle a {
  position: relative;
  display: inline-block;
  vertical-align: top;
  width: 100%;
  height: 100%;
}

.mkdf-icon-shortcode.mkdf-circle .mkdf-icon-element, .mkdf-icon-shortcode.mkdf-square .mkdf-icon-element, .mkdf-icon-shortcode.mkdf-dropcaps.mkdf-circle .mkdf-icon-element {
  color: #fff;
  line-height: inherit;
}

.mkdf-icon-shortcode.mkdf-circle {
  border-radius: 50%;
}

.mkdf-icon-shortcode .mkdf-icon-element {
  display: block;
  line-height: inherit;
  -webkit-transition: color 0.15s ease-in-out;
  transition: color 0.15s ease-in-out;
}

.mkdf-icon-shortcode .mkdf-icon-element:before {
  display: block;
  line-height: inherit;
}

.mkdf-icon-shortcode.mkdf-icon-switch {
  overflow: hidden;
}

.mkdf-icon-shortcode.mkdf-icon-switch .mkdf-icon-original {
  -webkit-transform: translateY(0);
  -ms-transform: translateY(0);
  transform: translateY(0);
  -webkit-transition: -webkit-transform 0.5s ease;
  transition: -webkit-transform 0.5s ease;
  transition: transform 0.5s ease;
  transition: transform 0.5s ease, -webkit-transform 0.5s ease;
}

.mkdf-icon-shortcode.mkdf-icon-switch .mkdf-icon-duplicate {
  position: absolute;
  top: 100%;
  left: 0;
  width: 100%;
  height: 100%;
  -webkit-transform: translateY(0);
  -ms-transform: translateY(0);
  transform: translateY(0);
  -webkit-transition: -webkit-transform 0.5s ease;
  transition: -webkit-transform 0.5s ease;
  transition: transform 0.5s ease;
  transition: transform 0.5s ease, -webkit-transform 0.5s ease;
}

.mkdf-icon-shortcode.mkdf-icon-switch:hover .mkdf-icon-original {
  -webkit-transform: translateY(-100%);
  -ms-transform: translateY(-100%);
  transform: translateY(-100%);
}

.mkdf-icon-shortcode.mkdf-icon-switch:hover .mkdf-icon-duplicate {
  -webkit-transform: translateY(-100%);
  -ms-transform: translateY(-100%);
  transform: translateY(-100%);
}

.mkdf-icon-animation-holder {
  position: relative;
  display: inline-block;
  vertical-align: middle;
  -webkit-transform: scale(0);
  -ms-transform: scale(0);
  transform: scale(0);
  -webkit-transition: transform 0.15s ease-in-out;
  -webkit-transition: -webkit-transform 0.15s ease-in-out;
  transition: -webkit-transform 0.15s ease-in-out;
  transition: transform 0.15s ease-in-out;
  transition: transform 0.15s ease-in-out, -webkit-transform 0.15s ease-in-out;
}

.mkdf-icon-animation-holder.mkdf-icon-animation-show {
  -webkit-transform: scale(1);
  -ms-transform: scale(1);
  transform: scale(1);
}

.mkdf-icon-tiny {
  font-size: 1.33333333em;
  line-height: .75em;
  vertical-align: -15%;
}

.mkdf-icon-small {
  font-size: 2em;
}

.mkdf-icon-medium {
  font-size: 3em;
}

.mkdf-icon-large {
  font-size: 4em;
}

.mkdf-icon-huge {
  font-size: 5em;
}

/* ==========================================================================
   Icon shortcode style - end
   ========================================================================== */
/* ==========================================================================
   Image Gallery shortcode style - begin
   ========================================================================== */
.mkdf-image-gallery {
  position: relative;
  display: inline-block;
  width: 100%;
  vertical-align: middle;
  /***** Image Gallery Masonry Style - begin *****/
  /***** Image Gallery Masonry Style - end *****/
  /***** Custom Link Behavior Style - begin *****/
  /***** Custom Link Behavior Style - end *****/
  /***** Lightbox Behavior Style - begin *****/
  /***** Lightbox Behavior Style - end *****/
  /***** Zoom Behavior Style - begin *****/
  /***** Zoom Behavior Style - end *****/
  /***** Grayscale Behavior Style - begin *****/
  /***** Grayscale Behavior Style - end *****/
}

@media only screen and (max-width: 680px) {
  .mkdf-image-gallery .owl-nav {
    display: none;
  }
}

.mkdf-image-gallery .owl-nav .owl-prev, .mkdf-image-gallery .owl-nav .owl-next {
  -webkit-transition: opacity .2s ease;
  transition: opacity .2s ease;
  -webkit-transform: translateY(-50%) !important;
  -ms-transform: translateY(-50%) !important;
  transform: translateY(-50%) !important;
}

.mkdf-image-gallery .owl-nav .owl-prev.disabled, .mkdf-image-gallery .owl-nav .owl-next.disabled {
  opacity: 0;
}

.mkdf-image-gallery .owl-nav .owl-prev span, .mkdf-image-gallery .owl-nav .owl-next span {
  font-size: 48px;
  color: #fff;
  width: 80px;
  height: 80px;
  line-height: 80px;
  background-color: #ea3d56;
  border-radius: 50%;
  -webkit-box-shadow: 0 -5px 10px rgba(234, 61, 86, 0.3);
  box-shadow: 0 -5px 10px rgba(234, 61, 86, 0.3);
}

.mkdf-image-gallery .owl-nav .owl-prev {
  left: 13%;
}

.mkdf-image-gallery .owl-nav .owl-next {
  right: 13%;
}

.mkdf-image-gallery .owl-dots {
  display: none;
}

@media only screen and (max-width: 680px) {
  .mkdf-image-gallery .owl-dots {
    display: block;
  }
}

.mkdf-image-gallery.mkdf-ig-carousel-type .owl-item:first-child {
  margin-left: -346px;
}

@media screen and (max-width: 1366px) {
  .mkdf-image-gallery.mkdf-ig-carousel-type .owl-item:first-child {
    margin-left: -247px;
  }
}

@media screen and (max-width: 1024px) {
  .mkdf-image-gallery.mkdf-ig-carousel-type .owl-item:first-child {
    margin-left: -82px;
  }
}

@media screen and (max-width: 768px) {
  .mkdf-image-gallery.mkdf-ig-carousel-type .owl-item:first-child {
    margin-left: 0;
  }
}

.mkdf-image-gallery.mkdf-has-shadow .mkdf-ig-image-inner {
  -webkit-box-shadow: 0 0 4.85px 0.15px rgba(0, 0, 0, 0.09);
  box-shadow: 0 0 4.85px 0.15px rgba(0, 0, 0, 0.09);
}

.mkdf-image-gallery.mkdf-has-shadow.mkdf-ig-slider-type .owl-stage-outer, .mkdf-image-gallery.mkdf-has-shadow.mkdf-ig-carousel-type .owl-stage-outer {
  padding: 0 0 20px;
}

.mkdf-image-gallery.mkdf-has-shadow.mkdf-ig-slider-type .mkdf-ig-image, .mkdf-image-gallery.mkdf-has-shadow.mkdf-ig-carousel-type .mkdf-ig-image {
  -webkit-box-shadow: 0 0 4.85px 0.15px rgba(0, 0, 0, 0.09);
  box-shadow: 0 0 4.85px 0.15px rgba(0, 0, 0, 0.09);
}

.mkdf-image-gallery .mkdf-ig-image a, .mkdf-image-gallery .mkdf-ig-image img {
  position: relative;
  display: block;
}

.mkdf-image-gallery .mkdf-ig-image-inner {
  position: relative;
  display: inline-block;
  width: 100%;
  vertical-align: middle;
}

.mkdf-image-gallery .mkdf-ig-slider {
  position: relative;
  display: inline-block;
  width: 100%;
  vertical-align: middle;
}

.mkdf-image-gallery.mkdf-ig-masonry-type .mkdf-ig-image.mkdf-fixed-masonry-item .mkdf-ig-image-inner,
.mkdf-image-gallery.mkdf-ig-masonry-type .mkdf-ig-image.mkdf-fixed-masonry-item a {
  height: 100%;
}

.mkdf-image-gallery.mkdf-image-behavior-custom-link .mkdf-ig-image a:hover:after {
  opacity: 1;
}

.mkdf-image-gallery.mkdf-image-behavior-custom-link .mkdf-ig-image a:after {
  position: absolute;
  display: block;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  content: '';
  background-color: rgba(234, 61, 86, 0.4);
  opacity: 0;
  -webkit-transition: opacity 0.2s ease-in-out;
  transition: opacity 0.2s ease-in-out;
}

.mkdf-image-gallery.mkdf-image-behavior-custom-link .mkdf-ig-image a img {
  border-radius: 10px;
}

.mkdf-image-gallery.mkdf-image-behavior-custom-link .mkdf-ig-image a:after {
  border-radius: 10px;
}

.mkdf-image-gallery.mkdf-image-behavior-custom-link.mkdf-ig-carousel-type .mkdf-ig-image {
  -webkit-transition: all 0.3s ease-in-out;
  transition: all 0.3s ease-in-out;
}

.mkdf-image-gallery.mkdf-image-behavior-custom-link.mkdf-ig-carousel-type .mkdf-ig-image a:after {
  display: none;
}

.mkdf-image-gallery.mkdf-image-behavior-custom-link.mkdf-ig-carousel-type .mkdf-ig-image:hover {
  -webkit-box-shadow: 6px 36px 67px -27px rgba(0, 0, 0, 0.1);
  box-shadow: 6px 36px 67px -27px rgba(0, 0, 0, 0.1);
}

.mkdf-image-gallery.mkdf-image-behavior-custom-link .owl-dot span {
  background: rgba(0, 0, 0, 0.25);
  border: 0;
}

.mkdf-image-gallery.mkdf-image-behavior-custom-link .owl-dot.active span {
  background: rgba(0, 0, 0, 0.5);
}

.mkdf-image-gallery.mkdf-image-behavior-lightbox .mkdf-ig-image {
  position: relative;
}

.mkdf-image-gallery.mkdf-image-behavior-lightbox .mkdf-ig-image a:after {
  position: absolute;
  display: block;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  content: '';
  background-color: #74cccd;
  opacity: 0;
  -webkit-transition: opacity 0.2s ease-in-out;
  transition: opacity 0.2s ease-in-out;
}

.mkdf-image-gallery.mkdf-image-behavior-lightbox .mkdf-ig-image a:before {
  content: "\4c";
  font-family: ElegantIcons;
  position: absolute;
  font-size: 72px;
  color: #fff;
  opacity: 0;
  text-align: center;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  top: calc(50% - 36px);
  left: calc(50% - 36px);
  right: 0;
  z-index: 9;
  -webkit-transition: all 0.4s ease-in-out;
  transition: all 0.4s ease-in-out;
  -webkit-transform-origin: 50% 50%;
  -ms-transform-origin: 50% 50%;
  transform-origin: 50% 50%;
  width: 72px;
  height: 72px;
  line-height: 72px;
  display: block;
}

.mkdf-image-gallery.mkdf-image-behavior-lightbox .mkdf-ig-image:hover a:after {
  opacity: 1;
}

.mkdf-image-gallery.mkdf-image-behavior-lightbox .mkdf-ig-image:hover a:before {
  opacity: 1;
  -webkit-transform: rotate(90deg);
  -ms-transform: rotate(90deg);
  transform: rotate(90deg);
}

.touch .mkdf-image-gallery.mkdf-image-behavior-zoom .mkdf-ig-image {
  cursor: pointer;
}

.mkdf-image-gallery.mkdf-image-behavior-zoom .mkdf-ig-image:hover img {
  -webkit-transform: scale(1.04);
  -ms-transform: scale(1.04);
  transform: scale(1.04);
}

.mkdf-image-gallery.mkdf-image-behavior-zoom .mkdf-ig-image .mkdf-ig-image-inner {
  overflow: hidden;
}

.mkdf-image-gallery.mkdf-image-behavior-zoom .mkdf-ig-image img {
  -webkit-transform: scale(1);
  -ms-transform: scale(1);
  transform: scale(1);
  -webkit-transition: -webkit-transform 0.3s ease-in-out;
  transition: -webkit-transform 0.3s ease-in-out;
  transition: transform 0.3s ease-in-out;
  transition: transform 0.3s ease-in-out, -webkit-transform 0.3s ease-in-out;
}

.mkdf-image-gallery.mkdf-image-behavior-grayscale .mkdf-ig-image {
  overflow: hidden;
}

.touch .mkdf-image-gallery.mkdf-image-behavior-grayscale .mkdf-ig-image {
  cursor: pointer;
}

.mkdf-image-gallery.mkdf-image-behavior-grayscale .mkdf-ig-image:hover img {
  -webkit-filter: grayscale(0);
  filter: none;
}

.mkdf-image-gallery.mkdf-image-behavior-grayscale .mkdf-ig-image img {
  filter: url("img/desaturate.svg#grayscale");
  -webkit-filter: grayscale(100%);
  -moz-filter: grayscale(100%);
  filter: gray;
  filter: grayscale(100%);
  -webkit-transition: all 0.3s ease-in-out;
  transition: all 0.3s ease-in-out;
}

/* ==========================================================================
   Image Gallery shortcode style - end
   ========================================================================== */
/* ==========================================================================
   Image With Text shortcode style - begin
   ========================================================================== */
.mkdf-image-with-text-holder {
  position: relative;
  display: inline-block;
  width: 100%;
  vertical-align: middle;
  -webkit-transition: all 0.3s ease-in-out;
  transition: all 0.3s ease-in-out;
  /***** Custom Link Behavior Style - begin *****/
  /***** Custom Link Behavior Style - end *****/
  /***** Lightbox Behavior Style - begin *****/
  /***** Lightbox Behavior Style - end *****/
  /***** Zoom Behavior Style - begin *****/
  /***** Zoom Behavior Style - end *****/
  /***** Grayscale Behavior Style - begin *****/
  /***** Grayscale Behavior Style - end *****/
}

.mkdf-image-with-text-holder.mkdf-has-shadow .mkdf-iwt-image {
  -webkit-box-shadow: 0 0 42.85px 7.15px rgba(0, 0, 0, 0.09);
  box-shadow: 0 0 42.85px 7.15px rgba(0, 0, 0, 0.09);
}

.mkdf-image-with-text-holder .mkdf-iwt-image {
  position: relative;
  display: inline-block;
  width: 100%;
  vertical-align: middle;
  width: auto;
}

.mkdf-image-with-text-holder .mkdf-iwt-image a, .mkdf-image-with-text-holder .mkdf-iwt-image img {
  position: relative;
  display: block;
  -webkit-transition: all 0.3s ease;
  transition: all 0.3s ease;
}

.mkdf-image-with-text-holder .mkdf-iwt-image .mkdf-icon-shortcode {
  position: absolute;
  right: -20px;
  top: -15px;
  z-index: 5;
  font-size: 20px;
  -webkit-transition: all 0.3s ease-out;
  transition: all 0.3s ease-out;
}

.mkdf-image-with-text-holder .mkdf-iwt-image:hover .mkdf-icon-shortcode {
  -webkit-animation: mkdfPulsesmallfirst 1.8s infinite;
  animation: mkdfPulsesmallfirst 1.8s infinite;
}

.mkdf-image-with-text-holder .mkdf-iwt-text-holder {
  position: relative;
  display: inline-block;
  width: 100%;
  vertical-align: middle;
}

.mkdf-image-with-text-holder .mkdf-iwt-title {
  margin: 40px 0 0;
}

.mkdf-image-with-text-holder .mkdf-iwt-text {
  margin: 18px 0 0;
  line-height: 1.8em;
}

.mkdf-image-with-text-holder.mkdf-image-behavior-custom-link:hover {
  -webkit-transform: translate(0, -8px);
  -webkit-transform: translate(0, -8px);
  -ms-transform: translate(0, -8px);
  transform: translate(0, -8px);
}

.mkdf-image-with-text-holder.mkdf-image-behavior-lightbox .mkdf-iwt-image a:hover:after {
  opacity: 1;
}

.mkdf-image-with-text-holder.mkdf-image-behavior-lightbox .mkdf-iwt-image a:after {
  position: absolute;
  display: block;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  content: '';
  background-color: rgba(234, 61, 86, 0.4);
  opacity: 0;
  -webkit-transition: opacity 0.2s ease-in-out;
  transition: opacity 0.2s ease-in-out;
}

.mkdf-image-with-text-holder.mkdf-image-behavior-zoom .mkdf-iwt-image {
  overflow: hidden;
}

.touch .mkdf-image-with-text-holder.mkdf-image-behavior-zoom .mkdf-iwt-image {
  cursor: pointer;
}

.mkdf-image-with-text-holder.mkdf-image-behavior-zoom .mkdf-iwt-image:hover img {
  -webkit-transform: scale(1.04);
  -ms-transform: scale(1.04);
  transform: scale(1.04);
}

.mkdf-image-with-text-holder.mkdf-image-behavior-zoom .mkdf-iwt-image img {
  -webkit-transform: scale(1);
  -ms-transform: scale(1);
  transform: scale(1);
  -webkit-transition: -webkit-transform 0.3s ease-in-out;
  transition: -webkit-transform 0.3s ease-in-out;
  transition: transform 0.3s ease-in-out;
  transition: transform 0.3s ease-in-out, -webkit-transform 0.3s ease-in-out;
}

.mkdf-image-with-text-holder.mkdf-image-behavior-grayscale .mkdf-iwt-image {
  overflow: hidden;
}

.touch .mkdf-image-with-text-holder.mkdf-image-behavior-grayscale .mkdf-iwt-image {
  cursor: pointer;
}

.mkdf-image-with-text-holder.mkdf-image-behavior-grayscale .mkdf-iwt-image:hover img {
  -webkit-filter: grayscale(0);
  filter: none;
}

.mkdf-image-with-text-holder.mkdf-image-behavior-grayscale .mkdf-iwt-image img {
  filter: url("img/desaturate.svg#grayscale");
  -webkit-filter: grayscale(100%);
  -moz-filter: grayscale(100%);
  filter: gray;
  filter: grayscale(100%);
  -webkit-transition: all 0.3s ease-in-out;
  transition: all 0.3s ease-in-out;
}

/***** Landing Appeared Item start *****/
.custom-image-padding-row {
  -webkit-transition: opacity 0.22s cubic-bezier(0.23, 1, 0.32, 1), -webkit-transform 0s cubic-bezier(0.23, 1, 0.32, 1);
  transition: opacity 0.22s cubic-bezier(0.23, 1, 0.32, 1), -webkit-transform 0s cubic-bezier(0.23, 1, 0.32, 1);
  transition: opacity 0.22s cubic-bezier(0.23, 1, 0.32, 1), transform 0s cubic-bezier(0.23, 1, 0.32, 1);
  transition: opacity 0.22s cubic-bezier(0.23, 1, 0.32, 1), transform 0s cubic-bezier(0.23, 1, 0.32, 1), -webkit-transform 0s cubic-bezier(0.23, 1, 0.32, 1);
}

.custom-image-padding-row .mkdf-image-with-text-holder .mkdf-iwt-image {
  opacity: 0;
  -webkit-transform: scale(0.6);
  -ms-transform: scale(0.6);
  transform: scale(0.6);
  -webkit-transition: -webkit-transform 0.6s cubic-bezier(0.18, 0.89, 0.32, 1), opacity 0.2s ease-out;
  -webkit-transition: opacity 0.2s ease-out, -webkit-transform 0.4s cubic-bezier(0.18, 0.89, 0.32, 1);
  transition: opacity 0.2s ease-out, -webkit-transform 0.4s cubic-bezier(0.18, 0.89, 0.32, 1);
  transition: transform 0.4s cubic-bezier(0.18, 0.89, 0.32, 1), opacity 0.2s ease-out;
  transition: transform 0.4s cubic-bezier(0.18, 0.89, 0.32, 1), opacity 0.2s ease-out, -webkit-transform 0.4s cubic-bezier(0.18, 0.89, 0.32, 1);
}

.custom-image-padding-row .mkdf-image-with-text-holder .mkdf-iwt-image.mkdf-landing-appeared {
  opacity: 1;
  -webkit-transform: scale(1);
  -ms-transform: scale(1);
  transform: scale(1);
}

/***** Landing Appeared Item end *****/
/* ==========================================================================
   Image With Text shortcode style - end
   ========================================================================== */
/* ==========================================================================
   Pie Chart shortcode style - begin
   ========================================================================== */
.mkdf-pie-chart-holder {
  position: relative;
  display: inline-block;
  width: 100%;
  vertical-align: middle;
  opacity: 0;
  -webkit-transition: opacity 0.2s ease-in;
  transition: opacity 0.2s ease-in;
}

.mkdf-pie-chart-holder .mkdf-pc-percentage {
  position: relative;
  display: block;
  height: 176px;
  width: 176px;
  line-height: 176px;
  text-align: center;
  margin: 0 auto;
}

.mkdf-pie-chart-holder .mkdf-pc-percentage canvas {
  position: absolute;
  top: 0;
  left: 0;
}

.mkdf-pie-chart-holder .mkdf-pc-percentage .mkdf-pc-percent {
  position: relative;
  display: inline-block;
  vertical-align: middle;
  color: #1b2c58;
  font-size: 30px;
  line-height: inherit;
  font-weight: 600;
  font-family: "Josefin Sans", sans-serif;
}

.mkdf-pie-chart-holder .mkdf-pc-percentage .mkdf-pc-percent:after {
  position: relative;
  content: '%';
  font-size: 30px;
}

.mkdf-pie-chart-holder .mkdf-pc-text-holder {
  position: relative;
  display: inline-block;
  width: 100%;
  vertical-align: middle;
  text-align: center;
  margin: 38px 0 0;
}

.mkdf-pie-chart-holder .mkdf-pc-text-holder .mkdf-pc-title {
  margin: 0;
}

.mkdf-pie-chart-holder .mkdf-pc-text-holder .mkdf-pc-text {
  margin: 7px 0 0;
  font-family: "Josefin Sans", sans-serif;
  font-size: 12px;
  font-weight: 700;
  letter-spacing: 0.09em;
}

/* ==========================================================================
   Pie Chart shortcode style - end
   ========================================================================== */
/* ==========================================================================
   Pricing Tables shortcode style - begin
   ========================================================================== */
.mkdf-pricing-tables {
  position: relative;
  display: inline-block;
  width: 100%;
  vertical-align: middle;
}

@media screen and (max-width: 680px) {
  .mkdf-price-table {
    padding: 0 0 37px !important;
  }
  .mkdf-price-table:last-child {
    padding-bottom: 0 !important;
  }
}

.mkdf-price-table .mkdf-pt-inner {
  position: relative;
  display: inline-block;
  width: 100%;
  vertical-align: middle;
  border: 2px solid rgba(237, 238, 239, 0.6);
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}

.mkdf-price-table .mkdf-pt-inner > ul {
  list-style: none;
  margin: 0;
  padding: 0;
}

.mkdf-price-table .mkdf-pt-inner > ul > li {
  margin: 0;
  text-align: center;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}

.mkdf-price-table .mkdf-pt-inner > ul > li.mkdf-pt-title-holder {
  padding: 10px 20px;
  position: relative;
  height: 203px;
  color: #1b2c58;
  font-size: 18px;
  line-height: 26px;
  font-weight: 600;
}

.mkdf-price-table .mkdf-pt-inner > ul > li.mkdf-pt-title-holder .mkdf-pt-active-title {
  display: block;
  font-family: "Josefin Sans", sans-serif;
  color: #ea3d56;
  font-size: 14px;
  font-weight: 700;
  letter-spacing: .1em;
  text-transform: uppercase;
  padding: 15px 0 17px;
}

.mkdf-price-table .mkdf-pt-inner > ul > li.mkdf-pt-title-holder .mkdf-pt-title {
  display: block;
  font-size: 36px;
  font-family: "Josefin Sans", sans-serif;
  letter-spacing: -.025em;
  font-weight: 600;
  padding: 28px 0 22px;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}

.mkdf-price-table .mkdf-pt-inner > ul > li.mkdf-pt-content > ul {
  list-style: none;
  margin: 0;
  padding: 0;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
}

.mkdf-price-table .mkdf-pt-inner > ul > li.mkdf-pt-content > ul > li {
  padding: 20px;
  line-height: 33px;
  border-top: 2px solid rgba(225, 225, 225, 0.3);
  font-size: 20px;
  font-weight: 600;
  color: #1b2c58;
  letter-spacing: -.025em;
}

.mkdf-price-table .mkdf-pt-inner > ul > li.mkdf-pt-content > ul > li:last-child {
  border-bottom: 2px solid rgba(225, 225, 225, 0.3);
}

.mkdf-price-table .mkdf-pt-inner > ul > li.mkdf-pt-prices {
  position: relative;
  padding: 33px 15px 28px;
  border-bottom: 2px solid rgba(225, 225, 225, 0.3);
}

.mkdf-price-table .mkdf-pt-inner > ul > li.mkdf-pt-prices .mkdf-pt-value {
  position: relative;
  display: inline-block;
  vertical-align: middle;
  font-size: 60px;
  font-weight: 600;
  line-height: 1em;
  color: #1b2c58;
  font-family: "Josefin Sans", sans-serif;
}

.mkdf-price-table .mkdf-pt-inner > ul > li.mkdf-pt-prices .mkdf-pt-price {
  position: relative;
  display: inline-block;
  vertical-align: middle;
  font-size: 60px;
  font-weight: 600;
  line-height: 1em;
  color: #1b2c58;
  font-family: "Josefin Sans", sans-serif;
}

.mkdf-price-table .mkdf-pt-inner > ul > li.mkdf-pt-prices .mkdf-pt-mark {
  position: relative;
  display: block;
  font-size: 14px;
  font-weight: 600;
  line-height: 1em;
  color: rgba(204, 204, 204, 0.8);
  font-family: "Josefin Sans", sans-serif;
  letter-spacing: .1em;
  margin: 12px 0 0;
}

.mkdf-price-table .mkdf-pt-inner > ul > li.mkdf-pt-button {
  padding: 0;
}

.mkdf-price-table .mkdf-pt-inner > ul > li.mkdf-pt-button a {
  padding: 24px 0;
  width: 100%;
  border: none !important;
  color: #1b2c58;
}

.mkdf-price-table .mkdf-pt-inner > ul > li.mkdf-pt-button a.mkdf-btn-solid {
  color: #fff !important;
}

.mkdf-pt-active-item.mkdf-price-table {
  -webkit-transform: translateY(-85px);
  -ms-transform: translateY(-85px);
  transform: translateY(-85px);
  z-index: 10;
}

@media screen and (max-width: 680px) {
  .mkdf-pt-active-item.mkdf-price-table {
    -webkit-transform: none;
    -ms-transform: none;
    transform: none;
  }
}

.mkdf-pt-active-item.mkdf-price-table .mkdf-pt-inner {
  border-color: transparent;
  border-left: 0;
  border-right: 0;
  border-bottom: 0;
  width: calc(100% + 6px);
  margin: 0 -3px;
}

@media screen and (max-width: 680px) {
  .mkdf-pt-active-item.mkdf-price-table .mkdf-pt-inner {
    width: 100%;
    margin: 0;
  }
}

.mkdf-pt-active-item.mkdf-price-table .mkdf-pt-inner > ul > li.mkdf-pt-title-holder {
  height: 288px;
}

.mkdf-pt-active-item.mkdf-price-table .mkdf-pt-inner > ul > li.mkdf-pt-title-holder .mkdf-pt-title {
  padding: 0 0 22px;
}

.mkdf-pt-active-item.mkdf-price-table .mkdf-pt-inner > ul > li.mkdf-pt-button {
  padding: 0;
}

.mkdf-pt-active-item.mkdf-price-table .mkdf-pt-inner > ul > li.mkdf-pt-button a {
  padding: 26px 0;
  margin-top: -2px;
}

/* ==========================================================================
   Custom left menu style - start
   ========================================================================== */
.mkdf-custom-opacity {
  -webkit-transition: opacity 0.22s cubic-bezier(0.23, 1, 0.32, 1), -webkit-transform 0s cubic-bezier(0.23, 1, 0.32, 1);
  transition: opacity 0.22s cubic-bezier(0.23, 1, 0.32, 1), -webkit-transform 0s cubic-bezier(0.23, 1, 0.32, 1);
  transition: opacity 0.22s cubic-bezier(0.23, 1, 0.32, 1), transform 0s cubic-bezier(0.23, 1, 0.32, 1);
  transition: opacity 0.22s cubic-bezier(0.23, 1, 0.32, 1), transform 0s cubic-bezier(0.23, 1, 0.32, 1), -webkit-transform 0s cubic-bezier(0.23, 1, 0.32, 1);
}

.mkdf-custom-opacity h5 {
  -webkit-transition: all 0.3s ease-in-out;
  transition: all 0.3s ease-in-out;
  opacity: 0;
  -webkit-transition: -webkit-transform 0.6s cubic-bezier(0.18, 0.89, 0.32, 1), opacity 0.2s ease-out;
  -webkit-transition: opacity 0.2s ease-out, -webkit-transform 0.4s cubic-bezier(0.18, 0.89, 0.32, 1);
  transition: opacity 0.2s ease-out, -webkit-transform 0.4s cubic-bezier(0.18, 0.89, 0.32, 1);
  transition: transform 0.4s cubic-bezier(0.18, 0.89, 0.32, 1), opacity 0.2s ease-out;
  transition: transform 0.4s cubic-bezier(0.18, 0.89, 0.32, 1), opacity 0.2s ease-out, -webkit-transform 0.4s cubic-bezier(0.18, 0.89, 0.32, 1);
}

.mkdf-custom-opacity h5.mkdf-appeared {
  opacity: 1;
}

.mkdf-custom-opacity h5:hover {
  opacity: 0.6;
}

/* ==========================================================================
   Custom left menu style - start
   ========================================================================== */
/* ==========================================================================
   Pricing Tables shortcode style - end
   ========================================================================== */
/* ==========================================================================
   Process shortcode style - begin
   ========================================================================== */
.mkdf-process-holder {
  position: relative;
  display: inline-block;
  width: 100%;
  vertical-align: middle;
}

.mkdf-process-holder.mkdf-two-columns .mkdf-mark-horizontal-holder .mkdf-process-mark {
  width: 50%;
}

.mkdf-process-holder.mkdf-two-columns .mkdf-mark-vertical-holder .mkdf-process-mark {
  height: 50%;
}

.mkdf-process-holder.mkdf-two-columns .mkdf-process-item {
  width: 50%;
}

.mkdf-process-holder.mkdf-three-columns .mkdf-mark-horizontal-holder .mkdf-process-mark {
  width: 33.33333%;
}

.mkdf-process-holder.mkdf-three-columns .mkdf-mark-vertical-holder .mkdf-process-mark {
  height: 33.33333%;
}

.mkdf-process-holder.mkdf-three-columns .mkdf-process-item {
  width: 33.33333%;
}

.mkdf-process-holder.mkdf-four-columns .mkdf-mark-horizontal-holder .mkdf-process-mark {
  width: 25%;
}

.mkdf-process-holder.mkdf-four-columns .mkdf-mark-vertical-holder .mkdf-process-mark {
  height: 25%;
}

.mkdf-process-holder.mkdf-four-columns .mkdf-process-item {
  width: 25%;
}

.mkdf-process-holder.mkdf-process-appeared .mkdf-process-circle {
  opacity: 1;
  -webkit-transform: scale(1);
  -ms-transform: scale(1);
  transform: scale(1);
}

.mkdf-process-holder.mkdf-process-appeared .mkdf-mark-horizontal-holder .mkdf-process-line {
  width: 100%;
}

.mkdf-process-holder.mkdf-process-appeared .mkdf-mark-vertical-holder .mkdf-process-line {
  height: 100%;
}

.mkdf-process-holder.mkdf-process-appeared .mkdf-process-item {
  opacity: 1;
}

.mkdf-process-holder .mkdf-mark-horizontal-holder {
  position: relative;
  display: inline-block;
  width: 100%;
  vertical-align: middle;
  clear: both;
}

.mkdf-process-holder .mkdf-mark-horizontal-holder .mkdf-process-mark {
  float: left;
}

.mkdf-process-holder .mkdf-mark-horizontal-holder .mkdf-process-line {
  top: 50%;
  left: 50%;
  width: 0;
  height: 1px;
  -webkit-transition: width 0.4s ease 0.1s;
  transition: width 0.4s ease 0.1s;
}

.mkdf-process-holder .mkdf-mark-vertical-holder {
  position: absolute;
  top: 26px;
  left: 0;
  display: none;
  width: 46px;
  height: 100%;
}

.mkdf-process-holder .mkdf-mark-vertical-holder .mkdf-process-line {
  top: 23px;
  left: 50%;
  width: 1px;
  height: 0;
  -webkit-transition: height 0.4s ease 0.1s;
  transition: height 0.4s ease 0.1s;
}

.mkdf-process-holder .mkdf-process-mark {
  position: relative;
  display: inline-block;
  vertical-align: top;
  text-align: center;
}

.mkdf-process-holder .mkdf-process-mark:last-child .mkdf-process-line {
  display: none;
}

.mkdf-process-holder .mkdf-process-mark:nth-child(2) .mkdf-process-circle {
  -webkit-transition-delay: 0.5s;
  transition-delay: 0.5s;
}

.mkdf-process-holder .mkdf-process-mark:nth-child(2) .mkdf-process-line {
  -webkit-transition-delay: 0.6s;
  transition-delay: 0.6s;
}

.mkdf-process-holder .mkdf-process-mark:nth-child(3) .mkdf-process-circle {
  -webkit-transition-delay: 1s;
  transition-delay: 1s;
}

.mkdf-process-holder .mkdf-process-mark:nth-child(3) .mkdf-process-line {
  -webkit-transition-delay: 1.2s;
  transition-delay: 1.2s;
}

.mkdf-process-holder .mkdf-process-mark:nth-child(4) .mkdf-process-circle {
  -webkit-transition-delay: 1.5s;
  transition-delay: 1.5s;
}

.mkdf-process-holder .mkdf-process-mark:nth-child(4) .mkdf-process-line {
  -webkit-transition-delay: 1.8s;
  transition-delay: 1.8s;
}

.mkdf-process-holder .mkdf-process-circle {
  position: relative;
  display: inline-block;
  vertical-align: top;
  width: 46px;
  height: 46px;
  font-size: 18px;
  line-height: 46px;
  font-weight: 700;
  color: #fff;
  background-color: #ea3d56;
  border-radius: 100%;
  opacity: 0;
  -webkit-transition: opacity .2s ease, -webkit-transform .3s ease;
  transition: opacity .2s ease, -webkit-transform .3s ease;
  transition: opacity .2s ease, transform .3s ease;
  transition: opacity .2s ease, transform .3s ease, -webkit-transform .3s ease;
  -webkit-transform: scale(0.6);
  -ms-transform: scale(0.6);
  transform: scale(0.6);
}

.mkdf-process-holder .mkdf-process-line {
  position: absolute;
  background-color: #ea3d56;
}

.mkdf-process-holder .mkdf-process-inner {
  margin: 0 -15px;
}

.mkdf-process-holder .mkdf-process-item {
  position: relative;
  display: inline-block;
  vertical-align: top;
  float: left;
  padding: 0 15px;
  opacity: 0;
  text-align: center;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  -webkit-transition: opacity 0.2s ease;
  transition: opacity 0.2s ease;
}

.mkdf-process-holder .mkdf-process-item:nth-child(2) {
  -webkit-transition-delay: 0.5s;
  transition-delay: 0.5s;
}

.mkdf-process-holder .mkdf-process-item:nth-child(3) {
  -webkit-transition-delay: 1s;
  transition-delay: 1s;
}

.mkdf-process-holder .mkdf-process-item:nth-child(4) {
  -webkit-transition-delay: 1.5s;
  transition-delay: 1.5s;
}

.mkdf-process-holder .mkdf-pi-content {
  position: relative;
  display: inline-block;
  width: 100%;
  vertical-align: middle;
  margin: 26px 0 10px;
}

.mkdf-process-holder .mkdf-pi-title {
  margin: 0;
}

.mkdf-process-holder .mkdf-pi-text {
  margin: 11px 0 0;
}

/* ==========================================================================
   Process shortcode style - end
   ========================================================================== */
/* ==========================================================================
   Progress Bar shortcode style - begin
   ========================================================================== */
.mkdf-progress-bar {
  position: relative;
  display: inline-block;
  width: 100%;
  vertical-align: middle;
}

.mkdf-progress-bar.mkdf-pb-percent-floating {
  width: 100%;
  height: 100%;
}

.mkdf-progress-bar.mkdf-pb-percent-floating .mkdf-pb-percent {
  position: absolute;
  left: 0;
  right: auto;
  bottom: 0;
  -webkit-transform: translateX(-50%);
  -ms-transform: translateX(-50%);
  transform: translateX(-50%);
}

.mkdf-progress-bar .mkdf-pb-title-holder {
  position: relative;
  margin: 10px 0 6px;
}

.mkdf-progress-bar .mkdf-pb-title-holder .mkdf-pb-title {
  position: relative;
  display: inline-block;
  vertical-align: middle;
  z-index: 100;
}

.mkdf-progress-bar .mkdf-pb-percent {
  position: absolute;
  right: 0;
  bottom: -2px;
  width: auto;
  display: inline-block;
  vertical-align: middle;
  opacity: 0;
  z-index: 10;
}

.mkdf-progress-bar .mkdf-pb-percent:after {
  content: '%';
}

.mkdf-progress-bar .mkdf-pb-content-holder {
  position: relative;
  height: 5px;
  overflow: hidden;
  background-color: #ebebeb;
}

.mkdf-progress-bar .mkdf-pb-content-holder .mkdf-pb-content {
  height: 5px;
  max-width: 100%;
  overflow: hidden;
  background-color: #ea3d56;
}

/* ==========================================================================
   Progress Bar shortcode style - end
   ========================================================================== */
/* ==========================================================================
   Roadmap shortcode style - begin
   ========================================================================== */
.mkdf-roadmap {
  position: relative;
  display: inline-block;
  width: 100%;
  vertical-align: middle;
  padding: 200px 0;
  overflow: hidden;
}

.mkdf-roadmap .mkdf-roadmap-holder {
  overflow: hidden;
}

.mkdf-roadmap .mkdf-roadmap-line {
  position: relative;
  width: 0%;
  height: 3px;
  background-color: #f6f6f6;
}

.mkdf-roadmap .mkdf-roadmap-line .mkdf-rl-arrow-left,
.mkdf-roadmap .mkdf-roadmap-line .mkdf-rl-arrow-right {
  position: absolute;
  top: 50%;
  font-size: 30px;
  color: #ea3d56;
  cursor: pointer;
  -webkit-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  transform: translateY(-50%);
  z-index: 50;
}

.mkdf-roadmap .mkdf-roadmap-line .mkdf-rl-arrow-left {
  left: -2px;
  padding: 10px 10px 10px 0;
}

.mkdf-roadmap .mkdf-roadmap-line .mkdf-rl-arrow-left svg {
  -webkit-transform: rotate(180deg);
  -ms-transform: rotate(180deg);
  transform: rotate(180deg);
}

.mkdf-roadmap .mkdf-roadmap-line .mkdf-rl-arrow-right {
  right: -2px;
  padding: 10px 0 10px 10px;
}

.mkdf-roadmap .mkdf-roadmap-inner-holder {
  -webkit-transition: all 0.2s ease-in-out;
  transition: all 0.2s ease-in-out;
}

.mkdf-roadmap .mkdf-roadmap-item {
  position: relative;
  float: left;
  text-align: center;
  -webkit-transform: translateY(-17px);
  -ms-transform: translateY(-17px);
  transform: translateY(-17px);
}

.mkdf-roadmap .mkdf-roadmap-item .mkdf-roadmap-item-circle-holder {
  font-size: 0;
}

.mkdf-roadmap .mkdf-roadmap-item .mkdf-roadmap-item-before-circle,
.mkdf-roadmap .mkdf-roadmap-item .mkdf-roadmap-item-after-circle {
  display: inline-block;
  vertical-align: middle;
  width: 0;
  height: 3px;
  background-color: #dfdfdf;
  position: absolute;
  top: 14px;
}

.mkdf-roadmap .mkdf-roadmap-item .mkdf-roadmap-item-before-circle {
  left: 0;
}

.mkdf-roadmap .mkdf-roadmap-item .mkdf-roadmap-item-after-circle {
  left: calc(50% - -11px);
}

.mkdf-roadmap .mkdf-roadmap-item .mkdf-roadmap-item-circle {
  display: inline-block;
  vertical-align: middle;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background-color: #f6f6f6;
  -webkit-box-shadow: inset 0px 0px 0px 6px #ea3d56;
  box-shadow: inset 0px 0px 0px 6px #ea3d56;
}

.mkdf-roadmap .mkdf-roadmap-item .mkdf-roadmap-item-stage-title-holder {
  position: absolute;
  left: 0;
  width: 100%;
}

.mkdf-roadmap .mkdf-roadmap-item .mkdf-roadmap-item-stage-title-holder .mkdf-ris-title {
  color: #1b2c58;
  font-size: 20px;
  font-family: "Josefin Sans", sans-serif;
  font-weight: 600;
}

.mkdf-roadmap .mkdf-roadmap-item .mkdf-roadmap-item-content-holder {
  position: absolute;
  left: 4%;
  width: 92%;
  text-align: left;
  padding: 30px 36px;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  -webkit-box-shadow: 0px 5px 31px 0px rgba(0, 0, 0, 0.2);
  box-shadow: 0px 5px 31px 0px rgba(0, 0, 0, 0.2);
  background-color: #fff;
  border-radius: 5px;
  z-index: -1;
}

@media screen and (max-width: 1440px) and (min-width: 1280px) {
  .mkdf-roadmap .mkdf-roadmap-item .mkdf-roadmap-item-content-holder {
    padding: 20px;
  }
}

.mkdf-roadmap .mkdf-roadmap-item .mkdf-roadmap-item-content-holder .mkdf-ric-title {
  margin: 0 0 14px;
}

@media only screen and (max-width: 480px) {
  .mkdf-roadmap .mkdf-roadmap-item .mkdf-roadmap-item-content-holder .mkdf-ric-title {
    text-align: center;
  }
}

@media only screen and (max-width: 480px) {
  .mkdf-roadmap .mkdf-roadmap-item .mkdf-roadmap-item-content-holder .mkdf-ric-content {
    text-align: center;
  }
}

.mkdf-roadmap .mkdf-roadmap-item .mkdf-roadmap-item-content-holder:after {
  content: '';
  position: absolute;
  left: 50%;
  width: 3px;
  height: 70px;
  background-color: #dfdfdf;
  -webkit-transform: translateX(-50%);
  -ms-transform: translateX(-50%);
  transform: translateX(-50%);
  z-index: -1;
}

.mkdf-roadmap .mkdf-roadmap-item.mkdf-roadmap-item-above .mkdf-roadmap-item-stage-title-holder {
  top: 35px;
}

.mkdf-roadmap .mkdf-roadmap-item.mkdf-roadmap-item-above .mkdf-roadmap-item-content-holder {
  bottom: 75px;
}

.mkdf-roadmap .mkdf-roadmap-item.mkdf-roadmap-item-above .mkdf-roadmap-item-content-holder:after {
  top: 100%;
}

.mkdf-roadmap .mkdf-roadmap-item.mkdf-roadmap-item-below .mkdf-roadmap-item-stage-title-holder {
  bottom: 32px;
}

.mkdf-roadmap .mkdf-roadmap-item.mkdf-roadmap-item-below .mkdf-roadmap-item-content-holder {
  top: 75px;
}

.mkdf-roadmap .mkdf-roadmap-item.mkdf-roadmap-item-below .mkdf-roadmap-item-content-holder:after {
  bottom: 100%;
}

.mkdf-roadmap .mkdf-roadmap-item.mkdf-roadmap-reached-item .mkdf-roadmap-item-before-circle {
  background-color: #ea3d56;
  left: 0;
}

.mkdf-roadmap .mkdf-roadmap-item.mkdf-roadmap-passed-item .mkdf-roadmap-item-before-circle,
.mkdf-roadmap .mkdf-roadmap-item.mkdf-roadmap-passed-item .mkdf-roadmap-item-after-circle {
  background-color: #ea3d56;
}

.mkdf-roadmap.mkdf-roadmap-skin-dark .mkdf-roadmap-item-content-holder {
  background-color: #fff;
}

.mkdf-roadmap.mkdf-roadmap-skin-firstmain .mkdf-roadmap-item .mkdf-roadmap-item-content-holder {
  background-color: #ea3d56;
}

.mkdf-roadmap.mkdf-roadmap-skin-firstmain .mkdf-roadmap-item .mkdf-roadmap-item-content-holder:after {
  background-color: #ea3d56;
}

.mkdf-roadmap.mkdf-roadmap-skin-firstmain .mkdf-roadmap-item .mkdf-roadmap-item-content-holder .mkdf-ric-title {
  color: #fff;
}

.mkdf-roadmap.mkdf-roadmap-skin-firstmain .mkdf-roadmap-item .mkdf-roadmap-item-content-holder .mkdf-ric-content {
  color: #fff;
}

.mkdf-roadmap.mkdf-roadmap-skin-firstmain .mkdf-roadmap-item .mkdf-roadmap-item-before-circle,
.mkdf-roadmap.mkdf-roadmap-skin-firstmain .mkdf-roadmap-item .mkdf-roadmap-item-after-circle {
  background-color: rgba(234, 61, 86, 0.3);
}

.mkdf-roadmap.mkdf-roadmap-skin-firstmain .mkdf-roadmap-item .mkdf-roadmap-item-circle {
  background-color: #fff;
  -webkit-box-shadow: inset 0px 0px 0px 6px #ea3d56;
  box-shadow: inset 0px 0px 0px 6px #ea3d56;
}

.mkdf-roadmap.mkdf-roadmap-skin-firstmain .mkdf-roadmap-item.mkdf-roadmap-reached-item .mkdf-roadmap-item-before-circle {
  background-color: #ea3d56;
}

.mkdf-roadmap.mkdf-roadmap-skin-firstmain .mkdf-roadmap-item.mkdf-roadmap-passed-item .mkdf-roadmap-item-before-circle,
.mkdf-roadmap.mkdf-roadmap-skin-firstmain .mkdf-roadmap-item.mkdf-roadmap-passed-item .mkdf-roadmap-item-after-circle {
  background-color: #ea3d56;
}

.mkdf-roadmap.mkdf-roadmap-skin-firstmain .mkdf-roadmap-item-stage-title-holder .mkdf-ris-title {
  color: #ea3d56;
}

.mkdf-roadmap.mkdf-roadmap-skin-firstmain .mkdf-roadmap-line {
  background-color: rgba(234, 61, 86, 0.3);
}

.mkdf-roadmap.mkdf-roadmap-skin-firstmain .mkdf-roadmap-line .mkdf-rl-arrow-left,
.mkdf-roadmap.mkdf-roadmap-skin-firstmain .mkdf-roadmap-line .mkdf-rl-arrow-right {
  color: #ea3d56;
}

.mkdf-roadmap.mkdf-roadmap-skin-light .mkdf-roadmap-item .mkdf-roadmap-item-content-holder {
  background-color: #fff;
}

.mkdf-roadmap.mkdf-roadmap-skin-light .mkdf-roadmap-item .mkdf-roadmap-item-content-holder:after {
  background-color: #fff;
}

.mkdf-roadmap.mkdf-roadmap-skin-light .mkdf-roadmap-item .mkdf-roadmap-item-before-circle,
.mkdf-roadmap.mkdf-roadmap-skin-light .mkdf-roadmap-item .mkdf-roadmap-item-after-circle {
  display: inline-block;
  vertical-align: middle;
  width: 0;
  height: 3px;
  background-color: rgba(255, 255, 255, 0.3);
}

.mkdf-roadmap.mkdf-roadmap-skin-light .mkdf-roadmap-item .mkdf-roadmap-item-circle {
  background-color: #1f75ff;
  -webkit-box-shadow: inset 0px 0px 0px 6px #fff;
  box-shadow: inset 0px 0px 0px 6px #fff;
}

.mkdf-roadmap.mkdf-roadmap-skin-light .mkdf-roadmap-item.mkdf-roadmap-reached-item .mkdf-roadmap-item-before-circle {
  background-color: #fff;
}

.mkdf-roadmap.mkdf-roadmap-skin-light .mkdf-roadmap-item.mkdf-roadmap-passed-item .mkdf-roadmap-item-before-circle,
.mkdf-roadmap.mkdf-roadmap-skin-light .mkdf-roadmap-item.mkdf-roadmap-passed-item .mkdf-roadmap-item-after-circle {
  background-color: #fff;
}

.mkdf-roadmap.mkdf-roadmap-skin-light .mkdf-roadmap-item-stage-title-holder .mkdf-ris-title {
  color: #fff;
}

.mkdf-roadmap.mkdf-roadmap-skin-light .mkdf-roadmap-line {
  background-color: rgba(255, 255, 255, 0.3);
}

.mkdf-roadmap.mkdf-roadmap-skin-light .mkdf-roadmap-line .mkdf-rl-arrow-left,
.mkdf-roadmap.mkdf-roadmap-skin-light .mkdf-roadmap-line .mkdf-rl-arrow-right {
  color: #fff;
}

/* ==========================================================================
   Roadmap shortcode style - end
   ========================================================================== */
/* ==========================================================================
   Section Title shortcode styles - begin
   ========================================================================== */
.mkdf-section-title-holder {
  position: relative;
  display: inline-block;
  width: 100%;
  vertical-align: middle;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}

.mkdf-section-title-holder.mkdf-st-two-columns.mkdf-st-tiny-space .mkdf-st-inner {
  margin: 0 -5px;
}

.mkdf-section-title-holder.mkdf-st-two-columns.mkdf-st-tiny-space .mkdf-st-title,
.mkdf-section-title-holder.mkdf-st-two-columns.mkdf-st-tiny-space .mkdf-st-text {
  padding: 0 5px;
}

.mkdf-section-title-holder.mkdf-st-two-columns.mkdf-st-small-space .mkdf-st-inner {
  margin: 0 -10px;
}

.mkdf-section-title-holder.mkdf-st-two-columns.mkdf-st-small-space .mkdf-st-title,
.mkdf-section-title-holder.mkdf-st-two-columns.mkdf-st-small-space .mkdf-st-text {
  padding: 0 10px;
}

.mkdf-section-title-holder.mkdf-st-two-columns.mkdf-st-normal-space .mkdf-st-inner {
  margin: 0 -15px;
}

.mkdf-section-title-holder.mkdf-st-two-columns.mkdf-st-normal-space .mkdf-st-title,
.mkdf-section-title-holder.mkdf-st-two-columns.mkdf-st-normal-space .mkdf-st-text {
  padding: 0 15px;
}

.mkdf-section-title-holder.mkdf-st-two-columns .mkdf-st-title,
.mkdf-section-title-holder.mkdf-st-two-columns .mkdf-st-text {
  position: relative;
  display: inline-block;
  vertical-align: middle;
  width: 50%;
  float: left;
  margin: 0;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}

.mkdf-section-title-holder.mkdf-st-two-columns.mkdf-st-title-left .mkdf-st-title {
  text-align: right;
}

.mkdf-section-title-holder.mkdf-st-two-columns.mkdf-st-title-left .mkdf-st-text {
  text-align: left;
}

.mkdf-section-title-holder.mkdf-st-two-columns.mkdf-st-title-right .mkdf-st-title {
  float: right;
  text-align: left;
}

.mkdf-section-title-holder.mkdf-st-two-columns.mkdf-st-title-right .mkdf-st-text {
  text-align: right;
}

.mkdf-section-title-holder .mkdf-st-title {
  display: block;
  margin: 0;
}

.mkdf-section-title-holder .mkdf-st-title .mkdf-st-title-bold {
  font-weight: 700;
}

.mkdf-section-title-holder .mkdf-st-title .mkdf-st-title-light {
  font-weight: 300;
}

.mkdf-section-title-holder .mkdf-st-text {
  display: block;
  margin: 7px 0 0;
  font-size: 18px;
  line-height: 1.88em;
}

/* ==========================================================================
   Section Title shortcode styles - end
   ========================================================================== */
/* ==========================================================================
   Separator shortcode style - begin
   ========================================================================== */
.mkdf-separator-holder {
  position: relative;
  height: auto;
  font-size: 0;
  line-height: 1em;
}

.mkdf-separator-holder.mkdf-separator-center {
  text-align: center;
}

.mkdf-separator-holder.mkdf-separator-left {
  text-align: left;
}

.mkdf-separator-holder.mkdf-separator-right {
  text-align: right;
}

.mkdf-separator-holder.mkdf-separator-full-width .mkdf-separator {
  width: 100% !important;
}

.mkdf-separator {
  position: relative;
  display: inline-block;
  vertical-align: middle;
  border-bottom: 1px solid #ea3d56;
  margin: 10px 0;
}

/* ==========================================================================
   Separator shortcode style - end
   ========================================================================== */
/* ==========================================================================
   Single Image shortcode style - begin
   ========================================================================== */
.mkdf-single-image-holder {
  position: relative;
  display: inline-block;
  width: 100%;
  vertical-align: middle;
  /***** Custom Link Behavior Style - end *****/
  /***** Lightbox Behavior Style - begin *****/
  /***** Lightbox Behavior Style - end *****/
  /***** Zoom Behavior Style - begin *****/
  /***** Zoom Behavior Style - end *****/
  /***** Grayscale Behavior Style - begin *****/
  /***** Grayscale Behavior Style - end *****/
  /***** Moving Behavior Style - begin *****/
  /***** Moving Behavior Style - end *****/
}

.mkdf-single-image-holder.mkdf-has-shadow .mkdf-si-inner {
  -webkit-box-shadow: 0 0 4.85px 0.15px rgba(0, 0, 0, 0.09);
  box-shadow: 0 0 4.85px 0.15px rgba(0, 0, 0, 0.09);
}

.mkdf-single-image-holder.mkdf-has-border .mkdf-si-inner {
  border: 2px solid rgba(225, 225, 225, 0.3);
  padding: 34px;
}

.mkdf-single-image-holder .mkdf-si-inner {
  position: relative;
  display: inline-block;
  width: 100%;
  vertical-align: middle;
}

.mkdf-single-image-holder .mkdf-si-inner a, .mkdf-single-image-holder .mkdf-si-inner img {
  position: relative;
  display: block;
}

.mkdf-tabs .mkdf-single-image-holder.mkdf-image-behavior-custom-link .mkdf-si-inner a:hover:after {
  opacity: 1;
}

.mkdf-tabs .mkdf-single-image-holder.mkdf-image-behavior-custom-link .mkdf-si-inner a:after {
  position: absolute;
  display: block;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  content: '';
  background-color: rgba(234, 61, 86, 0.4);
  opacity: 0;
  -webkit-transition: opacity 0.2s ease-in-out;
  transition: opacity 0.2s ease-in-out;
}

.mkdf-single-image-holder.mkdf-image-behavior-lightbox .mkdf-si-inner a:hover:after {
  opacity: 1;
}

.mkdf-single-image-holder.mkdf-image-behavior-lightbox .mkdf-si-inner a:after {
  position: absolute;
  display: block;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  content: '';
  background-color: rgba(234, 61, 86, 0.4);
  opacity: 0;
  -webkit-transition: opacity 0.2s ease-in-out;
  transition: opacity 0.2s ease-in-out;
}

.mkdf-single-image-holder.mkdf-image-behavior-zoom .mkdf-si-inner {
  overflow: hidden;
}

.touch .mkdf-single-image-holder.mkdf-image-behavior-zoom .mkdf-si-inner {
  cursor: pointer;
}

.mkdf-single-image-holder.mkdf-image-behavior-zoom .mkdf-si-inner:hover img {
  -webkit-transform: scale(1.04);
  -ms-transform: scale(1.04);
  transform: scale(1.04);
}

.mkdf-single-image-holder.mkdf-image-behavior-zoom .mkdf-si-inner img {
  -webkit-transform: scale(1);
  -ms-transform: scale(1);
  transform: scale(1);
  -webkit-transition: -webkit-transform 0.3s ease-in-out;
  transition: -webkit-transform 0.3s ease-in-out;
  transition: transform 0.3s ease-in-out;
  transition: transform 0.3s ease-in-out, -webkit-transform 0.3s ease-in-out;
}

.mkdf-single-image-holder.mkdf-image-behavior-grayscale .mkdf-si-inner {
  overflow: hidden;
}

.touch .mkdf-single-image-holder.mkdf-image-behavior-grayscale .mkdf-si-inner {
  cursor: pointer;
}

.mkdf-single-image-holder.mkdf-image-behavior-grayscale .mkdf-si-inner:hover img {
  -webkit-filter: grayscale(0);
  filter: none;
}

.mkdf-single-image-holder.mkdf-image-behavior-grayscale .mkdf-si-inner img {
  filter: url("img/desaturate.svg#grayscale");
  -webkit-filter: grayscale(100%);
  -moz-filter: grayscale(100%);
  filter: gray;
  filter: grayscale(100%);
  -webkit-transition: all 0.3s ease-in-out;
  transition: all 0.3s ease-in-out;
}

.mkdf-single-image-holder.mkdf-image-behavior-moving .mkdf-si-inner {
  overflow: hidden;
  padding: 10% 0;
  background-repeat: no-repeat;
  background-position: 0 center;
  background-size: 120%;
  -webkit-transition: background 0.7s ease-out;
  transition: background 0.7s ease-out;
}

.mkdf-single-image-holder.mkdf-image-behavior-moving .mkdf-si-inner:hover {
  background-position: 90% center;
}

.touch .mkdf-single-image-holder.mkdf-image-behavior-moving .mkdf-si-inner {
  cursor: pointer;
}

.mkdf-single-image-holder.mkdf-image-behavior-moving .mkdf-si-inner img {
  z-index: -1;
  max-width: 80%;
}

@media only screen and (max-width: 1024px) {
  .mkdf-single-image-holder.mkdf-image-behavior-moving .mkdf-si-inner {
    padding: 0;
    background: none;
  }
  .mkdf-single-image-holder.mkdf-image-behavior-moving .mkdf-si-inner img {
    z-index: inherit;
    max-width: 100%;
  }
}

/* ==========================================================================
   Single Image shortcode style - end
   ========================================================================== */
/* ==========================================================================
   Social Share shortcode style - begin
   ========================================================================== */
.mkdf-social-share-holder {
  position: relative;
  display: inline-block;
  vertical-align: top;
}

.mkdf-social-share-holder .mkdf-social-title {
  position: relative;
  display: inline-block;
  vertical-align: top;
  margin-right: 13px;
}

.mkdf-social-share-holder ul {
  position: relative;
  display: inline-block;
  vertical-align: top;
  list-style: none;
  padding: 0;
  margin: 0;
}

.mkdf-social-share-holder li {
  position: relative;
  display: inline-block;
  vertical-align: top;
  padding: 0;
  margin: 0;
}

.mkdf-social-share-holder li a {
  font-size: 14px;
}

.mkdf-social-share-holder.mkdf-list li {
  margin-right: 13px;
}

.mkdf-social-share-holder.mkdf-list li:last-child {
  margin-right: 0;
}

.mkdf-social-share-holder.mkdf-text li {
  margin-right: 13px;
}

.mkdf-social-share-holder.mkdf-text li:last-child {
  margin-right: 0;
}

.mkdf-social-share-holder.mkdf-dropdown {
  position: relative;
  display: inline-block;
  vertical-align: bottom;
}

.mkdf-social-share-holder.mkdf-dropdown:hover .mkdf-social-share-dropdown ul li {
  opacity: 1;
  visibility: visible;
  cursor: pointer;
  /* opacity and visibility need to be different, but not background-color */
}

.mkdf-social-share-holder.mkdf-dropdown:hover .mkdf-social-share-dropdown ul li:nth-child(2) {
  -webkit-transition-delay: 0.2s;
  transition-delay: 0.2s;
}

.mkdf-social-share-holder.mkdf-dropdown:hover .mkdf-social-share-dropdown ul li:nth-child(3) {
  -webkit-transition-delay: 0.3s;
  transition-delay: 0.3s;
}

.mkdf-social-share-holder.mkdf-dropdown:hover .mkdf-social-share-dropdown ul li:nth-child(4) {
  -webkit-transition-delay: 0.4s;
  transition-delay: 0.4s;
}

.mkdf-social-share-holder.mkdf-dropdown:hover .mkdf-social-share-dropdown ul li:nth-child(5) {
  -webkit-transition-delay: 0.5s;
  transition-delay: 0.5s;
}

.mkdf-social-share-holder.mkdf-dropdown:hover .mkdf-social-share-dropdown ul li:nth-child(6) {
  -webkit-transition-delay: 0.6s;
  transition-delay: 0.6s;
}

.mkdf-social-share-holder.mkdf-dropdown:hover .mkdf-social-share-dropdown ul li:nth-child(7) {
  -webkit-transition-delay: 0.7s;
  transition-delay: 0.7s;
}

.mkdf-social-share-holder.mkdf-dropdown .mkdf-social-share-dropdown-opener {
  display: block;
}

.mkdf-social-share-holder.mkdf-dropdown .mkdf-social-share-dropdown-opener .mkdf-social-share-title {
  display: inline-block;
  vertical-align: top;
  margin-right: 5px;
}

.mkdf-social-share-holder.mkdf-dropdown .mkdf-social-share-dropdown {
  position: absolute;
  visibility: hidden;
  z-index: 950;
}

.mkdf-social-share-holder.mkdf-dropdown .mkdf-social-share-dropdown ul {
  position: relative;
  display: block;
  z-index: 990;
  margin: 0;
  padding: 0 !important;
}

.mkdf-social-share-holder.mkdf-dropdown .mkdf-social-share-dropdown li {
  position: absolute;
  display: block;
  text-align: center;
  visibility: hidden;
  overflow: hidden;
  opacity: 0;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  -webkit-transition: opacity 0.2s ease-out, visibility 0.2s ease-out;
  transition: opacity 0.2s ease-out, visibility 0.2s ease-out;
}

.mkdf-social-share-holder.mkdf-dropdown .mkdf-social-share-dropdown li a {
  -webkit-transition: color 0.2s ease-out, background-color 0.2s ease-out;
  transition: color 0.2s ease-out, background-color 0.2s ease-out;
}

.mkdf-social-share-holder.mkdf-dropdown .mkdf-social-share-dropdown li * {
  display: block;
  line-height: inherit;
}

.mkdf-social-share-holder.mkdf-dropdown.mkdf-bottom .mkdf-social-share-dropdown {
  bottom: 0;
  left: 0;
}

.mkdf-social-share-holder.mkdf-dropdown.mkdf-bottom .mkdf-social-share-dropdown li {
  width: 90px;
  height: 30px;
  line-height: 30px;
  border: 1px solid rgba(225, 225, 225, 0.3);
}

.mkdf-social-share-holder.mkdf-dropdown.mkdf-bottom .mkdf-social-share-dropdown li:not(:first-child) {
  border-top: none;
}

.mkdf-social-share-holder.mkdf-dropdown.mkdf-bottom .mkdf-social-share-dropdown li.mkdf-facebook-share a:hover {
  background-color: #3b5998;
  color: #fff;
}

.mkdf-social-share-holder.mkdf-dropdown.mkdf-bottom .mkdf-social-share-dropdown li.mkdf-twitter-share a:hover {
  background-color: #00aced;
  color: #fff;
}

.mkdf-social-share-holder.mkdf-dropdown.mkdf-bottom .mkdf-social-share-dropdown li.mkdf-google_plus-share a:hover {
  background-color: #dd4b39;
  color: #fff;
}

.mkdf-social-share-holder.mkdf-dropdown.mkdf-bottom .mkdf-social-share-dropdown li.mkdf-linkedin-share a:hover {
  background-color: #007bb5;
  color: #fff;
}

.mkdf-social-share-holder.mkdf-dropdown.mkdf-bottom .mkdf-social-share-dropdown li.mkdf-tumblr-share a:hover {
  background-color: #32506d;
  color: #fff;
}

.mkdf-social-share-holder.mkdf-dropdown.mkdf-bottom .mkdf-social-share-dropdown li.mkdf-pinterest-share a:hover {
  background-color: #cb2027;
  color: #fff;
}

.mkdf-social-share-holder.mkdf-dropdown.mkdf-bottom .mkdf-social-share-dropdown li.mkdf-vk-share a:hover {
  background-color: #45668e;
  color: #fff;
}

.mkdf-social-share-holder.mkdf-dropdown.mkdf-bottom .mkdf-social-share-dropdown li a {
  font-size: 12px;
  color: #868890;
  background-color: #fff;
}

.mkdf-social-share-holder.mkdf-dropdown.mkdf-bottom .mkdf-social-share-dropdown li:nth-child(1) {
  bottom: -30px;
}

.mkdf-social-share-holder.mkdf-dropdown.mkdf-bottom .mkdf-social-share-dropdown li:nth-child(2) {
  bottom: -60px;
}

.mkdf-social-share-holder.mkdf-dropdown.mkdf-bottom .mkdf-social-share-dropdown li:nth-child(3) {
  bottom: -90px;
}

.mkdf-social-share-holder.mkdf-dropdown.mkdf-bottom .mkdf-social-share-dropdown li:nth-child(4) {
  bottom: -120px;
}

.mkdf-social-share-holder.mkdf-dropdown.mkdf-bottom .mkdf-social-share-dropdown li:nth-child(5) {
  bottom: -150px;
}

.mkdf-social-share-holder.mkdf-dropdown.mkdf-bottom .mkdf-social-share-dropdown li:nth-child(6) {
  bottom: -180px;
}

.mkdf-social-share-holder.mkdf-dropdown.mkdf-bottom .mkdf-social-share-dropdown li:nth-child(7) {
  bottom: -210px;
}

.mkdf-social-share-holder.mkdf-dropdown.mkdf-right .mkdf-social-share-dropdown {
  top: 0;
  right: 0;
}

.mkdf-social-share-holder.mkdf-dropdown.mkdf-right .mkdf-social-share-dropdown li {
  width: calc(90px / 3);
}

.mkdf-social-share-holder.mkdf-dropdown.mkdf-right .mkdf-social-share-dropdown li:nth-child(1) {
  left: 5px;
}

.mkdf-social-share-holder.mkdf-dropdown.mkdf-right .mkdf-social-share-dropdown li:nth-child(2) {
  left: 35px;
}

.mkdf-social-share-holder.mkdf-dropdown.mkdf-right .mkdf-social-share-dropdown li:nth-child(3) {
  left: 65px;
}

.mkdf-social-share-holder.mkdf-dropdown.mkdf-right .mkdf-social-share-dropdown li:nth-child(4) {
  left: 95px;
}

.mkdf-social-share-holder.mkdf-dropdown.mkdf-right .mkdf-social-share-dropdown li:nth-child(5) {
  left: 125px;
}

.mkdf-social-share-holder.mkdf-dropdown.mkdf-right .mkdf-social-share-dropdown li:nth-child(6) {
  left: 155px;
}

.mkdf-social-share-holder.mkdf-dropdown.mkdf-right .mkdf-social-share-dropdown li:nth-child(7) {
  left: 185px;
}

.mkdf-social-share-holder.mkdf-dropdown.mkdf-left .mkdf-social-share-dropdown {
  top: 0;
  left: 0;
}

.mkdf-social-share-holder.mkdf-dropdown.mkdf-left .mkdf-social-share-dropdown li {
  width: calc(90px / 3);
}

.mkdf-social-share-holder.mkdf-dropdown.mkdf-left .mkdf-social-share-dropdown li:nth-child(1) {
  right: 5px;
}

.mkdf-social-share-holder.mkdf-dropdown.mkdf-left .mkdf-social-share-dropdown li:nth-child(2) {
  right: 35px;
}

.mkdf-social-share-holder.mkdf-dropdown.mkdf-left .mkdf-social-share-dropdown li:nth-child(3) {
  right: 65px;
}

.mkdf-social-share-holder.mkdf-dropdown.mkdf-left .mkdf-social-share-dropdown li:nth-child(4) {
  right: 95px;
}

.mkdf-social-share-holder.mkdf-dropdown.mkdf-left .mkdf-social-share-dropdown li:nth-child(5) {
  right: 125px;
}

.mkdf-social-share-holder.mkdf-dropdown.mkdf-left .mkdf-social-share-dropdown li:nth-child(6) {
  right: 155px;
}

.mkdf-social-share-holder.mkdf-dropdown.mkdf-left .mkdf-social-share-dropdown li:nth-child(7) {
  right: 185px;
}

/* ==========================================================================
   Social Share shortcode style - end
   ========================================================================== */
/* ==========================================================================
   Tabs shortcode style - begin
   ========================================================================== */
.mkdf-tabs {
  position: relative;
  display: inline-block;
  width: 100%;
  vertical-align: middle;
}

.mkdf-tabs .mkdf-tabs-nav {
  position: relative;
  display: inline-block;
  width: 100%;
  vertical-align: middle;
  margin: 0;
  padding: 0;
  list-style: none;
  border-bottom: 2px solid rgba(225, 225, 225, 0.3);
}

.mkdf-tabs .mkdf-tabs-nav li {
  float: left;
  margin: 0;
  padding: 0;
  position: relative !important;
}

.mkdf-tabs .mkdf-tabs-nav li a {
  position: relative;
  display: inline-block;
  vertical-align: middle;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  -webkit-transition: all 0.2s ease-out, background-color 0.2s ease-out, border-color 0.2s ease-out;
  transition: all 0.2s ease-out, background-color 0.2s ease-out, border-color 0.2s ease-out;
  font-size: 16px;
  font-family: "Josefin Sans", sans-serif;
  opacity: 0.6;
}

.mkdf-tabs .mkdf-tabs-nav li .mkdf-tabs-underline {
  width: 0%;
  height: 8px;
  position: absolute;
  bottom: -2px;
  left: 0;
  opacity: 1 !important;
  -webkit-transition: all 0.4s ease-in;
  transition: all 0.4s ease-in;
}

.mkdf-tabs .mkdf-tabs-nav li.ui-state-active a {
  opacity: 1;
}

.mkdf-tabs .mkdf-tab-container {
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  position: relative;
  display: inline-block;
  width: 100%;
  vertical-align: middle;
}

.mkdf-tabs .mkdf-tab-container p {
  margin: 0;
}

.mkdf-tabs.mkdf-tabs-standard .mkdf-tabs-nav li a {
  padding: 21px 42px;
  line-height: 25px;
  font-weight: 600;
  letter-spacing: 1px;
  color: #1b2c58;
  margin-bottom: -2px;
  border-bottom: 8px solid transparent;
}

.mkdf-tabs.mkdf-tabs-standard .mkdf-tabs-nav li.ui-state-active a {
  border-bottom: 8px solid transparent;
  opacity: 1;
}

.mkdf-tabs.mkdf-tabs-standard .mkdf-tab-container {
  margin: 25px 0 0;
}

.mkdf-tabs.mkdf-tabs-boxed .mkdf-tabs-nav li {
  margin: 0 12px 0 0;
}

.mkdf-tabs.mkdf-tabs-boxed .mkdf-tabs-nav li a {
  padding: 21px 43px;
  line-height: 25px;
  font-weight: 400;
  letter-spacing: 1px;
  color: #fff;
  background-color: #1b2c58;
  margin-bottom: -2px;
}

.mkdf-tabs.mkdf-tabs-boxed .mkdf-tabs-nav li.ui-state-active a,
.mkdf-tabs.mkdf-tabs-boxed .mkdf-tabs-nav li.ui-state-hover a {
  color: #fff;
  background-color: #ea3d56;
}

.mkdf-tabs.mkdf-tabs-boxed .mkdf-tabs-nav li:last-child {
  margin: 0;
}

.mkdf-tabs.mkdf-tabs-boxed .mkdf-tab-container {
  margin: 25px 0 0;
}

.mkdf-tabs.mkdf-tabs-simple .mkdf-tabs-nav {
  border-bottom: 1px solid rgba(225, 225, 225, 0.3);
}

.mkdf-tabs.mkdf-tabs-simple .mkdf-tabs-nav li {
  margin: 0 31px 0 0;
}

.mkdf-tabs.mkdf-tabs-simple .mkdf-tabs-nav li a {
  padding: 13px 0;
  font-size: 18px;
  line-height: 26px;
  font-weight: 400;
  color: #1b2c58;
}

.mkdf-tabs.mkdf-tabs-simple .mkdf-tabs-nav li.ui-state-active a,
.mkdf-tabs.mkdf-tabs-simple .mkdf-tabs-nav li.ui-state-hover a {
  color: #ea3d56;
}

.mkdf-tabs.mkdf-tabs-simple .mkdf-tabs-nav li:last-child {
  margin: 0;
}

.mkdf-tabs.mkdf-tabs-simple .mkdf-tab-container {
  padding: 31px 0;
}

.mkdf-tabs.mkdf-tabs-vertical {
  display: table;
}

.mkdf-tabs.mkdf-tabs-vertical .mkdf-tabs-nav {
  display: table-cell;
  vertical-align: top;
  width: 140px;
  height: 100%;
  border-right: 1px solid rgba(225, 225, 225, 0.3);
  border-bottom: 0;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}

.mkdf-tabs.mkdf-tabs-vertical .mkdf-tabs-nav li {
  display: block;
  float: none;
}

.mkdf-tabs.mkdf-tabs-vertical .mkdf-tabs-nav li a {
  font-size: 16px;
  line-height: 26px;
  font-weight: 400;
  display: block;
  color: #1b2c58;
  padding: 12px 0 12px;
}

.mkdf-tabs.mkdf-tabs-vertical .mkdf-tabs-nav li.ui-state-active a,
.mkdf-tabs.mkdf-tabs-vertical .mkdf-tabs-nav li.ui-state-hover a {
  color: #ea3d56;
  border-right: 8px solid transparent;
}

.mkdf-tabs.mkdf-tabs-vertical .mkdf-tabs-nav li:last-child {
  margin: 0;
}

.mkdf-tabs.mkdf-tabs-vertical .mkdf-tab-container {
  display: table-cell;
  vertical-align: top;
  width: calc(100% - 140px);
  height: 100%;
  padding: 0 0 0 45px;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}

.ui-widget-content {
  padding: 0;
  font-family: inherit;
  font-size: inherit;
  color: inherit;
  background: none;
  border: 0;
  border-radius: 0;
}

.ui-widget-content .ui-widget-header {
  font-size: inherit;
  line-height: inherit;
  font-weight: inherit;
  color: initial;
  background: none;
  border-radius: 0;
}

.ui-widget-content .ui-tabs-nav li {
  position: initial;
  font-weight: inherit;
  color: inherit;
  background: initial;
  border: 0;
  border-radius: 0;
}

.ui-widget-content .ui-widget-content {
  color: inherit;
  background: none;
  border: 0;
  border-radius: 0;
}

/* ==========================================================================
   Tabs shortcode style - end
   ========================================================================== */
/* ==========================================================================
   Team shortcode style - begin
   ========================================================================== */
.mkdf-team-holder {
  position: relative;
  display: inline-block;
  width: 100%;
  vertical-align: middle;
}

.touch .mkdf-team-holder.mkdf-team-info-on-image {
  cursor: pointer;
}

.mkdf-team-holder.mkdf-team-info-on-image:hover .mkdf-team-social-wrapper {
  opacity: 1;
}

.mkdf-team-holder.mkdf-team-info-on-image:hover .mkdf-team-social-inner {
  -webkit-transition: -webkit-transform 0.45s cubic-bezier(0.64, 0.01, 0.15, 1.16), opacity 0.5s;
  -webkit-transition: opacity 0.5s, -webkit-transform 0.45s cubic-bezier(0.64, 0.01, 0.15, 1.16);
  transition: opacity 0.5s, -webkit-transform 0.45s cubic-bezier(0.64, 0.01, 0.15, 1.16);
  transition: transform 0.45s cubic-bezier(0.64, 0.01, 0.15, 1.16), opacity 0.5s;
  transition: transform 0.45s cubic-bezier(0.64, 0.01, 0.15, 1.16), opacity 0.5s, -webkit-transform 0.45s cubic-bezier(0.64, 0.01, 0.15, 1.16);
  -webkit-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

.mkdf-team-holder.mkdf-team-info-on-image .mkdf-team-social-holder {
  margin: 7px 0 0;
}

.mkdf-team-holder .mkdf-team-inner {
  text-align: center;
  border: 1px solid rgba(225, 225, 225, 0.2);
  -webkit-transition: all 0.3s ease-out;
  transition: all 0.3s ease-out;
}

.mkdf-team-holder .mkdf-team-inner:hover {
  border: 1px solid rgba(225, 225, 225, 0.4);
}

.mkdf-team-holder .mkdf-team-image {
  position: relative;
  display: inline-block;
  width: 100%;
  vertical-align: middle;
  width: auto;
  margin-top: 38px;
}

.mkdf-team-holder .mkdf-team-image img {
  display: block;
  width: 200px;
  height: 200px;
  -o-object-fit: cover;
  object-fit: cover;
  border-radius: 50%;
  border: 3px solid #fff;
}

.mkdf-team-holder .mkdf-team-info {
  position: relative;
  display: inline-block;
  width: 100%;
  vertical-align: middle;
  margin: 7px 0 57px;
  text-align: center;
}

.mkdf-team-holder .mkdf-team-name {
  margin: 0;
}

.mkdf-team-holder .mkdf-team-position {
  margin: 12px 0 0;
  font-size: 14px;
  opacity: 0.4;
  text-transform: uppercase;
  letter-spacing: 0.01em;
  font-weight: 700;
  line-height: 26px;
}

.mkdf-team-holder .mkdf-team-text {
  margin: 10px 0 0;
}

.mkdf-team-holder .mkdf-team-social-wrapper {
  position: absolute;
  display: block;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  background-color: rgba(255, 255, 255, 0.85);
  z-index: 1;
  opacity: 0;
  -webkit-transform: translateZ(0);
  transform: translateZ(0);
  -webkit-transition: opacity 0.3s;
  transition: opacity 0.3s;
}

.mkdf-team-holder .mkdf-team-social-outer {
  position: relative;
  display: table;
  table-layout: fixed;
  height: 100%;
  width: 100%;
}

.mkdf-team-holder .mkdf-team-social-inner {
  position: relative;
  display: table-cell;
  height: 100%;
  width: 100%;
  padding: 20px 40px 33px;
  vertical-align: bottom;
  -webkit-transition: -webkit-transform 0.2s ease;
  transition: -webkit-transform 0.2s ease;
  transition: transform 0.2s ease;
  transition: transform 0.2s ease, -webkit-transform 0.2s ease;
  -webkit-transform: translate3d(0, 40px, 0);
  transform: translate3d(0, 40px, 0);
}

.mkdf-team-holder .mkdf-team-social-holder {
  position: relative;
  display: inline-block;
  width: 100%;
  vertical-align: middle;
}

.mkdf-team-holder .mkdf-team-social-holder .mkdf-team-icon {
  font-size: 14px;
  margin: 0;
}

.mkdf-team-holder .mkdf-team-social-holder .mkdf-team-icon:first-child {
  font-size: 24px;
  position: absolute;
  right: 5px;
  bottom: 24px;
}

.mkdf-team-holder .mkdf-team-social-holder .mkdf-team-icon:nth-child(2) {
  font-size: 16px;
  position: absolute;
  right: -9px;
  bottom: 69px;
}

.mkdf-team-holder .mkdf-team-social-holder .mkdf-team-icon:nth-child(3) {
  font-size: 14px;
  position: absolute;
  right: -13px;
  bottom: 103px;
}

.mkdf-team-holder .mkdf-team-social-holder .mkdf-team-icon:nth-child(4) {
  font-size: 12px;
  position: absolute;
  right: -8px;
  bottom: 132px;
}

.mkdf-team-holder .mkdf-team-social-holder .mkdf-team-icon:nth-child(5) {
  font-size: 10px;
  position: absolute;
  right: 4px;
  bottom: 155px;
}

.mkdf-team-holder .mkdf-team-social-holder .mkdf-team-icon .mkdf-icon-element {
  font-size: inherit;
  -webkit-transition: none;
  transition: none;
  color: #fff;
}

/* ==========================================================================
   Team shortcode style - end
   ========================================================================== */
/* ==========================================================================
   Text Marquee shortcode style - begin
   ========================================================================== */
.mkdf-text-marquee {
  position: relative;
  white-space: nowrap;
  color: #1b2c58;
  font-size: 60px;
  line-height: 1.2em;
  font-weight: 600;
  overflow: hidden;
}

.mkdf-text-marquee .mkdf-marquee-element {
  position: relative;
  display: inline-block;
  vertical-align: top;
  padding: 0 25px;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}

.mkdf-text-marquee .mkdf-marquee-element.mkdf-aux-text {
  position: absolute;
  top: 0;
  left: 0;
}

/* ==========================================================================
   Text Marquee shortcode style - end
   ========================================================================== */
/* ==========================================================================
   Video Button shortcode start styles
   ========================================================================== */
.mkdf-video-button-holder {
  position: relative;
  display: inline-block;
  vertical-align: middle;
}

.mkdf-video-button-holder.mkdf-vb-has-img .mkdf-video-button-play,
.mkdf-video-button-holder.mkdf-vb-has-img .mkdf-video-button-play-image {
  position: absolute;
  display: block;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  z-index: 1;
}

.mkdf-video-button-holder.mkdf-vb-has-img .mkdf-video-button-play .mkdf-video-button-play-inner,
.mkdf-video-button-holder.mkdf-vb-has-img .mkdf-video-button-play-image .mkdf-video-button-play-inner {
  position: relative;
  top: 50%;
  left: 0;
  display: block;
  text-align: center;
}

.mkdf-video-button-holder.mkdf-vb-has-img .mkdf-video-button-play .mkdf-video-button-play-inner.top-right,
.mkdf-video-button-holder.mkdf-vb-has-img .mkdf-video-button-play-image .mkdf-video-button-play-inner.top-right {
  position: absolute;
  top: 14%;
  right: 0;
  -webkit-transform: translate(50%, -50%);
  -ms-transform: translate(50%, -50%);
  transform: translate(50%, -50%);
}

.mkdf-video-button-holder .mkdf-video-button-image {
  position: relative;
  display: inline-block;
  width: 100%;
  vertical-align: middle;
}

.mkdf-video-button-holder .mkdf-video-button-image img {
  display: block;
}

.mkdf-video-button-holder .mkdf-video-button-play,
.mkdf-video-button-holder .mkdf-video-button-play-image {
  position: relative;
  display: inline-block;
  vertical-align: top;
  z-index: 1;
}

.mkdf-video-button-holder .mkdf-video-button-play {
  color: #fff;
  font-size: 40px;
  line-height: 1;
}

.mkdf-video-button-holder .mkdf-video-button-play span {
  display: block;
  line-height: inherit;
}

.mkdf-video-button-holder .mkdf-video-button-play span:before {
  display: block;
  line-height: inherit;
}

.mkdf-video-button-holder .mkdf-video-button-play span span {
  display: inline-block;
  width: 105px;
  height: 105px;
  top: 50%;
  line-height: 101px;
  -webkit-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  transform: translateY(-50%);
  border-radius: 50%;
  -webkit-transition: all 0.2s ease-out;
  transition: all 0.2s ease-out;
}

.mkdf-video-button-holder .mkdf-video-button-play span span:hover .icon-basic-animation {
  -webkit-animation: mkdfPulsebig 1.8s infinite;
  animation: mkdfPulsebig 1.8s infinite;
}

.mkdf-video-button-holder .mkdf-video-button-play span .icon-basic-video {
  position: relative;
}

.mkdf-video-button-holder .mkdf-video-button-play span .icon-basic-animation {
  display: block;
  position: absolute;
  top: 50%;
  left: 0;
  /* padding: 28px; */
  width: 105px;
  border-radius: 50%;
  height: 105px;
  -webkit-transition: all .2s ease-out;
  transition: all .2s ease-out;
}

.mkdf-video-button-holder .mkdf-video-button-play-image.mkdf-vb-has-hover-image:hover img:first-child {
  opacity: 0;
}

.mkdf-video-button-holder .mkdf-video-button-play-image.mkdf-vb-has-hover-image:hover img:nth-child(2) {
  opacity: 1;
}

.mkdf-video-button-holder .mkdf-video-button-play-image img {
  display: block;
  margin: 0 auto;
  -webkit-transition: opacity 0.3s ease-in-out;
  transition: opacity 0.3s ease-in-out;
}

.mkdf-video-button-holder .mkdf-video-button-play-image img:first-child {
  position: relative;
  opacity: 1;
}

.mkdf-video-button-holder .mkdf-video-button-play-image img:nth-child(2) {
  position: absolute;
  top: 0;
  left: 50%;
  opacity: 0;
  -webkit-transform: translateX(-50%) translateZ(0);
  transform: translateX(-50%) translateZ(0);
}

.rev_slider_wrapper .mkdf-video-button-holder .mkdf-video-button-play-image img:nth-child(2) {
  -webkit-transform: translateZ(0);
  transform: translateZ(0);
}

.mkdf-video-button-holder .mkdf-video-button-text {
  display: table;
  padding: 60px 50px;
  color: #fff;
}

.mkdf-video-button-holder .mkdf-video-button-text .mkdf-video-button-title {
  width: 30%;
  display: table-cell;
  vertical-align: middle;
  color: #fff;
}

.mkdf-video-button-holder .mkdf-video-button-text p {
  width: 70%;
  font-size: 18px;
  display: table-cell;
  vertical-align: middle;
}

.mkdf-video-button-holder .mkdf-video-button-text-shadow-holder {
  position: absolute;
  bottom: 70px;
  left: 50%;
  -webkit-transform: translateX(-50%);
  -ms-transform: translateX(-50%);
  transform: translateX(-50%);
  width: 76%;
  z-index: -5;
}

/* ==========================================================================
   Video Button shortcode end styles
   ========================================================================== */
.mkdf-workflow {
  margin-top: 50px;
  position: relative;
}

.mkdf-workflow .main-line {
  background: #dee0e0;
  left: 50%;
  margin-left: -1px;
  position: absolute;
  right: 50%;
  top: 0;
  height: 100%;
  width: 2px;
}

.mkdf-workflow .mkdf-workflow-item {
  margin-left: auto;
  margin-right: auto;
  max-width: 80%;
  position: relative;
  padding-bottom: 21px;
  overflow: hidden;
}

.mkdf-workflow .mkdf-workflow-item:nth-of-type(2n) {
  text-align: left;
}

.mkdf-workflow .mkdf-workflow-item:nth-of-type(2n) .mkdf-workflow-item-inner {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.mkdf-workflow .mkdf-workflow-item:nth-of-type(2n) .mkdf-workflow-item-inner .mkdf-workflow-image {
  text-align: right;
}

.mkdf-workflow .mkdf-workflow-item:nth-of-type(2n+1) {
  text-align: right;
}

.mkdf-workflow .mkdf-workflow-item:nth-of-type(2n+1) .mkdf-workflow-item-inner {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: reverse;
  -ms-flex-direction: row-reverse;
  flex-direction: row-reverse;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

.mkdf-workflow .mkdf-workflow-item:nth-of-type(2n+1) .mkdf-workflow-item-inner .mkdf-workflow-image {
  text-align: left;
}

.mkdf-workflow .mkdf-workflow-item .mkdf-workflow-item-inner {
  display: inline-block;
  position: relative;
  width: 100%;
  vertical-align: middle;
}

.mkdf-workflow .mkdf-workflow-item .mkdf-workflow-item-inner .mkdf-workflow-image,
.mkdf-workflow .mkdf-workflow-item .mkdf-workflow-item-inner .mkdf-workflow-text {
  float: left;
  margin: 0;
  width: 50%;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}

.mkdf-workflow .mkdf-workflow-item .mkdf-workflow-item-inner .mkdf-workflow-image {
  padding: 0 90px;
}

.mkdf-workflow .mkdf-workflow-item .mkdf-workflow-item-inner .mkdf-workflow-image.left {
  text-align: left;
}

.mkdf-workflow .mkdf-workflow-item .mkdf-workflow-item-inner .mkdf-workflow-image.right {
  text-align: right;
}

.mkdf-workflow .mkdf-workflow-item .mkdf-workflow-item-inner .mkdf-workflow-image .mkdf-workflow-image-inner {
  position: relative;
  padding: 20px;
  display: inline-block;
}

.mkdf-workflow .mkdf-workflow-item .mkdf-workflow-item-inner .mkdf-workflow-image .mkdf-icon-shortcode {
  position: absolute;
  right: 0;
  top: 0;
  z-index: 5;
  font-size: 20px;
}

.mkdf-workflow .mkdf-workflow-item .mkdf-workflow-item-inner .mkdf-workflow-text {
  padding: 0 90px;
}

.mkdf-workflow .mkdf-workflow-item .mkdf-workflow-item-inner .mkdf-workflow-text.left {
  text-align: left;
}

.mkdf-workflow .mkdf-workflow-item .mkdf-workflow-item-inner .mkdf-workflow-text.right {
  text-align: right;
}

.mkdf-workflow .mkdf-workflow-item .mkdf-workflow-item-inner .mkdf-workflow-text .mkdf-workflow-text-inner {
  padding: 0 20px;
}

.mkdf-workflow .mkdf-workflow-item .mkdf-workflow-item-inner .mkdf-workflow-text h4 {
  margin-top: 12px;
  margin-bottom: 0px;
}

.mkdf-workflow .mkdf-workflow-item .mkdf-workflow-item-inner .mkdf-workflow-text p.text {
  margin-top: 14px;
}

.mkdf-workflow .mkdf-workflow-item .mkdf-workflow-item-inner .mkdf-workflow-text .circle {
  background: transparent;
  border: 3px solid #dee0e0;
  border-radius: 50%;
  content: "";
  height: 14px;
  left: 50%;
  margin: 0 0 0 -10px;
  position: absolute;
  top: 50%;
  width: 14px;
}

.mkdf-workflow .mkdf-workflow-item .line {
  background-color: #fff;
  height: 0;
  left: 50%;
  margin-left: -1px;
  position: absolute;
  width: 2px;
}

.mkdf-workflow .mkdf-workflow-item .line.line-one {
  top: 0;
}

.mkdf-workflow .mkdf-workflow-item .line.line-two {
  top: calc(50% + 10px);
}

.mkdf-workflow .mkdf-workflow-item:first-of-type .line-one {
  display: none;
}

.mkdf-workflow .mkdf-workflow-item:last-of-type .line-two {
  display: none;
}

.mkdf-workflow.mkdf-workflow-animate {
  -webkit-transform: translateY(100px);
  -ms-transform: translateY(100px);
  transform: translateY(100px);
  opacity: 0;
  -webkit-transition: opacity 0.55s cubic-bezier(0.23, 1, 0.32, 1), -webkit-transform 0.55s cubic-bezier(0.23, 1, 0.32, 1);
  transition: opacity 0.55s cubic-bezier(0.23, 1, 0.32, 1), -webkit-transform 0.55s cubic-bezier(0.23, 1, 0.32, 1);
  transition: opacity 0.55s cubic-bezier(0.23, 1, 0.32, 1), transform 0.55s cubic-bezier(0.23, 1, 0.32, 1);
  transition: opacity 0.55s cubic-bezier(0.23, 1, 0.32, 1), transform 0.55s cubic-bezier(0.23, 1, 0.32, 1), -webkit-transform 0.55s cubic-bezier(0.23, 1, 0.32, 1);
}

.mkdf-workflow.mkdf-workflow-animate .main-line {
  opacity: 0;
  height: 0;
  -webkit-transition: opacity 0.55s cubic-bezier(0.23, 1, 0.32, 1), height 1.8s ease-out;
  transition: opacity 0.55s cubic-bezier(0.23, 1, 0.32, 1), height 1.8s ease-out;
}

.mkdf-workflow.mkdf-workflow-animate .circle {
  -webkit-transform: scale(0.2);
  -ms-transform: scale(0.2);
  transform: scale(0.2);
  -webkit-transition: -webkit-transform 0.6s cubic-bezier(0.18, 0.89, 0.32, 1.68) 0.5s;
  transition: -webkit-transform 0.6s cubic-bezier(0.18, 0.89, 0.32, 1.68) 0.5s;
  transition: transform 0.6s cubic-bezier(0.18, 0.89, 0.32, 1.68) 0.5s;
  transition: transform 0.6s cubic-bezier(0.18, 0.89, 0.32, 1.68) 0.5s, -webkit-transform 0.6s cubic-bezier(0.18, 0.89, 0.32, 1.68) 0.5s;
}

.mkdf-workflow.mkdf-workflow-animate .mkdf-workflow-item .mkdf-workflow-item-inner .mkdf-workflow-image {
  opacity: 0;
  -webkit-transform: scale(0.6);
  -ms-transform: scale(0.6);
  transform: scale(0.6);
  -webkit-transition: -webkit-transform 0.6s cubic-bezier(0.18, 0.89, 0.32, 1), opacity 0.3s ease-out;
  -webkit-transition: opacity 0.3s ease-out, -webkit-transform 0.6s cubic-bezier(0.18, 0.89, 0.32, 1);
  transition: opacity 0.3s ease-out, -webkit-transform 0.6s cubic-bezier(0.18, 0.89, 0.32, 1);
  transition: transform 0.6s cubic-bezier(0.18, 0.89, 0.32, 1), opacity 0.3s ease-out;
  transition: transform 0.6s cubic-bezier(0.18, 0.89, 0.32, 1), opacity 0.3s ease-out, -webkit-transform 0.6s cubic-bezier(0.18, 0.89, 0.32, 1);
}

.mkdf-workflow.mkdf-workflow-animate .mkdf-workflow-item .mkdf-workflow-item-inner .mkdf-workflow-text h4, .mkdf-workflow.mkdf-workflow-animate .mkdf-workflow-item .mkdf-workflow-item-inner .mkdf-workflow-text p {
  opacity: 0;
  -webkit-transition: opacity 0.5s cubic-bezier(0.22, 0.61, 0.36, 1) 0.2s;
  transition: opacity 0.5s cubic-bezier(0.22, 0.61, 0.36, 1) 0.2s;
}

.mkdf-workflow.mkdf-workflow-animate.mkdf-appeared {
  -webkit-transform: translateY(0);
  -ms-transform: translateY(0);
  transform: translateY(0);
  opacity: 1;
}

.mkdf-workflow.mkdf-workflow-animate.mkdf-appeared .main-line {
  opacity: 1;
  height: 100%;
}

.mkdf-workflow.mkdf-workflow-animate.mkdf-appeared .mkdf-workflow-item.mkdf-appeared .mkdf-workflow-image {
  opacity: 1;
  -webkit-transform: scale(1);
  -ms-transform: scale(1);
  transform: scale(1);
}

.mkdf-workflow.mkdf-workflow-animate.mkdf-appeared .mkdf-workflow-item.mkdf-appeared .mkdf-workflow-text h4, .mkdf-workflow.mkdf-workflow-animate.mkdf-appeared .mkdf-workflow-item.mkdf-appeared .mkdf-workflow-text p {
  opacity: 1;
}

.mkdf-workflow.mkdf-workflow-animate.mkdf-appeared .circle {
  -webkit-transform: scale(1);
  -ms-transform: scale(1);
  transform: scale(1);
}

.mkdf-workflow.mkdf-workflow-light h4, .mkdf-workflow.mkdf-workflow-light p.text {
  color: #fff;
}

/*# sourceMappingURL=../../../../plugins/boostup-core/assets/css/shortcodes-map.css.map */
