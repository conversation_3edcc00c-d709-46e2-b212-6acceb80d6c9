<?php
/**
 * Must-Use Plugin: BoostUp Core Admin Fix
 * 
 * This mu-plugin ensures that admin URL fixes are loaded before any other plugins
 * and helps resolve 404 issues when editing pages on GoDaddy servers.
 * 
 * This file should be placed in wp-content/mu-plugins/ directory.
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Force proper admin URL handling
 */
function boostup_core_mu_fix_admin_urls() {
    // Ensure WP_ADMIN is defined in admin context
    if (is_admin() && !defined('WP_ADMIN')) {
        define('WP_ADMIN', true);
    }
    
    // Fix GoDaddy-specific issues
    if (isset($_SERVER['HTTP_HOST'])) {
        $host = $_SERVER['HTTP_HOST'];
        $is_godaddy = (strpos($host, 'godaddy') !== false || 
                      strpos($host, 'secureserver') !== false ||
                      strpos($host, 'malachilabs') !== false);
        
        if ($is_godaddy && is_admin() && !headers_sent()) {
            // Add headers to prevent mod_security issues
            header('X-Content-Type-Options: nosniff');
            header('X-Frame-Options: SAMEORIGIN');
            
            // Ensure proper content type for admin pages
            if (!isset($_SERVER['CONTENT_TYPE'])) {
                header('Content-Type: text/html; charset=UTF-8');
            }
        }
    }
}
add_action('init', 'boostup_core_mu_fix_admin_urls', 1);

/**
 * Ensure admin rewrite rules are protected
 */
function boostup_core_mu_protect_admin_rules($rules) {
    if (!is_array($rules)) {
        $rules = array();
    }
    
    // Ensure admin URLs are always preserved
    $admin_rules = array(
        'wp-admin/?$' => 'index.php',
        'wp-admin/(.*)' => 'wp-admin/$1'
    );
    
    // Merge admin rules at the beginning
    return array_merge($admin_rules, $rules);
}
add_filter('rewrite_rules_array', 'boostup_core_mu_protect_admin_rules', 1);

/**
 * Force rewrite rules flush on GoDaddy servers when needed
 */
function boostup_core_mu_check_rewrite_rules() {
    // Only run in admin and not during AJAX
    if (!is_admin() || wp_doing_ajax()) {
        return;
    }
    
    // Check if we're on a GoDaddy server
    if (isset($_SERVER['HTTP_HOST'])) {
        $host = $_SERVER['HTTP_HOST'];
        $is_godaddy = (strpos($host, 'godaddy') !== false || 
                      strpos($host, 'secureserver') !== false ||
                      strpos($host, 'malachilabs') !== false);
        
        if ($is_godaddy) {
            $rules = get_option('rewrite_rules');
            $last_check = get_option('boostup_core_mu_last_check', 0);
            $current_time = time();
            
            // Check every 30 minutes on GoDaddy servers
            if (($current_time - $last_check) > 1800) {
                if (empty($rules) || !is_array($rules)) {
                    flush_rewrite_rules(false);
                }
                update_option('boostup_core_mu_last_check', $current_time);
            }
        }
    }
}
add_action('admin_init', 'boostup_core_mu_check_rewrite_rules');

/**
 * Add admin notice if there are rewrite issues
 */
function boostup_core_mu_admin_notice() {
    // Only show to administrators
    if (!current_user_can('manage_options')) {
        return;
    }
    
    // Check if rewrite rules exist
    $rules = get_option('rewrite_rules');
    if (empty($rules) || !is_array($rules)) {
        echo '<div class="notice notice-warning is-dismissible">';
        echo '<p><strong>BoostUp Core:</strong> Rewrite rules may need to be flushed. ';
        echo 'If you\'re experiencing 404 errors when editing pages, ';
        echo '<a href="' . admin_url('options-permalink.php') . '">visit Permalinks settings</a> and click Save.</p>';
        echo '</div>';
    }
}
add_action('admin_notices', 'boostup_core_mu_admin_notice');

/**
 * Log admin URL access attempts for debugging
 */
function boostup_core_mu_log_admin_access() {
    // Only log on GoDaddy servers and when WP_DEBUG is enabled
    if (!defined('WP_DEBUG') || !WP_DEBUG) {
        return;
    }
    
    if (isset($_SERVER['HTTP_HOST']) && isset($_SERVER['REQUEST_URI'])) {
        $host = $_SERVER['HTTP_HOST'];
        $is_godaddy = (strpos($host, 'godaddy') !== false || 
                      strpos($host, 'secureserver') !== false ||
                      strpos($host, 'malachilabs') !== false);
        
        if ($is_godaddy && is_admin()) {
            $request_uri = $_SERVER['REQUEST_URI'];
            
            // Log edit page attempts
            if (strpos($request_uri, 'post.php') !== false && strpos($request_uri, 'action=edit') !== false) {
                error_log('BoostUp Core MU: Edit page access attempt - ' . $request_uri);
            }
        }
    }
}
add_action('init', 'boostup_core_mu_log_admin_access');
