/* ==========================================================================
   Portfolio Single page style - begin
   ========================================================================== */

.mkdf-portfolio-single-holder {
	@include mkdfRelativeHolderLayout();
	margin: 0 0 50px;
	
    .mkdf-ps-image-holder {
	    @include mkdfRelativeHolderLayout();
	    
	    .mkdf-ps-image {
		    
		    &:not(.mkdf-item-space) {
			    @include mkdfRelativeHolderLayout();
			    box-sizing: border-box;
		    }
		    
		    a, img {
			    position: relative;
			    display: block;
		    }
	    }
    }
	
	.mkdf-ps-info-holder {
		@include mkdfRelativeHolderLayout();
		
		.mkdf-ps-info-item {
			@include mkdfRelativeHolderLayout();

			&.mkdf-ps-content-item {
				margin: 15px 0 70px;
			}

			&.mkdf-ps-social-share {
				border-top: 1px solid $default-border-color;
				padding: 20px 0 0;

				span {
					color: #fff;
					padding: 7px;
					border-radius: 50%;

					&.social_facebook {
						background: #3b5998;
					}
					&.social_twitter {
						background: #55acee;
					}
					&.social_linkedin {
						background: #007bb5;
					}
					&.social_instagram {
						background: #cd486b;
					}
					&.social_pinterest {
						background: #cb2027;
					}
					&.social_tumblr {
						background: #32506d;
					}
					&.social_googleplus {
						background: #dd4b39;
					}
					&.fa-vk {
						background: #45668e;
					}
				}
			}

			margin: 0 0 10px;
			
			p, a {
				margin: 0;
			}

			h6, p {
				display: inline;
			}
		}
		
		.mkdf-ps-info-title,
		.mkdf-ps-title {
			margin: 0;
		}
	}
}

/* ==========================================================================
   Portfolio Single page style - end
   ========================================================================== */

/* ==========================================================================
   Portfolio Single page specific style for types - begin
   ========================================================================== */

.mkdf-portfolio-single-holder {
	
	.mkdf-ps-image-holder {
		
		&.mkdf-grid-masonry-list {
			
			.mkdf-ps-image {
				
				a {
					height: 100%;
				}
			}
		}
	}
}
/* ==========================================================================
   Portfolio Single page specific style for types - end
   ========================================================================== */