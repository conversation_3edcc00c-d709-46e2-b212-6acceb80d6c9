.mkdf-workflow {
	margin-top: 50px;
	position: relative;

	.main-line {
		background: #dee0e0;
		left: 50%;
		margin-left: -1px;
		position: absolute;
		right: 50%;
		top: 0;
		height: 100%;
		width: 2px;
	}

	.mkdf-workflow-item {
		margin-left: auto;
		margin-right: auto;
		max-width: 80%;
		position: relative;
		padding-bottom: 21px;
		overflow: hidden;

		&:nth-of-type(2n) {
			text-align: left;

			.mkdf-workflow-item-inner {
				display: flex;
				align-items: center;
				.mkdf-workflow-image {
					text-align: right;
				}
			}
		}

		&:nth-of-type(2n+1) {
			text-align: right;

			.mkdf-workflow-item-inner {
				display: -webkit-box;
				display: -webkit-flex;
				display: -ms-flexbox;
				display: flex;
				align-items: center;
				-webkit-box-orient: horizontal;
				-webkit-box-direction: reverse;
				-webkit-flex-direction: row-reverse;
				-ms-flex-direction: row-reverse;
				flex-direction: row-reverse;
				-webkit-flex-wrap: wrap;
				-ms-flex-wrap: wrap;
				flex-wrap: wrap;

				.mkdf-workflow-image {
					text-align: left;
				}
			}
		}

		.mkdf-workflow-item-inner {
			display: inline-block;
			position: relative;
			width: 100%;
			vertical-align: middle;

			.mkdf-workflow-image,
			.mkdf-workflow-text {
				float: left;
				margin: 0;
				width: 50%;
				box-sizing: border-box;
			}

			.mkdf-workflow-image {
				padding: 0 90px;

				&.left{
					text-align: left;
				}
				&.right{
					text-align: right;
				}

				.mkdf-workflow-image-inner {
					position: relative;
					padding: 20px;
					display: inline-block;
				}

				.mkdf-icon-shortcode {
					position:absolute;
					right: 0;
					top: 0;
					z-index: 5;
					font-size: 20px;
				}
			}

			.mkdf-workflow-text {
				padding: 0 90px;

				&.left{
					text-align: left;
				}
				&.right{
					text-align: right;
				}

				.mkdf-workflow-text-inner {
					padding: 0 20px;
				}

				h4 {
					margin-top: 12px;
					margin-bottom: 0px;
				}

				p.text {
					margin-top: 14px;
				}

				.circle {
					background: transparent;
					border: 3px solid #dee0e0;
					border-radius: 50%;
					content: "";
					height: 14px;
					left: 50%;
					margin: 0 0 0 -10px;
					position: absolute;
					top: 50%;
					width: 14px;
				}
			}
		}

		.line {
			background-color: #fff;
			height: 0;
			left: 50%;
			margin-left: -1px;
			position: absolute;
			width: 2px;

			&.line-one {
				top : 0;
			}

			&.line-two {
				top: calc(50% + 10px);
			}
		}

		&:first-of-type .line-one {
			display: none;
		}

		
		&:last-of-type .line-two {
			display: none;
		}

	}

	&.mkdf-workflow-animate {
		@include mkdfTransform(translateY(100px));
		opacity: 0;
		-webkit-transition: opacity .55s cubic-bezier(0.23, 1, 0.32, 1), -webkit-transform .55s cubic-bezier(0.23, 1, 0.32, 1);
		transition: opacity .55s cubic-bezier(0.23, 1, 0.32, 1), transform .55s cubic-bezier(0.23, 1, 0.32, 1);
		.main-line {
			opacity: 0;
			height: 0;
			@include mkdfTransition(opacity .55s cubic-bezier(0.23, 1, 0.32, 1), height 1.8s ease-out);
		}
		.circle {
			@include mkdfTransform(scale(.2));
			-webkit-transition: -webkit-transform .6s cubic-bezier(0.18, 0.89, 0.32, 1.68) .5s;
			transition: transform .6s cubic-bezier(0.18, 0.89, 0.32, 1.68) .5s;
		}
		.mkdf-workflow-item {
			.mkdf-workflow-item-inner {
				.mkdf-workflow-image {
					opacity: 0;
					@include mkdfTransform(scale(.6));
					-webkit-transition: -webkit-transform .6s cubic-bezier(0.18, 0.89, 0.32, 1), opacity .3s ease-out;
					transition: transform .6s cubic-bezier(0.18, 0.89, 0.32, 1), opacity .3s ease-out;
				}
				.mkdf-workflow-text {
					h4, p {
						opacity: 0;
						@include mkdfTransition(opacity .5s cubic-bezier(0.22, 0.61, 0.36, 1) .2s);
					}
				}
			}
		}
		&.mkdf-appeared {
			@include mkdfTransform(translateY(0));
			opacity: 1;
			.main-line {
				opacity: 1;
				height: 100%;
			}
			.mkdf-workflow-item.mkdf-appeared {
				.mkdf-workflow-image {
					opacity: 1;
					@include mkdfTransform(scale(1));
				}
				.mkdf-workflow-text {
					h4, p {
						opacity: 1;
					}
				}

			}
			.circle {
				@include mkdfTransform(scale(1));
			}
		}
	}
	&.mkdf-workflow-light {
		h4, p.text {
			color: #fff;
		}
	}
}