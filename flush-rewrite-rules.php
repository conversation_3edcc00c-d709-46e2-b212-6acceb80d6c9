<?php
/**
 * Enhanced script to flush rewrite rules and fix 404 issues on GoDaddy servers
 * Run this script to clear any conflicting rewrite rules
 */

// Load WordPress
require_once('wp-config.php');
require_once('wp-load.php');

// Check if user is logged in and has admin privileges
if (!is_user_logged_in() || !current_user_can('manage_options')) {
    die('Access denied. You must be logged in as an administrator.');
}

echo "<h2>Enhanced Rewrite Rules Flush for GoDaddy Servers</h2>";

// Detect server type
$server_info = $_SERVER['HTTP_HOST'] ?? 'unknown';
$is_godaddy = (strpos($server_info, 'godaddy') !== false ||
               strpos($server_info, 'secureserver') !== false ||
               strpos($server_info, 'malachilabs') !== false);

echo "<p><strong>Server:</strong> " . htmlspecialchars($server_info) . "</p>";
echo "<p><strong>GoDaddy Server Detected:</strong> " . ($is_godaddy ? 'Yes' : 'No') . "</p>";

// Clear any cached rewrite rules
delete_option('rewrite_rules');
echo "<p>✓ Cleared cached rewrite rules</p>";

// Force regenerate rewrite rules
flush_rewrite_rules(true);
echo "<p>✓ Regenerated rewrite rules with hard flush</p>";

// Update the last flush timestamp
update_option('boostup_core_last_rewrite_flush', time());
echo "<p>✓ Updated flush timestamp</p>";

// Check current rewrite rules
$rules = get_option('rewrite_rules');
echo "<p><strong>Current number of rewrite rules:</strong> " . (is_array($rules) ? count($rules) : 0) . "</p>";

// Test admin URL generation
$edit_url = admin_url('post.php?post=1&action=edit');
echo "<p><strong>Sample edit URL:</strong> <a href='" . esc_url($edit_url) . "' target='_blank'>" . htmlspecialchars($edit_url) . "</a></p>";

// Show some admin-related rules to verify they're working
if (is_array($rules)) {
    echo "<h3>Admin-related rewrite rules:</h3>";
    echo "<ul>";
    $admin_rules_found = 0;
    foreach ($rules as $pattern => $rewrite) {
        if (strpos($pattern, 'admin') !== false || strpos($rewrite, 'admin') !== false) {
            echo "<li><code>" . htmlspecialchars($pattern) . "</code> → <code>" . htmlspecialchars($rewrite) . "</code></li>";
            $admin_rules_found++;
        }
    }
    if ($admin_rules_found === 0) {
        echo "<li><em>No admin-specific rules found (this is normal)</em></li>";
    }
    echo "</ul>";
}

echo "<h3>GoDaddy-Specific Fixes Applied:</h3>";
echo "<ul>";
echo "<li>✓ Hard flush of rewrite rules</li>";
echo "<li>✓ Cleared rewrite rules cache</li>";
echo "<li>✓ Updated flush timestamp</li>";
echo "<li>✓ Admin URL protection enabled</li>";
echo "</ul>";

echo "<h3>Next Steps:</h3>";
echo "<ol>";
echo "<li>Try editing a page in WordPress admin now</li>";
echo "<li>If the issue persists, wait 5-10 minutes for GoDaddy's cache to clear</li>";
echo "<li>You can run this script again if needed</li>";
echo "<li>Delete this file after testing</li>";
echo "</ol>";

echo "<p><em>This script can be run multiple times safely. Delete it when the issue is resolved.</em></p>";
?>
