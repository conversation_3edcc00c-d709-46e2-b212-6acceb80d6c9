<?php

namespace BoostUpCore\CPT\Shortcodes\VideoButton;

use BoostUpCore\Lib;

class VideoButton implements Lib\ShortcodeInterface {
	private $base;
	
	public function __construct() {
		$this->base = 'mkdf_video_button';
		
		add_action( 'vc_before_init', array( $this, 'vcMap' ) );
	}
	
	public function getBase() {
		return $this->base;
	}
	
	public function vcMap() {
		if ( function_exists( 'vc_map' ) ) {
			vc_map(
				array(
					'name'                      => esc_html__( 'Video Button', 'boostup-core' ),
					'base'                      => $this->getBase(),
					'category'                  => esc_html__( 'by BOOSTUP', 'boostup-core' ),
					'icon'                      => 'icon-wpb-video-button extended-custom-icon',
					'allowed_container_element' => 'vc_row',
					'params'                    => array(
						array(
							'type'        => 'textfield',
							'param_name'  => 'custom_class',
							'heading'     => esc_html__( 'Custom CSS Class', 'boostup-core' ),
							'description' => esc_html__( 'Style particular content element differently - add a class name and refer to it in custom CSS', 'boostup-core' )
						),
						array(
							'type'       => 'textfield',
							'param_name' => 'video_link',
							'heading'    => esc_html__( 'Video Link', 'boostup-core' )
						),
						array(
							'type'        => 'attach_image',
							'param_name'  => 'video_image',
							'heading'     => esc_html__( 'Video Image', 'boostup-core' ),
							'description' => esc_html__( 'Select image from media library', 'boostup-core' )
						),
                        array(
                            'type'       => 'textfield',
                            'param_name' => 'title',
                            'heading'    => esc_html__( 'Title', 'boostup-core' )
                        ),
                        array(
                            'type'        => 'dropdown',
                            'param_name'  => 'title_tag',
                            'heading'     => esc_html__( 'Title Tag', 'boostup-core' ),
                            'value'       => array_flip( boostup_mikado_get_title_tag( true ) ),
                            'save_always' => true,
                            'dependency'  => array( 'element' => 'title', 'not_empty' => true )
                        ),
                        array(
                            'type'       => 'textfield',
                            'param_name' => 'video_text',
                            'heading'    => esc_html__( 'Text', 'boostup-core' ),
                            'dependency'  => array( 'element' => 'title', 'not_empty' => true )
                        ),
                        array(
                            'type'       => 'colorpicker',
                            'param_name' => 'text_box_color',
                            'heading'    => esc_html__( 'Text Box Color', 'boostup-core' ),
                            'dependency'  => array( 'element' => 'title', 'not_empty' => true )
                        ),
                        array(
                            'type'       => 'dropdown',
                            'param_name' => 'holder_shadow',
                            'heading'    => esc_html__( 'Enable Text Box Shadow', 'boostup-core' ),
                            'value'      => array(
                                esc_html__( 'Yes', 'boostup-core' )     => 'shadow-yes',
                                esc_html__( 'No', 'boostup-core' )       => 'shadow-no',
                            )
                        ),
                        array(
                            'type'       => 'colorpicker',
                            'param_name' => 'holder_shadow_color',
                            'heading'    => esc_html__( 'Text Box Shadow Color', 'boostup-core' ),
                            'dependency'  => array( 'element' => 'holder_shadow', 'value'   => 'shadow-yes' )
                        ),
                        array(
                            'type'       => 'dropdown',
                            'param_name' => 'play_button_position',
                            'heading'    => esc_html__( 'Play Button Position', 'boostup-core' ),
                            'value'      => array(
                                esc_html__( 'Center', 'boostup-core' )     => 'button-center',
                                esc_html__( 'Top Right', 'boostup-core' )       => 'button-top-right',
                            )
                        ),
						array(
							'type'       => 'textfield',
							'param_name' => 'play_button_size',
							'heading'    => esc_html__( 'Play Button Size (px)', 'boostup-core' )
						),
                        array(
                            'type'       => 'colorpicker',
                            'param_name' => 'play_button_color',
                            'heading'    => esc_html__( 'Play Button Color', 'boostup-core' )
                        ),
                        array(
                            'type'       => 'colorpicker',
                            'param_name' => 'circle_color',
                            'heading'    => esc_html__( 'Play Button Circle Color', 'boostup-core' )
                        ),
                        array(
                            'type'       => 'dropdown',
                            'param_name' => 'button_shadow',
                            'heading'    => esc_html__( 'Enable Button Shadow', 'boostup-core' ),
                            'value'      => array(
                                esc_html__( 'Yes', 'boostup-core' )     => 'button-shadow-yes',
                                esc_html__( 'No', 'boostup-core' )       => 'button-shadow-no',
                            )
                        ),
                        array(
                            'type'       => 'colorpicker',
                            'param_name' => 'button_shadow_color',
                            'heading'    => esc_html__( 'Button Shadow Color', 'boostup-core' ),
                            'dependency'  => array( 'element' => 'button_shadow', 'value'   => 'button-shadow-yes' )
                        ),
						array(
							'type'        => 'attach_image',
							'param_name'  => 'play_button_image',
							'heading'     => esc_html__( 'Play Button Custom Image', 'boostup-core' ),
							'description' => esc_html__( 'Select image from media library. If you use this field then play button color and button size options will not work', 'boostup-core' )
						),
						array(
							'type'        => 'attach_image',
							'param_name'  => 'play_button_hover_image',
							'heading'     => esc_html__( 'Play Button Custom Hover Image', 'boostup-core' ),
							'description' => esc_html__( 'Select image from media library. If you use this field then play button color and button size options will not work', 'boostup-core' )
						)
					)
				)
			);
		}
	}
	
	public function render( $atts, $content = null ) {
		$args   = array(
			'custom_class'            => '',
			'video_link'              => '#',
			'video_image'             => '',
            'title' 				  => '',
            'title_tag'               => 'h4',
            'video_text' 		      => '',
            'text_box_color' 		  => '#ea3d56',
            'holder_shadow'           => 'shadow-yes',
            'holder_shadow_color'     => 'rgba(234, 61, 86, 0.3)',
            'button_shadow'           => 'button-shadow-yes',
            'button_shadow_color'     => 'rgba(24, 26, 45, 0.5)',
            'play_button_position'       => 'button-center',
			'play_button_color'       => '',
            'circle_color'            => '#ea3d56',
			'play_button_size'        => '',
			'play_button_image'       => '',
			'play_button_hover_image' => ''
		);
		$params = shortcode_atts( $args, $atts );

        $params['button_classes']     = $this->getButtonClasses( $params );
		$params['holder_classes']     = $this->getHolderClasses( $params );
		$params['play_button_styles'] = $this->getPlayButtonStyles( $params );
        $params['circle_style'] = $this->getCircleStyle( $params );
        $params['circle_animation_style'] = $this->getCircleColorAnimation( $params );
        $params['text_box_style'] = $this->getTextBoxStyle( $params );
        $params['text_shadow_style'] = $this->getTextShadowStyle( $params );
        $params['title_tag']          = ! empty( $params['title_tag'] ) ? $params['title_tag'] : $args['title_tag'];
		
		$html = boostup_core_get_shortcode_module_template_part( 'templates/video-button', 'video-button', '', $params );
		
		return $html;
	}
	
	private function getHolderClasses( $params ) {
		$buttonClasses = array();

        $buttonClasses[] = ! empty( $params['video_image'] ) ? 'mkdf-vb-has-img' : '';
		
		return implode( ' ', $buttonClasses );
	}

    private function getButtonClasses( $params ) {
        $holderClasses = array();

        $holderClasses[] =  ( $params['play_button_position'] == 'button-top-right' ) ? 'top-right' : '';

        return implode( ' ', $holderClasses );
    }
	
	private function getPlayButtonStyles( $params ) {
		$styles = array();
		
		if ( ! empty( $params['play_button_color'] ) ) {
			$styles[] = 'color: ' . $params['play_button_color'];
		}

		if ( ! empty( $params['play_button_size'] ) ) {
			$styles[] = 'font-size: ' . boostup_mikado_filter_px( $params['play_button_size'] ) . 'px';
		}
		
		return implode( ';', $styles );
	}

    private function getCircleStyle( $params ) {
        $styles = array();

        if ( ! empty( $params['circle_color'] ) ) {
            $styles[] = 'background-color: ' . $params['circle_color'];
        }

        if (  ($params['button_shadow']) == 'button-shadow-yes'  ) {
            $styles[] = 'box-shadow: 0px 5px 32px 0px ' . $params['button_shadow_color'];
        }


        return implode( ';', $styles );
    }
    private function getCircleColorAnimation( $params ) {
        $styles = array();

        if ( ! empty( $params['circle_color'] ) ) {
            $styles[] = 'color: ' . $params['circle_color'];
        }

      


        return implode( ';', $styles );
    }

    private function getTextBoxStyle( $params ) {
        $styles = array();

        if ( ! empty( $params['text_box_color'] ) ) {
            $styles[] = 'background-color: ' . $params['text_box_color'];
        }

        return implode( ';', $styles );
    }

    private function getTextShadowStyle( $params ) {
        $styles = array();

        if (  ($params['holder_shadow']) == 'shadow-yes'  ) {
            $styles[] = 'box-shadow: 0px 35px 100px 80px ' . $params['holder_shadow_color'];
        }

        return implode( ';', $styles );
    }
}