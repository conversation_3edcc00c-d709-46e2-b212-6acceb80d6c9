/*!
 * WPBakery Page Builder v6.0.0 (https://wpbakery.com)
 * Copyright 2011-2023 <PERSON>, WPBakery
 * License: Commercial. More details: http://go.wpbakery.com/licensing
 */

// jscs:disable
// jshint ignore: start

!function($){"use strict";function Collapse(element,options){this.$element=$(element),this.options=$.extend({},Collapse.DEFAULTS,options),this.transitioning=null,this.options.parent&&(this.$parent=$(this.options.parent)),this.options.toggle&&this.toggle()}function Plugin(option){return this.each(function(){var $this=$(this),data=$this.data("bs.collapse"),options=$.extend({},Collapse.DEFAULTS,$this.data(),"object"==typeof option&&option);!data&&options.toggle&&"show"==option&&(option=!option),data||$this.data("bs.collapse",data=new Collapse(this,options)),"string"==typeof option&&data[option]()})}Collapse.VERSION="3.1.1",Collapse.DEFAULTS={toggle:!0},Collapse.prototype.dimension=function(){return this.$element.hasClass("width")?"width":"height"},Collapse.prototype.show=function(){if(!this.transitioning&&!this.$element.hasClass("in")){var startEvent=$.Event("show.bs.collapse");if(this.$element.trigger(startEvent),!startEvent.isDefaultPrevented()){startEvent=this.$parent&&this.$parent.find("> .panel > .in");if(startEvent&&startEvent.length){var hasData=startEvent.data("bs.collapse");if(hasData&&hasData.transitioning)return;Plugin.call(startEvent,"hide"),hasData||startEvent.data("bs.collapse",null)}var dimension=this.dimension(),complete=(this.$element.removeClass("collapse").addClass("collapsing")[dimension](0),this.transitioning=1,function(e){e&&e.target!=this.$element[0]?this.$element.one($.support.transition.end,$.proxy(complete,this)):(this.$element.removeClass("collapsing").addClass("collapse in")[dimension](""),this.transitioning=0,this.$element.off($.support.transition.end+".bs.collapse").trigger("shown.bs.collapse"))});if(!$.support.transition)return complete.call(this);hasData=$.camelCase(["scroll",dimension].join("-"));this.$element.on($.support.transition.end+".bs.collapse",$.proxy(complete,this)).emulateTransitionEnd(350)[dimension](this.$element[0][hasData])}}},Collapse.prototype.hide=function(){if(!this.transitioning&&this.$element.hasClass("in")){var startEvent=$.Event("hide.bs.collapse");if(this.$element.trigger(startEvent),!startEvent.isDefaultPrevented()){var startEvent=this.dimension(),complete=(this.$element[startEvent](this.$element[startEvent]())[0].offsetHeight,this.$element.addClass("collapsing").removeClass("collapse").removeClass("in"),this.transitioning=1,function(e){e&&e.target!=this.$element[0]?this.$element.one($.support.transition.end,$.proxy(complete,this)):(this.transitioning=0,this.$element.trigger("hidden.bs.collapse").removeClass("collapsing").addClass("collapse"))});if(!$.support.transition)return complete.call(this);this.$element[startEvent](0).one($.support.transition.end,$.proxy(complete,this)).emulateTransitionEnd(350)}}},Collapse.prototype.toggle=function(){this[this.$element.hasClass("in")?"hide":"show"]()};var old=$.fn.collapse;$.fn.collapse=Plugin,$.fn.collapse.Constructor=Collapse,$.fn.collapse.noConflict=function(){return $.fn.collapse=old,this},$(document).on("click.bs.collapse.data-api",'[data-toggle="collapse"]',function(e){var $this=$(this),e=$this.attr("data-target")||e.preventDefault()||(e=$this.attr("href"))&&e.replace(/.*(?=#[^\s]+$)/,""),e=$(e),data=e.data("bs.collapse"),option=data?"toggle":$this.data(),parent=$this.attr("data-parent"),$parent=parent&&$(parent);data&&data.transitioning||($parent&&$parent.find('[data-toggle="collapse"][data-parent="'+parent+'"]').not($this).addClass("collapsed"),$this[e.hasClass("in")?"addClass":"removeClass"]("collapsed")),Plugin.call(e,option)})}(jQuery);