<?php
$thumb_size = $this_object->getImageSize($params);
$featured_color = $this_object->getSwitchFeaturedColor($params);
?>
<div class="mkdf-pli-image">
	<?php if ( has_post_thumbnail() ) {
		$image_src = get_the_post_thumbnail_url( get_the_ID() );
		
		if ( strpos( $image_src, '.gif' ) !== false ) {
			echo get_the_post_thumbnail( get_the_ID(), 'full' );
		} else {
			echo get_the_post_thumbnail( get_the_ID(), $thumb_size );
		}
	} else { ?>
		<img itemprop="image" class="mkdf-pl-original-image" width="800" height="600" src="<?php echo BOOSTUP_CORE_CPT_URL_PATH.'/portfolio/assets/img/portfolio_featured_image.jpg'; ?>" alt="<?php esc_attr_e('Portfolio Featured Image', 'boostup-core'); ?>" />
	<?php } ?>

	<?php if ( ($params['item_style'] == 'standard-shader') || ($params['item_style'] == 'gallery-overlay')){ ?>
	<div class="mkdf-pli-image-hover"
         style="background-color:
             <?php if ($featured_color != '') {
                 echo esc_attr($featured_color);
             } else {
                 echo esc_attr($params['image_hover_color']);
             } ?>">
		<div class="mkdf-pli-image-hover-table">
			<i class="icon_plus"></i>
		</div>
	</div>
	<?php } ?>

</div>