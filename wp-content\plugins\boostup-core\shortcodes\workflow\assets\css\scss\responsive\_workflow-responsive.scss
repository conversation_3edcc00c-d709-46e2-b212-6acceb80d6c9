@include laptop-landscape {
	.mkdf-workflow {
		.mkdf-workflow-item {
			max-width: 100%;

			.mkdf-workflow-item-inner .mkdf-workflow-text {
				padding: 0px 40px;
			}

			 .mkdf-workflow-item-inner .mkdf-workflow-image {
				 padding: 0px 40px;
			 }
		}
	}
}

@include ipad-portrait {

}

@include phone-landscape {

	.mkdf-workflow {

		.main-line,
		.mkdf-workflow-item .line,
		.mkdf-workflow-item .mkdf-workflow-text .circle {
			display: none !important;
		}

		.mkdf-workflow-item {
			text-align: left;

			.mkdf-workflow-item-inner {

				.mkdf-workflow-image {
					width: 100%;
					margin-bottom: 20px;
					text-align: left;
					padding: 0px 40px 0 0;

					&.right {
						text-align: left !important;
					}
				}
				
				.mkdf-workflow-text {
					width: 100% !important;
					padding: 0px !important;
					text-align: left !important;
				}

			}


			
		}
		.mkdf-workflow-item:nth-of-type(2n)  {

			.mkdf-workflow-item-inner {
				display: block !important;
			}

		}

	}
	
}