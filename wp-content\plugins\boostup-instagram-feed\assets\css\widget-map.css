/* ==========================================================================
   Global partials
   ========================================================================== */
@-webkit-keyframes animate-btn-line {
  0% {
    -webkit-transform-origin: 0 50%;
    transform-origin: 0 50%;
    -webkit-transform: scaleX(0);
    transform: scaleX(0);
  }
  100% {
    -webkit-transform-origin: 0 50%;
    transform-origin: 0 50%;
    -webkit-transform: scaleX(1);
    transform: scaleX(1);
  }
}
@keyframes animate-btn-line {
  0% {
    -webkit-transform-origin: 0 50%;
    transform-origin: 0 50%;
    -webkit-transform: scaleX(0);
    transform: scaleX(0);
  }
  100% {
    -webkit-transform-origin: 0 50%;
    transform-origin: 0 50%;
    -webkit-transform: scaleX(1);
    transform: scaleX(1);
  }
}

@-webkit-keyframes mkdfPulsebig {
  0% {
    -webkit-box-shadow: 0 0 0 0 currentColor;
    box-shadow: 0 0 0 0 currentColor;
  }
  70% {
    -webkit-box-shadow: 0 0 0 20px rgba(234, 61, 86, 0);
    box-shadow: 0 0 0 20px rgba(234, 61, 86, 0);
  }
  100% {
    -webkit-box-shadow: 0 0 0 0 rgba(234, 61, 86, 0);
    box-shadow: 0 0 0 0 rgba(234, 61, 86, 0);
  }
}

@keyframes mkdfPulsebig {
  0% {
    -webkit-box-shadow: 0 0 0 0 currentColor;
    box-shadow: 0 0 0 0 currentColor;
  }
  70% {
    -webkit-box-shadow: 0 0 0 20px rgba(234, 61, 86, 0);
    box-shadow: 0 0 0 20px rgba(234, 61, 86, 0);
  }
  100% {
    -webkit-box-shadow: 0 0 0 0 rgba(234, 61, 86, 0);
    box-shadow: 0 0 0 0 rgba(234, 61, 86, 0);
  }
}

@-webkit-keyframes mkdfPulsesmall {
  0% {
    -webkit-box-shadow: 0 0 0 0 currentColor;
  }
  70% {
    -webkit-box-shadow: 0 0 0 10px rgba(234, 61, 86, 0.15);
  }
  100% {
    -webkit-box-shadow: 0 0 0 0 rgba(234, 61, 86, 0);
  }
}

@keyframes mkdfPulsesmall {
  0% {
    -webkit-box-shadow: 0 0 0 0 currentColor;
    box-shadow: 0 0 0 0 currentColor;
  }
  70% {
    -webkit-box-shadow: 0 0 0 10px rgba(234, 61, 86, 0);
    box-shadow: 0 0 0 10px rgba(234, 61, 86, 0);
  }
  100% {
    -webkit-box-shadow: 0 0 0 0 rgba(234, 61, 86, 0);
    box-shadow: 0 0 0 0 rgba(234, 61, 86, 0);
  }
}

@-webkit-keyframes mkdfPulsesmallfirst {
  0% {
    -webkit-box-shadow: 0 0 0 0 #ea3d56;
  }
  70% {
    -webkit-box-shadow: 0 0 0 10px rgba(234, 61, 86, 0.15);
  }
  100% {
    -webkit-box-shadow: 0 0 0 0 rgba(234, 61, 86, 0);
  }
}

@keyframes mkdfPulsesmallfirst {
  0% {
    -webkit-box-shadow: 0 0 0 0 #ea3d56;
    box-shadow: 0 0 0 0 #ea3d56;
  }
  70% {
    -webkit-box-shadow: 0 0 0 10px rgba(234, 61, 86, 0);
    box-shadow: 0 0 0 10px rgba(234, 61, 86, 0);
  }
  100% {
    -webkit-box-shadow: 0 0 0 0 rgba(234, 61, 86, 0);
    box-shadow: 0 0 0 0 rgba(234, 61, 86, 0);
  }
}

/* ==========================================================================
   Widgets styles
   ========================================================================== */
/* ==========================================================================
   Instagram widget style - begin
   ========================================================================== */
aside.mkdf-sidebar .widget.widget_mkdf_instagram_widget .mkdf-widget-title,
.wpb_widgetised_column .widget.widget_mkdf_instagram_widget .mkdf-widget-title {
  margin: 0 0 25px;
}

.mkdf-instagram-feed {
  list-style: none;
  padding: 0;
  margin: 0;
}

.mkdf-instagram-feed li {
  float: left;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  border: none !important;
}

.mkdf-instagram-feed li a {
  position: relative;
  display: block;
  overflow: hidden;
}

.mkdf-instagram-feed li a:hover:after {
  opacity: 1;
}

.mkdf-instagram-feed li a:after {
  position: absolute;
  display: block;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  content: '';
  background-color: rgba(234, 61, 86, 0.4);
  opacity: 0;
  -webkit-transition: opacity 0.2s ease-in-out;
  transition: opacity 0.2s ease-in-out;
}

.mkdf-instagram-feed li a .mkdf-instagram-icon {
  position: absolute;
  top: 50%;
  left: 50%;
  color: #1b2c58;
  opacity: 0;
  z-index: 1;
  -webkit-transition: opacity 0.2s ease-in-out;
  transition: opacity 0.2s ease-in-out;
  -webkit-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
}

.mkdf-instagram-feed li a:hover .mkdf-instagram-icon {
  opacity: 1;
}

.mkdf-instagram-feed li img {
  width: 100%;
  display: block;
}

.mkdf-instagram-feed.mkdf-instagram-gallery.mkdf-no-space {
  margin: 0;
}

.mkdf-instagram-feed.mkdf-instagram-gallery.mkdf-no-space li {
  padding: 0 0px;
  margin: 0 0 0px;
}

.mkdf-instagram-feed.mkdf-instagram-gallery.mkdf-tiny-space {
  margin: 0 -5px -10px;
}

.mkdf-instagram-feed.mkdf-instagram-gallery.mkdf-tiny-space li {
  padding: 0 5px;
  margin: 0 0 10px;
}

.mkdf-instagram-feed.mkdf-instagram-gallery.mkdf-small-space {
  margin: 0 -10px -20px;
}

.mkdf-instagram-feed.mkdf-instagram-gallery.mkdf-small-space li {
  padding: 0 10px;
  margin: 0 0 20px;
}

.mkdf-instagram-feed.mkdf-instagram-gallery.mkdf-normal-space {
  margin: 0 -15px -30px;
}

.mkdf-instagram-feed.mkdf-instagram-gallery.mkdf-normal-space li {
  padding: 0 15px;
  margin: 0 0 30px;
}

.mkdf-instagram-feed.mkdf-instagram-gallery.mkdf-col-2 li {
  width: 50%;
}

.mkdf-instagram-feed.mkdf-instagram-gallery.mkdf-col-3 li {
  width: 33.33333%;
}

.mkdf-instagram-feed.mkdf-instagram-gallery.mkdf-col-4 li {
  width: 25%;
}

.mkdf-instagram-feed.mkdf-instagram-gallery.mkdf-col-6 li {
  width: 16.66667%;
}

.mkdf-instagram-feed.mkdf-instagram-gallery.mkdf-col-9 li {
  width: 11.11111%;
}

.mkdf-instagram-feed.mkdf-instagram-carousel li {
  position: relative;
  width: 100%;
  margin: 0;
  padding: 0;
}

.mkdf-instagram-feed.mkdf-instagram-carousel li a {
  position: relative;
  display: block;
  height: 100%;
}

/* ==========================================================================
   Instagram widget style - end
   ========================================================================== */

/*# sourceMappingURL=../../../../plugins/boostup-instagram-feed/assets/css/widget-map.css.map */
