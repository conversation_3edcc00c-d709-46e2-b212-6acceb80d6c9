/* ==========================================================================
   Icon List Item shortcode style - begin
   ========================================================================== */

.mkdf-icon-list-holder {
    position: relative;
    display: table;
    table-layout: fixed;
    height: auto;
    width: 100%;
    margin-bottom: 8px;
	
	.mkdf-il-icon-holder,
	.mkdf-il-text {
		position: relative;
		display: table-cell;
		vertical-align: top;
	}

    .mkdf-il-icon-holder {
	    width: 1%;
	    
	    > * {
		    position: relative;
		    display: inline-block;
		    vertical-align: top;
		    color: $default-heading-color;
		    font-size: 17px;
		    line-height: inherit;
		
		    &:before {
			    display: block;
			    line-height: inherit;
		    }
	    }
    }

    .mkdf-il-text {
	    width: 99%;
	    padding: 0 0 0 13px;
	    box-sizing: border-box;
    }
}
/* ==========================================================================
   Icon List Item shortcode style - end
   ========================================================================== */