@include laptop-landscape-medium {

    .mkdf-instagram-list-holder {
        $columns_number: ('four', 'five');

        @for $i from 0 to length($columns_number) {
            &.mkdf-il-#{nth($columns_number,$i+1)}-columns {
                $column_width: 100% / 3;

                .mkdf-il-item {
                    width: $column_width;
                }

                @media only screen and (min-width: $ipad-landscape-plus-pixel) {

                    .mkdf-il-item {

                        &:nth-child(3n+1) {
                            clear: both;
                        }
                    }
                }
            }
        }
    }
}

@include ipad-landscape {

    .mkdf-instagram-list-holder {
        $columns_number: ('three', 'four', 'five');

        @for $i from 0 to length($columns_number) {
            &.mkdf-il-#{nth($columns_number,$i+1)}-columns {

                .mkdf-il-item {
                    width: 50%;
                }

                @media only screen and (min-width: $phone-landscape-plus-pixel) {

                    .mkdf-il-item {

                        &:nth-child(2n+1) {
                            clear: both;
                        }
                    }
                }
            }
        }
    }
}

@include phone-landscape {

    .mkdf-instagram-list-holder {
        
        .mkdf-il-item {
            width: 100% !important; // !important is set to override all other stronger selectors
        }
    }
}