<?php

if ( ! function_exists( 'boostup_core_add_dropcaps_shortcodes' ) ) {
	function boostup_core_add_dropcaps_shortcodes( $shortcodes_class_name ) {
		$shortcodes = array(
			'BoostUpCore\CPT\Shortcodes\Dropcaps\Dropcaps'
		);
		
		$shortcodes_class_name = array_merge( $shortcodes_class_name, $shortcodes );
		
		return $shortcodes_class_name;
	}
	
	add_filter( 'boostup_core_filter_add_vc_shortcode', 'boostup_core_add_dropcaps_shortcodes' );
}