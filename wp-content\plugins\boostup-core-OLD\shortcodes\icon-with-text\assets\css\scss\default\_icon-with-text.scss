/* ==========================================================================
   Icon With Text shortcode style - begin
   ========================================================================== */

.mkdf-iwt {
	@include mkdfRelativeHolderLayout();
	@include mkdfTransition(all .2s ease-out);

	&:hover {
		cursor: pointer;
	}
		
	.mkdf-iwt-icon {

		@include mkdfTransition(all 0.3s ease-in-out);
		
		
		a {
			position: relative;
			display: inline-block;
			vertical-align: middle;
		}
		
		.mkdf-icon-shortcode {
			line-height: 1;
			
			&.mkdf-circle,
			&.mkdf-square {
				line-height: 2;
			}
		}


	}
	
	.mkdf-iwt-title {
		margin: 0;
		line-height: 1.2em;
		
		a {
			position: relative;
			display: inline-block;
			vertical-align: top;
		}
	}
	
	.mkdf-iwt-title-text {
		display: block;
	}
	
	.mkdf-iwt-text {
		margin: 6px 0;
		font-weight: 500;

		a {

			&:hover {
				color: $first-main-color-medium-blue !important;
			}
		}
	}
	&:hover {
		.mkdf-iwt-icon {
			-webkit-transform: translate(0, -3px);
	        @include mkdfTransform(translate(0, -3px));
		}
	}
	
	&.mkdf-iwt-icon-left {
		width: auto;
		
		.mkdf-iwt-icon,
		.mkdf-iwt-content {
			display: table-cell;
			vertical-align: top;
		}
		
		.mkdf-iwt-icon {
			position: relative;
			top: 1px;
			padding: 5px 0 0 8px;
			
			img {
				max-width: none;
			}
		}
		
		.mkdf-iwt-content {
			padding: 0 0 0 22px;
			vertical-align: middle;
		}

	}
	
	&.mkdf-iwt-icon-left-from-title {
		
		.mkdf-iwt-icon,
		.mkdf-iwt-title-text {
			position: relative;
			display: table-cell;
			vertical-align: middle;
		}

		.mkdf-iwt-icon {
			
			.mkdf-icon-element {
				@include mkdfTransition(none);
			}
			
			img {
				max-width: none;
			}
		}
		
		.mkdf-iwt-title-text {
			padding: 0 0 0 17px;
		}
	}
	
	&.mkdf-iwt-icon-top {
		text-align: center;
		
		.mkdf-iwt-content {
			padding: 23px 0 0;
		}
	}

	&.boxed-shadow {
		width:100%;
		padding:30px;
		border-radius: 8px;
		box-sizing:border-box;
		background: #fff;
		@include mkdfTransition(all .2s ease-out);

	

		.mkdf-iwt-text {
			font-size: 11px;
			line-height: 1em;
			letter-spacing: 0.075em;
		}
	}

	&.boxed-hover-shadow {
		width:100%;
		padding:30px;
		border-radius: 8px;
		box-sizing:border-box;
		background: #fff;

		@include laptop-landscape-mac {
			&.mkdf-iwt-icon-medium {
				padding: 30px 30px 30px 20px !important;
			}
		}

		.mkdf-iwt-text {
			font-size: 11px;
			line-height: 1em;
			letter-spacing: 0.075em;
		}
	}
}
/* ==========================================================================
   Icon With Text shortcode style - end
   ========================================================================== */