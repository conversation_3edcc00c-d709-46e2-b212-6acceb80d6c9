<?php

if ( ! function_exists( 'boostup_mikado_portfolio_category_additional_fields' ) ) {
	function boostup_mikado_portfolio_category_additional_fields() {
		
		$fields = boostup_mikado_add_taxonomy_fields(
			array(
				'scope' => 'portfolio-category',
				'name'  => 'portfolio_category_options'
			)
		);
		
		boostup_mikado_add_taxonomy_field(
			array(
				'name'   => 'mkdf_portfolio_category_image_meta',
				'type'   => 'image',
				'label'  => esc_html__( 'Category Image', 'boostup-core' ),
				'parent' => $fields
			)
		);
	}
	
	add_action( 'boostup_mikado_action_custom_taxonomy_fields', 'boostup_mikado_portfolio_category_additional_fields' );
}