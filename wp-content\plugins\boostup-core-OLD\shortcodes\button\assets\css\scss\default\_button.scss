/* ==========================================================================
   Button shortcode style - begin
   ========================================================================== */

.mkdf-btn {
    @include mkdfButtonDefaultStyle();
    @include mkdfButtonSize();
    cursor: pointer;
    
    &.mkdf-btn-simple {
        padding: 0 !important;
        color: $default-text-color;
        background-color: transparent;
        border: 0;
        vertical-align: middle;

        .mkdf-btn-text {
            display: inline-block;
            vertical-align: middle;
            line-height: 50px;

        }


        &.mkdf-btn-icon {
            > i {
                padding: 11px 9px;
            }
            
    
            .mkdf-btn-text {
                position: relative;
                
            
               
                  
                  &::after {
                    content: '';
                    position: relative;
                    display: block;
                    top: -18px;
                    width: 100%;
                    height: 1px;
                    background-color: currentColor;
                    @include mkdfTransform(scaleX(1));
                    @include mkdfTransformOrigin(0 50%);
                    -webkit-transition: -webkit-transform .4s ease-out;
                    transition: transform .4s ease-out;
                    
                  }

                
            }

            
        }

        &:hover {
                .mkdf-btn-text {
                    &::after {
                        @include mkdfTransformOrigin(100% 50%);
                        @include mkdfTransform(scaleX(0));
                        @include mkdfAnimation(animate-btn-line .5s .2s forwards);
                    }
                    
                    
                }
            }



    }

    &.mkdf-btn-solid {
        @include mkdfButtonSolidColor();
        @include mkdfTransition(all .2s ease-out);
       

        &:not(.mkdf-btn-custom-hover-color):hover {
            //important because of inline color attribute. :not is used so we don't have to use important in JS
            color: #fff !important;
            

        }

        &:not(.mkdf-btn-custom-hover-bg):hover {
            //important because of inline color attribute. :not is used so we don't have to use important in JS
            background-color: #ff4661 !important;
          
        }

        &:not(.mkdf-btn-custom-border-hover):hover {
            //important because of inline color attribute. :not is used so we don't have to use important in JS
            border-color: #ff4661 !important;

        }

        &:hover {
            box-shadow: none !important;
        }
    }

    &.mkdf-btn-outline {
	    @include mkdfButtonOutlineColor();

        &:not(.mkdf-btn-custom-hover-color):hover {
            //important because of inline color attribute. :not is used so we don't have to use important in JS
            color: #fff !important;
        }

        &:not(.mkdf-btn-custom-hover-bg):hover {
            //important because of inline color attribute. :not is used so we don't have to use important in JS
            background-color: $first-main-color !important;
        }

        &:not(.mkdf-btn-custom-border-hover):hover {
            //important because of inline color attribute. :not is used so we don't have to use important in JS
            border-color: $first-main-color !important;
        }
    }
    
    &.mkdf-btn-small {
        @include mkdfButtonSize(small);
    }
    
    &.mkdf-btn-large {
        @include mkdfButtonSize(large);
    }
    
    &.mkdf-btn-huge {
        @include mkdfButtonSize(huge);
    }

    &.mkdf-btn-icon {

        > i,
        > span:not(.mkdf-btn-text) {
            position: relative;
            display: inline-block;
            vertical-align: middle;
            margin: 0 10px 0 0;
            font-size: 33px;
            line-height: inherit;

            &:before {
                display: block;
                line-height: inherit;
            }
        }
    }
}
/* ==========================================================================
   Button shortcode style - end
   ========================================================================== */