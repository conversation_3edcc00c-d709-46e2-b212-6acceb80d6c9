<?php

if ( ! function_exists( 'boostup_core_reviews_map' ) ) {
	function boostup_core_reviews_map() {
		
		$reviews_panel = boostup_mikado_add_admin_panel(
			array(
				'title' => esc_html__( 'Reviews', 'boostup-core' ),
				'name'  => 'panel_reviews',
				'page'  => '_page_page'
			)
		);
		
		boostup_mikado_add_admin_field(
			array(
				'parent'      => $reviews_panel,
				'type'        => 'text',
				'name'        => 'reviews_section_title',
				'label'       => esc_html__( 'Reviews Section Title', 'boostup-core' ),
				'description' => esc_html__( 'Enter title that you want to show before average rating on your page', 'boostup-core' ),
			)
		);
		
		boostup_mikado_add_admin_field(
			array(
				'parent'      => $reviews_panel,
				'type'        => 'textarea',
				'name'        => 'reviews_section_subtitle',
				'label'       => esc_html__( 'Reviews Section Subtitle', 'boostup-core' ),
				'description' => esc_html__( 'Enter subtitle that you want to show before average rating on your page', 'boostup-core' ),
			)
		);
	}
	
	add_action( 'boostup_mikado_action_additional_page_options_map', 'boostup_core_reviews_map', 75 ); //one after elements
}