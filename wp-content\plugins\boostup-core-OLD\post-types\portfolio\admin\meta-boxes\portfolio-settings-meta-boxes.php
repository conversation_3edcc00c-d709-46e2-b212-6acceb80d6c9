<?php

if ( ! function_exists( 'boostup_core_map_portfolio_settings_meta' ) ) {
	function boostup_core_map_portfolio_settings_meta() {
		$meta_box = boostup_mikado_create_meta_box( array(
			'scope' => 'portfolio-item',
			'title' => esc_html__( 'Portfolio Settings', 'boostup-core' ),
			'name'  => 'portfolio_settings_meta_box'
		) );
		
		boostup_mikado_create_meta_box_field( array(
			'name'        => 'mkdf_portfolio_single_template_meta',
			'type'        => 'select',
			'label'       => esc_html__( 'Portfolio Type', 'boostup-core' ),
			'description' => esc_html__( 'Choose a default type for Single Project pages', 'boostup-core' ),
			'parent'      => $meta_box,
			'options'     => array(
				''                  => esc_html__( 'Default', 'boostup-core' ),
				'huge-images'       => esc_html__( 'Portfolio Full Width Images', 'boostup-core' ),
				'images'            => esc_html__( 'Portfolio Images', 'boostup-core' ),
				'small-images'      => esc_html__( 'Portfolio Small Images', 'boostup-core' ),
				'slider'            => esc_html__( 'Portfolio Slider', 'boostup-core' ),
				'small-slider'      => esc_html__( 'Portfolio Small Slider', 'boostup-core' ),
				'gallery'           => esc_html__( 'Portfolio Gallery', 'boostup-core' ),
				'small-gallery'     => esc_html__( 'Portfolio Small Gallery', 'boostup-core' ),
				'masonry'           => esc_html__( 'Portfolio Masonry', 'boostup-core' ),
				'small-masonry'     => esc_html__( 'Portfolio Small Masonry', 'boostup-core' ),
				'custom'            => esc_html__( 'Portfolio Custom', 'boostup-core' ),
				'full-width-custom' => esc_html__( 'Portfolio Full Width Custom', 'boostup-core' )
			)
		) );
		
		/***************** Gallery Layout *****************/
		
		$gallery_type_meta_container = boostup_mikado_add_admin_container(
			array(
				'parent'          => $meta_box,
				'name'            => 'mkdf_gallery_type_meta_container',
				'dependency' => array(
					'show' => array(
						'mkdf_portfolio_single_template_meta'  => array(
							'gallery',
							'small-gallery'
						)
					)
				)
			)
		);
		
		boostup_mikado_create_meta_box_field(
			array(
				'name'          => 'mkdf_portfolio_single_gallery_columns_number_meta',
				'type'          => 'select',
				'label'         => esc_html__( 'Number of Columns', 'boostup-core' ),
				'default_value' => '',
				'description'   => esc_html__( 'Set number of columns for portfolio gallery type', 'boostup-core' ),
				'parent'        => $gallery_type_meta_container,
				'options'       => boostup_mikado_get_number_of_columns_array( true, array( 'one', 'five', 'six' ) )
			)
		);
		
		boostup_mikado_create_meta_box_field(
			array(
				'name'          => 'mkdf_portfolio_single_gallery_space_between_items_meta',
				'type'          => 'select',
				'label'         => esc_html__( 'Space Between Items', 'boostup-core' ),
				'description'   => esc_html__( 'Set space size between columns for portfolio gallery type', 'boostup-core' ),
				'default_value' => '',
				'options'       => boostup_mikado_get_space_between_items_array( true ),
				'parent'        => $gallery_type_meta_container
			)
		);
		
		/***************** Gallery Layout *****************/
		
		/***************** Masonry Layout *****************/
		
		$masonry_type_meta_container = boostup_mikado_add_admin_container(
			array(
				'parent'          => $meta_box,
				'name'            => 'mkdf_masonry_type_meta_container',
				'dependency' => array(
					'show' => array(
						'mkdf_portfolio_single_template_meta'  => array(
							'masonry',
							'small-masonry'
						)
					)
				)
			)
		);
		
		boostup_mikado_create_meta_box_field(
			array(
				'name'          => 'mkdf_portfolio_single_masonry_columns_number_meta',
				'type'          => 'select',
				'label'         => esc_html__( 'Number of Columns', 'boostup-core' ),
				'default_value' => '',
				'description'   => esc_html__( 'Set number of columns for portfolio masonry type', 'boostup-core' ),
				'parent'        => $masonry_type_meta_container,
				'options'       => boostup_mikado_get_number_of_columns_array( true, array( 'one', 'five', 'six' ) )
			)
		);
		
		boostup_mikado_create_meta_box_field(
			array(
				'name'          => 'mkdf_portfolio_single_masonry_space_between_items_meta',
				'type'          => 'select',
				'label'         => esc_html__( 'Space Between Items', 'boostup-core' ),
				'description'   => esc_html__( 'Set space size between columns for portfolio masonry type', 'boostup-core' ),
				'default_value' => '',
				'options'       => boostup_mikado_get_space_between_items_array( true ),
				'parent'        => $masonry_type_meta_container
			)
		);
		
		/***************** Masonry Layout *****************/
		
		boostup_mikado_create_meta_box_field(
			array(
				'name'          => 'mkdf_show_title_area_portfolio_single_meta',
				'type'          => 'select',
				'default_value' => '',
				'label'         => esc_html__( 'Show Title Area', 'boostup-core' ),
				'description'   => esc_html__( 'Enabling this option will show title area on your single portfolio page', 'boostup-core' ),
				'parent'        => $meta_box,
				'options'       => boostup_mikado_get_yes_no_select_array()
			)
		);
		
		boostup_mikado_create_meta_box_field(
			array(
				'name'        => 'portfolio_info_top_padding',
				'type'        => 'text',
				'label'       => esc_html__( 'Portfolio Info Top Padding', 'boostup-core' ),
				'description' => esc_html__( 'Set top padding for portfolio info elements holder. This option works only for Portfolio Images, Slider, Gallery and Masonry portfolio types', 'boostup-core' ),
				'parent'      => $meta_box,
				'args'        => array(
					'col_width' => 3,
					'suffix'    => 'px'
				)
			)
		);
		
		boostup_mikado_create_meta_box_field(
			array(
				'name'        => 'portfolio_external_link',
				'type'        => 'text',
				'label'       => esc_html__( 'Portfolio External Link', 'boostup-core' ),
				'description' => esc_html__( 'Enter URL to link from Portfolio List page', 'boostup-core' ),
				'parent'      => $meta_box,
				'args'        => array(
					'col_width' => 3
				)
			)
		);
		
		boostup_mikado_create_meta_box_field(
			array(
				'name'        => 'mkdf_portfolio_featured_image_meta',
				'type'        => 'image',
				'label'       => esc_html__( 'Featured Image', 'boostup-core' ),
				'description' => esc_html__( 'Choose an image for Portfolio Lists shortcode where Hover Type option is Switch Featured Images', 'boostup-core' ),
				'parent'      => $meta_box
			)
		);

        boostup_mikado_create_meta_box_field(
            array(
                'name'        => 'mkdf_portfolio_featured_color_meta',
                'type'        => 'color',
                'label'       => esc_html__( 'Featured Color', 'boostup-core' ),
                'description' => esc_html__( 'Choose an color for Portfolio Lists shortcode where Hover Type option is Overlay', 'boostup-core' ),
                'parent'      => $meta_box
            )
        );
		
		boostup_mikado_create_meta_box_field(
			array(
				'name'          => 'mkdf_portfolio_masonry_fixed_dimensions_meta',
				'type'          => 'select',
				'label'         => esc_html__( 'Dimensions for Masonry - Image Fixed Proportion', 'boostup-core' ),
				'description'   => esc_html__( 'Choose image layout when it appears in Masonry type portfolio lists where image proportion is fixed', 'boostup-core' ),
				'default_value' => '',
				'parent'        => $meta_box,
				'options'       => array(
					''                   => esc_html__( 'Default', 'boostup-core' ),
					'small'              => esc_html__( 'Small', 'boostup-core' ),
					'large-width'        => esc_html__( 'Large Width', 'boostup-core' ),
					'large-height'       => esc_html__( 'Large Height', 'boostup-core' ),
					'large-width-height' => esc_html__( 'Large Width/Height', 'boostup-core' )
				)
			)
		);
		
		boostup_mikado_create_meta_box_field(
			array(
				'name'          => 'mkdf_portfolio_masonry_original_dimensions_meta',
				'type'          => 'select',
				'label'         => esc_html__( 'Dimensions for Masonry - Image Original Proportion', 'boostup-core' ),
				'description'   => esc_html__( 'Choose image layout when it appears in Masonry type portfolio lists where image proportion is original', 'boostup-core' ),
				'default_value' => '',
				'parent'        => $meta_box,
				'options'       => array(
					''            => esc_html__( 'Default', 'boostup-core' ),
					'large-width' => esc_html__( 'Large Width', 'boostup-core' )
				)
			)
		);
		
		$all_pages = array();
		$pages     = get_pages();
		foreach ( $pages as $page ) {
			$all_pages[ $page->ID ] = $page->post_title;
		}
		
		boostup_mikado_create_meta_box_field(
			array(
				'name'        => 'portfolio_single_back_to_link',
				'type'        => 'select',
				'label'       => esc_html__( '"Back To" Link', 'boostup-core' ),
				'description' => esc_html__( 'Choose "Back To" page to link from portfolio Single Project page', 'boostup-core' ),
				'parent'      => $meta_box,
				'options'     => $all_pages,
				'args'        => array(
					'select2' => true
				)
			)
		);
	}
	
	add_action( 'boostup_mikado_action_meta_boxes_map', 'boostup_core_map_portfolio_settings_meta', 41 );
}