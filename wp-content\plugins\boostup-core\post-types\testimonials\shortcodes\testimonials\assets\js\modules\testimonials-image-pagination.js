(function($) {
	'use strict';

	var testimonialsImagePagination = {};
	mkdf.modules.testimonialsImagePagination = testimonialsImagePagination;

	testimonialsImagePagination.mkdfOnDocumentReady = mkdfOnDocumentReady;

	$(document).ready(mkdfOnDocumentReady);

	/*
	 All functions to be called on $(document).ready() should be in this function
	 */
	function mkdfOnDocumentReady() {
		mkdfTestimonialsImagePagination();
	}

	/**
	 * Init Owl Carousel
	 */
	function mkdfTestimonialsImagePagination() {
		var sliders = $('#mkdf-testimonial-pagination');

		if (sliders.length) {
			sliders.each(function() {
				var slider = $(this),
					slideItemsNumber = slider.children().length,
					contentItems = slider.siblings('.mkdf-testimonials').find('.mkdf-testimonial-content'),
					loop = true,
					autoplay = false,
					autoplayHoverPause = false,
					sliderSpeed = 3500,
					sliderSpeedAnimation = 500,
					margin = 0,
					stagePadding = 0,
					center = false,
					autoWidth = false,
					animateInClass = false, // keyframe css animation
					animateOutClass = false, // keyframe css animation
					navigation = true,
					pagination = false,
					drag = false,
					nav = true,
					sliderDataHolder = slider;

				if (sliderDataHolder.data('enable-loop') === 'no') {
					loop = false;
				}
				if (typeof sliderDataHolder.data('slider-speed') !== 'undefined' && sliderDataHolder.data('slider-speed') !== false) {
					sliderSpeed = sliderDataHolder.data('slider-speed');
				}
				if (typeof sliderDataHolder.data('slider-speed-animation') !== 'undefined' && sliderDataHolder.data('slider-speed-animation') !== false) {
					sliderSpeedAnimation = sliderDataHolder.data('slider-speed-animation');
				}
				if (sliderDataHolder.data('enable-auto-width') === 'yes') {
					autoWidth = true;
				}
				if (typeof sliderDataHolder.data('slider-animate-in') !== 'undefined' && sliderDataHolder.data('slider-animate-in') !== false) {
					animateInClass = sliderDataHolder.data('slider-animate-in');
				}
				if (typeof sliderDataHolder.data('slider-animate-out') !== 'undefined' && sliderDataHolder.data('slider-animate-out') !== false) {
					animateOutClass = sliderDataHolder.data('slider-animate-out');
				}
				if (sliderDataHolder.data('enable-navigation') === 'no') {
					navigation = false;
				}
				if (sliderDataHolder.data('enable-pagination') === 'yes') {
					pagination = true;
				}
				if (sliderDataHolder.data('enable-autoplay') === 'yes') {
					autoplay = true;
				}

				if (navigation && pagination) {
					slider.addClass('mkdf-slider-has-both-nav');
				}

				if($('.mkdf-testimonial-content:nth-child(2)')){
					$('.mkdf-testimonial-content:nth-child(2)').addClass('active');
				}

				if (slideItemsNumber <= 1) {
					loop = false;
					autoplay = false;
					navigation = false;
					pagination = false;
				}

				slider.waitForImages(function () {
					$(this).owlCarousel({
						items: 3,
						loop: loop,
						autoplay: autoplay,
						autoplayHoverPause: autoplayHoverPause,
						autoplayTimeout: sliderSpeed,
						smartSpeed: sliderSpeedAnimation,
						margin: margin,
						stagePadding: stagePadding,
						center: center,
						autoWidth: autoWidth,
						animateIn: animateInClass,
						animateOut: animateOutClass,
						dots: pagination,
						// dotsContainer: dotsContainer,
						nav: navigation,
						drag: drag,
						callbacks: true,
						navText: [
							'<span class="mkdf-prev-icon icon icon-arrows-left"></span>',
							'<span class="mkdf-next-icon icon icon-arrows-right"></span>'
						],
						onInitialize: function () {
							slider.css('visibility', 'visible');
						},
						onInitialized: function () {
							$('.owl-item').filter('.active').eq(1).find('img').css("transform", "scale(1)");
							$('.owl-item').find('.mkdf-testimonial-author').css("opacity", "0");
							$('.owl-item').filter('.active').eq(1).find('.mkdf-testimonial-author').css("opacity", "1");
						},
						onDrag: function (e) {
							if (mkdf.body.hasClass('mkdf-smooth-page-transitions-fadeout')) {
								var sliderIsMoving = e.isTrigger > 0;

								if (sliderIsMoving) {
									slider.addClass('mkdf-slider-is-moving');
								}
							}
						},
						onDragged: function () {
							if (mkdf.body.hasClass('mkdf-smooth-page-transitions-fadeout') && slider.hasClass('mkdf-slider-is-moving')) {

								setTimeout(function () {
									slider.removeClass('mkdf-slider-is-moving');
								}, 500);
							}
						},
						onTranslated: function(property) {
							var activeItem = slider.find('.owl-item.active').eq(1).find('.mkdf-tsp-item').data('index');

							contentItems.removeClass('active');
							contentItems.eq(activeItem).addClass('active');

							$('.owl-item').filter('.active').eq(1).find('img').css("transform", "scale(1)");
							$('.owl-item').filter('.active').eq(1).find('.mkdf-testimonial-author').css("opacity", "1");
						},
						onTranslate: function() {
							$('.owl-item').find('img').css("transform", "scale(0.7)");
							$('.owl-item').find('.mkdf-testimonial-author').css("opacity", "0");
						},
					});

				});
			});
		}
	}

})(jQuery);