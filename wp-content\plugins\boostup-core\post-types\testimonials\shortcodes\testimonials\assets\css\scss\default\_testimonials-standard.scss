/* ==========================================================================
   Testimonials standard style - begin
   ========================================================================== */

.mkdf-testimonials-holder {
    @include mkdfRelativeHolderLayout();

    .mkdf-testimonials,
    .mkdf-testimonial-content,
    .mkdf-testimonial-text-holder {
        @include mkdfRelativeHolderLayout();
    }

    .mkdf-testimonials {
        box-sizing: border-box;

        .mkdf-testimonials-icon{
            position: absolute;
            display: inline-block;
            left: 50%;
            top: -80px;

            @include mkdfTransform(translateX(-50%));
        }
    }

    .mkdf-testimonial-image {

        img {
            width: auto !important;
            border-radius: 5em;
        }
    }
    
    &.mkdf-testimonials-standard {
        text-align: center;

        .mkdf-testimonial-content{
            max-width: 875px;
        }

        .mkdf-testimonial-image {
            @include mkdfRelativeHolderLayout();
            margin: 25px 0 0;

            img {
                display: block;
                margin: 0 auto;
                border: 4px solid #fff;
                border-radius: 50%;
                box-sizing: border-box;
            }
        }

        .mkdf-testimonial-title{
            font-size: 55px;
            font-weight: 600;
            margin: 0 0 39px;
        }

        .mkdf-testimonial-text {
            margin: 0 0 20px;
            font-size: 18px;
            color: #4e4e4e;
            line-height: 34px;
        }

        .mkdf-testimonial-author {
            margin-top: 43px;

            > .mkdf-testimonials-author-name{
                font-size: 26px;
                font-weight: 600;
                color: $first-main-color;
                margin: 25px 0 2px;
            }

            > .mkdf-testimonials-author-job{
                font-size: 14px;
                font-weight: 700;
                color: $first-main-color-dark-blue;
                margin: 25px 0 2px;
                letter-spacing: .1em;
                text-transform: uppercase;
                font-family: $default-text-font;
            }


        }

        /* Light/Dark styles */
        &.mkdf-testimonials-light {
            
            .mkdf-testimonial-title,
            .mkdf-testimonial-text,
            .mkdf-testimonial-author {
                color: #fff;
            }

            .mkdf-testimonial-author {

                > .mkdf-testimonials-author-name {
                    color: #fff;
                }
                > .mkdf-testimonials-author-job{
                    color: rgba(#fff,0.6);
                }
            }

            .owl-dots {
                
                .owl-dot {
                    
                    span {
                        border: 2px solid rgba(#fff, .5);
                    }
                    
                    &:hover,
                    &.active {

                        span {
                            background-color: #fff;
                            border-color: #fff;
                        }
                    }
                }
            }
            .mkdf-owl-slider {

                .owl-nav {

                    .owl-next,.owl-prev {

                        > span {

                            color: #fff;
                        }
                    }
                }
           
            }
        }

        @media (max-width: 1024px) {

            .mkdf-owl-slider {

                .owl-nav {

                    .owl-next,.owl-prev {

                        > span {

                            display: none;
                        }
                    }
                }
           
            }
        }
    }
}
/* ==========================================================================
   Testimonials standard style - end
   ========================================================================== */