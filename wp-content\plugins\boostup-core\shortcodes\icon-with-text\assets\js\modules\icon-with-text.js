(function($) {
    'use strict';
	
	var iwtshadowonhover = {};
	mkdf.modules.iwtshadowonhover = iwtshadowonhover;

    iwtshadowonhover.mkdfIwtShadowOnHover = mkdfIwtShadowOnHover;

    iwtshadowonhover.mkdfOnDocumentReady = mkdfOnDocumentReady;
	
	$(document).ready(mkdfOnDocumentReady);
	
	/*
	 All functions to be called on $(document).ready() should be in this function
	 */
	function mkdfOnDocumentReady() {
		mkdfIwtShadowOnHover();
	}
	
	/**
	 * Init accordions shortcode
	 */
	function mkdfIwtShadowOnHover(){

		var hoverIconElement;
        var iconElement;

        hoverIconElement = $('.boxed-hover-shadow');
        iconElement = $('.boxed-shadow');
		
		hoverIconElement.on('mouseenter', function() {
			var thisIcon = $(this);
			thisIcon.css("box-shadow","0px 10px 30px 0px");
			thisIcon.css("border","1px solid transparent");
		});
		
		hoverIconElement.on('mouseleave', function() {
			var thisIcon = $(this);
			thisIcon.css("box-shadow","0 0 0 0");
			thisIcon.css("border","1px solid");
		});
		
		iconElement.on('mouseenter', function() {
			var thisIcon = $(this);
			thisIcon.css("box-shadow", "0px 10px 30px 0px " + thisIcon.data("shadow-hover-color"));
		});
		iconElement.on('mouseleave', function() {
			var thisIcon = $(this);
			thisIcon.css("box-shadow", "0px 10px 30px 0px " + thisIcon.attr("data-shadow-color"));
		});
	}

})(jQuery);