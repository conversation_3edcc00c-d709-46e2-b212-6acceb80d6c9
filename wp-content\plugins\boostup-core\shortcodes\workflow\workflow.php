<?php
namespace BoostUpCore\CPT\Shortcodes\Workflow;

use BoostUpCore\Lib;

class Workflow implements Lib\ShortcodeInterface {
    private $base;

    function __construct() {
        $this->base = 'mkdf_workflow';
        add_action('vc_before_init', array($this, 'vcMap'));
    }

    public function getBase() {
        return $this->base;
    }

    public function vcMap() {
        if ( function_exists( 'vc_map' ) ) {
            vc_map(
                array(
                    'name'                      => esc_html__('Workflow', 'boostup-core'),
                    'base'                      => $this->base,
                    'as_parent'                 => array('only' => 'mkdf_workflow_item'),
                    'content_element'           => true,
                    'category'                  => esc_html__('by BOOSTUP', 'boostup-core'),
                    'icon'                      => 'icon-wpb-workflow extended-custom-icon',
                    'show_settings_on_create'   => true,
                    'js_view'                   => 'VcColumnView',
                    'params' => array(
                            array(
                                'type' => 'textfield',
                                'heading' => esc_html__('Extra class name', 'boostup-core'),
                                'param_name' => 'custom_clas',
                                'description' => esc_html__('Style particular content element differently - add a class name and refer to it in custom CSS.', 'boostup-core')
                            ),
                            
                            array(
                                'type' => 'dropdown',
                                'heading' => esc_html__('Animate Workflow', 'boostup-core'),
                                'param_name' => 'animate',
                                'value' => array(
                                    esc_html__('Yes', 'boostup-core') => 'yes',
                                    esc_html__('No', 'boostup-core') => 'no',
                                ),
                                'description' => esc_html__('Animate Workflow shortcode when it comes into viewport', 'boostup-core'),
                                'save_always' => true,
                            ),
                        array(
                            'type' => 'dropdown',
                            'heading' => esc_html__('Enable Light Skin', 'boostup-core'),
                            'param_name' => 'light_skin',
                            'value' => array(
                                esc_html__('No', 'boostup-core') => 'no',
                                esc_html__('Yes', 'boostup-core') => 'yes',
                            ),
                            'save_always' => true
                        )
                    )
                )
            );
        }
    }

    public function render($atts, $content = null) {
        $default_atts = (array(
            'custom_clas'     => '',
            'circle_color' => '',
            'animate'      => 'yes',
            'light_skin'      => 'no',
        ));

        $params       = shortcode_atts($default_atts, $atts);
       
        extract($params);

        $params['custom_clas'] = $this->getWorkflowClasses($params);
        $params['content']  = $content;
        $output             = '';

        $output .= boostup_core_get_shortcode_module_template_part('templates/workflow-holder-template', 'workflow', '', $params);

        return $output;
    }

    private function getWorkflowClasses($params) {

        $custom_clas = '';
        $class    = $params['custom_clas'];

        if($class !== '') {
            $custom_clas = $class;
        }

        if($params['animate'] == 'yes') {
            $custom_clas .= ' mkdf-workflow-animate';
        }

        if($params['light_skin'] == 'yes') {
            $custom_clas .= ' mkdf-workflow-light';
        }

        return $custom_clas;
    }

  

}
