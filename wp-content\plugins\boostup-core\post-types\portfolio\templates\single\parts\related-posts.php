<?php
// this option doesn't exist
$show_related_posts = boostup_mikado_options()->getOptionValue('portfolio_single_related_posts') == 'yes' ? true : false;

$post_id = get_the_ID();
$related_posts = boostup_core_get_portfolio_single_related_posts($post_id);
?>
<?php if($show_related_posts) { ?>
    <div class="mkdf-ps-related-posts-holder">
        <div class="mkdf-ps-related-title-holder">
            <h4 class="mkdf-ps-related-title"><?php esc_html_e('Related Projects', 'boostup-core'); ?></h4>
        </div>
        <div class="mkdf-ps-related-posts">
            <?php
	            if ( $related_posts && $related_posts->have_posts() ) :
	                while ( $related_posts->have_posts() ) : $related_posts->the_post(); ?>
                        <div class="mkdf-ps-related-post">
			                <?php if(has_post_thumbnail()) { ?>
		                        <div class="mkdf-ps-related-image">
			                        <a itemprop="url" href="<?php the_permalink(); ?>">
				                        <?php the_post_thumbnail('full'); ?>
			                        
				                         <div class="mkdf-pli-image-hover">
											<div class="mkdf-pli-image-hover-table">
												<i class="icon_plus"></i>
											</div>
										</div>
									</a>
	                            </div>
			                <?php } ?>

	                       
                        </div>
	                <?php
	                endwhile;
	            endif;
            
                wp_reset_postdata();
            ?>
        </div>
    </div>
<?php } ?>
