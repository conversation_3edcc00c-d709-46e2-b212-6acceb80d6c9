<div class="mkdf-price-table mkdf-item-space <?php echo esc_attr($holder_classes); ?>">
	<div class="mkdf-pt-inner" <?php echo boostup_mikado_get_inline_style($holder_styles); ?>>
		<ul>
			<li class="mkdf-pt-title-holder">
				<?php if($params['set_active_item'] === 'yes'){ ?>
					<span class="mkdf-pt-active-title" <?php echo boostup_mikado_get_inline_style($active_title_styles); ?>><?php echo esc_html($active_title); ?></span>
				<?php } ?>
				<span class="mkdf-pt-title" <?php echo boostup_mikado_get_inline_style($title_styles); ?>><?php echo esc_html($title); ?></span>
				<?php if(!empty($custom_icon)) : ?>
					<?php echo wp_get_attachment_image($custom_icon, 'full'); ?>
				<?php endif ?>
			</li>
			<li class="mkdf-pt-content">
				<?php echo do_shortcode($content); ?>
			</li>
			<li class="mkdf-pt-prices">
				<div class="mkdf-pt-value" <?php echo boostup_mikado_get_inline_style($currency_styles); ?>><?php echo esc_html($currency); ?></div>
				<span class="mkdf-pt-price" <?php echo boostup_mikado_get_inline_style($price_styles); ?>><?php echo esc_html($price); ?></span>
				<h6 class="mkdf-pt-mark" <?php echo boostup_mikado_get_inline_style($price_period_styles); ?>><?php echo esc_html($price_period); ?></h6>
			</li>
			<?php 
			if($set_active_item == 'yes' && !empty($button_text)) { ?>
				<li class="mkdf-pt-button">
					<?php echo boostup_mikado_get_button_html(array(
						'link' => $link,
						'text' => $button_text,
						'type' => 'solid',
						'popup' => 'no',
                        'size' => 'huge',
						'color' => $button_color,
						'hover_color' => $button_hover_color,
						'background_color'       => $button_background_color,
						'hover_background_color' => $button_hover_background_color,
					)); ?>
				</li>				
			<?php } else { ?>
                <li class="mkdf-pt-button">
                    <?php echo boostup_mikado_get_button_html(array(
                        'link' => $link,
                        'text' => $button_text,
                        'type' => 'outline',
                        'size' => 'huge',
                        'popup' => 'no',
                        'color' => $button_color,
                        'hover_color' => $button_hover_color,
                        'background_color'       => $button_background_color,
                        'hover_background_color' => $button_hover_background_color,
                    )); ?>
                </li>
            <?php } ?>
		</ul>
	</div>
</div>