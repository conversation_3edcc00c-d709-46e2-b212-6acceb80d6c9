
.mkdf-instagram-list-holder {
	@include mkdfRelativeHolderLayout();
	clear: both;
	
	&:not(.mkdf-il-one-column) {
		
		.mkdf-il-item {
			float: left;
		}
	}
	
	.mkdf-instagram-carousel {
		.mkdf-il-item {
			width: auto !important;
			padding: 0 !important;
			margin: 0 !important;
			float: none !important;
		}
	}
	
	.mkdf-il-item {
		@include mkdfRelativeHolderLayout();
		padding: 0;
		box-sizing: border-box;
	}
	
	$columns_number: ('two', 'three', 'four', 'five');
	
	@for $i from 0 to length($columns_number) {
		&.mkdf-il-#{nth($columns_number,$i+1)}-columns {
			
			.mkdf-il-item {
				width: 100% / ($i+2);
			}
			
			@if ($i > 1) { // set different break point for four and five columns
				@media only screen and (min-width: $laptop-landscape-medium-plus-pixel) {
					.mkdf-il-item {
						
						&:nth-child(#{$i+2}n+1) {
							clear: both;
						}
					}
				}
			} @else if ($i == 1) { // set different break point for three columns
				@media only screen and (min-width: $laptop-landscape-plus-pixel) {
					
					.mkdf-il-item {
						
						&:nth-child(#{$i+2}n+1) {
							clear: both;
						}
					}
				}
			} @else {
				@media only screen and (min-width: $ipad-landscape-plus-pixel) {
					
					.mkdf-il-item {
						
						&:nth-child(#{$i+2}n+1) {
							clear: both;
						}
					}
				}
			}
		}
	}
}
