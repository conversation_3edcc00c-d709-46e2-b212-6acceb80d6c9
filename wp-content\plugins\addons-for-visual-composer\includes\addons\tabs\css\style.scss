@import "../../../../assets/css/lvca-lib";
$theme_color: #f94213;

@keyframes lvca-fade {
  0% {
    opacity: 0;
    }
  100% {
    opacity: 1;
    }
  }
/* ---------- General tab styles ---------- */

.lvca-tabs {
  position: relative;
  overflow: hidden;
  .lvca-tab-nav {
     display: flex;
    flex-flow: row wrap;
    justify-content: flex-start;
    .lvca-tab {
      text-align: center;
      @include flex(0 1 auto);
      a {
        display: block;
        text-overflow: ellipsis;
        white-space: normal;
        padding: 20px 40px;
        text-decoration: none;
        border: none;
        margin: 0;
        outline: none;
        transition: color .3s ease-in-out 0s;
        @include respond-to-max(1024) {
          padding: 20px 25px;
          }
        }
      span.lvca-icon-wrapper span {
        font-size: 32px;
        font-weight: 400;
        vertical-align: middle;
        margin-right: 10px;
        }
      span.lvca-image-wrapper img {
        max-width: 24px;
        display: inline-block;
        vertical-align: middle;
        height: auto;
        width: auto;
        padding: 0;
        margin: 0 10px 0 0;
        border: none;
        }
      span.lvca-tab-title {
        font-size: 14px;
        line-height: 1;
        font-weight: 700;
        text-transform: uppercase;
        letter-spacing: 1px;
        }
      }
    }
  .lvca-tab-panes {
    position: relative;
    .lvca-tab-pane {
      padding: 40px;
      display: none;
      overflow: hidden;
      &.lvca-active {
        display: block;
        animation: lvca-fade 0.3s ease-in-out;
        }
      }
    }
  }
/* ----- Fallback for IE 8/9 ----- */
.lvca-no-flexbox {
  .lvca-tab-nav {
    display: block;
    .lvca-tab {
      min-width: 15%;
      display: inline-block;
      }
    }
  }

/* ------------- Vertical tab styles ----------------- */

.lvca-tabs.lvca-vertical {
  display: flex;
  .lvca-tab-nav {
    @include flex (1 1 auto);
    @include flex-direction(column);
    justify-content: flex-start;
    }
  .lvca-tab-panes {
    @include flex (4 1 auto);
    }
  &.lvca-mobile-layout {
    @include flex-direction(column);
    }
  }
/* --------- Tab navigation in mobile ------------- */

.lvca-tab-mobile-menu {
  display: none; /* Hide on desktop */
  position: absolute;
  top: 23px;
  right: 20px;
  background: transparent;
  border: none;
  z-index: 10;
  i {
    font-size: 18px;
    color: #777;
    font-weight: bold;
    }
  }
.lvca-tabs.lvca-mobile-layout {
  .lvca-tab-mobile-menu {
    display: block; /* Show on mobile only */
    }
  .lvca-tab-nav {
    @include flex-direction(column);
    cursor: pointer;
    .lvca-tab {
      text-align: center;
      display: none;
      &.lvca-active {
        display: block;
        }
      }
    }
  &.lvca-mobile-open {
    /* Open all tab navs and change the expand menu button to close button */
    .lvca-tab-nav {
      .lvca-tab {
        display: block;
        }
    }
    .lvca-tab-mobile-menu {
      i:before {
        content: '\e911';
        }
      }
    }
  }
/* ------------- Style 1 ----------------- */

.lvca-tabs.lvca-style1 {

  .lvca-tab-nav {
    .lvca-tab {
      border-left: 1px solid #d9d9d9;
      border-bottom: 1px solid #e2e2e2;
      background: #e9e9e9;
      &:first-child {
        border-left-color: transparent;
        border-radius: 5px 0 0 0;
        }
      &:last-child {
        border-radius: 0 5px 0 0;
        }
      &.lvca-active {
        border-bottom: none;
        background: #f2f2f2;
        }
      a {
        color: #777;
        &:hover, &:focus {
          color: #333;
          }
        }
      &.lvca-active a {
        color: #333;
        }
      }
    }
  .lvca-tab-panes {
    background: #f2f2f2;
    border-radius: 0 4px 4px 4px;
    }
  }


.lvca-tabs.lvca-style1.lvca-mobile-layout {
  &:not(.lvca-mobile-open) {
      .lvca-tab.lvca-active {
        background: #eeeeee;
      }
    }
  .lvca-tab {
    border-left: none;
    border-bottom-color: #d9d9d9;
    &:first-child {
      border-radius: 5px 5px 0 0;
      }
    &:last-child {
      border-radius: 0;
      }
  }
  .lvca-tab-panes {
    border-radius: 0;
  }
}

/* -------- Style 2 ----------- */

.lvca-tabs.lvca-style2 {
  .lvca-tab-nav {
    background: #f2f2f2;
    border-radius: 5px 5px 0 0;
    padding: 0 30px;
    .lvca-tab {
      padding: 20px 10px;
      position: relative;
      a {
        display: inline-block;
        padding: 5px 20px;
        border-radius: 34px;
        color: #666;
        transition: all .3s ease-in-out 0s;
        &:hover, &:focus {
          color: #888;
          }
        }
      &.lvca-active:after {
        content: '';
        display: block;
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        width: 8px;
        margin: 0 auto;
        border-left: 8px solid transparent;
        border-right: 8px solid transparent;
        border-bottom: 8px solid #3c3d41;
        }
      &.lvca-active a {
        background: #838d8f;
        color: #fff;
        }
      }
    }
  .lvca-tab-panes {
    background: #3c3d41;
    border-radius: 0 0 5px 5px;
    .lvca-tab-pane {
      color: #838d8f;
      h1, h2, h3, h4, h5, h6 {
        color: #fff;
        }
      }
    }
  }
.lvca-tabs.lvca-style2.lvca-mobile-layout {
  .lvca-tab-mobile-menu {
    top: 27px;
  }
  .lvca-tab-nav {
    padding: 0;
  }
  &.lvca-mobile-open {
    .lvca-tab {
      border-bottom: 1px solid #e2e2e2;
      &:last-child {
        border-bottom: none;
      }
    }
    .lvca-tab.lvca-active:after {
      display: none;
    }
  }
}
.lvca-dark-bg .lvca-tabs.lvca-style2 {
  .lvca-tab-nav {
    .lvca-tab {
      a {
        color: #333;
        &:hover, &:focus {
          color: #666;
          }
        }
      &.lvca-active a {
        background: #aaa;
        color: #fff;
        }
      &.lvca-active:after {
        border-bottom: 8px solid #e7e7e7;
        }
      }
    }
  .lvca-tab-panes {
    background: #e7e7e7;
    .lvca-tab-pane {
      color: #666;
      h1, h2, h3, h4, h5, h6 {
        color: #333333;
        }
      }
    }
  }
/* -------- Style 3 ----------- */

.lvca-tabs.lvca-style3 {
  .lvca-tab-nav {
    background: #3c3d41;
    border-radius: 5px 5px 0 0;
    .lvca-tab {
      position: relative;
      border-right: 1px solid #4e4f53;
      a {
        padding: 20px 30px;
        border-radius: 34px;
        color: #8f8e93;
        transition: all .3s ease-in-out 0s;
        &:hover, &:focus {
          color: #ccc;
          }
        }
      &.lvca-active:after {
        content: '';
        display: block;
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        width: 8px;
        margin: 0 auto;
        border-left: 8px solid transparent;
        border-right: 8px solid transparent;
        border-bottom: 8px solid #f2f2f2;
        }
      span.lvca-icon-wrapper span, span.lvca-image-wrapper img {
        margin: 0 auto;
        }
      span.lvca-tab-title {
        display: none;
        }
      &.lvca-active a {
        color: #eeeeee;
        }
      }
    }
  .lvca-tab-panes {
    background: #f2f2f2;
    border-radius: 0 0 5px 5px;
    }
  }
.lvca-tabs.lvca-style3.lvca-mobile-layout {
  .lvca-tab-nav {
     @include flex-direction(row);
     padding-right: 60px;
  }
  &.lvca-mobile-open {
    .lvca-tab {
      border-bottom: 1px solid #4e4f53;
      .lvca-dark-bg & {
        border-bottom-color: #e5e5e5;
      }
      &.lvca-active:after {
        display: none;
      }
    }
  }
}
.lvca-dark-bg .lvca-tabs.lvca-style3 {
  .lvca-tab-nav {
    background: #fff;
    .lvca-tab {
      border-right: 1px solid #ececec;
      a {
        color: #969696;
        &:hover, &:focus {
          color: #666;
          }
        }
      &.lvca-active a {
        color: #333;
        }
      &.lvca-active:after {
        border-bottom: 8px solid #e7e7e7;
        }
      }
    }
  .lvca-tab-panes {
    background: #e7e7e7;
    .lvca-tab-pane {
      color: #666;
      h1, h2, h3, h4, h5, h6 {
        color: #333;
        }
      }
    }
  }
/* ----------- Style 4 --------------- */

.lvca-tabs.lvca-style4 {
  background: #f2f2f2;
  border-radius: 5px;
  .lvca-tab-nav {
    border-bottom: 1px solid #dddddd;
    margin: 0 40px;
    .lvca-tab {
      position: relative;
      z-index: 1;
      margin-right: 20px;
      &:last-child {
        margin-right: 0;
        }
      a {
        color: #888;
        padding: 30px 20px;
        }
      &:before {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 2px;
        background: transparent;
        transition: background .3s ease-in-out 0s;
        }
      &:hover a {
        color: #565656;
        }
      &.lvca-active {
        a {
          color: #333;
          }
        &:before {
          background: $theme_color;
          height: 2px;
          }
        }
      }
    }
  .lvca-tab-pane {
    padding: 40px;
    }
  }


.lvca-tabs.lvca-style4.lvca-mobile-layout {
  .lvca-tab-nav {
    cursor: pointer;
    padding: 0;
    margin: 0;
    border: none;
    .lvca-tab {
      margin: 0;
      border-bottom: 1px solid #e0e0e0;
      .lvca-dark-bg & {
        border-left: 1px solid #404040;
        border-right: 1px solid #404040;
        border-bottom-color: #404040;
      }
      .lvca-dark-bg &:first-child {
        border-top: 1px solid #404040;
      }
      a {
        padding: 20px 25px;
      }
      &:before {
        display: none;
      }
    }
  }
  &.lvca-mobile-open {
    .lvca-tab.lvca-active {
       border-left: 2px solid $theme_color;
       border-right: 2px solid $theme_color;
    }
  }
  &:not(.lvca-mobile-open) {
    .lvca-tab.lvca-active {
       .lvca-dark-bg & {
         border-top: 1px solid #404040;
       }
    }
  }
}
.lvca-dark-bg .lvca-tabs.lvca-style4 {
  background: transparent;

  .lvca-tab-nav {
    margin: 0;
    border-bottom: 1px solid #2a2a2a;
    .lvca-tab {
      a {
        color: #707070;
        }
      }
    .lvca-tab:hover {
      a {
        color: #b0b0b0;
        }
      }
    .lvca-tab.lvca-active {
      a {
        color: #e5e5e5;
        }
      }
    }

  .lvca-tab-pane {
    padding: 40px 0 0; 
    color: #909090;
    h1, h2, h3, h4, h5, h6 {
      color: #e5e5e5;
      }
    }
  }
/* ----------- Style 5 --------------- */

.lvca-tabs.lvca-style5 {
  .lvca-tab-nav {
    a {
      padding: 20px 50px;
      color: #777;
      position: relative;
      z-index: 1;
      &:after {
        position: absolute;
        top: 0;
        left: 0;
        z-index: -1;
        width: 100%;
        height: 100%;
        background-color: #e9e9e9;
        content: '';
        -webkit-transition: -webkit-transform 0.3s, background-color 0.3s;
        transition: transform 0.3s, background-color 0.3s;
        -webkit-transform: perspective(900px) rotate3d(1, 0, 0, 90deg);
        transform: perspective(900px) rotate3d(1, 0, 0, 90deg);
        -webkit-transform-origin: 50% 100%;
        transform-origin: 50% 100%;
        -webkit-perspective-origin: 50% 100%;
        perspective-origin: 50% 100%;
        }
      &:hover, &:focus {
        color: #333;
        }
      }
    .lvca-tab.lvca-active a {
      color: #333;
      &:after {
        background-color: #f2f2f2;
        -webkit-transform: perspective(900px) rotate3d(1, 0, 0, 0deg);
        transform: perspective(900px) rotate3d(1, 0, 0, 0deg);
        }
      }
    }
  .lvca-tab-panes {
    background: #f2f2f2;
    }
  }

.lvca-tabs.lvca-style5.lvca-mobile-layout {
  .lvca-tab-nav {
    .lvca-tab {
      background: #f2f2f2;
      border-bottom: 1px solid #e5e5e5;
    }
  }
}

.lvca-dark-bg .lvca-tabs.lvca-style5 {
  .lvca-tab-nav {
    .lvca-tab a {
    color: #b0b0b0;
    &:hover, &:focus {
      color: #dddddd;
    }
    }
  .lvca-tab.lvca-active a {
    color: #333;
    }
  }
}

/* ------------- Style 6 and Vertical Style 7 ----------------- */

.lvca-tabs.lvca-style6, .lvca-tabs.lvca-style7 {
  .lvca-tab-nav {
    .lvca-tab {
      text-align: left;
      a {
        padding: 5px 2px;
        color: #666;
        transition: all .3s ease-in-out 0s;
        border-top: 2px solid transparent;
        border-bottom: 2px solid transparent;
        display: inline-block;
        &:hover, &:focus {
          color: #333333;
          }
        }
      &.lvca-active a {
        border-color: $theme_color;
        color: #333;
        }
      }
    }
  .lvca-tab-pane {
    padding: 40px 0 0;
    }
  }
.lvca-tabs.lvca-style6 {
  .lvca-tab-nav {
    margin: 0 auto;
    text-align: left;
    .lvca-tab {
      margin-right: 50px;
      &:last-child {
        margin-right: 0;
        }
      }
    }
  .lvca-tab-pane {
    padding: 40px 0 0;
    }
  }
.lvca-tabs.lvca-style7 {
  .lvca-tab-nav {
    .lvca-tab {
      padding: 0 25px 0 0;
      a {
        max-width: none;
        margin: 6px 0;
        @include respond-to-max(479) {
          text-align: center;
          }
        }
      }
    }
  .lvca-tab-panes {
    @include flex(6 1 auto);
    .lvca-tab-pane {
      padding: 0 0 0 20px;
      }
    }
  }



.lvca-tabs.lvca-style6.lvca-mobile-layout, .lvca-tabs.lvca-style7.lvca-mobile-layout {
  .lvca-tab-mobile-menu {
    top: 22px;
    }
  .lvca-tab-nav {
    .lvca-tab {
      padding: 12px 0;
      width: 100%;
      text-align: center;
      a {
        margin: 0;
      }
    }
  }
  .lvca-tab-pane {
    padding: 30px 0 0;
  }
}

.lvca-dark-bg .lvca-tabs.lvca-style6, .lvca-dark-bg .lvca-tabs.lvca-style7 {
  .lvca-tab-nav {
    .lvca-tab {
      a {
        color: #b0b0b0;
        }
      }
    .lvca-tab:hover {
      a {
        color: #dddddd;
        }
      }
    .lvca-tab.lvca-active {
      a {
        color: #eaeaea;
        }
      }
    }
  .lvca-tab-pane {
    color: #909090;
    h1, h2, h3, h4, h5, h6 {
      color: #e5e5e5;
      }
    }
  }
/* ------------- Vertical Style 8 ----------------- */

.lvca-tabs.lvca-style8 {
  .lvca-tab-nav {
    .lvca-tab {
      margin: 2px 0;
      a {
        padding: 15px 30px;
        border-radius: 4px;
        background: #f2f2f2;
        color: #777777;
        transition: all .3s ease-in-out 0s;
        border-left: 3px solid transparent;
        text-align: left;
        &:hover, &:focus {
          color: #333333;
          }
        }
      &.lvca-active a {
        color: #333;
        border-left-color: $theme_color;
        }
      }
    }
  .lvca-tab-pane {
    padding: 0 0 0 40px;
    }
  }


.lvca-tabs.lvca-style8.lvca-mobile-layout {
  .lvca-tab-mobile-menu {
    top: 18px;
  }
  .lvca-tab-nav {
    .lvca-tab a {
      text-align: left;
    }
  }
  &:not(.lvca-mobile-open) {
    .lvca-tab.lvca-active a {
      border-color: transparent !important;
    }
  }
  .lvca-tab-pane {
    padding: 30px 0 0;
  }
}


.lvca-dark-bg .lvca-tabs.lvca-style8 {
  .lvca-tab-pane {
    color: #909090;
    h1, h2, h3, h4, h5, h6 {
      color: #e5e5e5;
      }
    }
  }

/* ------------- Vertical Style 9 ----------------- */

.lvca-tabs.lvca-style9 {
    background: #f2f2f2;
    border-radius: 5px;
  .lvca-tab-nav {
    border-right: 1px solid #dddddd;
    .lvca-tab {
      border-bottom: 1px solid #d8d8d8;
      background: #e9e9e9;
      &.lvca-active {
        margin-right: -1px;
        background: #f2f2f2;
        }
      a {
        padding: 20px 30px;
        color: #777;
        &:hover, &:focus {
          color: #333;
          }
        }
      &.lvca-active a {
        color: #333;
        }
      }
    }
  .lvca-tab-panes {
    @include flex(5 1 auto);
    }
  }

.lvca-tabs.lvca-style9.lvca-mobile-layout {
  .lvca-tab-nav {
    border-right: none;
  }
  &:not(.lvca-mobile-open) {
    .lvca-tab.lvca-active {
      background: #eeeeee;
      border: none;
    }
  }
}
/* -------- Vertical Style 10 ----------- */

.lvca-tabs.lvca-style10 {
  .lvca-tab-nav {
    background: #3c3d41;
    border-radius: 5px 0 0 5px;
    .lvca-tab {
      position: relative;
      border-bottom: 1px solid #4e4f53;
      padding: 0;
      &:last-child {
        border-bottom: none;
        }
      a {
        padding: 20px;
        color: #8f8e93;
        transition: all .3s ease-in-out 0s;
        &:hover, &:focus {
          color: #ccc;
          }
        }
      &.lvca-active:after {
        content: '';
        display: block;
        position: absolute;
        top: 32px;
        right: 0;
        height: 8px;
        margin: 0 auto;
        border-top: 8px solid transparent;
        border-right: 8px solid #f2f2f2;
        border-bottom: 8px solid transparent;
        }
      span.lvca-icon-wrapper span, span.lvca-image-wrapper {
        margin: 0 auto;
        }
      span.lvca-tab-title {
        display: none;
        }
      &.lvca-active a {
        color: #fff;
        }
      }
    }
  .lvca-tab-panes {
    background: #f2f2f2;
    border-radius: 0 5px 5px 0;
    }
  }
.lvca-tabs.lvca-style10.lvca-mobile-layout {
  @include flex-direction(row);
  .lvca-tab-mobile-menu {
    display: none;
  }
  .lvca-tab-nav {
    .lvca-tab {
      display: block;
      }
    }
}
.lvca-dark-bg .lvca-tabs.lvca-style10 {
  .lvca-tab-nav {
    background: #fff;
    .lvca-tab {
      border-bottom: 1px solid #ececec;
      &:last-child {
        border-bottom: none;
        }
      a {
        color: #969696;
        &:hover, &:focus {
          color: #666;
          }
        }
      &.lvca-active a {
        color: #333;
        }
      &.lvca-active:after {
        border-right: 8px solid #e7e7e7;
        }
      }
    }
  .lvca-tab-panes {
    background: #e7e7e7;
    .lvca-tab-pane {
      color: #666;
      h1, h2, h3, h4, h5, h6 {
        color: #333;
        }
      }
    }
  }