/* ==========================================================================
   Portfolio Project Info shortcode style - begin
   ========================================================================== */

.mkdf-portfolio-project-info {
    position: relative;
	display: inline-block;
	vertical-align: middle;

	.mkdf-ppi-label {
		margin: 0;
		padding: 0;
	}

	> div {
		position: relative;
		display: inline-block;
		vertical-align: middle;

		a {
			position: relative;
			display: inline-block;
			vertical-align: middle;
			margin: 0 5px 0 0;

			&:last-child {
				margin: 0;
			}
		}
	}

	.mkdf-ppi-title {
		position: relative;
		display: inline-block;
		vertical-align: middle;
		margin: 0;
	}
	
	.mkdf-ppi-image {
		position: relative;
		display: inline-block;
		vertical-align: middle;
		margin: 0;
		
		img {
			display: block;
		}
	}
}
/* ==========================================================================
   Portfolio Project Info shortcode style - end
   ========================================================================== */