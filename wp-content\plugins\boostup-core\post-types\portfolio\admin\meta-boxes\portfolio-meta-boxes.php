<?php

if ( ! function_exists( 'boostup_core_map_portfolio_meta' ) ) {
	function boostup_core_map_portfolio_meta() {
		global $boostup_mikado_global_Framework;
		
		$boostup_pages = array();
		$pages      = get_pages();
		foreach ( $pages as $page ) {
			$boostup_pages[ $page->ID ] = $page->post_title;
		}
		
		//Portfolio Images
		
		$boostup_portfolio_images = new BoostUpMikadoClassMetaBox( 'portfolio-item', esc_html__( 'Portfolio Images (multiple upload)', 'boostup-core' ), '', '', 'portfolio_images' );
		$boostup_mikado_global_Framework->mkdMetaBoxes->addMetaBox( 'portfolio_images', $boostup_portfolio_images );
		
		$boostup_portfolio_image_gallery = new BoostUpMikadoClassMultipleImages( 'mkdf-portfolio-image-gallery', esc_html__( 'Portfolio Images', 'boostup-core' ), esc_html__( 'Choose your portfolio images', 'boostup-core' ) );
		$boostup_portfolio_images->addChild( 'mkdf-portfolio-image-gallery', $boostup_portfolio_image_gallery );
		
		//Portfolio Single Upload Images/Videos 
		
		$boostup_portfolio_images_videos = boostup_mikado_create_meta_box(
			array(
				'scope' => array( 'portfolio-item' ),
				'title' => esc_html__( 'Portfolio Images/Videos (single upload)', 'boostup-core' ),
				'name'  => 'mkdf_portfolio_images_videos'
			)
		);
		boostup_mikado_add_repeater_field(
			array(
				'name'        => 'mkdf_portfolio_single_upload',
				'parent'      => $boostup_portfolio_images_videos,
				'button_text' => esc_html__( 'Add Image/Video', 'boostup-core' ),
				'fields'      => array(
					array(
						'type'        => 'select',
						'name'        => 'file_type',
						'label'       => esc_html__( 'File Type', 'boostup-core' ),
						'options' => array(
							'image' => esc_html__('Image','boostup-core'),
							'video' => esc_html__('Video','boostup-core'),
						)
					),
					array(
						'type'        => 'image',
						'name'        => 'single_image',
						'label'       => esc_html__( 'Image', 'boostup-core' ),
						'dependency' => array(
							'show' => array(
								'file_type'  => 'image'
							)
						)
					),
					array(
						'type'        => 'select',
						'name'        => 'video_type',
						'label'       => esc_html__( 'Video Type', 'boostup-core' ),
						'options'	  => array(
							'youtube' => esc_html__('YouTube', 'boostup-core'),
							'vimeo' => esc_html__('Vimeo', 'boostup-core'),
							'self' => esc_html__('Self Hosted', 'boostup-core'),
						),
						'dependency' => array(
							'show' => array(
								'file_type'  => 'video'
							)
						)
					),
					array(
						'type'        => 'text',
						'name'        => 'video_id',
						'label'       => esc_html__( 'Video ID', 'boostup-core' ),
						'dependency' => array(
							'show' => array(
								'file_type' => 'video',
								'video_type'  => array('youtube','vimeo')
							)
						)
					),
					array(
						'type'        => 'text',
						'name'        => 'video_mp4',
						'label'       => esc_html__( 'Video mp4', 'boostup-core' ),
						'dependency' => array(
							'show' => array(
								'file_type' => 'video',
								'video_type'  => 'self'
							)
						)
					),
					array(
						'type'        => 'image',
						'name'        => 'video_cover_image',
						'label'       => esc_html__( 'Video Cover Image', 'boostup-core' ),
						'dependency' => array(
							'show' => array(
								'file_type' => 'video',
								'video_type'  => 'self'
							)
						)
					)
				)
			)
		);
		
		//Portfolio Additional Sidebar Items
		
		$boostup_additional_sidebar_items = boostup_mikado_create_meta_box(
			array(
				'scope' => array( 'portfolio-item' ),
				'title' => esc_html__( 'Additional Portfolio Sidebar Items', 'boostup-core' ),
				'name'  => 'portfolio_properties'
			)
		);

		boostup_mikado_add_repeater_field(
			array(
				'name'        => 'mkdf_portfolio_properties',
				'parent'      => $boostup_additional_sidebar_items,
				'button_text' => esc_html__( 'Add New Item', 'boostup-core' ),
				'fields'      => array(
					array(
						'type'        => 'text',
						'name'        => 'item_title',
						'label'       => esc_html__( 'Item Title', 'boostup-core' ),
					),
					array(
						'type'        => 'text',
						'name'        => 'item_text',
						'label'       => esc_html__( 'Item Text', 'boostup-core' )
					),
					array(
						'type'        => 'text',
						'name'        => 'item_url',
						'label'       => esc_html__( 'Enter Full URL for Item Text Link', 'boostup-core' )
					)
				)
			)
		);
	}
	
	add_action( 'boostup_mikado_action_meta_boxes_map', 'boostup_core_map_portfolio_meta', 40 );
}