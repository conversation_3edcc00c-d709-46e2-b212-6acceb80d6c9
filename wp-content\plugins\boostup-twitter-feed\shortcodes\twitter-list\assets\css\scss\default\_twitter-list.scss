.mkdf-twitter-list-holder {
	@include mkdfRelativeHolderLayout();
	clear: both;
	
	.mkdf-twitter-list {
		@include mkdfRelativeHolderLayout();
		list-style: none;
		margin: 0;
		padding: 0;
	}
	
	.mkdf-tl-item {
		@include mkdfRelativeHolderLayout();
		padding: 0;
		margin: 0;
		box-sizing: border-box;
	}
	
	.mkdf-tli-inner {
		@include mkdfRelativeHolderLayout();
		background-color: #ffffff;
		@include mkdfTransition(all .2s ease-in-out);
		
		&:hover {
			box-shadow: -2px 4px 13px 0 rgba(81, 137, 162, .05);
			@include mkdfTransform(translateY(-3px));
		}
	}
	
	.mkdf-tli-content {
		@include mkdfRelativeHolderLayout();
		padding: 35px 23px;
		border: 1px solid #f2f2f2;
		box-sizing: border-box;
	}
	
	.mkdf-twitter-content-top {
		@include mkdfRelativeHolderLayout();
		margin-bottom: 25px;
		box-sizing: border-box;
	}
	
	.mkdf-twitter-link-over {
		@include mkdfAbsoluteHolderLayout();
		z-index: 1;
	}
	
	.mkdf-twitter-user {
		@include mkdfRelativeHolderLayout();
		padding-right: 30px;
		box-sizing: border-box;
		
		.mkdf-twitter-image {
			position: relative;
			display: inline-block;
			vertical-align: top;
			float: left;
			width: 56px;
			height: 56px;
			
			img {
				border-radius: 50%;
			}
		}
		
		.mkdf-twitter-name {
			position: relative;
			display: inline-block;
			vertical-align: top;
			float: left;
			width: calc(100% - 56px);
			padding-left: 15px;
			box-sizing: border-box;
			
			* {
				margin: 0;
			}
		}
	}
	
	.mkdf-twitter-icon {
		position: absolute;
		top: -7px;
		right: 3px;
		width: 20px;
		display: inline-block;
		vertical-align: top;
		color: $first-main-color;
		font-size: 24px;
		text-align: right;
	}
	
	.mkdf-tweet-text {
		padding-left: 12px;
		box-sizing: border-box;
		
		a {
			position: relative;
			color: #808080;
			z-index: 2;
			
			&:hover {
				color: $first-main-color;
			}
		}
	}
	
	.mkdf-twitter-profile {
		
		a {
			position: relative;
			color: #808080;
			z-index: 2;
			
			&:hover {
				color: $first-main-color;
			}
		}
	}
}