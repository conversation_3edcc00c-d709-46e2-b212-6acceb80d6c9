.lvca-clients {
  clear: both;
  overflow: hidden;
  margin: 0 auto; }
  @media (max-width: 479px) {
    .lvca-clients .lvca-grid-mobile-1 .lvca-grid-item:nth-child(1n + 1) {
      border-left: 1px solid #ddd; }
    .lvca-clients .lvca-grid-mobile-1 .lvca-grid-item:nth-child(-n + 1) {
      border-top: 1px solid #ddd; }
    .lvca-clients .lvca-grid-mobile-2 .lvca-grid-item:nth-child(2n + 1) {
      border-left: 1px solid #ddd; }
    .lvca-clients .lvca-grid-mobile-2 .lvca-grid-item:nth-child(-n + 2) {
      border-top: 1px solid #ddd; }
    .lvca-clients .lvca-grid-mobile-3 .lvca-grid-item:nth-child(3n + 1) {
      border-left: 1px solid #ddd; }
    .lvca-clients .lvca-grid-mobile-3 .lvca-grid-item:nth-child(-n + 3) {
      border-top: 1px solid #ddd; }
    .lvca-clients .lvca-grid-mobile-4 .lvca-grid-item:nth-child(4n + 1) {
      border-left: 1px solid #ddd; }
    .lvca-clients .lvca-grid-mobile-4 .lvca-grid-item:nth-child(-n + 4) {
      border-top: 1px solid #ddd; }
    .lvca-clients .lvca-grid-mobile-5 .lvca-grid-item:nth-child(5n + 1) {
      border-left: 1px solid #ddd; }
    .lvca-clients .lvca-grid-mobile-5 .lvca-grid-item:nth-child(-n + 5) {
      border-top: 1px solid #ddd; }
    .lvca-clients .lvca-grid-mobile-6 .lvca-grid-item:nth-child(6n + 1) {
      border-left: 1px solid #ddd; }
    .lvca-clients .lvca-grid-mobile-6 .lvca-grid-item:nth-child(-n + 6) {
      border-top: 1px solid #ddd; } }
  @media (min-width: 480px) and (max-width: 800px) {
    .lvca-clients .lvca-grid-tablet-1 .lvca-grid-item:nth-child(1n + 1) {
      border-left: 1px solid #ddd; }
    .lvca-clients .lvca-grid-tablet-1 .lvca-grid-item:nth-child(-n + 1) {
      border-top: 1px solid #ddd; }
    .lvca-clients .lvca-grid-tablet-2 .lvca-grid-item:nth-child(2n + 1) {
      border-left: 1px solid #ddd; }
    .lvca-clients .lvca-grid-tablet-2 .lvca-grid-item:nth-child(-n + 2) {
      border-top: 1px solid #ddd; }
    .lvca-clients .lvca-grid-tablet-3 .lvca-grid-item:nth-child(3n + 1) {
      border-left: 1px solid #ddd; }
    .lvca-clients .lvca-grid-tablet-3 .lvca-grid-item:nth-child(-n + 3) {
      border-top: 1px solid #ddd; }
    .lvca-clients .lvca-grid-tablet-4 .lvca-grid-item:nth-child(4n + 1) {
      border-left: 1px solid #ddd; }
    .lvca-clients .lvca-grid-tablet-4 .lvca-grid-item:nth-child(-n + 4) {
      border-top: 1px solid #ddd; }
    .lvca-clients .lvca-grid-tablet-5 .lvca-grid-item:nth-child(5n + 1) {
      border-left: 1px solid #ddd; }
    .lvca-clients .lvca-grid-tablet-5 .lvca-grid-item:nth-child(-n + 5) {
      border-top: 1px solid #ddd; }
    .lvca-clients .lvca-grid-tablet-6 .lvca-grid-item:nth-child(6n + 1) {
      border-left: 1px solid #ddd; }
    .lvca-clients .lvca-grid-tablet-6 .lvca-grid-item:nth-child(-n + 6) {
      border-top: 1px solid #ddd; } }
  @media only screen and (min-width: 801px) {
    .lvca-clients .lvca-grid-desktop-1 .lvca-grid-item:nth-child(1n + 1) {
      border-left: 1px solid #ddd; }
    .lvca-clients .lvca-grid-desktop-1 .lvca-grid-item:nth-child(-n + 1) {
      border-top: 1px solid #ddd; }
    .lvca-clients .lvca-grid-desktop-2 .lvca-grid-item:nth-child(2n + 1) {
      border-left: 1px solid #ddd; }
    .lvca-clients .lvca-grid-desktop-2 .lvca-grid-item:nth-child(-n + 2) {
      border-top: 1px solid #ddd; }
    .lvca-clients .lvca-grid-desktop-3 .lvca-grid-item:nth-child(3n + 1) {
      border-left: 1px solid #ddd; }
    .lvca-clients .lvca-grid-desktop-3 .lvca-grid-item:nth-child(-n + 3) {
      border-top: 1px solid #ddd; }
    .lvca-clients .lvca-grid-desktop-4 .lvca-grid-item:nth-child(4n + 1) {
      border-left: 1px solid #ddd; }
    .lvca-clients .lvca-grid-desktop-4 .lvca-grid-item:nth-child(-n + 4) {
      border-top: 1px solid #ddd; }
    .lvca-clients .lvca-grid-desktop-5 .lvca-grid-item:nth-child(5n + 1) {
      border-left: 1px solid #ddd; }
    .lvca-clients .lvca-grid-desktop-5 .lvca-grid-item:nth-child(-n + 5) {
      border-top: 1px solid #ddd; }
    .lvca-clients .lvca-grid-desktop-6 .lvca-grid-item:nth-child(6n + 1) {
      border-left: 1px solid #ddd; }
    .lvca-clients .lvca-grid-desktop-6 .lvca-grid-item:nth-child(-n + 6) {
      border-top: 1px solid #ddd; } }
  .lvca-clients .lvca-client {
    position: relative;
    border-right: 1px solid #ddd;
    border-bottom: 1px solid #ddd;
    overflow: hidden; }
    .lvca-dark-bg .lvca-clients .lvca-client {
      border-color: #505050 !important; }
    .lvca-clients .lvca-client img {
      -webkit-transition: all 0.3s ease-in-out 0s;
      transition: all 0.3s ease-in-out 0s;
      width: 100%;
      margin: 0;
      display: block; }
    .lvca-clients .lvca-client .lvca-client-name {
      position: absolute;
      z-index: 2;
      top: 50%;
      text-align: center;
      width: 100%;
      height: 100%;
      margin-top: -12px;
      color: #fff;
      font-size: 18px;
      line-height: 26px;
      -webkit-transition: opacity .4s ease-in-out 0s;
      transition: opacity .4s ease-in-out 0s;
      opacity: 0; }
      .lvca-clients .lvca-client .lvca-client-name a {
        color: #fff;
        text-decoration: none; }
    .lvca-clients .lvca-client .lvca-image-overlay {
      position: absolute;
      left: 0;
      top: 0;
      overflow: hidden;
      width: 100%;
      height: 100%;
      background: #000;
      filter: alpha(opacity=0);
      -moz-opacity: 0;
      opacity: 0;
      -webkit-transition: opacity .4s ease-in-out 0s;
      transition: opacity .4s ease-in-out 0s; }
    .lvca-clients .lvca-client:hover .lvca-image-overlay {
      opacity: 0.7; }
      .lvca-dark-bg .lvca-clients .lvca-client:hover .lvca-image-overlay {
        opacity: 0.8; }
    .lvca-clients .lvca-client:hover .lvca-client-name {
      opacity: 1; }

/*# sourceMappingURL=style.css.map */