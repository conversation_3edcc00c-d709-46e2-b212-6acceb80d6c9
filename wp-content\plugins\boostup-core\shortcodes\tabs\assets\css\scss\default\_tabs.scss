/* ==========================================================================
   Tabs shortcode style - begin
   ========================================================================== */

.mkdf-tabs {
	@include mkdfRelativeHolderLayout();
	
	.mkdf-tabs-nav {
		@include mkdfRelativeHolderLayout();
		margin: 0;
		padding: 0;
		list-style: none;
		border-bottom: 2px solid $default-border-color;
		
		li {
			float: left;
			margin: 0;
			padding: 0;
			position: relative !important;
			
			a {
				position: relative;
				display: inline-block;
				vertical-align: middle;
				box-sizing: border-box;
				@include mkdfTransition(all .2s ease-out, background-color .2s ease-out, border-color .2s ease-out);
				font-size: 16px;
				font-family: $default-text-font;
				opacity: 0.6;

				
			}
			.mkdf-tabs-underline {
					width: 0%;
					height: 8px;
					position: absolute;
					bottom: -2px;
					left: 0;
					opacity: 1 !important;
					@include mkdfTransition(all 0.4s ease-in);
				}
			&.ui-state-active a{
					
					
					opacity: 1;
				}
		}
	}
	
	.mkdf-tab-container {
        box-sizing: border-box;
		@include mkdfRelativeHolderLayout();
		
		p {
			margin: 0;
		}
	}
	
	&.mkdf-tabs-standard {
		
		.mkdf-tabs-nav {
			
			li {
				
				a {
					padding: 21px 42px;
					line-height: 25px;
					font-weight: 600;
					letter-spacing: 1px;
					color: $default-heading-color;
					margin-bottom: -2px;
					border-bottom: 8px solid transparent;
				}
				
				&.ui-state-active a{
					
					border-bottom: 8px solid transparent;
					opacity: 1;
				}
			}
		}
		
		.mkdf-tab-container {
			margin: 25px 0 0;
		}
	}
	
	&.mkdf-tabs-boxed {
		
		.mkdf-tabs-nav {
			
			li {
				margin: 0 12px 0 0;
				
				a {
					padding: 21px 43px;
					line-height: 25px;
					font-weight: 400;
					letter-spacing: 1px;
					color: #fff;
					background-color: $default-heading-color;
					margin-bottom: -2px;
				}
				
				&.ui-state-active a,
				&.ui-state-hover a {
					color: #fff;
					background-color: $first-main-color;
				}
				
				&:last-child {
					margin: 0;
				}
			}
		}
		
		.mkdf-tab-container {
			margin: 25px 0 0;
		}
	}
	
	&.mkdf-tabs-simple {
		
		.mkdf-tabs-nav {
			border-bottom: 1px solid $default-border-color;
			
			li {
				margin: 0 31px 0 0;
				
				a {
					padding: 13px 0;
					font-size: 18px;
					line-height: 26px;
					font-weight: 400;
					color: $default-heading-color;
				}
				
				&.ui-state-active a,
				&.ui-state-hover a {
					color: $first-main-color;
				}
				
				&:last-child {
					margin: 0;
				}
			}
		}
		
		.mkdf-tab-container {
			padding: 31px 0;
		}
	}
	
	&.mkdf-tabs-vertical {
		display: table;
		
		.mkdf-tabs-nav {
			display: table-cell;
			vertical-align: top;
			width: 140px;
			height: 100%;
			border-right: 1px solid $default-border-color;
			border-bottom: 0;
			box-sizing: border-box;
			
			li {
				display: block;
				float: none;
				
				a {
					font-size: 16px;
					line-height: 26px;
					font-weight: 400;
					display: block;
					color: $default-heading-color;
					padding: 12px 0 12px;
				}
				
				
				&.ui-state-active a,
				&.ui-state-hover a {
					color: $first-main-color;
					border-right: 8px solid transparent;
				}
				
				&:last-child {
					margin: 0;
				}
			}
		}
		
		.mkdf-tab-container {
			display: table-cell;
			vertical-align: top;
			width: calc(100% - 140px);
			height: 100%;
			padding: 0 0 0 45px;
			box-sizing: border-box;
		}
	}
}

.ui-widget-content {
	padding: 0;
	font-family: inherit;
	font-size: inherit;
	color: inherit;
	background: none;
	border: 0;
	border-radius: 0;

    .ui-widget-header {
	    font-size: inherit;
	    line-height: inherit;
	    font-weight: inherit;
	    color: initial;
	    background: none;
        border-radius: 0;
    }
	
    .ui-tabs-nav {
	    
        li {
	        position: initial;
	        font-weight: inherit;
	        color: inherit;
	        background: initial;
	        border: 0;
	        border-radius: 0;
        }
    }

    .ui-widget-content {
	    color: inherit;
	    background: none;
	    border: 0;
        border-radius: 0;
    }
}
/* ==========================================================================
   Tabs shortcode style - end
   ========================================================================== */