/*!
 * WPBakery Page Builder v6.0.0 (https://wpbakery.com)
 * Copyright 2011-2023 <PERSON>, WPBakery
 * License: Commercial. More details: http://go.wpbakery.com/licensing
 */

// jscs:disable
// jshint ignore: start

!function($){"use strict";function Affix(element,options){this.options=$.extend({},Affix.DEFAULTS,options),this.$target=$(this.options.target).on("scroll.bs.affix.data-api",$.proxy(this.checkPosition,this)).on("click.bs.affix.data-api",$.proxy(this.checkPositionWithEventLoop,this)),this.$element=$(element),this.affixed=this.unpin=this.pinnedOffset=null,this.checkPosition()}function Plugin(option){return this.each(function(){var $this=$(this),data=$this.data("bs.affix");data||$this.data("bs.affix",data=new Affix(this,"object"==typeof option&&option)),"string"==typeof option&&data[option]()})}Affix.VERSION="3.1.1",Affix.RESET="affix affix-top affix-bottom",Affix.DEFAULTS={offset:0,target:window},Affix.prototype.getPinnedOffset=function(){if(this.pinnedOffset)return this.pinnedOffset;this.$element.removeClass(Affix.RESET).addClass("affix");var scrollTop=this.$target.scrollTop(),position=this.$element.offset();return this.pinnedOffset=position.top-scrollTop},Affix.prototype.checkPositionWithEventLoop=function(){setTimeout($.proxy(this.checkPosition,this),1)},Affix.prototype.checkPosition=function(){var scrollHeight,offsetTop,offsetBottom,offset,position,scrollTop;this.$element.is(":visible")&&(scrollHeight=$(document).height(),scrollTop=this.$target.scrollTop(),position=this.$element.offset(),offsetTop=(offset=this.options.offset).top,offsetBottom=offset.bottom,"object"!=typeof offset&&(offsetBottom=offsetTop=offset),"function"==typeof offsetTop&&(offsetTop=offset.top(this.$element)),"function"==typeof offsetBottom&&(offsetBottom=offset.bottom(this.$element)),offset=!(null!=this.unpin&&scrollTop+this.unpin<=position.top)&&(null!=offsetBottom&&position.top+this.$element.height()>=scrollHeight-offsetBottom?"bottom":null!=offsetTop&&scrollTop<=offsetTop&&"top"),this.affixed===offset||(null!=this.unpin&&this.$element.css("top",""),scrollTop=$.Event((position="affix"+(offset?"-"+offset:""))+".bs.affix"),this.$element.trigger(scrollTop),scrollTop.isDefaultPrevented())||(this.affixed=offset,this.unpin="bottom"==offset?this.getPinnedOffset():null,this.$element.removeClass(Affix.RESET).addClass(position).trigger($.Event(position.replace("affix","affixed"))),"bottom"==offset&&this.$element.offset({top:scrollHeight-this.$element.height()-offsetBottom})))};var old=$.fn.affix;$.fn.affix=Plugin,$.fn.affix.Constructor=Affix,$.fn.affix.noConflict=function(){return $.fn.affix=old,this},$(window).on("load",function(){$('[data-spy="affix"]').each(function(){var $spy=$(this),data=$spy.data();data.offset=data.offset||{},data.offsetBottom&&(data.offset.bottom=data.offsetBottom),data.offsetTop&&(data.offset.top=data.offsetTop),Plugin.call($spy,data)})})}(jQuery);