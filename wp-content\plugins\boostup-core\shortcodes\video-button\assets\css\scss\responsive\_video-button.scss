@include ipad-portrait {

	.mkdf-video-button-holder {

		.mkdf-video-button-play {
			font-size: 36px !important;

			span {
				span {
					padding: 4px;
					width: 53px;
    				height: 53px;
    				line-height: 53px;

    				.icon-basic-animation {
					    width: 53px;
	    				height: 53px;
	    				line-height: 53px;
    				}
				}
			}
			.mkdf-video-button-play-inner {

				&.top-right {
					top: 9% !important;
    				
				}

			}
		}

		.mkdf-video-button-text {
			padding: 30px 30px 20px;

			.mkdf-video-button-title {
				display:block;
				width:100%;
			}

			p {
				width:100%;
				display:block;
			}
		}

		.mkdf-video-button-text-shadow-holder {
			display: none;
		}
	}
}

@include phone-portrait {

	.mkdf-video-button-holder {

		.mkdf-video-button-play {
			font-size: 20px;

			.mkdf-video-button-play-inner {

				&.top-right {
					top: 16% !important;
    				
				}

			}
		}
	}
}

@include smaller-phone-portrait {

	.mkdf-video-button-holder {

		.mkdf-video-button-play {
			font-size: 24px !important;

			span {
				span {
					
					width: 20px;
    				height: 20px;
    				line-height: 20px;

    				.icon-basic-animation {
					    width: 20px;
	    				height: 20px;
	    				line-height: 20px;
    				}
				}
			}
			.mkdf-video-button-play-inner {

				&.top-right {
					top: 13% !important;
					right: 4px !important;
    				
				}

			}
		}

		
	}
}
