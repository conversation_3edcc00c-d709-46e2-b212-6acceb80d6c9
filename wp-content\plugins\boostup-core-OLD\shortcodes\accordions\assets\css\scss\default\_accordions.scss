/* ==========================================================================
   Accordions shortcode style - begin
   ========================================================================== */

.mkdf-accordion-holder {
    @include mkdfRelativeHolderLayout();

    .mkdf-accordion-title {
        position: relative;
        cursor: pointer;
        margin: 0;
        box-sizing: border-box;
        @include mkdfTransform(translateZ(0px));
		border: 2px $default-border-color solid;
	
	    .mkdf-tab-title {
		    display: block;
		    line-height: inherit;
	    }

        .mkdf-accordion-mark {
            position: absolute;
            top: 50%;
	        right: 0;
            width: 40px;
	        height: 40px;
	        margin: -1px 0 0;
	        font-size: 40px;
	        line-height: 40px;
            text-align: center;
            @include mkdfTransform(translateY(-50%));
            @include mkdfTransition(width 0.2s ease-in-out);
	
	        span {
		        position: absolute;
		        display: block;
		        width: 100%;
		        height: 100%;
		        font-size: inherit;
		        line-height: inherit;
		        @include mkdfTransition(opacity .2s ease-out);
		
		        &:before {
			        display: block;
			        line-height: inherit;
		        }

		        
	            
		        
		        
	        }
	        .mkdf-eye-line {
	        	display: block;
	        	@include mkdfTransform (rotate(-45deg));

	        	&:after {
	                content: '';
	                display: block;
	                height: 2px;
	                width: 100%;
	                position: absolute;
	                left: 0;
	                bottom: 19px;
	                background-color: currentColor;
	                @include mkdfTransition(width 0.2s ease-in-out);
	                
	            }

	        }
	        &:hover {
	        	.mkdf-eye-line {
	        		&:after {
	        			width: 100%;
	        		}
	        	}
	        }
        }
	    
	    &.ui-state-active,
	    &.ui-state-hover {
		
		    .mkdf-accordion-mark {
			    
			    .mkdf-eye-line {
	        		&:after {
	        			width: 0%;
	        			
	        		}
	        	}
		    }
	    }
	    
    }

    .mkdf-accordion-content {
        margin: 0;
		border: 2px solid $default-border-color;
		border-top: none;
	    
	    p {
		    margin: 0;
	    }
    }
	
	&.mkdf-ac-boxed {
		
		&.mkdf-white-skin {

			color: #fff;
			
			.mkdf-accordion-title {
				color: #fff;
			}
		}
		
		.mkdf-accordion-title {
			margin: 23px 0 0;
			padding: 24px 70px 24px 33px;
			
			&:first-child {
				margin: 0;
			}
			
			.mkdf-accordion-mark {
				right: 28px;
			}
			
			&.ui-state-active {
				border-bottom: none;
			}
		}
		
		.mkdf-accordion-content {
			padding: 11px 36px 35px 36px;
		}
	}
	
	&.mkdf-ac-simple {
		border-bottom: 1px solid $default-border-color;
		
		.mkdf-accordion-title {
			padding: 17px 0 17px 30px;
			border-top: 1px solid $default-border-color;
		}
		
		.mkdf-accordion-content {
			border-top: 1px solid transparent;
			
			&.ui-accordion-content-active {
				border-color: $default-border-color;
			}
		}
		
		.mkdf-accordion-content {
			padding: 21px 0 16px;
		}
	}
}
/* ==========================================================================
   Accordions shortcode style - end
   ========================================================================== */