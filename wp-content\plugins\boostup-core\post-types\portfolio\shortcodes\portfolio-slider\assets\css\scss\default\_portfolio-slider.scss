/* ==========================================================================
   Portfolio Slider shortcode style - begin
   ========================================================================== */

.mkdf-portfolio-slider-holder {
    @include mkdfRelativeHolderLayout();

	.mkdf-portfolio-list-holder {

		&.mkdf-nav-light-skin {

			.owl-nav {

				.owl-prev,
				.owl-next {
					color: $header-light-color;
					
					&:hover {
						color: $header-light-hover-color;
					}
				}
			}
		}

		&.mkdf-nav-dark-skin {

			.owl-nav {

				.owl-prev,
				.owl-next {
					color: $header-dark-color;
					
					&:hover {
						color: $header-dark-hover-color;
					}
				}
			}
		}

		&.mkdf-pag-light-skin {

			.owl-dots {

				.owl-dot {

					span {
						background-color: rgba($header-light-color, .2);
					}

					&.active,
					&:hover {

						span {
							background-color: $header-light-hover-color;
						}
					}
				}
			}
		}

		&.mkdf-pag-dark-skin {

			.owl-dots {

				.owl-dot {

					span {
						background-color: rgba($header-dark-color, .2);
					}

					&.active,
					&:hover {

						span {
							background-color: $header-dark-hover-color;
						}
					}
				}
			}
		}

		&.mkdf-pag-on-slider {

			.owl-nav {

				.owl-prev,
				.owl-next {
					@include mkdfTransform(translateY(-50%));
				}
			}

			.owl-dots {
				position: absolute;
				left: 0;
				bottom: 20px;
				width: 100%;
				margin: 0;
			}
		}
	}
}
/* ==========================================================================
   Portfolio Slider shortcode style - end
   ========================================================================== */