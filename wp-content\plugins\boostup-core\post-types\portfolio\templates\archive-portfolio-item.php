<?php
get_header();
boostup_mikado_get_title();
do_action( 'boostup_mikado_action_before_main_content' ); ?>
<div class="mkdf-container mkdf-default-page-template">
	<?php do_action( 'boostup_mikado_action_after_container_open' ); ?>
	<div class="mkdf-container-inner clearfix">
		<?php
			$boostup_taxonomy_id   = get_queried_object_id();
			$boostup_taxonomy_type = is_tax( 'portfolio-tag' ) ? 'portfolio-tag' : 'portfolio-category';
			$boostup_taxonomy      = ! empty( $boostup_taxonomy_id ) ? get_term_by( 'id', $boostup_taxonomy_id, $boostup_taxonomy_type ) : '';
			$boostup_taxonomy_slug = ! empty( $boostup_taxonomy ) ? $boostup_taxonomy->slug : '';
			$boostup_taxonomy_name = ! empty( $boostup_taxonomy ) ? $boostup_taxonomy->taxonomy : '';
			
			boostup_core_get_archive_portfolio_list( $boostup_taxonomy_slug, $boostup_taxonomy_name );
		?>
	</div>
	<?php do_action( 'boostup_mikado_action_before_container_close' ); ?>
</div>
<?php get_footer(); ?>
