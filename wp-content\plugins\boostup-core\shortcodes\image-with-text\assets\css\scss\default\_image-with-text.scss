/* ==========================================================================
   Image With Text shortcode style - begin
   ========================================================================== */

.mkdf-image-with-text-holder {
    @include mkdfRelativeHolderLayout();
    @include mkdfTransition(all 0.3s ease-in-out);

    
	
	&.mkdf-has-shadow {
		
		.mkdf-iwt-image {
			box-shadow: 0 0 42.85px 7.15px rgba(0,0,0,.09);
		}
	}
	
	.mkdf-iwt-image {
		@include mkdfRelativeHolderLayout();
		
		width: auto;
		
		a, img {
			position: relative;
			display: block;
			@include mkdfTransition(all .3s ease);
		}

		.mkdf-icon-shortcode {
			position:absolute;
			right: -20px;
			top: -15px;
			z-index: 5;
			font-size: 20px;
			@include mkdfTransition(all .3s ease-out);


		}

		&:hover {

			.mkdf-icon-shortcode {

				animation: mkdfPulsesmallfirst 1.8s infinite;
			}

				
		}
	}
	
	.mkdf-iwt-text-holder {
		@include mkdfRelativeHolderLayout();
	}
	
	.mkdf-iwt-title {
		margin: 40px 0 0;
	}
	
	.mkdf-iwt-text {
		margin: 18px 0 0;
		line-height: 1.8em;
	}
	
	/***** Custom Link Behavior Style - begin *****/
	
	&.mkdf-image-behavior-custom-link {
		
		&:hover {
            -webkit-transform: translate(0, -8px);
            @include mkdfTransform(translate(0, -8px));
			

		}
	}
	
	/***** Custom Link Behavior Style - end *****/
	
	/***** Lightbox Behavior Style - begin *****/
	
	&.mkdf-image-behavior-lightbox {
		
		.mkdf-iwt-image {
			
			a {
				@include mkdfImageOverlayHoverStyle();
			}
		}
	}
	
	/***** Lightbox Behavior Style - end *****/
	
	/***** Zoom Behavior Style - begin *****/
	
	&.mkdf-image-behavior-zoom {
		
		.mkdf-iwt-image {
			overflow: hidden;
			
			.touch & {
				cursor: pointer;
			}
			
			&:hover {
				
				img {
					@include mkdfTransform(scale(1.04));
				}
			}
			
			img {
				@include mkdfTransform(scale(1));
				@include mkdfTransitionTransform(.3s ease-in-out);
			}
		}
	}
	
	/***** Zoom Behavior Style - end *****/
	
	/***** Grayscale Behavior Style - begin *****/
	
	&.mkdf-image-behavior-grayscale {
		
		.mkdf-iwt-image {
			overflow: hidden;
			
			.touch & {
				cursor: pointer;
			}
			
			&:hover {
				
				img {
					-webkit-filter: grayscale(0);
					filter: none;
				}
			}
			
			img {
				filter: url('img/desaturate.svg#grayscale');
				-webkit-filter: grayscale(100%);
				-moz-filter: grayscale(100%);
				filter: gray;
				filter: grayscale(100%);
				@include mkdfTransition(all .3s ease-in-out);
			}
		}
	}
	
	/***** Grayscale Behavior Style - end *****/


}
/***** Landing Appeared Item start *****/


	.custom-image-padding-row {

		
		
		-webkit-transition: opacity .22s cubic-bezier(0.23, 1, 0.32, 1), -webkit-transform 0s cubic-bezier(0.23, 1, 0.32, 1);
		transition: opacity .22s cubic-bezier(0.23, 1, 0.32, 1), transform 0s cubic-bezier(0.23, 1, 0.32, 1);

		.mkdf-image-with-text-holder {

			.mkdf-iwt-image {

				opacity: 0;
				@include mkdfTransform(scale(.6));
				-webkit-transition: -webkit-transform .6s cubic-bezier(0.18, 0.89, 0.32, 1), opacity .2s ease-out;
				transition: transform .4s cubic-bezier(0.18, 0.89, 0.32, 1), opacity .2s ease-out;

				&.mkdf-landing-appeared {

					opacity: 1;
					@include mkdfTransform(scale(1));

				}
			}

		}

	}

	
	


    /***** Landing Appeared Item end *****/
/* ==========================================================================
   Image With Text shortcode style - end
   ========================================================================== */
