/*!
 * WPBakery Page Builder v6.0.0 (https://wpbakery.com)
 * Copyright 2011-2023 <PERSON>, WPBakery
 * License: Commercial. More details: http://go.wpbakery.com/licensing
 */

// jscs:disable
// jshint ignore: start

!function($){"use strict";$.fn.vcRoundChart=function(){var vcwaypoint=void 0!==$.fn.vcwaypoint;return this.each(function(){var data,gradient,chart,$this=$(this),ctx=$this.find("canvas")[0].getContext("2d"),stroke_width=$this.data("vcStrokeWidth")?parseInt($this.data("vcStrokeWidth"),10):0,options={showTooltips:$this.data("vcTooltips"),animation:{duration:800,easing:$this.data("vcAnimation")||"easeOutQuart"},segmentStrokeColor:$this.data("vcStrokeColor"),borderColor:$this.data("vcStrokeColor"),segmentShowStroke:0!==stroke_width,segmentStrokeWidth:stroke_width,strokeWidth:stroke_width,borderWidth:stroke_width,responsive:!0,plugins:{legend:{display:!0}}};function addchart(){var type;$this.data("animated")||(type="pie","doughnut"===$this.data("vcType")&&(type="doughnut"),chart=new Chart(ctx,{type:type,data:data,options:options}),$this.data("vcChartId",chart.id),$this.data("chart",chart),$this.data("animated",!0))}$this.data("vcLegend")||(options.plugins.legend={display:!1}),options.plugins.legend.display&&$this.data("vcLegendColor")&&(options.plugins.legend.labels={color:$this.data("vcLegendColor")}),options.plugins.legend.display&&$this.data("vcLegendPosition")&&(options.plugins.legend.position=$this.data("vcLegendPosition")),$this.data("vcTooltips")||(options.plugins.tooltip={enabled:!1}),$this.data("chart")&&($this.data("chart").destroy(),$this.removeData("animated")),data=$this.data("vcValues"),ctx.canvas.width=$this.width(),ctx.canvas.height=$this.width(),data.datasets[0].backgroundColor.forEach(function(color,i){Array.isArray(color)&&((gradient=ctx.createLinearGradient(0,0,0,ctx.canvas.height)).addColorStop(0,color[0]),gradient.addColorStop(1,color[1]),data.datasets[0].backgroundColor[i]=gradient)}),vcwaypoint?$this.vcwaypoint($.proxy(addchart,$this),{offset:"85%"}):addchart()}),this},"function"!=typeof window.vc_round_charts&&(window.vc_round_charts=function(model_id){var selector=".vc_round-chart";$(selector=void 0!==model_id?'[data-model-id="'+model_id+'"] '+selector:selector).vcRoundChart()}),$(document).ready(function(){window.vc_iframe||vc_round_charts()})}(jQuery);