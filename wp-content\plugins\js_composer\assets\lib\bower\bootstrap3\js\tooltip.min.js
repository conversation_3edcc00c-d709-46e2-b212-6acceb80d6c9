/*!
 * WPBakery Page Builder v6.0.0 (https://wpbakery.com)
 * Copyright 2011-2023 <PERSON>, WPBakery
 * License: Commercial. More details: http://go.wpbakery.com/licensing
 */

// jscs:disable
// jshint ignore: start

!function($){"use strict";function Tooltip(element,options){this.type=this.options=this.enabled=this.timeout=this.hoverState=this.$element=null,this.init("tooltip",element,options)}Tooltip.VERSION="3.1.1",Tooltip.DEFAULTS={animation:!0,placement:"top",selector:!1,template:'<div class="tooltip" role="tooltip"><div class="tooltip-arrow"></div><div class="tooltip-inner"></div></div>',trigger:"hover focus",title:"",delay:0,html:!1,container:!1,viewport:{selector:"body",padding:0}},Tooltip.prototype.init=function(type,element,options){this.enabled=!0,this.type=type,this.$element=$(element),this.options=this.getOptions(options),this.$viewport=this.options.viewport&&$(this.options.viewport.selector||this.options.viewport);for(var triggers=this.options.trigger.split(" "),i=triggers.length;i--;){var eventOut,trigger=triggers[i];"click"==trigger?this.$element.on("click."+this.type,this.options.selector,$.proxy(this.toggle,this)):"manual"!=trigger&&(eventOut="hover"==trigger?"mouseleave":"focusout",this.$element.on(("hover"==trigger?"mouseenter":"focusin")+"."+this.type,this.options.selector,$.proxy(this.enter,this)),this.$element.on(eventOut+"."+this.type,this.options.selector,$.proxy(this.leave,this)))}this.options.selector?this._options=$.extend({},this.options,{trigger:"manual",selector:""}):this.fixTitle()},Tooltip.prototype.getDefaults=function(){return Tooltip.DEFAULTS},Tooltip.prototype.getOptions=function(options){return(options=$.extend({},this.getDefaults(),this.$element.data(),options)).delay&&"number"==typeof options.delay&&(options.delay={show:options.delay,hide:options.delay}),options},Tooltip.prototype.getDelegateOptions=function(){var options={},defaults=this.getDefaults();return this._options&&$.each(this._options,function(key,value){defaults[key]!=value&&(options[key]=value)}),options},Tooltip.prototype.enter=function(obj){var self=obj instanceof this.constructor?obj:$(obj.currentTarget).data("bs."+this.type);if(self||(self=new this.constructor(obj.currentTarget,this.getDelegateOptions()),$(obj.currentTarget).data("bs."+this.type,self)),clearTimeout(self.timeout),self.hoverState="in",!self.options.delay||!self.options.delay.show)return self.show();self.timeout=setTimeout(function(){"in"==self.hoverState&&self.show()},self.options.delay.show)},Tooltip.prototype.leave=function(obj){var self=obj instanceof this.constructor?obj:$(obj.currentTarget).data("bs."+this.type);if(self||(self=new this.constructor(obj.currentTarget,this.getDelegateOptions()),$(obj.currentTarget).data("bs."+this.type,self)),clearTimeout(self.timeout),self.hoverState="out",!self.options.delay||!self.options.delay.hide)return self.hide();self.timeout=setTimeout(function(){"out"==self.hoverState&&self.hide()},self.options.delay.hide)},Tooltip.prototype.show=function(){var that,autoToken,actualWidth,actualHeight,placement,$parent,autoPlace,e=$.Event("show.bs."+this.type);this.hasContent()&&this.enabled&&(this.$element.trigger(e),e.isDefaultPrevented()||(e=(that=this).tip(),this.setContent(),this.options.animation&&e.addClass("fade"),placement="function"==typeof this.options.placement?this.options.placement.call(this,e[0],this.$element[0]):this.options.placement,(autoPlace=(autoToken=/\s?auto?\s?/i).test(placement))&&(placement=placement.replace(autoToken,"")||"top"),e.detach().css({top:0,left:0,display:"block"}).addClass(placement),this.options.container?e.appendTo(this.options.container):e.insertAfter(this.$element),autoToken=this.getPosition(),actualWidth=e[0].offsetWidth,actualHeight=e[0].offsetHeight,autoPlace&&(autoPlace=placement,$parent=this.$element.parent(),$parent=this.getPosition($parent),placement="bottom"==placement&&autoToken.top+autoToken.height+actualHeight-$parent.scroll>$parent.height?"top":"top"==placement&&autoToken.top-$parent.scroll-actualHeight<0?"bottom":"right"==placement&&autoToken.right+actualWidth>$parent.width?"left":"left"==placement&&autoToken.left-actualWidth<$parent.left?"right":placement,e.removeClass(autoPlace).addClass(placement)),$parent=this.getCalculatedOffset(placement,autoToken,actualWidth,actualHeight),this.applyPlacement($parent,placement),this.hoverState=null,autoPlace=function(){that.$element.trigger("shown.bs."+that.type)},$.support.transition&&this.$tip.hasClass("fade")?e.one($.support.transition.end,autoPlace).emulateTransitionEnd(150):autoPlace()))},Tooltip.prototype.applyPlacement=function(offset,placement){var $tip=this.tip(),width=$tip[0].offsetWidth,height=$tip[0].offsetHeight,marginTop=parseInt($tip.css("margin-top"),10),marginLeft=parseInt($tip.css("margin-left"),10),marginTop=(isNaN(marginTop)&&(marginTop=0),isNaN(marginLeft)&&(marginLeft=0),offset.top=offset.top+marginTop,offset.left=offset.left+marginLeft,$.offset.setOffset($tip[0],$.extend({using:function(props){$tip.css({top:Math.round(props.top),left:Math.round(props.left)})}},offset),0),$tip.addClass("in"),$tip[0].offsetWidth),marginLeft=$tip[0].offsetHeight,placement=("top"==placement&&marginLeft!=height&&(offset.top=offset.top+height-marginLeft),this.getViewportAdjustedDelta(placement,offset,marginTop,marginLeft)),width=(placement.left?offset.left+=placement.left:offset.top+=placement.top,placement.left?2*placement.left-width+marginTop:2*placement.top-height+marginLeft),marginTop=placement.left?"left":"top",height=placement.left?"offsetWidth":"offsetHeight";$tip.offset(offset),this.replaceArrow(width,$tip[0][height],marginTop)},Tooltip.prototype.replaceArrow=function(delta,dimension,position){this.arrow().css(position,delta?50*(1-delta/dimension)+"%":"")},Tooltip.prototype.setContent=function(){var $tip=this.tip(),title=this.getTitle();$tip.find(".tooltip-inner")[this.options.html?"html":"text"](title),$tip.removeClass("fade in top bottom left right")},Tooltip.prototype.hide=function(){var that=this,$tip=this.tip(),e=$.Event("hide.bs."+this.type);function complete(){"in"!=that.hoverState&&$tip.detach(),that.$element.trigger("hidden.bs."+that.type)}if(this.$element.trigger(e),!e.isDefaultPrevented())return $tip.removeClass("in"),$.support.transition&&this.$tip.hasClass("fade")?$tip.one($.support.transition.end,complete).emulateTransitionEnd(150):complete(),this.hoverState=null,this},Tooltip.prototype.fixTitle=function(){var $e=this.$element;!$e.attr("title")&&"string"==typeof $e.attr("data-original-title")||$e.attr("data-original-title",$e.attr("title")||"").attr("title","")},Tooltip.prototype.hasContent=function(){return this.getTitle()},Tooltip.prototype.getPosition=function($element){var el=($element=$element||this.$element)[0],isBody="BODY"==el.tagName;return $.extend({},"function"==typeof el.getBoundingClientRect?el.getBoundingClientRect():null,{scroll:isBody?document.documentElement.scrollTop||document.body.scrollTop:$element.scrollTop(),width:isBody?$(window).width():$element.outerWidth(),height:isBody?$(window).height():$element.outerHeight()},isBody?{top:0,left:0}:$element.offset())},Tooltip.prototype.getCalculatedOffset=function(placement,pos,actualWidth,actualHeight){return"bottom"==placement?{top:pos.top+pos.height,left:pos.left+pos.width/2-actualWidth/2}:"top"==placement?{top:pos.top-actualHeight,left:pos.left+pos.width/2-actualWidth/2}:"left"==placement?{top:pos.top+pos.height/2-actualHeight/2,left:pos.left-actualWidth}:{top:pos.top+pos.height/2-actualHeight/2,left:pos.left+pos.width}},Tooltip.prototype.getViewportAdjustedDelta=function(placement,pos,actualWidth,actualHeight){var viewportPadding,viewportDimensions,delta={top:0,left:0};return this.$viewport&&(viewportPadding=this.options.viewport&&this.options.viewport.padding||0,viewportDimensions=this.getPosition(this.$viewport),/right|left/.test(placement)?(placement=pos.top-viewportPadding-viewportDimensions.scroll,actualHeight=pos.top+viewportPadding-viewportDimensions.scroll+actualHeight,placement<viewportDimensions.top?delta.top=viewportDimensions.top-placement:actualHeight>viewportDimensions.top+viewportDimensions.height&&(delta.top=viewportDimensions.top+viewportDimensions.height-actualHeight)):(placement=pos.left-viewportPadding,actualHeight=pos.left+viewportPadding+actualWidth,placement<viewportDimensions.left?delta.left=viewportDimensions.left-placement:actualHeight>viewportDimensions.width&&(delta.left=viewportDimensions.left+viewportDimensions.width-actualHeight))),delta},Tooltip.prototype.getTitle=function(){var $e=this.$element,o=this.options;return $e.attr("data-original-title")||("function"==typeof o.title?o.title.call($e[0]):o.title)},Tooltip.prototype.tip=function(){return this.$tip=this.$tip||$(this.options.template)},Tooltip.prototype.arrow=function(){return this.$arrow=this.$arrow||this.tip().find(".tooltip-arrow")},Tooltip.prototype.validate=function(){this.$element[0].parentNode||(this.hide(),this.$element=null,this.options=null)},Tooltip.prototype.enable=function(){this.enabled=!0},Tooltip.prototype.disable=function(){this.enabled=!1},Tooltip.prototype.toggleEnabled=function(){this.enabled=!this.enabled},Tooltip.prototype.toggle=function(e){var self=this;e&&!(self=$(e.currentTarget).data("bs."+this.type))&&(self=new this.constructor(e.currentTarget,this.getDelegateOptions()),$(e.currentTarget).data("bs."+this.type,self)),self.tip().hasClass("in")?self.leave(self):self.enter(self)},Tooltip.prototype.destroy=function(){clearTimeout(this.timeout),this.hide().$element.off("."+this.type).removeData("bs."+this.type)};var old=$.fn.tooltip;$.fn.tooltip=function(option){return this.each(function(){var $this=$(this),data=$this.data("bs.tooltip");!data&&"destroy"==option||(data||$this.data("bs.tooltip",data=new Tooltip(this,"object"==typeof option&&option)),"string"!=typeof option)||data[option]()})},$.fn.tooltip.Constructor=Tooltip,$.fn.tooltip.noConflict=function(){return $.fn.tooltip=old,this}}(jQuery);