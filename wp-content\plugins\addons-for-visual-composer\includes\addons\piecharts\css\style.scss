@import "../../../../assets/css/lvca-lib";

/*---- <PERSON> Charts --------- */

.lvca-piechart {
  position: relative;
  text-align: center;
  float: left;
  overflow: hidden;
  float: left;
  padding: 10px;
  canvas {
    position: relative;
    top: 0;
    left: 0;
    max-width: 100%;
    margin: 0 auto;
    }
  .lvca-label {
    text-align: center;
    position: absolute;
    left: 0;
    right: 0;
    margin-left: auto;
    margin-right: auto;
    top: 55%;
    max-width: 65%;
    color: #888;
    .lvca-dark-bg & {
      color: #909090;
    }
    }
  .lvca-percentage {
    span {
      position: absolute;
      top: 25%;
      left: 0;
      right: 0;
      margin-left: auto;
      margin-right: auto;
      font-size: 60px;
      line-height: 60px;
      font-weight: 300;
      text-align: center;
      color: #333;
      font-weight: bolder;
      .lvca-dark-bg & {
        color: #e5e5e5;
      }
      }
    sup {
      font-size: 18px;
      vertical-align: super;
      }
    }
  &.dark-bg {
    .lvca-label {
      color: #fff;
      }
    .lvca-percentage span {
      color: #eee;
      }
    }
  }

@media only screen and (max-width: 479px) {
  .lvca-piechart canvas {
    margin-bottom: 15px;
    }
  }