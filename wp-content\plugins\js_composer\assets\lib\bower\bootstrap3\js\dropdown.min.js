/*!
 * WPBakery Page Builder v6.0.0 (https://wpbakery.com)
 * Copyright 2011-2023 <PERSON>, WPBakery
 * License: Commercial. More details: http://go.wpbakery.com/licensing
 */

// jscs:disable
// jshint ignore: start

!function($){"use strict";function Dropdown(element){$(element).on("click.bs.dropdown",this.toggle)}var toggle='[data-toggle="dropdown"]';function clearMenus(e){e&&3===e.which||($(".dropdown-backdrop").remove(),$(toggle).each(function(){var $parent=getParent($(this)),relatedTarget={relatedTarget:this};$parent.hasClass("open")&&($parent.trigger(e=$.Event("hide.bs.dropdown",relatedTarget)),e.isDefaultPrevented()||$parent.removeClass("open").trigger("hidden.bs.dropdown",relatedTarget))}))}function getParent($this){var selector=$this.attr("data-target"),selector=(selector=selector||(selector=$this.attr("href"))&&/#[A-Za-z]/.test(selector)&&selector.replace(/.*(?=#[^\s]*$)/,""))&&$(selector);return selector&&selector.length?selector:$this.parent()}Dropdown.VERSION="3.1.1",Dropdown.prototype.toggle=function(e){var $this=$(this);if(!$this.is(".disabled, :disabled")){var $parent=getParent($this),isActive=$parent.hasClass("open");if(clearMenus(),!isActive){"ontouchstart"in document.documentElement&&!$parent.closest(".navbar-nav").length&&$('<div class="dropdown-backdrop"/>').insertAfter($(this)).on("click",clearMenus);isActive={relatedTarget:this};if($parent.trigger(e=$.Event("show.bs.dropdown",isActive)),e.isDefaultPrevented())return;$this.trigger("focus"),$parent.toggleClass("open").trigger("shown.bs.dropdown",isActive)}return!1}},Dropdown.prototype.keydown=function(e){if(/(38|40|27)/.test(e.keyCode)){var $this=$(this);if(e.preventDefault(),e.stopPropagation(),!$this.is(".disabled, :disabled")){var $parent=getParent($this),isActive=$parent.hasClass("open");if(!isActive||27==e.keyCode)return 27==e.which&&$parent.find(toggle).trigger("focus"),$this.trigger("click");isActive=" li:not(.divider):visible a",$this=$parent.find('[role="menu"]'+isActive+', [role="listbox"]'+isActive);$this.length&&($parent=$this.index($this.filter(":focus")),38==e.keyCode&&0<$parent&&$parent--,40==e.keyCode&&$parent<$this.length-1&&$parent++,$this.eq($parent=~$parent?$parent:0).trigger("focus"))}}};var old=$.fn.dropdown;$.fn.dropdown=function(option){return this.each(function(){var $this=$(this),data=$this.data("bs.dropdown");data||$this.data("bs.dropdown",data=new Dropdown(this)),"string"==typeof option&&data[option].call($this)})},$.fn.dropdown.Constructor=Dropdown,$.fn.dropdown.noConflict=function(){return $.fn.dropdown=old,this},$(document).on("click.bs.dropdown.data-api",clearMenus).on("click.bs.dropdown.data-api",".dropdown form",function(e){e.stopPropagation()}).on("click.bs.dropdown.data-api",toggle,Dropdown.prototype.toggle).on("keydown.bs.dropdown.data-api",toggle+', [role="menu"], [role="listbox"]',Dropdown.prototype.keydown)}(jQuery);