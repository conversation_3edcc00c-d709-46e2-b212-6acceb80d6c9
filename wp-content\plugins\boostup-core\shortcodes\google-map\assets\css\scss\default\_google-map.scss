/* ==========================================================================
   Google Map shortcode style - begin
   ========================================================================== */

.mkdf-google-map-holder {
	@include mkdfRelativeHolderLayout();
	
	.mkdf-google-map-direction {
		position: absolute;
		top: 10px;
		left: 10px;
		padding: 0 8px;
		font-size: 13px;
		line-height: 24px;
		color: $default-text-color;
		background-color: #fff;
		z-index: 999;
		box-sizing: border-box;
		
		&:hover {
			color: $default-heading-color;
		}
	}
	
	.mkdf-google-map {
		display: block;
		width: 100%;
		height: 300px;
		
		iframe,
		object,
		embed {
			width: 100%;
			display: block;
		}
		
		img {
			max-width: none;
		}
	}
	
	.mkdf-snazzy-map {
		display: none;
	}
	
	.mkdf-google-map-overlay {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		display: none;
		z-index: 1000;
	}
}

/* ==========================================================================
   Google Map shortcode style - end
   ========================================================================== */