/*!
 * WPBakery Page Builder v6.0.0 (https://wpbakery.com)
 * Copyright 2011-2023 <PERSON>, WPBakery
 * License: Commercial. More details: http://go.wpbakery.com/licensing
 */

// jscs:disable
// jshint ignore: start

!function(r){"use strict";var i={theme:"fip-vc-theme-grey",source:!1,emptyIcon:!0,emptyIconValue:"",iconsPerPage:20,hasSearch:!0,searchSource:!1,useAttribute:!1,attributeName:"data-icon",convertToHex:!0,allCategoryText:"From all categories",unCategorizedText:"Uncategorized",iconDownClass:"fip-icon-down-dir",iconUpClass:"fip-icon-up-dir",iconLeftClass:"fip-icon-left-dir",iconRightClass:"fip-icon-right-dir",iconSearchClass:"fip-icon-search",iconCancelClass:"fip-icon-cancel",iconSpinClass:"fip-icon-spin3",iconBlockClass:"fip-icon-block",searchPlaceholder:"Search Icon",mainClass:"vc-icons-selector"};function s(t,e){this.element=r(t),this.settings=r.extend({},i,e),this.settings.emptyIcon&&this.settings.iconsPerPage--,this.iconPicker=r("<div/>",{class:this.settings.mainClass,style:"position: relative",html:'<div class="selector"><span class="selected-icon"><i class="fip-block-icon '+this.settings.iconBlockClass+'"></i></span><span class="selector-button"><i class="'+this.settings.iconDownClass+'"></i></span></div><div class="selector-popup" style="display: none;">'+(this.settings.hasSearch?'<div class="selector-search"><input type="text" name="" value="" placeholder="'+this.settings.searchPlaceholder+'" class="icons-search-input"/><i class="'+this.settings.iconSearchClass+'"></i></div>':"")+'<div class="selector-category"><select name="" class="icon-category-select" style="display: none"></select></div><div class="fip-icons-container"></div><div class="selector-footer" style="display:none;"><span class="selector-pages">1/2</span><span class="selector-arrows"><span class="selector-arrow-left" style="display:none;"><i class="'+this.settings.iconLeftClass+'"></i></span><span class="selector-arrow-right"><i class="'+this.settings.iconRightClass+'"></i></span></span></div></div>'}),this.iconContainer=this.iconPicker.find(".fip-icons-container"),this.searchIcon=this.iconPicker.find(".selector-search i"),this.iconsSearched=[],this.isSearch=!1,this.totalPage=1,this.currentPage=1,this.currentIcon=!1,this.initialized=!1,this.iconsPaged=!1,this.iconsCount=0,this.open=!1,this.searchValues=[],this.availableCategoriesSearch=[],this.triggerEvent=null,this.backupSource=[],this.backupSearch=[],this.isCategorized=!1,this.selectCategory=this.iconPicker.find(".icon-category-select"),this.selectedCategory=!1,this.availableCategories=[],this.unCategorizedKey=null,this.quickInit()}s.prototype={quickInit:function(){var t=!0;this.iconPicker.addClass(this.settings.theme),this.iconPicker.css({left:-9999}).appendTo("body");var e=this.iconPicker.outerHeight(),i=this.iconPicker.outerWidth();if(this.iconPicker.css({left:""}),this.element.before(this.iconPicker),this.element.css({visibility:"hidden",top:0,position:"relative",zIndex:"-1",left:"-"+i+"px",display:"none",height:e+"px",width:i+"px",padding:"0",margin:"0 -"+i+"px 0 0",border:"0 none",verticalAlign:"top"}).hide(),!this.element.is("select")){var s=function(){for(var t=3,e=document.createElement("div"),i=e.all||[];e.innerHTML="\x3c!--[if gt IE "+ ++t+"]><br><![endif]--\x3e",i[0];);return 4<t?t:!t}(),n=document.createElement("div");this.triggerEvent=9!==s&&"oninput"in n?["input","keyup"]:["keyup"]}this.setSelectedIcon(this.element.val()),this.selectCategory.on("change keyup",r.proxy(function(t){if(!1===this.isCategorized)return!1;var e=r(t.currentTarget),i=e.val();if("all"===e.val())this.settings.source=this.backupSource,this.searchValues=this.backupSearch;else{var s=parseInt(i,10);this.availableCategories[s]&&(this.settings.source=this.availableCategories[s],this.searchValues=this.availableCategoriesSearch[s])}this.resetSearch(),this.loadIcons()},this)),this.iconPicker.find(".selector-button").on("click",r.proxy(function(){!this.open&&t&&(t=!1,this.initCategories()),this.toggleIconSelector()},this)),this.iconPicker.find(".selector-arrow-right").on("click",r.proxy(function(t){this.currentPage<this.totalPage&&(this.iconPicker.find(".selector-arrow-left").show(),this.currentPage=this.currentPage+1,this.renderIconContainer(),this.renderIcons()),this.currentPage===this.totalPage&&r(t.currentTarget).hide()},this)),this.iconPicker.find(".selector-arrow-left").on("click",r.proxy(function(t){1<this.currentPage&&(this.iconPicker.find(".selector-arrow-right").show(),this.currentPage=this.currentPage-1,this.renderIconContainer(),this.renderIcons()),1===this.currentPage&&r(t.currentTarget).hide()},this)),this.iconPicker.find(".icons-search-input").on("keyup",r.proxy(function(t){var i=r(t.currentTarget).val();""!==i?(this.searchIcon.removeClass(this.settings.iconSearchClass),this.searchIcon.addClass(this.settings.iconCancelClass),this.isSearch=!0,this.currentPage=1,this.iconsSearched=[],r.grep(this.searchValues,r.proxy(function(t,e){if(0<=t.toLowerCase().search(i.toLowerCase()))return this.iconsSearched[this.iconsSearched.length]=this.settings.source[e],!0},this)),this.iconsSearched=this.iconsSearched.filter(this.getOnlyUnique),this.renderIconContainer(),this.renderIcons()):this.resetSearch()},this)),this.iconPicker.find(".selector-search i").on("click",r.proxy(function(){this.iconPicker.find(".icons-search-input").focus(),this.resetSearch()},this)),this.iconContainer.on("click",".fip-box",r.proxy(function(t){this.setSelectedIcon(r(t.currentTarget).find("i").attr("data-fip-value")),this.toggleIconSelector()},this)),this.iconPicker.on("click",function(t){return t.stopPropagation(),!1}),r("html").on("click",r.proxy(function(){this.open&&this.toggleIconSelector()},this))},init:function(){this.iconPicker.addClass(this.settings.theme),this.iconPicker.css({left:-9999}).appendTo("body");var t=this.iconPicker.outerHeight(),e=this.iconPicker.outerWidth();if(this.iconPicker.css({left:""}),this.element.before(this.iconPicker),this.element.css({visibility:"hidden",top:0,position:"relative",zIndex:"-1",left:"-"+e+"px",display:"none",height:t+"px",width:e+"px",padding:"0",margin:"0 -"+e+"px 0 0",border:"0 none",verticalAlign:"top"}).hide(),!this.element.is("select")){var i=function(){for(var t=3,e=document.createElement("div"),i=e.all||[];e.innerHTML="\x3c!--[if gt IE "+ ++t+"]><br><![endif]--\x3e",i[0];);return 4<t?t:!t}(),s=document.createElement("div");this.triggerEvent=9!==i&&"oninput"in s?["input","keyup"]:["keyup"]}this.initCategories(),this.selectCategory.on("change keyup",r.proxy(function(t){if(!1===this.isCategorized)return!1;var e=r(t.currentTarget),i=e.val();if("all"===e.val())this.settings.source=this.backupSource,this.searchValues=this.backupSearch;else{var s=parseInt(i,10);this.availableCategories[s]&&(this.settings.source=this.availableCategories[s],this.searchValues=this.availableCategoriesSearch[s])}this.resetSearch(),this.loadIcons()},this)),this.iconPicker.find(".selector-button").on("click",r.proxy(function(){this.toggleIconSelector()},this)),this.iconPicker.find(".selector-arrow-right").on("click",r.proxy(function(t){this.currentPage<this.totalPage&&(this.iconPicker.find(".selector-arrow-left").show(),this.currentPage=this.currentPage+1,this.renderIconContainer(),this.renderIcons()),this.currentPage===this.totalPage&&r(t.currentTarget).hide()},this)),this.iconPicker.find(".selector-arrow-left").on("click",r.proxy(function(t){1<this.currentPage&&(this.iconPicker.find(".selector-arrow-right").show(),this.currentPage=this.currentPage-1,this.renderIconContainer(),this.renderIcons()),1===this.currentPage&&r(t.currentTarget).hide()},this)),this.iconPicker.find(".icons-search-input").on("keyup",r.proxy(function(t){var i=r(t.currentTarget).val();""!==i?(this.searchIcon.removeClass(this.settings.iconSearchClass),this.searchIcon.addClass(this.settings.iconCancelClass),this.isSearch=!0,this.currentPage=1,this.iconsSearched=[],r.grep(this.searchValues,r.proxy(function(t,e){if(0<=t.toLowerCase().search(i.toLowerCase()))return this.iconsSearched[this.iconsSearched.length]=this.settings.source[e],!0},this)),this.iconsSearched=this.iconsSearched.filter(this.getOnlyUnique),this.renderIconContainer(),this.renderIcons()):this.resetSearch()},this)),this.iconPicker.find(".selector-search i").on("click",r.proxy(function(){this.iconPicker.find(".icons-search-input").focus(),this.resetSearch()},this)),this.iconContainer.on("click",".fip-box",r.proxy(function(t){this.setSelectedIcon(r(t.currentTarget).find("i").attr("data-fip-value")),this.toggleIconSelector()},this)),this.iconPicker.on("click",function(t){return t.stopPropagation(),!1}),r("html").on("click",r.proxy(function(){this.open&&this.toggleIconSelector()},this))},initCategories:function(){!this.settings.source&&this.element.is("select")?(this.settings.source=[],this.settings.searchSource=[],this.element.find("optgroup").length?(this.isCategorized=!0,this.element.find("optgroup").each(r.proxy(function(t,e){var n=this.availableCategories.length,i=r("<option />");i.attr("value",n),i.html(r(e).attr("label")),this.selectCategory.append(i),this.availableCategories[n]=[],this.availableCategoriesSearch[n]=[],r(e).find("option").each(r.proxy(function(t,e){var i=r(e).val(),s=r(e).html();i&&i!==this.settings.emptyIconValue&&(this.settings.source.push(i),this.availableCategories[n].push(i),this.searchValues.push(s),this.availableCategoriesSearch[n].push(s))},this))},this)),this.element.find("> option").length&&this.element.find("> option").each(r.proxy(function(t,e){var i=r(e).val(),s=r(e).html();if(!i||""===i||i==this.settings.emptyIconValue)return!0;null===this.unCategorizedKey&&(this.unCategorizedKey=this.availableCategories.length,this.availableCategories[this.unCategorizedKey]=[],this.availableCategoriesSearch[this.unCategorizedKey]=[],r("<option />").attr("value",this.unCategorizedKey).html(this.settings.unCategorizedText).appendTo(this.selectCategory)),this.settings.source.push(i),this.availableCategories[this.unCategorizedKey].push(i),this.searchValues.push(s),this.availableCategoriesSearch[this.unCategorizedKey].push(s)},this))):this.element.find("option").each(r.proxy(function(t,e){var i=r(e).val(),s=r(e).html();i&&(this.settings.source.push(i),this.searchValues.push(s))},this)),this.backupSource=this.settings.source.slice(0),this.backupSearch=this.searchValues.slice(0),this.loadCategories()):this.initSourceIndex(),this.loadIcons()},getOnlyUnique:function(t,e,i){return i.indexOf(t)===e},initSourceIndex:function(){if("object"==typeof this.settings.source){if(r.isArray(this.settings.source))this.isCategorized=!1,this.selectCategory.html("").hide(),this.settings.source=r.map(this.settings.source,function(t,e){return"function"==typeof t.toString?t.toString():t}),r.isArray(this.settings.searchSource)?this.searchValues=r.map(this.settings.searchSource,function(t,e){return"function"==typeof t.toString?t.toString():t}):this.searchValues=this.settings.source.slice(0);else{var t=r.extend(!0,{},this.settings.source);for(var e in this.settings.source=[],this.searchValues=[],this.availableCategoriesSearch=[],this.selectedCategory=!1,this.availableCategories=[],this.unCategorizedKey=null,this.isCategorized=!0,this.selectCategory.html(""),t){var i=this.availableCategories.length,s=r("<option />");for(var n in s.attr("value",i),s.html(e),this.selectCategory.append(s),this.availableCategories[i]=[],this.availableCategoriesSearch[i]=[],t[e]){var c=t[e][n],o=this.settings.searchSource&&this.settings.searchSource[e]&&this.settings.searchSource[e][n]?this.settings.searchSource[e][n]:c;"function"==typeof c.toString&&(c=c.toString()),c&&c!==this.settings.emptyIconValue&&(this.settings.source.push(c),this.availableCategories[i].push(c),this.searchValues.push(o),this.availableCategoriesSearch[i].push(o))}}}this.backupSource=this.settings.source.slice(0),this.backupSearch=this.searchValues.slice(0),this.loadCategories()}},loadCategories:function(){!1!==this.isCategorized&&(r('<option value="all">'+this.settings.allCategoryText+"</option>").prependTo(this.selectCategory),this.selectCategory.show().val("all").trigger("change"))},loadIcons:function(){this.iconContainer.html('<i class="'+this.settings.iconSpinClass+' animate-spin loading"></i>'),this.settings.source instanceof Array&&(this.renderIconContainer(),this.renderIcons(),this.setContainerSelectedItems())},renderIconContainer:function(){var t,e=[];if(e=this.isSearch?this.iconsSearched:this.settings.source,this.iconsCount=e.length,this.totalPage=Math.ceil(this.iconsCount/this.settings.iconsPerPage),1<this.totalPage?this.iconPicker.find(".selector-footer").show():this.iconPicker.find(".selector-footer").hide(),this.iconPicker.find(".selector-pages").html(this.currentPage+"/"+this.totalPage+" <em>("+this.iconsCount+")</em>"),t=(this.currentPage-1)*this.settings.iconsPerPage,this.settings.emptyIcon)this.iconContainer.html('<span class="fip-box"><i class="fip-block-icon '+this.settings.iconBlockClass+'" data-fip-value="'+this.settings.iconBlockClass+'"></i></span>');else{if(e.length<1)return void this.iconContainer.html('<span class="icons-picker-error"><i class="fip-block-icon '+this.settings.iconBlockClass+'" data-fip-value="'+this.settings.iconBlockClass+'"></i></span>');this.iconContainer.html("")}e=e.slice(t,t+this.settings.iconsPerPage),this.iconsPaged=e},setContainerSelectedItems:function(){this.settings.emptyIcon||this.element.val()&&-1!==r.inArray(this.element.val(),this.settings.source)?-1===r.inArray(this.element.val(),this.settings.source)?this.setSelectedIcon():this.setSelectedIcon(this.element.val()):this.setSelectedIcon(this.iconsPaged[0])},setHighlightedIcon:function(){this.iconContainer.find(".current-icon").removeClass("current-icon"),this.currentIcon&&this.iconContainer.find('[data-fip-value="'+this.currentIcon+'"]').parent("span").addClass("current-icon")},setSelectedIcon:function(t){if(t===this.settings.iconBlockClass&&(t=""),this.settings.useAttribute?t?this.iconPicker.find(".selected-icon").html("<i "+this.settings.attributeName+'="'+(this.settings.convertToHex?"&#x"+parseInt(t,10).toString(16)+";":t)+'"></i>'):this.iconPicker.find(".selected-icon").html('<i class="fip-block-icon '+this.settings.iconBlockClass+'"></i>'):this.iconPicker.find(".selected-icon").html('<i class="'+(t||"fip-block-icon "+this.settings.iconBlockClass)+'"></i>'),this.element.val(""===t?this.settings.emptyIconValue:t).trigger("change"),null!==this.triggerEvent)for(var e in this.triggerEvent)this.element.trigger(this.triggerEvent[e]);this.currentIcon=t,this.setHighlightedIcon()},toggleIconSelector:function(){this.open=this.open?0:1,this.iconPicker.find(".selector-popup").slideToggle(300),this.iconPicker.find(".selector-button i").toggleClass(this.settings.iconDownClass),this.iconPicker.find(".selector-button i").toggleClass(this.settings.iconUpClass),this.open&&(this.iconPicker.find(".icons-search-input").focus().select(),this.initialized||(this.renderIconContainer(),this.renderIcons(),this.initialized=!0))},renderIcons:function(){for(var t=0;t<this.iconsPaged.length;t++){var i=this.iconsPaged[t],s=i;r.grep(this.settings.source,r.proxy(function(t,e){return t===i&&(s=this.searchValues[e],!0)},this)),r("<span/>",{html:'<i data-fip-value="'+i+'" '+(this.settings.useAttribute?this.settings.attributeName+'="'+(this.settings.convertToHex?"&#x"+parseInt(i,10).toString(16)+";":i)+'"':'class="'+i+'"')+"></i>",class:"fip-box",title:s}).appendTo(this.iconContainer)}this.setContainerSelectedItems()},resetSearch:function(){this.iconPicker.find(".icons-search-input").val(""),this.searchIcon.removeClass(this.settings.iconCancelClass),this.searchIcon.addClass(this.settings.iconSearchClass),this.iconPicker.find(".selector-arrow-left").hide(),this.currentPage=1,this.isSearch=!1,this.renderIconContainer(),this.renderIcons(),1<this.totalPage&&this.iconPicker.find(".selector-arrow-right").show()}},r.fn.vcFontIconPicker=function(e){return this.each(function(){r.data(this,"vcFontIconPicker")||r.data(this,"vcFontIconPicker",new s(this,e))}),this.setIcons=r.proxy(function(t,e){void 0===t&&(t=!1),void 0===e&&(e=!1),this.each(function(){r.data(this,"vcFontIconPicker").settings.source=t,r.data(this,"vcFontIconPicker").settings.searchSource=e,r.data(this,"vcFontIconPicker").initSourceIndex(),r.data(this,"vcFontIconPicker").resetSearch(),r.data(this,"vcFontIconPicker").loadIcons()})},this),this.destroyPicker=r.proxy(function(){this.each(function(){r.data(this,"vcFontIconPicker")&&(r.data(this,"vcFontIconPicker").iconPicker.remove(),r.data(this,"vcFontIconPicker").element.css({visibility:"",top:"",position:"",zIndex:"",left:"",display:"block",height:"",width:"",padding:"",margin:"",border:"",verticalAlign:""}).show(),r.removeData(this,"vcFontIconPicker"))})},this),this.refreshPicker=r.proxy(function(t){t||(t=e),this.destroyPicker(),this.each(function(){r.data(this,"vcFontIconPicker")||r.data(this,"vcFontIconPicker",new s(this,t))})},this),this}}(jQuery);