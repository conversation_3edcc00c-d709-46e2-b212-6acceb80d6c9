@import "../../../../assets/css/lvca-lib";

.lvca-accordion {

  .lvca-panel-title {
    display: block;
    cursor: pointer;
    }
  .lvca-panel-content {
    display: none;
    overflow: hidden;
    }
  }

.lvca-accordion.lvca-style1, .lvca-accordion.lvca-style3 {
  .lvca-panel {
    .lvca-panel-title {
      transition: all .3s ease-in-out 0s;
      &:after {
        @include lvca-icon-font();
        position: absolute;
        content: "\e912";
        right: 30px;
        top: 26px;
        font-size: 14px;
        line-height: 1;
        color: #666;
        font-weight: 700;
        transition: all .3s ease-in-out 0s;
        }
      }
    &.lvca-active {
      .lvca-panel-title:after {
        transform: rotate(45deg);
        }
      }
    }
  }

.lvca-accordion.lvca-style2 {
  .lvca-panel {
    .lvca-panel-title {
      transition: all .3s ease-in-out 0s;
      &:after {
        @include lvca-icon-font();
        position: absolute;
        right: 30px;
        top: 25px;
        content: "\e913";
        color: #999;
        font-size: 18px;
        line-height: 1;
        -webkit-transition: color .3s ease-in 0s, transform .3s ease-in-out 0s;
        transition: color .3s ease-in 0s, transform .3s ease-in-out 0s;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        }
      }
    &.lvca-active {
      .lvca-panel-title:after {
        transform: rotate(90deg);
        }
      }
    }
  }
/* ----- Style 1 ------ */

.lvca-accordion.lvca-style1 {
  .lvca-panel {
    margin: 20px 0 0;
    background: #eee;
    border-radius: 5px;
    overflow: hidden;
    .lvca-panel-title {
      position: relative;
      display: block;
      padding: 20px 50px 20px 30px;
      font-size: 18px;
      line-height: 26px;
      letter-spacing: 0;
      font-weight: bold;
      color: #666;
      margin: 0;
      }
    .lvca-panel-content {
      background: #f8f8f8;
      padding: 30px 30px;
      }
    &:hover {
      .lvca-panel-title {
        background: #e2e2e2;
        }
      }
    &.lvca-active {
      .lvca-panel-title {
        color: #333;
        background: #e2e2e2;
        }
      .lvca-panel-title:after {
        color: #333;
        }
      }
    }
  }
/* ----- Style 2 ------ */

.lvca-accordion.lvca-style2 {
  .lvca-panel {
    margin: 20px 0 0;
    color: #333;
    -webkit-transition: color .3s;
    transition: color .3s;
    position: relative;
    .lvca-panel-title {
      position: relative;
      display: block;
      border-radius: 5px;
      border: 1px solid #dcdcdc;
      overflow: hidden;
      padding: 20px 50px 20px 30px;
      font-size: 18px;
      line-height: 26px;
      letter-spacing: 0;
      font-weight: bold;
      color: #666;
      margin: 0;
      transition: all .3s ease-in-out 0s;
      z-index: 1;
      &:before {
        position: absolute;
        content: "";
        left: 0;
        bottom: 0;
        height: 0;
        width: 100%;
        background: #4c5053;
        -webkit-transition: height .3s cubic-bezier(.77, 0, .175, 1);
        transition: height .3s cubic-bezier(.77, 0, .175, 1);
        z-index: -1;
        }
      }
    .lvca-panel-content {
      padding: 20px 30px;
      border-color: #dcdcdc;
      border-style: solid;
      border-width: 0 1px 1px 1px;
      border-radius: 0 0 5px 5px;
      }
    &:hover {
      .lvca-panel-title, .lvca-panel-title:after {
        color: #fff;
        border-color: #333;
        }
      .lvca-panel-title:before {
        height: 100%;
        }
      }
    &.lvca-active {
      .lvca-panel-title {
        background: #4c5053;
        color: #fff;
        border-radius: 5px 5px 0 0;
        border-color: #333;
        }
      .lvca-panel-title:after {
        color: #fff;
        }
      }
    }
  }

.lvca-dark-bg .lvca-accordion.lvca-style2 {
  .lvca-panel {
    .lvca-panel-title {
      color: #888;
      border-color: #404040;
      &:before {
        background: #e5e5e5;
      }
    }
    &:hover {
      .lvca-panel-title {
        color: #333;
        }
      .lvca-panel-title:after {
        color: #666;
        }
      }
    &.lvca-active {
      .lvca-panel-title {
        background: #e5e5e5;
        color: #333;
        border-color: #e5e5e5;
        }
      .lvca-panel-title:after {
        color: #666;
        }
      }

    .lvca-panel-content {
      border-color: #404040;
      color: #909090;
      h1, h2, h3, h4, h5, h6 {
        color: #e5e5e5;
        }
      }
  }
}
/* ----- Style 3 ------ */

.lvca-accordion.lvca-style3 {
  .lvca-panel {
    margin: 0;
    border-bottom: 1px solid #dcdcdc;
    &:first-child {
      border-top: 1px solid #dcdcdc;
      }
    .lvca-panel-title {
      position: relative;
      display: block;
      padding: 20px 50px 20px 5px;
      font-size: 14px;
      line-height: 22px;
      letter-spacing: 1px;
      font-weight: bold;
      text-transform: uppercase;
      color: #666;
      margin: 0;
      &:after {
        top: 24px;
        }
      }
    .lvca-panel-content {
      padding: 10px 50px 30px 5px;
      }
    &:hover {
      .lvca-panel-title {
        color: #333;
        }
      }
    &.lvca-active {
      .lvca-panel-title {
        color: #333;
        }
      .lvca-panel-title:after {
        color: #333;
        }
      }
    }
  }

.lvca-dark-bg .lvca-accordion.lvca-style3 {
  .lvca-panel {
      border-color: #404040;
    .lvca-panel-title {
      color: #b0b0b0;
      &:after {
        color: #888;
      }
    }
    &:hover {
      .lvca-panel-title {
        color: #eaeaea;
        }
      .lvca-panel-title:after {
        color: #aaa;
        }
      }
    &.lvca-active {
      .lvca-panel-title {
        color: #eaeaea;
        }
      .lvca-panel-title:after {
        color: #aaa;
        }
      }

    .lvca-panel-content {
      color: #909090;
      h1, h2, h3, h4, h5, h6 {
        color: #e5e5e5;
        }
      }
  }
}