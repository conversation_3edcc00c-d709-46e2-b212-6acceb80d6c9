/* ==========================================================================
   Global partials
   ========================================================================== */
@-webkit-keyframes animate-btn-line {
  0% {
    -webkit-transform-origin: 0 50%;
    transform-origin: 0 50%;
    -webkit-transform: scaleX(0);
    transform: scaleX(0);
  }
  100% {
    -webkit-transform-origin: 0 50%;
    transform-origin: 0 50%;
    -webkit-transform: scaleX(1);
    transform: scaleX(1);
  }
}
@keyframes animate-btn-line {
  0% {
    -webkit-transform-origin: 0 50%;
    transform-origin: 0 50%;
    -webkit-transform: scaleX(0);
    transform: scaleX(0);
  }
  100% {
    -webkit-transform-origin: 0 50%;
    transform-origin: 0 50%;
    -webkit-transform: scaleX(1);
    transform: scaleX(1);
  }
}

@-webkit-keyframes mkdfPulsebig {
  0% {
    -webkit-box-shadow: 0 0 0 0 currentColor;
    box-shadow: 0 0 0 0 currentColor;
  }
  70% {
    -webkit-box-shadow: 0 0 0 20px rgba(234, 61, 86, 0);
    box-shadow: 0 0 0 20px rgba(234, 61, 86, 0);
  }
  100% {
    -webkit-box-shadow: 0 0 0 0 rgba(234, 61, 86, 0);
    box-shadow: 0 0 0 0 rgba(234, 61, 86, 0);
  }
}

@keyframes mkdfPulsebig {
  0% {
    -webkit-box-shadow: 0 0 0 0 currentColor;
    box-shadow: 0 0 0 0 currentColor;
  }
  70% {
    -webkit-box-shadow: 0 0 0 20px rgba(234, 61, 86, 0);
    box-shadow: 0 0 0 20px rgba(234, 61, 86, 0);
  }
  100% {
    -webkit-box-shadow: 0 0 0 0 rgba(234, 61, 86, 0);
    box-shadow: 0 0 0 0 rgba(234, 61, 86, 0);
  }
}

@-webkit-keyframes mkdfPulsesmall {
  0% {
    -webkit-box-shadow: 0 0 0 0 currentColor;
  }
  70% {
    -webkit-box-shadow: 0 0 0 10px rgba(234, 61, 86, 0.15);
  }
  100% {
    -webkit-box-shadow: 0 0 0 0 rgba(234, 61, 86, 0);
  }
}

@keyframes mkdfPulsesmall {
  0% {
    -webkit-box-shadow: 0 0 0 0 currentColor;
    box-shadow: 0 0 0 0 currentColor;
  }
  70% {
    -webkit-box-shadow: 0 0 0 10px rgba(234, 61, 86, 0);
    box-shadow: 0 0 0 10px rgba(234, 61, 86, 0);
  }
  100% {
    -webkit-box-shadow: 0 0 0 0 rgba(234, 61, 86, 0);
    box-shadow: 0 0 0 0 rgba(234, 61, 86, 0);
  }
}

@-webkit-keyframes mkdfPulsesmallfirst {
  0% {
    -webkit-box-shadow: 0 0 0 0 #ea3d56;
  }
  70% {
    -webkit-box-shadow: 0 0 0 10px rgba(234, 61, 86, 0.15);
  }
  100% {
    -webkit-box-shadow: 0 0 0 0 rgba(234, 61, 86, 0);
  }
}

@keyframes mkdfPulsesmallfirst {
  0% {
    -webkit-box-shadow: 0 0 0 0 #ea3d56;
    box-shadow: 0 0 0 0 #ea3d56;
  }
  70% {
    -webkit-box-shadow: 0 0 0 10px rgba(234, 61, 86, 0);
    box-shadow: 0 0 0 10px rgba(234, 61, 86, 0);
  }
  100% {
    -webkit-box-shadow: 0 0 0 0 rgba(234, 61, 86, 0);
    box-shadow: 0 0 0 0 rgba(234, 61, 86, 0);
  }
}

/* ==========================================================================
   Shortcodes responsive styles
   ========================================================================== */
/* ==========================================================================
   Button shortcode responsive style - begin
   ========================================================================== */
@media only screen and (max-width: 1024px) {
  .mkdf-btn.mkdf-btn-large {
    padding: 13px 34px 11px;
  }
  .mkdf-btn.mkdf-btn-huge {
    padding: 21px 60px 16px;
    font-size: 14px;
  }
}

/* ==========================================================================
   Button shortcode responsive style - end
   ========================================================================== */
/* ==========================================================================
   Call To Action shortcode responsive style - begin
   ========================================================================== */
@media only screen and (min-width: 1201px) and (max-width: 1300px) {
  .mkdf-call-to-action-holder .mkdf-grid {
    width: 1100px;
  }
}

@media only screen and (max-width: 1200px) {
  .mkdf-call-to-action-holder.mkdf-three-quarters-columns .mkdf-cta-text-holder, .mkdf-call-to-action-holder.mkdf-four-fifths-columns .mkdf-cta-text-holder {
    width: 66.66666666666667%;
  }
  .mkdf-call-to-action-holder.mkdf-three-quarters-columns .mkdf-cta-button-holder, .mkdf-call-to-action-holder.mkdf-four-fifths-columns .mkdf-cta-button-holder {
    width: 33.33333333333333%;
  }
}

@media only screen and (max-width: 768px) {
  .mkdf-call-to-action-holder.mkdf-normal-layout .mkdf-cta-inner,
  .mkdf-call-to-action-holder.mkdf-normal-layout .mkdf-cta-text-holder,
  .mkdf-call-to-action-holder.mkdf-normal-layout .mkdf-cta-button-holder {
    display: block;
  }
  .mkdf-call-to-action-holder.mkdf-normal-layout .mkdf-cta-button-holder {
    margin: 28px 0 0;
    text-align: initial;
  }
  .mkdf-call-to-action-holder.mkdf-two-halves-columns .mkdf-cta-text-holder,
  .mkdf-call-to-action-holder.mkdf-two-halves-columns .mkdf-cta-button-holder, .mkdf-call-to-action-holder.mkdf-two-thirds-columns .mkdf-cta-text-holder,
  .mkdf-call-to-action-holder.mkdf-two-thirds-columns .mkdf-cta-button-holder, .mkdf-call-to-action-holder.mkdf-three-quarters-columns .mkdf-cta-text-holder,
  .mkdf-call-to-action-holder.mkdf-three-quarters-columns .mkdf-cta-button-holder, .mkdf-call-to-action-holder.mkdf-four-fifths-columns .mkdf-cta-text-holder,
  .mkdf-call-to-action-holder.mkdf-four-fifths-columns .mkdf-cta-button-holder {
    width: 100%;
  }
}

/* ==========================================================================
   Call To Action shortcode responsive style - end
   ========================================================================== */
/* ==========================================================================
   Countdown shortcode responsive style - begin
   ========================================================================== */
@media only screen and (max-width: 1200px) {
  .mkdf-countdown .countdown-row .countdown-section .countdown-amount {
    font-size: 60px;
  }
}

@media only screen and (max-width: 768px) {
  .mkdf-countdown .countdown-row.countdown-show4 .countdown-section, .mkdf-countdown .countdown-row.countdown-show5 .countdown-section, .mkdf-countdown .countdown-row.countdown-show6 .countdown-section {
    width: 33.33333333333333%;
  }
  .mkdf-countdown .countdown-row .countdown-section .countdown-amount {
    font-size: 50px;
  }
}

@media only screen and (max-width: 680px) {
  .mkdf-countdown .countdown-row .countdown-section .countdown-amount {
    font-size: 40px;
  }
}

/* ==========================================================================
   Countdown shortcode responsive style - end
   ========================================================================== */
/* ==========================================================================
   Custom Font shortcode responsive styles - begin
   ========================================================================== */
@media only screen and (max-width: 768px) {
  .mkdf-custom-font-holder.mkdf-disable-title-break br {
    display: none;
  }
}

/* ==========================================================================
   Custom Font shortcode responsive styles - end
   ========================================================================== */
/* ==========================================================================
   Elements Holder shortcode responsive style - begin
   ========================================================================== */
@media only screen and (max-width: 1366px) {
  .mkdf-elements-holder.mkdf-responsive-mode-1366.mkdf-two-columns .mkdf-eh-item {
    width: 100%;
    height: auto;
    display: inline-block;
  }
  .mkdf-elements-holder.mkdf-responsive-mode-1366.mkdf-three-columns .mkdf-eh-item {
    width: 100%;
    height: auto;
    display: inline-block;
  }
  .mkdf-elements-holder.mkdf-responsive-mode-1366.mkdf-four-columns .mkdf-eh-item {
    width: 100%;
    height: auto;
    display: inline-block;
  }
  .mkdf-elements-holder.mkdf-responsive-mode-1366.mkdf-five-columns .mkdf-eh-item {
    width: 100%;
    height: auto;
    display: inline-block;
  }
  .mkdf-elements-holder.mkdf-responsive-mode-1366.mkdf-six-columns .mkdf-eh-item {
    width: 100%;
    height: auto;
    display: inline-block;
  }
  .mkdf-elements-holder.mkdf-responsive-mode-1366.mkdf-one-column-alignment-left .mkdf-eh-item .mkdf-eh-item-content {
    text-align: left;
  }
  .mkdf-elements-holder.mkdf-responsive-mode-1366.mkdf-one-column-alignment-right .mkdf-eh-item .mkdf-eh-item-content {
    text-align: right;
  }
  .mkdf-elements-holder.mkdf-responsive-mode-1366.mkdf-one-column-alignment-center .mkdf-eh-item .mkdf-eh-item-content {
    text-align: center;
  }
}

@media only screen and (max-width: 1024px) {
  .mkdf-elements-holder.mkdf-responsive-mode-1024.mkdf-two-columns .mkdf-eh-item {
    width: 100%;
    height: auto;
    display: inline-block;
  }
  .mkdf-elements-holder.mkdf-responsive-mode-1024.mkdf-three-columns .mkdf-eh-item {
    width: 100%;
    height: auto;
    display: inline-block;
  }
  .mkdf-elements-holder.mkdf-responsive-mode-1024.mkdf-four-columns .mkdf-eh-item {
    width: 100%;
    height: auto;
    display: inline-block;
  }
  .mkdf-elements-holder.mkdf-responsive-mode-1024.mkdf-five-columns .mkdf-eh-item {
    width: 100%;
    height: auto;
    display: inline-block;
  }
  .mkdf-elements-holder.mkdf-responsive-mode-1024.mkdf-six-columns .mkdf-eh-item {
    width: 100%;
    height: auto;
    display: inline-block;
  }
  .mkdf-elements-holder.mkdf-responsive-mode-1024.mkdf-one-column-alignment-left .mkdf-eh-item .mkdf-eh-item-content {
    text-align: left;
  }
  .mkdf-elements-holder.mkdf-responsive-mode-1024.mkdf-one-column-alignment-right .mkdf-eh-item .mkdf-eh-item-content {
    text-align: right;
  }
  .mkdf-elements-holder.mkdf-responsive-mode-1024.mkdf-one-column-alignment-center .mkdf-eh-item .mkdf-eh-item-content {
    text-align: center;
  }
}

@media only screen and (max-width: 768px) {
  .mkdf-elements-holder.mkdf-responsive-mode-768.mkdf-two-columns .mkdf-eh-item {
    width: 100%;
    height: auto;
    display: inline-block;
  }
  .mkdf-elements-holder.mkdf-responsive-mode-768.mkdf-three-columns .mkdf-eh-item {
    width: 100%;
    height: auto;
    display: inline-block;
  }
  .mkdf-elements-holder.mkdf-responsive-mode-768.mkdf-four-columns .mkdf-eh-item {
    width: 100%;
    height: auto;
    display: inline-block;
  }
  .mkdf-elements-holder.mkdf-responsive-mode-768.mkdf-five-columns .mkdf-eh-item {
    width: 100%;
    height: auto;
    display: inline-block;
  }
  .mkdf-elements-holder.mkdf-responsive-mode-768.mkdf-six-columns .mkdf-eh-item {
    width: 100%;
    height: auto;
    display: inline-block;
  }
  .mkdf-elements-holder.mkdf-responsive-mode-768.mkdf-one-column-alignment-left .mkdf-eh-item .mkdf-eh-item-content {
    text-align: left;
  }
  .mkdf-elements-holder.mkdf-responsive-mode-768.mkdf-one-column-alignment-right .mkdf-eh-item .mkdf-eh-item-content {
    text-align: right;
  }
  .mkdf-elements-holder.mkdf-responsive-mode-768.mkdf-one-column-alignment-center .mkdf-eh-item .mkdf-eh-item-content {
    text-align: center;
  }
}

@media only screen and (max-width: 680px) {
  .mkdf-elements-holder.mkdf-responsive-mode-680.mkdf-two-columns .mkdf-eh-item {
    width: 100%;
    height: auto;
    display: inline-block;
  }
  .mkdf-elements-holder.mkdf-responsive-mode-680.mkdf-three-columns .mkdf-eh-item {
    width: 100%;
    height: auto;
    display: inline-block;
  }
  .mkdf-elements-holder.mkdf-responsive-mode-680.mkdf-four-columns .mkdf-eh-item {
    width: 100%;
    height: auto;
    display: inline-block;
  }
  .mkdf-elements-holder.mkdf-responsive-mode-680.mkdf-five-columns .mkdf-eh-item {
    width: 100%;
    height: auto;
    display: inline-block;
  }
  .mkdf-elements-holder.mkdf-responsive-mode-680.mkdf-six-columns .mkdf-eh-item {
    width: 100%;
    height: auto;
    display: inline-block;
  }
  .mkdf-elements-holder.mkdf-responsive-mode-680.mkdf-one-column-alignment-left .mkdf-eh-item .mkdf-eh-item-content {
    text-align: left;
  }
  .mkdf-elements-holder.mkdf-responsive-mode-680.mkdf-one-column-alignment-right .mkdf-eh-item .mkdf-eh-item-content {
    text-align: right;
  }
  .mkdf-elements-holder.mkdf-responsive-mode-680.mkdf-one-column-alignment-center .mkdf-eh-item .mkdf-eh-item-content {
    text-align: center;
  }
}

@media only screen and (max-width: 480px) {
  .mkdf-elements-holder.mkdf-responsive-mode-480.mkdf-two-columns .mkdf-eh-item {
    width: 100%;
    height: auto;
    display: inline-block;
  }
  .mkdf-elements-holder.mkdf-responsive-mode-480.mkdf-three-columns .mkdf-eh-item {
    width: 100%;
    height: auto;
    display: inline-block;
  }
  .mkdf-elements-holder.mkdf-responsive-mode-480.mkdf-four-columns .mkdf-eh-item {
    width: 100%;
    height: auto;
    display: inline-block;
  }
  .mkdf-elements-holder.mkdf-responsive-mode-480.mkdf-five-columns .mkdf-eh-item {
    width: 100%;
    height: auto;
    display: inline-block;
  }
  .mkdf-elements-holder.mkdf-responsive-mode-480.mkdf-six-columns .mkdf-eh-item {
    width: 100%;
    height: auto;
    display: inline-block;
  }
  .mkdf-elements-holder.mkdf-responsive-mode-480.mkdf-one-column-alignment-left .mkdf-eh-item .mkdf-eh-item-content {
    text-align: left;
  }
  .mkdf-elements-holder.mkdf-responsive-mode-480.mkdf-one-column-alignment-right .mkdf-eh-item .mkdf-eh-item-content {
    text-align: right;
  }
  .mkdf-elements-holder.mkdf-responsive-mode-480.mkdf-one-column-alignment-center .mkdf-eh-item .mkdf-eh-item-content {
    text-align: center;
  }
  .mkdf-elements-holder .mkdf-eh-item-content {
    padding: 0 10px;
  }
}

/* ==========================================================================
   Elements Holder shortcode responsive style - end
   ========================================================================== */
/* ==========================================================================
   Google Map shortcode responsive style - begin
   ========================================================================== */
@media only screen and (max-width: 1024px) {
  .mkdf-google-map-overlay {
    display: block;
  }
}

/* ==========================================================================
   Google Map shortcode responsive style - end
   ========================================================================== */
/* ==========================================================================
   Process shortcode responsive style - begin
   ========================================================================== */
@media only screen and (max-width: 1366px) {
  .mkdf-process-holder.mkdf-responsive-1366 .mkdf-mark-horizontal-holder {
    display: none;
  }
  .mkdf-process-holder.mkdf-responsive-1366 .mkdf-mark-vertical-holder {
    display: block;
  }
  .mkdf-process-holder.mkdf-responsive-1366 .mkdf-process-inner {
    position: relative;
    display: inline-block;
    width: 100%;
    vertical-align: middle;
    padding: 0 0 0 76px;
    margin: 0;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
  }
  .mkdf-process-holder.mkdf-responsive-1366 .mkdf-process-item {
    width: 100%;
    float: none;
    padding: 0;
    text-align: inherit;
  }
}

@media only screen and (max-width: 1024px) {
  .mkdf-process-holder.mkdf-responsive-1024 .mkdf-mark-horizontal-holder {
    display: none;
  }
  .mkdf-process-holder.mkdf-responsive-1024 .mkdf-mark-vertical-holder {
    display: block;
  }
  .mkdf-process-holder.mkdf-responsive-1024 .mkdf-process-inner {
    position: relative;
    display: inline-block;
    width: 100%;
    vertical-align: middle;
    padding: 0 0 0 76px;
    margin: 0;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
  }
  .mkdf-process-holder.mkdf-responsive-1024 .mkdf-process-item {
    width: 100%;
    float: none;
    padding: 0;
    text-align: inherit;
  }
}

@media only screen and (max-width: 768px) {
  .mkdf-process-holder.mkdf-responsive-768 .mkdf-mark-horizontal-holder {
    display: none;
  }
  .mkdf-process-holder.mkdf-responsive-768 .mkdf-mark-vertical-holder {
    display: block;
  }
  .mkdf-process-holder.mkdf-responsive-768 .mkdf-process-inner {
    position: relative;
    display: inline-block;
    width: 100%;
    vertical-align: middle;
    padding: 0 0 0 76px;
    margin: 0;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
  }
  .mkdf-process-holder.mkdf-responsive-768 .mkdf-process-item {
    width: 100%;
    float: none;
    padding: 0;
    text-align: inherit;
  }
}

@media only screen and (max-width: 680px) {
  .mkdf-process-holder.mkdf-responsive-680 .mkdf-mark-horizontal-holder {
    display: none;
  }
  .mkdf-process-holder.mkdf-responsive-680 .mkdf-mark-vertical-holder {
    display: block;
  }
  .mkdf-process-holder.mkdf-responsive-680 .mkdf-process-inner {
    position: relative;
    display: inline-block;
    width: 100%;
    vertical-align: middle;
    padding: 0 0 0 76px;
    margin: 0;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
  }
  .mkdf-process-holder.mkdf-responsive-680 .mkdf-process-item {
    width: 100%;
    float: none;
    padding: 0;
    text-align: inherit;
  }
}

@media only screen and (max-width: 480px) {
  .mkdf-process-holder.mkdf-responsive-480 .mkdf-mark-horizontal-holder {
    display: none;
  }
  .mkdf-process-holder.mkdf-responsive-480 .mkdf-mark-vertical-holder {
    display: block;
  }
  .mkdf-process-holder.mkdf-responsive-480 .mkdf-process-inner {
    position: relative;
    display: inline-block;
    width: 100%;
    vertical-align: middle;
    padding: 0 0 0 76px;
    margin: 0;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
  }
  .mkdf-process-holder.mkdf-responsive-480 .mkdf-process-item {
    width: 100%;
    float: none;
    padding: 0;
    text-align: inherit;
  }
}

/* ==========================================================================
   Process shortcode responsive style - end
   ========================================================================== */
/* ==========================================================================
   Roadmap shortcode style - begin
   ========================================================================== */
@media only screen and (max-width: 680px) {
  .mkdf-roadmap {
    padding-top: 100px !important;
  }
  .mkdf-roadmap .mkdf-roadmap-item.mkdf-roadmap-item-above .mkdf-roadmap-item-stage-title-holder {
    top: auto;
    bottom: 35px;
  }
  .mkdf-roadmap .mkdf-roadmap-item.mkdf-roadmap-item-above .mkdf-roadmap-item-content-holder {
    top: 75px;
    bottom: auto;
  }
  .mkdf-roadmap .mkdf-roadmap-item.mkdf-roadmap-item-above .mkdf-roadmap-item-content-holder:after {
    top: auto;
    bottom: 100%;
  }
}

/* ==========================================================================
   Roadmap shortcode style - end
   ========================================================================== */
/* ==========================================================================
   Section Title shortcode responsive styles - begin
   ========================================================================== */
@media only screen and (max-width: 1024px) {
  .mkdf-section-title-holder.mkdf-st-two-columns {
    padding: 0 !important;
  }
}

@media only screen and (max-width: 768px) {
  .mkdf-section-title-holder {
    padding: 0 !important;
  }
  .mkdf-section-title-holder.mkdf-st-two-columns .mkdf-st-title,
  .mkdf-section-title-holder.mkdf-st-two-columns .mkdf-st-text {
    width: 100%;
    float: none !important;
    text-align: initial !important;
  }
  .mkdf-section-title-holder.mkdf-st-two-columns .mkdf-st-text {
    margin: 14px 0 0;
  }
  .mkdf-section-title-holder.mkdf-st-disable-title-break .mkdf-st-title br {
    display: none;
  }
}

/* ==========================================================================
   Section Title shortcode responsive styles - end
   ========================================================================== */
/* ==========================================================================
   Tabs shortcode responsive style - begin
   ========================================================================== */
@media only screen and (max-width: 1024px) {
  .mkdf-tabs.mkdf-tabs-standard .mkdf-tabs-nav li a {
    padding: 7px 21px;
  }
  .mkdf-tabs.mkdf-tabs-boxed .mkdf-tabs-nav li {
    margin: 0 8px 0 0;
  }
  .mkdf-tabs.mkdf-tabs-boxed .mkdf-tabs-nav li a {
    padding: 7px 18px;
  }
  .mkdf-tabs.mkdf-tabs-simple .mkdf-tabs-nav li {
    margin: 0 26px 0 0;
  }
  .mkdf-tabs.mkdf-tabs-vertical .mkdf-tabs-nav {
    width: 180px;
  }
  .mkdf-tabs.mkdf-tabs-vertical .mkdf-tab-container {
    width: calc(100% - 180px);
    padding: 0 0 0 30px;
  }
}

@media only screen and (max-width: 768px) {
  .mkdf-tabs.mkdf-tabs-standard .mkdf-tabs-nav li {
    display: block;
    float: none;
  }
  .mkdf-tabs.mkdf-tabs-standard .mkdf-tabs-nav li a {
    width: 100%;
  }
  .mkdf-tabs.mkdf-tabs-boxed .mkdf-tabs-nav li {
    display: block;
    float: none;
    margin: 0 0 8px;
  }
  .mkdf-tabs.mkdf-tabs-boxed .mkdf-tabs-nav li a {
    width: 100%;
  }
  .mkdf-tabs.mkdf-tabs-simple .mkdf-tabs-nav li {
    margin: 0 20px 0 0;
  }
  .mkdf-tabs.mkdf-tabs-vertical .mkdf-tabs-nav,
  .mkdf-tabs.mkdf-tabs-vertical .mkdf-tab-container {
    display: inline-block;
    width: 100%;
    height: auto;
  }
  .mkdf-tabs.mkdf-tabs-vertical .mkdf-tabs-nav {
    border-right: 0;
  }
  .mkdf-tabs.mkdf-tabs-vertical .mkdf-tabs-nav li {
    float: left;
    margin: 0 20px 0 0;
  }
  .mkdf-tabs.mkdf-tabs-vertical .mkdf-tab-container {
    padding: 31px 0 0;
  }
  .mkdf-tabs .mkdf-tab-container img {
    display: none;
  }
}

@media only screen and (max-width: 680px) {
  .mkdf-tabs.mkdf-tabs-simple .mkdf-tabs-nav {
    padding: 0 0 20px;
  }
  .mkdf-tabs.mkdf-tabs-simple .mkdf-tabs-nav li {
    display: block;
    float: none;
    margin: 0 0 20px;
  }
  .mkdf-tabs.mkdf-tabs-simple .mkdf-tabs-nav li a {
    padding: 0;
    width: 100%;
  }
  .mkdf-tabs.mkdf-tabs-vertical {
    display: block;
  }
  .mkdf-tabs.mkdf-tabs-vertical .mkdf-tabs-nav li {
    display: block;
    float: none;
    margin: 0 0 20px;
  }
  .mkdf-tabs.mkdf-tabs-vertical .mkdf-tabs-nav li a {
    padding: 5px 10px 0 0;
    width: 100%;
    display: inline;
  }
}

@media only screen and (max-width: 480px) {
  .mkdf-tabs.mkdf-tabs-standard .mkdf-tabs-nav li a {
    padding: 13px 21px 7px;
  }
}

/* ==========================================================================
   Tabs shortcode responsive style - end
   ========================================================================== */
@media only screen and (max-width: 768px) {
  .mkdf-video-button-holder .mkdf-video-button-play {
    font-size: 36px !important;
  }
  .mkdf-video-button-holder .mkdf-video-button-play span span {
    padding: 4px;
    width: 53px;
    height: 53px;
    line-height: 53px;
  }
  .mkdf-video-button-holder .mkdf-video-button-play span span .icon-basic-animation {
    width: 53px;
    height: 53px;
    line-height: 53px;
  }
  .mkdf-video-button-holder .mkdf-video-button-play .mkdf-video-button-play-inner.top-right {
    top: 9% !important;
  }
  .mkdf-video-button-holder .mkdf-video-button-text {
    padding: 30px 30px 20px;
  }
  .mkdf-video-button-holder .mkdf-video-button-text .mkdf-video-button-title {
    display: block;
    width: 100%;
  }
  .mkdf-video-button-holder .mkdf-video-button-text p {
    width: 100%;
    display: block;
  }
  .mkdf-video-button-holder .mkdf-video-button-text-shadow-holder {
    display: none;
  }
}

@media only screen and (max-width: 480px) {
  .mkdf-video-button-holder .mkdf-video-button-play {
    font-size: 20px;
  }
  .mkdf-video-button-holder .mkdf-video-button-play .mkdf-video-button-play-inner.top-right {
    top: 16% !important;
  }
}

@media only screen and (max-width: 320px) {
  .mkdf-video-button-holder .mkdf-video-button-play {
    font-size: 24px !important;
  }
  .mkdf-video-button-holder .mkdf-video-button-play span span {
    width: 20px;
    height: 20px;
    line-height: 20px;
  }
  .mkdf-video-button-holder .mkdf-video-button-play span span .icon-basic-animation {
    width: 20px;
    height: 20px;
    line-height: 20px;
  }
  .mkdf-video-button-holder .mkdf-video-button-play .mkdf-video-button-play-inner.top-right {
    top: 13% !important;
    right: 4px !important;
  }
}

@media only screen and (max-width: 1200px) {
  .mkdf-workflow .mkdf-workflow-item {
    max-width: 100%;
  }
  .mkdf-workflow .mkdf-workflow-item .mkdf-workflow-item-inner .mkdf-workflow-text {
    padding: 0px 40px;
  }
  .mkdf-workflow .mkdf-workflow-item .mkdf-workflow-item-inner .mkdf-workflow-image {
    padding: 0px 40px;
  }
}

@media only screen and (max-width: 680px) {
  .mkdf-workflow .main-line,
  .mkdf-workflow .mkdf-workflow-item .line,
  .mkdf-workflow .mkdf-workflow-item .mkdf-workflow-text .circle {
    display: none !important;
  }
  .mkdf-workflow .mkdf-workflow-item {
    text-align: left;
  }
  .mkdf-workflow .mkdf-workflow-item .mkdf-workflow-item-inner .mkdf-workflow-image {
    width: 100%;
    margin-bottom: 20px;
    text-align: left;
    padding: 0px 40px 0 0;
  }
  .mkdf-workflow .mkdf-workflow-item .mkdf-workflow-item-inner .mkdf-workflow-image.right {
    text-align: left !important;
  }
  .mkdf-workflow .mkdf-workflow-item .mkdf-workflow-item-inner .mkdf-workflow-text {
    width: 100% !important;
    padding: 0px !important;
    text-align: left !important;
  }
  .mkdf-workflow .mkdf-workflow-item:nth-of-type(2n) .mkdf-workflow-item-inner {
    display: block !important;
  }
}

/*# sourceMappingURL=../../../../plugins/boostup-core/assets/css/shortcodes-map-responsive.css.map */
