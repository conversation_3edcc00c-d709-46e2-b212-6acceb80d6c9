/* ==========================================================================
   Social Share shortcode style - begin
   ========================================================================== */

.mkdf-social-share-holder {
	position: relative;
	display: inline-block;
	vertical-align: top;
	
	.mkdf-social-title {
		position: relative;
		display: inline-block;
		vertical-align: top;
		margin-right: 13px;
	}
	
	ul {
		position: relative;
		display: inline-block;
		vertical-align: top;
		list-style: none;
		padding: 0;
		margin: 0;
	}
	
	li {
		position: relative;
		display: inline-block;
		vertical-align: top;
		padding: 0;
		margin: 0;
		
		a {
			font-size: 14px;
		}
	}
	
	&.mkdf-list {
		
		li {
			margin-right: 13px;
			
			&:last-child {
				margin-right: 0;
			}
		}
	}
	
	&.mkdf-text {
		
		li {
			margin-right: 13px;
			
			&:last-child {
				margin-right: 0;
			}
		}
	}
	
	$socialShareWidth: 90px;
	$socialShareHeight: 30px;
	
	&.mkdf-dropdown {
		position: relative;
		display: inline-block;
		vertical-align: bottom;
		
		&:hover {
			
			.mkdf-social-share-dropdown ul li {
				opacity: 1;
				visibility: visible;
				cursor: pointer;
				
				/* opacity and visibility need to be different, but not background-color */
				@for $i from 2 through 7 {
					&:nth-child(#{$i}) {
						$transition-delay: #{($i)/10+s};
						
						-webkit-transition-delay: $transition-delay;
						-moz-transition-delay: $transition-delay;
						transition-delay: $transition-delay;
					}
				}
			}
		}
		
		.mkdf-social-share-dropdown-opener {
			display: block;
			
			.mkdf-social-share-title {
				display: inline-block;
				vertical-align: top;
				margin-right: 5px;
			}
		}
		
		.mkdf-social-share-dropdown {
			position: absolute;
			visibility: hidden;
			z-index: 950;
			
			ul {
				position: relative;
				display: block;
				z-index: 990;
				margin: 0;
				padding: 0 !important;
			}
			
			li {
				position: absolute;
				display: block;
				text-align: center;
				visibility: hidden;
				overflow: hidden;
				opacity: 0;
				box-sizing: border-box;
				@include mkdfTransition(opacity .2s ease-out, visibility .2s ease-out);
				
				a {
					@include mkdfTransition(color .2s ease-out, background-color .2s ease-out);
				}
				
				* {
					display: block;
					line-height: inherit;
				}
			}
		}
		
		&.mkdf-bottom {
			
			.mkdf-social-share-dropdown {
				bottom: 0;
				left: 0;
				
				li {
					width: $socialShareWidth;
					height: $socialShareHeight;
					line-height: $socialShareHeight;
					border: 1px solid $default-border-color;
					
					&:not(:first-child) {
						border-top: none;
					}
					
					&.mkdf-facebook-share a:hover {
						background-color: #3b5998;
						color: #fff;
					}
					
					&.mkdf-twitter-share a:hover {
						background-color: #00aced;
						color: #fff;
					}
					
					&.mkdf-google_plus-share a:hover {
						background-color: #dd4b39;
						color: #fff;
					}
					
					&.mkdf-linkedin-share a:hover {
						background-color: #007bb5;
						color: #fff;
					}
					
					&.mkdf-tumblr-share a:hover {
						background-color: #32506d;
						color: #fff;
					}
					
					&.mkdf-pinterest-share a:hover {
						background-color: #cb2027;
						color: #fff;
					}
					
					&.mkdf-vk-share a:hover {
						background-color: #45668e;
						color: #fff;
					}
					
					a {
						font-size: 12px;
						color: $default-text-color;
						background-color: #fff;
					}
					
					@for $i from 1 through 7 {
						&:nth-child(#{$i}) {
							bottom: #{-$i*(($socialShareHeight))};
						}
					}
				}
			}
		}
		
		&.mkdf-right {
			
			.mkdf-social-share-dropdown {
				top: 0;
				right: 0;
				
				li {
					width: calc(#{$socialShareWidth} / 3);
					
					@for $i from 1 through 7 {
						&:nth-child(#{$i}) {
							left: #{($i - 1)*(($socialShareWidth / 3)) + 5};
						}
					}
				}
			}
		}
		
		&.mkdf-left {
			
			.mkdf-social-share-dropdown {
				top: 0;
				left: 0;
				
				li {
					width: calc(#{$socialShareWidth} / 3);
					
					@for $i from 1 through 7 {
						&:nth-child(#{$i}) {
							right: #{($i - 1)*(($socialShareWidth / 3)) + 5};
						}
					}
				}
			}
		}
	}
}

/* ==========================================================================
   Social Share shortcode style - end
   ========================================================================== */