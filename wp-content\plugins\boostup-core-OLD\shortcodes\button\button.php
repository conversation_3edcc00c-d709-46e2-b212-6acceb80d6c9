<?php
namespace BoostUpCore\CPT\Shortcodes\Button;

use BoostUpCore\Lib;

class Button implements Lib\ShortcodeInterface {
	private $base;
	
	public function __construct() {
		$this->base = 'mkdf_button';
		
		add_action( 'vc_before_init', array( $this, 'vcMap' ) );
	}
	
	public function getBase() {
		return $this->base;
	}
	
	public function vcMap() {
		if ( function_exists( 'vc_map' ) ) {
			vc_map(
				array(
					'name'                      => esc_html__( 'Button', 'boostup-core' ),
					'base'                      => $this->base,
					'category'                  => esc_html__( 'by BOOSTUP', 'boostup-core' ),
					'icon'                      => 'icon-wpb-button extended-custom-icon',
					'allowed_container_element' => 'vc_row',
					'params'                    => array_merge(
						array(
							array(
								'type'        => 'textfield',
								'param_name'  => 'custom_class',
								'heading'     => esc_html__( 'Custom CSS Class', 'boostup-core' ),
								'description' => esc_html__( 'Style particular content element differently - add a class name and refer to it in custom CSS', 'boostup-core' )
							),
							array(
								'type'        => 'dropdown',
								'param_name'  => 'type',
								'heading'     => esc_html__( 'Type', 'boostup-core' ),
								'value'       => array(
									esc_html__( 'Solid', 'boostup-core' )   => 'solid',
									esc_html__( 'Outline', 'boostup-core' ) => 'outline',
									esc_html__( 'Simple', 'boostup-core' )  => 'simple'
								),
								'admin_label' => true
							),
							array(
								'type'       => 'dropdown',
								'param_name' => 'size',
								'heading'    => esc_html__( 'Size', 'boostup-core' ),
								'value'      => array(
									esc_html__( 'Default', 'boostup-core' ) => '',
									esc_html__( 'Small', 'boostup-core' )   => 'small',
									esc_html__( 'Medium', 'boostup-core' )  => 'medium',
									esc_html__( 'Large', 'boostup-core' )   => 'large',
									esc_html__( 'Huge', 'boostup-core' )    => 'huge'
								),
								'dependency' => array( 'element' => 'type', 'value' => array( 'solid', 'outline' ) )
							),
							array(
								'type'        => 'textfield',
								'param_name'  => 'text',
								'heading'     => esc_html__( 'Text', 'boostup-core' ),
								'value'       => esc_html__( 'Button Text', 'boostup-core' ),
								'save_always' => true,
								'admin_label' => true
							),
							array(
								'type'       => 'textfield',
								'param_name' => 'link',
								'heading'    => esc_html__( 'Link', 'boostup-core' )
							),
							array(
								'type'        => 'dropdown',
								'param_name'  => 'target',
								'heading'     => esc_html__( 'Link Target', 'boostup-core' ),
								'value'       => array_flip( boostup_mikado_get_link_target_array() ),
								'save_always' => true
							),
							array(
								'type'        => 'dropdown',
								'param_name'  => 'popup',
								'heading'     => esc_html__( 'Open Link in Pop Up Window', 'boostup-core' ),
								'value'       => array_flip( boostup_mikado_get_yes_no_select_array( false ) ),
								'save_always' => true
							)
						),
						boostup_mikado_icon_collections()->getVCParamsArray( array(), '', true ),
						array(
							array(
								'type'       => 'colorpicker',
								'param_name' => 'color',
								'heading'    => esc_html__( 'Color', 'boostup-core' ),
								'group'      => esc_html__( 'Design Options', 'boostup-core' )
							),
							array(
								'type'       => 'colorpicker',
								'param_name' => 'hover_color',
								'heading'    => esc_html__( 'Hover Color', 'boostup-core' ),
								'group'      => esc_html__( 'Design Options', 'boostup-core' )
							),
							array(
								'type'       => 'colorpicker',
								'param_name' => 'background_color',
								'heading'    => esc_html__( 'Background Color', 'boostup-core' ),
								'dependency' => array( 'element' => 'type', 'value' => array( 'solid' ) ),
								'group'      => esc_html__( 'Design Options', 'boostup-core' )
							),
							array(
								'type'       => 'colorpicker',
								'param_name' => 'hover_background_color',
								'heading'    => esc_html__( 'Hover Background Color', 'boostup-core' ),
								'dependency' => array( 'element' => 'type', 'value' => array( 'solid', 'outline' ) ),
								'group'      => esc_html__( 'Design Options', 'boostup-core' )
							),
							array(
								'type'       => 'colorpicker',
								'param_name' => 'shadow_color',
								'heading'    => esc_html__( 'Box Shadow Color', 'boostup-core' ),
								'dependency' => array( 'element' => 'type', 'value' => array( 'solid' ) ),
								'group'      => esc_html__( 'Design Options', 'boostup-core' )
							),
							array(
								'type'       => 'colorpicker',
								'param_name' => 'background_icon_color',
								'heading'    => esc_html__( 'Background Icon Color', 'boostup-core' ),
								'dependency' => array( 'element' => 'type', 'value' => array( 'simple' ) ),
								'group'      => esc_html__( 'Design Options', 'boostup-core' )
							),
							array(
								'type'       => 'colorpicker',
								'param_name' => 'icon_color',
								'heading'    => esc_html__( 'Icon Color', 'boostup-core' ),
								'dependency' => array( 'element' => 'type', 'value' => array( 'simple' ) ),
								'group'      => esc_html__( 'Design Options', 'boostup-core' )
							),
							array(
								'type'       => 'colorpicker',
								'param_name' => 'border_icon_color',
								'heading'    => esc_html__( 'Border Icon Color', 'boostup-core' ),
								'dependency' => array( 'element' => 'type', 'value' => array( 'simple' ) ),
								'group'      => esc_html__( 'Design Options', 'boostup-core' )
							),
							array(
								'type'       => 'textfield',
								'param_name' => 'shape_size',
								'heading'    => esc_html__( 'Icon Size', 'boostup-core' ),
								'dependency' => array( 'element' => 'type', 'value' => array( 'simple' ) ),
								'group'      => esc_html__( 'Design Options', 'boostup-core' )
							),
							array(
								'type'       => 'colorpicker',
								'param_name' => 'border_color',
								'heading'    => esc_html__( 'Border Color', 'boostup-core' ),
								'dependency' => array( 'element' => 'type', 'value' => array( 'solid', 'outline' ) ),
								'group'      => esc_html__( 'Design Options', 'boostup-core' )
							),
							array(
								'type'       => 'colorpicker',
								'param_name' => 'hover_border_color',
								'heading'    => esc_html__( 'Hover Border Color', 'boostup-core' ),
								'dependency' => array( 'element' => 'type', 'value' => array( 'solid', 'outline' ) ),
								'group'      => esc_html__( 'Design Options', 'boostup-core' )
							),
							array(
								'type'       => 'textfield',
								'param_name' => 'font_size',
								'heading'    => esc_html__( 'Font Size (px)', 'boostup-core' ),
								'group'      => esc_html__( 'Design Options', 'boostup-core' )
							),
							array(
								'type'        => 'dropdown',
								'param_name'  => 'font_weight',
								'heading'     => esc_html__( 'Font Weight', 'boostup-core' ),
								'value'       => array_flip( boostup_mikado_get_font_weight_array( true ) ),
								'save_always' => true,
								'group'       => esc_html__( 'Design Options', 'boostup-core' )
							),
							array(
								'type'        => 'dropdown',
								'param_name'  => 'text_transform',
								'heading'     => esc_html__( 'Text Transform', 'boostup-core' ),
								'value'       => array_flip( boostup_mikado_get_text_transform_array( true ) ),
								'save_always' => true
							),
							array(
								'type'        => 'textfield',
								'param_name'  => 'margin',
								'heading'     => esc_html__( 'Margin', 'boostup-core' ),
								'description' => esc_html__( 'Insert margin in format: top right bottom left (e.g. 10px 5px 10px 5px)', 'boostup-core' ),
								'group'       => esc_html__( 'Design Options', 'boostup-core' )
							),
							array(
								'type'        => 'textfield',
								'param_name'  => 'padding',
								'heading'     => esc_html__( 'Button Padding', 'boostup-core' ),
								'description' => esc_html__( 'Insert padding in format: top right bottom left (e.g. 10px 5px 10px 5px)', 'boostup-core' ),
								'dependency'  => array( 'element' => 'type', 'value' => array( 'solid', 'outline' ) ),
								'group'       => esc_html__( 'Design Options', 'boostup-core' )
							)
						)
					)
				)
			);
		}
	}
	
	public function render( $atts, $content = null ) {
		$default_atts = array(
			'size'                   => '',
			'type'                   => 'solid',
			'text'                   => '',
			'link'                   => '',
			'target'                 => '_self',
			'popup'                  => 'no',
			'color'                  => '',
			'hover_color'            => '',
			'background_color'       => '',
			'shadow_color'           => '',
			'hover_background_color' => '',
			'border_color'           => '',
			'hover_border_color'     => '',
			'background_icon_color'  => '',
			'border_icon_color'  => '',
			'icon_color'             => '',
			'shape_size'             => '',
			'font_size'              => '',
			'font_weight'            => '',
			'text_transform'         => '',
			'margin'                 => '',
			'padding'                => '',
			'custom_class'           => '',
			'html_type'              => 'anchor',
			'input_name'             => '',
			'custom_attrs'           => array()
		);
		$default_atts = array_merge( $default_atts, boostup_mikado_icon_collections()->getShortcodeParams() );
		$params       = shortcode_atts( $default_atts, $atts );
		
		if ( $params['html_type'] !== 'input' ) {
			$iconPackName   = boostup_mikado_icon_collections()->getIconCollectionParamNameByKey( $params['icon_pack'] );
			$params['icon'] = $iconPackName ? $params[ $iconPackName ] : '';
		}
		
		$params['size'] = ! empty( $params['size'] ) ? $params['size'] : 'medium';
		$params['type'] = ! empty( $params['type'] ) ? $params['type'] : 'solid';
		
		$params['link']   = ! empty( $params['link'] ) ? $params['link'] : '#';
		$params['target'] = ! empty( $params['target'] ) ? $params['target'] : $default_atts['target'];
		
		$params['button_classes']      = $this->getButtonClasses( $params );
		$params['button_custom_attrs'] = ! empty( $params['custom_attrs'] ) ? $params['custom_attrs'] : array();
		$params['button_styles']       = $this->getButtonStyles( $params );
		$params['button_data']         = $this->getButtonDataAttr( $params );
		$params['icon_params']         = $this->generateIconParams( $params );
		$params['popup']               = $this->getLinkPopup( $params );
		
		return boostup_core_get_shortcode_module_template_part( 'templates/' . $params['html_type'], 'button', '', $params );
	}

	private function generateIconParams( $params ) {
		$iconParams = array( 'icon_attributes' => array() );
		
		$iconParams['icon_attributes']['style'] = $this->generateIconStyles( $params );
		$iconParams['icon_attributes']['class'] = 'mkdf-icon-element';
		
		return $iconParams;
	}
	private function generateIconStyles( $params ) {
		$iconStyles = array();
		
		if ( ! empty( $params['icon_color'] ) ) {
			$iconStyles[] = 'color: ' . $params['icon_color'];
		}
		if ( ! empty( $params['shape_size'] ) ) {
				$iconStyles[] = 'font-size:' . boostup_mikado_filter_px( $params['shape_size'] ) . 'px';
			}
		if ( ! empty( $params['background_icon_color'] ) ) {
				$iconStyles[] = 'background-color: ' . $params['background_icon_color'];
			}
		if ( ! empty( $params['border_icon_color'] ) ) {
				$iconStyles[] = 'border: 1px solid ' . $params['border_icon_color'];
		}
		
		
		return implode( ';', $iconStyles );
	}
	
	
	
	private function getButtonStyles( $params ) {
		$styles = array();
		
		if ( ! empty( $params['color'] ) ) {
			$styles[] = 'color: ' . $params['color'];
		}
		
		if ( ! empty( $params['background_color'] ) && $params['type'] !== 'outline' ) {
			$styles[] = 'background-color: ' . $params['background_color'];
		}
		if ( ! empty( $params['shadow_color'] ) && $params['type'] !== 'outline' ) {
			$styles[] = 'box-shadow: 0 10px 20px ' . $params['shadow_color'];
		}
		
		
		if ( ! empty( $params['border_color'] ) ) {
			$styles[] = 'border-color: ' . $params['border_color'];
		}
		
		if ( ! empty( $params['font_size'] ) ) {
			$styles[] = 'font-size: ' . boostup_mikado_filter_px( $params['font_size'] ) . 'px';
		}
		
		if ( ! empty( $params['font_weight'] ) && $params['font_weight'] !== '' ) {
			$styles[] = 'font-weight: ' . $params['font_weight'];
		}
		
		if ( ! empty( $params['text_transform'] ) ) {
			$styles[] = 'text-transform: ' . $params['text_transform'];
		}
		
		if ( $params['margin'] !== '' ) {
			$styles[] = 'margin: ' . $params['margin'];
		}
		
		if ( $params['padding'] !== '' ) {
			$styles[] = 'padding: ' . $params['padding'];
		}
		
		return $styles;
	}
	
	private function getButtonDataAttr( $params ) {
		$data = array();
		
		if ( ! empty( $params['hover_color'] ) ) {
			$data['data-hover-color'] = $params['hover_color'];
		}
		
		if ( ! empty( $params['hover_background_color'] ) ) {
			$data['data-hover-bg-color'] = $params['hover_background_color'];
		}
		
		if ( ! empty( $params['hover_border_color'] ) ) {
			$data['data-hover-border-color'] = $params['hover_border_color'];
		}
		
		return $data;
	}

	private function getLinkPopup( $params ) {
		$link_data = array();
		
		if (  $params['popup'] == 'yes' ) {

		
			$link_data['data-rel'] = esc_attr('prettyPhoto[video_button_pretty_photo_604]');
		}

		return $link_data;
	}
	
	
	private function getButtonClasses( $params ) {
		$buttonClasses = array(
			'mkdf-btn',
			'mkdf-btn-' . $params['size'],
			'mkdf-btn-' . $params['type']
		);
		
		if ( ! empty( $params['hover_background_color'] ) ) {
			$buttonClasses[] = 'mkdf-btn-custom-hover-bg';
		}
		
		if ( ! empty( $params['hover_border_color'] ) ) {
			$buttonClasses[] = 'mkdf-btn-custom-border-hover';
		}
		
		if ( ! empty( $params['hover_color'] ) ) {
			$buttonClasses[] = 'mkdf-btn-custom-hover-color';
		}
		
		if ( ! empty( $params['icon'] ) ) {
			$buttonClasses[] = 'mkdf-btn-icon';
		}
		
		if ( ! empty( $params['custom_class'] ) ) {
			$buttonClasses[] = esc_attr( $params['custom_class'] );
		}
		
		return $buttonClasses;
	}
}