<?php
namespace BoostUpCore\CPT\Shortcodes\IconWithText;

use BoostUpCore\Lib;

class IconWithText implements Lib\ShortcodeInterface {
	private $base;
	
	public function __construct() {
		$this->base = 'mkdf_icon_with_text';
		
		add_action( 'vc_before_init', array( $this, 'vcMap' ) );
	}
	
	public function getBase() {
		return $this->base;
	}
	
	public function vcMap() {
		if ( function_exists( 'vc_map' ) ) {
			vc_map(
				array(
					'name'                      => esc_html__( 'Icon With Text', 'boostup-core' ),
					'base'                      => $this->base,
					'icon'                      => 'icon-wpb-icon-with-text extended-custom-icon',
					'category'                  => esc_html__( 'by BOOSTUP', 'boostup-core' ),
					'allowed_container_element' => 'vc_row',
					'params'                    => array_merge(
						array(
							array(
								'type'        => 'textfield',
								'param_name'  => 'custom_class',
								'heading'     => esc_html__( 'Custom CSS Class', 'boostup-core' ),
								'description' => esc_html__( 'Style particular content element differently - add a class name and refer to it in custom CSS', 'boostup-core' )
							),
							array(
								'type'        => 'dropdown',
								'param_name'  => 'type',
								'heading'     => esc_html__( 'Type', 'boostup-core' ),
								'value'       => array(
									esc_html__( 'Icon Left From Text', 'boostup-core' )  => 'icon-left',
									esc_html__( 'Icon Left From Title', 'boostup-core' ) => 'icon-left-from-title',
									esc_html__( 'Icon Top', 'boostup-core' )             => 'icon-top'
								),
								'save_always' => true
							),
                            array(
                                'type'        => 'dropdown',
                                'param_name'  => 'layout',
                                'heading'     => esc_html__( 'Layout', 'boostup-core' ),
                                'value'       => array(
                                    esc_html__( 'Standard', 'boostup-core' )                               => '',
                                    esc_html__( 'Boxed With Shadow', 'boostup-core' )                      => 'boxed-shadow',
                                    esc_html__( 'Boxed with Shadow On Hover', 'boostup-core' )             => 'boxed-hover-shadow'
                                ),
                                'save_always' => true
                            ),
                            array(
                                'type'       => 'colorpicker',
                                'param_name' => 'boxed_color',
                                'heading'    => esc_html__( 'Shadow Color', 'boostup-core' ),
                                'dependency'  => array( 'element' => 'layout', 'value'   => array('boxed-shadow', 'boxed-hover-shadow') )
                            ),
                            array(
                                'type'       => 'colorpicker',
                                'param_name' => 'boxed_color_hover',
                                'heading'    => esc_html__( 'Shadow Color On Hover', 'boostup-core' ),
                                'dependency'  => array( 'element' => 'layout', 'value'   => 'boxed-shadow' )
                            ),
						),
						boostup_mikado_icon_collections()->getVCParamsArray(),
						array(
							array(
								'type'       => 'attach_image',
								'param_name' => 'custom_icon',
								'heading'    => esc_html__( 'Custom Icon', 'boostup-core' )
							),
							array(
								'type'       => 'dropdown',
								'param_name' => 'icon_type',
								'heading'    => esc_html__( 'Icon Type', 'boostup-core' ),
								'value'      => array(
									esc_html__( 'Normal', 'boostup-core' ) => 'mkdf-normal',
									esc_html__( 'Circle', 'boostup-core' ) => 'mkdf-circle',
									esc_html__( 'Square', 'boostup-core' ) => 'mkdf-square'
								),
								'group'      => esc_html__( 'Icon Settings', 'boostup-core' )
							),
							array(
								'type'       => 'dropdown',
								'param_name' => 'icon_size',
								'heading'    => esc_html__( 'Icon Size', 'boostup-core' ),
								'value'      => array(
									esc_html__( 'Medium', 'boostup-core' )     => 'mkdf-icon-medium',
									esc_html__( 'Tiny', 'boostup-core' )       => 'mkdf-icon-tiny',
									esc_html__( 'Small', 'boostup-core' )      => 'mkdf-icon-small',
									esc_html__( 'Large', 'boostup-core' )      => 'mkdf-icon-large',
									esc_html__( 'Very Large', 'boostup-core' ) => 'mkdf-icon-huge'
								),
								'group'      => esc_html__( 'Icon Settings', 'boostup-core' )
							),
							array(
								'type'       => 'textfield',
								'param_name' => 'custom_icon_size',
								'heading'    => esc_html__( 'Custom Icon Size (px)', 'boostup-core' ),
								'group'      => esc_html__( 'Icon Settings', 'boostup-core' )
							),
							array(
								'type'       => 'textfield',
								'param_name' => 'shape_size',
								'heading'    => esc_html__( 'Shape Size (px)', 'boostup-core' ),
								'group'      => esc_html__( 'Icon Settings', 'boostup-core' )
							),
							array(
								'type'       => 'colorpicker',
								'param_name' => 'icon_color',
								'heading'    => esc_html__( 'Icon Color', 'boostup-core' ),
								'group'      => esc_html__( 'Icon Settings', 'boostup-core' )
							),
							array(
								'type'       => 'colorpicker',
								'param_name' => 'icon_hover_color',
								'heading'    => esc_html__( 'Icon Hover Color', 'boostup-core' ),
								'group'      => esc_html__( 'Icon Settings', 'boostup-core' )
							),
							array(
								'type'       => 'colorpicker',
								'param_name' => 'icon_background_color',
								'heading'    => esc_html__( 'Icon Background Color', 'boostup-core' ),
								'dependency' => array(
									'element' => 'icon_type',
									'value'   => array( 'mkdf-square', 'mkdf-circle' )
								),
								'group'      => esc_html__( 'Icon Settings', 'boostup-core' )
							),
							array(
								'type'       => 'colorpicker',
								'param_name' => 'icon_hover_background_color',
								'heading'    => esc_html__( 'Icon Hover Background Color', 'boostup-core' ),
								'dependency' => array(
									'element' => 'icon_type',
									'value'   => array( 'mkdf-square', 'mkdf-circle' )
								),
								'group'      => esc_html__( 'Icon Settings', 'boostup-core' )
							),
							array(
								'type'       => 'colorpicker',
								'param_name' => 'icon_border_color',
								'heading'    => esc_html__( 'Icon Border Color', 'boostup-core' ),
								'dependency' => array(
									'element' => 'icon_type',
									'value'   => array( 'mkdf-square', 'mkdf-circle' )
								),
								'group'      => esc_html__( 'Icon Settings', 'boostup-core' )
							),
							array(
								'type'       => 'colorpicker',
								'param_name' => 'icon_border_hover_color',
								'heading'    => esc_html__( 'Icon Border Hover Color', 'boostup-core' ),
								'dependency' => array(
									'element' => 'icon_type',
									'value'   => array( 'mkdf-square', 'mkdf-circle' )
								),
								'group'      => esc_html__( 'Icon Settings', 'boostup-core' )
							),
							array(
								'type'       => 'textfield',
								'param_name' => 'icon_border_width',
								'heading'    => esc_html__( 'Border Width (px)', 'boostup-core' ),
								'dependency' => array(
									'element' => 'icon_type',
									'value'   => array( 'mkdf-square', 'mkdf-circle' )
								),
								'group'      => esc_html__( 'Icon Settings', 'boostup-core' )
							),
							array(
								'type'       => 'dropdown',
								'param_name' => 'icon_animation',
								'heading'    => esc_html__( 'Icon Animation', 'boostup-core' ),
								'value'      => array_flip( boostup_mikado_get_yes_no_select_array( false ) ),
								'group'      => esc_html__( 'Icon Settings', 'boostup-core' )
							),
							array(
								'type'       => 'textfield',
								'param_name' => 'icon_animation_delay',
								'heading'    => esc_html__( 'Icon Animation Delay (ms)', 'boostup-core' ),
								'dependency' => array( 'element' => 'icon_animation', 'value' => array( 'yes' ) ),
								'group'      => esc_html__( 'Icon Settings', 'boostup-core' )
							),
							array(
								'type'       => 'textfield',
								'param_name' => 'title',
								'heading'    => esc_html__( 'Title', 'boostup-core' )
							),
							array(
								'type'        => 'dropdown',
								'param_name'  => 'title_tag',
								'heading'     => esc_html__( 'Title Tag', 'boostup-core' ),
								'value'       => array_flip( boostup_mikado_get_title_tag( true ) ),
								'save_always' => true,
								'dependency'  => array( 'element' => 'title', 'not_empty' => true ),
								'group'       => esc_html__( 'Text Settings', 'boostup-core' )
							),
							array(
								'type'       => 'colorpicker',
								'param_name' => 'title_color',
								'heading'    => esc_html__( 'Title Color', 'boostup-core' ),
								'dependency' => array( 'element' => 'title', 'not_empty' => true ),
								'group'      => esc_html__( 'Text Settings', 'boostup-core' )
							),
							array(
								'type'       => 'textfield',
								'param_name' => 'title_top_margin',
								'heading'    => esc_html__( 'Title Top Margin (px)', 'boostup-core' ),
								'dependency' => array( 'element' => 'title', 'not_empty' => true ),
								'group'      => esc_html__( 'Text Settings', 'boostup-core' )
							),
							array(
								'type'       => 'textarea_html',
								'param_name' => 'content',
								'save_always' => true,
								'heading'    => esc_html__( 'Text', 'boostup-core' ),
								'value'      => ''
							),
							array(
								'type'       => 'textfield',
								'param_name' => 'text_size',
								'heading'    => esc_html__( 'Text Size (px)', 'boostup-core' ),
								'dependency' => array( 'element' => 'content', 'not_empty' => true ),
								'group'      => esc_html__( 'Text Settings', 'boostup-core' )
							),
							array(
								'type'       => 'colorpicker',
								'param_name' => 'text_color',
								'heading'    => esc_html__( 'Text Color', 'boostup-core' ),
								'dependency' => array( 'element' => 'content', 'not_empty' => true ),
								'group'      => esc_html__( 'Text Settings', 'boostup-core' )
							),
							array(
								'type'       => 'textfield',
								'param_name' => 'text_top_margin',
								'heading'    => esc_html__( 'Text Top Margin (px)', 'boostup-core' ),
								'dependency' => array( 'element' => 'content', 'not_empty' => true ),
								'group'      => esc_html__( 'Text Settings', 'boostup-core' )
							),
							array(
								'type'        => 'textfield',
								'param_name'  => 'link',
								'heading'     => esc_html__( 'Link', 'boostup-core' ),
								'description' => esc_html__( 'Set link around icon and title', 'boostup-core' )
							),
							array(
								'type'       => 'dropdown',
								'param_name' => 'target',
								'heading'    => esc_html__( 'Target', 'boostup-core' ),
								'value'      => array_flip( boostup_mikado_get_link_target_array() ),
								'dependency' => array( 'element' => 'link', 'not_empty' => true ),
							),
							array(
								'type'        => 'textfield',
								'param_name'  => 'text_padding',
								'heading'     => esc_html__( 'Text Padding (px)', 'boostup-core' ),
								'description' => esc_html__( 'Set left or top padding dependence of type for your text holder. Default value is 13 for left type and 25 for top icon with text type', 'boostup-core' ),
								'dependency'  => array( 'element' => 'type', 'value'   => array( 'icon-left', 'icon-top' ) ),
								'group'       => esc_html__( 'Text Settings', 'boostup-core' )
							)
						)
					)
				)
			);
		}
	}
	
	public function render( $atts, $content = null ) {
		$default_atts = array(
			'custom_class'                => '',
			'type'                        => 'icon-left',
            'layout'                      => 'standard',
            'boxed_color'                 => 'rgba(234, 61, 86, 0.1)',
            'boxed_color_hover'           => 'rgba(234, 61, 86, 0.3)',
			'custom_icon'                 => '',
			'icon_type'                   => 'mkdf-normal',
			'icon_size'                   => 'mkdf-icon-medium',
			'custom_icon_size'            => '',
			'shape_size'                  => '',
			'icon_color'                  => '',
			'icon_hover_color'            => '',
			'icon_background_color'       => '',
			'icon_hover_background_color' => '',
			'icon_border_color'           => '',
			'icon_border_hover_color'     => '',
			'icon_border_width'           => '',
			'icon_animation'              => '',
			'icon_animation_delay'        => '',
			'title'                       => '',
			'title_tag'                   => 'h5',
			'title_color'                 => '',
			'title_top_margin'            => '',
			'content'                        => '',
			'text_color'                  => '',
			'text_size'                  => '',
			'text_top_margin'             => '',
			'link'                        => '',
			'target'                      => '_self',
			'text_padding'                => ''
		);
		$default_atts = array_merge( $default_atts, boostup_mikado_icon_collections()->getShortcodeParams() );
		$params       = shortcode_atts( $default_atts, $atts );
		
		$params['content']             = preg_replace( '#^<\/p>|<p>$#', '', $content ); // delete p tag before and after content
		$params['type'] = ! empty( $params['type'] ) ? $params['type'] : $default_atts['type'];
        $params['layout'] = ! empty( $params['layout'] ) ? $params['layout'] : $default_atts['layout'];
		
		$params['icon_parameters'] = $this->getIconParameters( $params );
		$params['holder_classes']  = $this->getHolderClasses( $params );
        $params['holder_styles']  = $this->getHolderStyles( $params );
		$params['content_styles']  = $this->getContentStyles( $params );
		$params['title_styles']    = $this->getTitleStyles( $params );
		$params['title_tag']       = ! empty( $params['title_tag'] ) ? $params['title_tag'] : $default_atts['title_tag'];
		$params['text_styles']     = $this->getTextStyles( $params );
		$params['target']          = ! empty( $params['target'] ) ? $params['target'] : $default_atts['target'];
		
		return boostup_core_get_shortcode_module_template_part( 'templates/iwt', 'icon-with-text', $params['type'], $params );
	}
	
	private function getIconParameters( $params ) {
		$params_array = array();
		
		if ( empty( $params['custom_icon'] ) ) {
			$iconPackName = boostup_mikado_icon_collections()->getIconCollectionParamNameByKey( $params['icon_pack'] );
			
			$params_array['icon_pack']     = $params['icon_pack'];
			$params_array[ $iconPackName ] = $params[ $iconPackName ];
			
			if ( ! empty( $params['icon_size'] ) ) {
				$params_array['size'] = $params['icon_size'];
			}
			
			if ( ! empty( $params['custom_icon_size'] ) ) {
				$params_array['custom_size'] = boostup_mikado_filter_px( $params['custom_icon_size'] ) . 'px';
			}
			
			if ( ! empty( $params['icon_type'] ) ) {
				$params_array['type'] = $params['icon_type'];
			}
			
			if ( ! empty( $params['shape_size'] ) ) {
				$params_array['shape_size'] = boostup_mikado_filter_px( $params['shape_size'] ) . 'px';
			}
			
			if ( ! empty( $params['icon_border_color'] ) ) {
				$params_array['border_color'] = $params['icon_border_color'];
			}
			
			if ( ! empty( $params['icon_border_hover_color'] ) ) {
				$params_array['hover_border_color'] = $params['icon_border_hover_color'];
			}
			
			if ( $params['icon_border_width'] !== '' ) {
				$params_array['border_width'] = boostup_mikado_filter_px( $params['icon_border_width'] ) . 'px';
			}
			
			if ( ! empty( $params['icon_background_color'] ) ) {
				$params_array['background_color'] = $params['icon_background_color'];
			}
			
			if ( ! empty( $params['icon_hover_background_color'] ) ) {
				$params_array['hover_background_color'] = $params['icon_hover_background_color'];
			}
			
			$params_array['icon_color'] = $params['icon_color'];
			
			if ( ! empty( $params['icon_hover_color'] ) ) {
				$params_array['hover_icon_color'] = $params['icon_hover_color'];
			}
			
			$params_array['icon_animation']       = $params['icon_animation'];
			$params_array['icon_animation_delay'] = $params['icon_animation_delay'];
		}
		
		return $params_array;
	}
	
	private function getHolderClasses( $params ) {
		$holderClasses = array( 'mkdf-iwt', 'clearfix' );
		
		$holderClasses[] = ! empty( $params['custom_class'] ) ? esc_attr( $params['custom_class'] ) : '';
		$holderClasses[] = ! empty( $params['type'] ) ? 'mkdf-iwt-' . $params['type'] : '';
        $holderClasses[] = ! empty( $params['layout'] ) ? $params['layout'] : '';
		$holderClasses[] = ! empty( $params['icon_size'] ) ? 'mkdf-iwt-' . str_replace( 'mkdf-', '', $params['icon_size'] ) : '';
		
		return $holderClasses;
	}

    private function getHolderStyles( $params ) {
        $styles = array();

        if ( ! empty ($params['boxed_color']) && $params['layout'] == 'boxed-shadow' ) {
            $styles[] = 'box-shadow: 0px 10px 30px 0px ' . $params['boxed_color'];
        }

        if ( ! empty ($params['boxed_color']) && $params['layout'] == 'boxed-hover-shadow' ) {
            $styles[] = 'border: 1px solid ';
            $styles[] = 'box-shadow: 0 0 0 0 ';
            $styles[] = 'color: ' . $params['boxed_color'];
        }

        return implode( ';', $styles );
    }

	private function getContentStyles( $params ) {
		$styles = array();
		
		if ( $params['text_padding'] !== '' && $params['type'] === 'icon-left' ) {
			$styles[] = 'padding-left: ' . boostup_mikado_filter_px( $params['text_padding'] ) . 'px';
		}
		
		if ( $params['text_padding'] !== '' && $params['type'] === 'icon-top' ) {
			$styles[] = 'padding-top: ' . boostup_mikado_filter_px( $params['text_padding'] ) . 'px';
		}
		
		return implode( ';', $styles );
	}
	
	private function getTitleStyles( $params ) {
		$styles = array();
		
		if ( ! empty( $params['title_color'] ) ) {
			$styles[] = 'color: ' . $params['title_color'];
		}
		
		if ( $params['title_top_margin'] !== '' ) {
			$styles[] = 'margin-top: ' . boostup_mikado_filter_px( $params['title_top_margin'] ) . 'px';
		}
		
		return implode( ';', $styles );
	}
	
	private function getTextStyles( $params ) {
		$styles = array();
		
		if ( ! empty( $params['text_color'] ) ) {
			$styles[] = 'color: ' . $params['text_color'];
		}
		if ( ! empty( $params['text_size'] ) ) {
			$styles[] = 'font-size: ' . $params['text_size'] . 'px';
		}
		if ( $params['text_top_margin'] !== '' ) {
			$styles[] = 'margin-top: ' . boostup_mikado_filter_px( $params['text_top_margin'] ) . 'px';
		}
		
		return implode( ';', $styles );
	}
}