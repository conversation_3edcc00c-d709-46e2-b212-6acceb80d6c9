@import "../../../../assets/css/lvca-lib";

.lvca-posts-carousel {
  clear: both;
  max-width: none;
  @include respond-to-min(1024) {
    max-width: 96%;
  }

  .lvca-posts-carousel-item {
    .hentry {
      background: #fff;
      border-radius: 6px;
      border: none;
      padding: 0;
      margin: 0;
      transition: box-shadow .25s ease 0s;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
      overflow: hidden;

      &:hover {
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
      }
    }

    .lvca-project-image {
      position: relative;
      overflow: hidden;

      .fl-photo-content {
        width: 100%;
      }

      img {
        width: 100%;
        display: block;
        transition: all .4s ease-in-out 0s;
        max-width: 100%;
      }

      &:hover {
        img {
          filter: brightness(50%);
        }
      }

      .lvca-image-info {
        display: block;
        text-align: center;

        .lvca-entry-info {
          text-align: center;
          display: block;
          position: absolute;
          top: 50%;
          left: 0;
          right: 0;
          margin: auto;
          max-width: 100%;
          transform: translateY(-50%);
        }

        .lvca-post-title {
          padding: 10px;
          margin: 0;
          font-size: 22px;
          line-height: 34px;
          font-weight: 400;
          color: #fff;
          opacity: 0;
          transition: opacity .4s ease-in-out 0s;
          @include respond-to-max(1024) {
            font-size: 18px;
            line-height: 26px;
          }

          a {
            display: inline;
            color: #fff;
            transition: all .3s ease-in-out 0s;
            border-bottom: 1px solid transparent;

            &:hover {
              border-bottom: 2px solid #ccc;
            }
          }
        }

        .lvca-terms {
          display: block;
          color: #f9f9f9;
          font-size: 14px;
          line-height: 22px;
          opacity: 0;
          transition: opacity .4s ease-in-out 0s;

          a {
            color: #ddd;
            position: relative;
            display: inline;
            zoom: 1;
            font-size: 14px;
            line-height: 22px;
            font-style: italic;
            transition: all .4s ease-in-out 0s;

            &:hover {
              color: #fff;
            }
          }
        }
      }

      &:hover {
        .lvca-image-info {
          .lvca-post-title, .lvca-terms {
            opacity: 1;
          }
        }
      }
    }

    .lvca-entry-text-wrap {
      text-align: center;
      max-width: 650px;
      margin: 0 auto;
      padding: 25px 15px;

      .entry-title {
        @include lvca-heading-style();
        font-size: 16px;
        line-height: 24px;
        margin-bottom: 10px;

        &:after, &:before {
          display: none;
        }

        a {
          color: #333333;
          transition: all .4s ease-in-out 0s;

          &:hover {
            color: #888;
          }
        }
      }

      .lvca-entry-meta {
        span {
          display: inline-block;
          padding: 0;
          margin: 0;
          font-style: italic;
          color: #999;

          a {
            @include transition(all 0.3s ease-in-out 0s);
            font-style: normal;
          }

          &:after {
            content: '//';
            padding-left: 6px;
            padding-right: 6px;
          }

          &:first-child {
            border: none;
            padding-left: 0;
          }

          &:last-child:after {
            display: none;
          }
        }
      }

      .entry-summary {
        padding: 0;
        margin: 10px auto 0;

        &:before {
          width: 35px;
          height: 1px;
          background: #aaa;
          display: block;
          content: "";
          text-align: center;
          margin: 0 auto 15px;
        }
      }

      .lvca-category-list {

        @include bottom-line(35, 1, #aaa);
        transition: all .4s ease-in-out 0s;

        &:after {
          text-align: center;
          margin: 10px auto 10px;
        }
      }

      .lvca-category-list a {
        font-style: italic;
        transition: all .4s ease-in-out 0s;
      }
    }

    .type-post .lvca-entry-text-wrap .entry-summary:before {
      display: none;
    }

    .lvca-read-more {
      margin: 25px 0 0 0;
    }

    .lvca-read-more a:not(.lvca-button) {
      color: #333;
      font-size: 12px;
      line-height: 1;
      font-weight: 600;
      text-transform: uppercase;
      display: block;
      padding: 0;
      transition: color 0.3s ease-in-out 0s;

      &:hover {
        color: #666;
      }

      &:after {
        content: '›';
        display: inline-block;
        margin-left: 7px;
      }

      .rtl & {
        &:after {
          margin: 0 7px 0 0;
        }
      }
    }
  }
}