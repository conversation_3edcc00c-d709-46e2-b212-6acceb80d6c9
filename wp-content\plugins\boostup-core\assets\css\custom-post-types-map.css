/* ==========================================================================
   Global partials
   ========================================================================== */
@-webkit-keyframes animate-btn-line {
  0% {
    -webkit-transform-origin: 0 50%;
    transform-origin: 0 50%;
    -webkit-transform: scaleX(0);
    transform: scaleX(0);
  }
  100% {
    -webkit-transform-origin: 0 50%;
    transform-origin: 0 50%;
    -webkit-transform: scaleX(1);
    transform: scaleX(1);
  }
}
@keyframes animate-btn-line {
  0% {
    -webkit-transform-origin: 0 50%;
    transform-origin: 0 50%;
    -webkit-transform: scaleX(0);
    transform: scaleX(0);
  }
  100% {
    -webkit-transform-origin: 0 50%;
    transform-origin: 0 50%;
    -webkit-transform: scaleX(1);
    transform: scaleX(1);
  }
}

@-webkit-keyframes mkdfPulsebig {
  0% {
    -webkit-box-shadow: 0 0 0 0 currentColor;
    box-shadow: 0 0 0 0 currentColor;
  }
  70% {
    -webkit-box-shadow: 0 0 0 20px rgba(234, 61, 86, 0);
    box-shadow: 0 0 0 20px rgba(234, 61, 86, 0);
  }
  100% {
    -webkit-box-shadow: 0 0 0 0 rgba(234, 61, 86, 0);
    box-shadow: 0 0 0 0 rgba(234, 61, 86, 0);
  }
}

@keyframes mkdfPulsebig {
  0% {
    -webkit-box-shadow: 0 0 0 0 currentColor;
    box-shadow: 0 0 0 0 currentColor;
  }
  70% {
    -webkit-box-shadow: 0 0 0 20px rgba(234, 61, 86, 0);
    box-shadow: 0 0 0 20px rgba(234, 61, 86, 0);
  }
  100% {
    -webkit-box-shadow: 0 0 0 0 rgba(234, 61, 86, 0);
    box-shadow: 0 0 0 0 rgba(234, 61, 86, 0);
  }
}

@-webkit-keyframes mkdfPulsesmall {
  0% {
    -webkit-box-shadow: 0 0 0 0 currentColor;
  }
  70% {
    -webkit-box-shadow: 0 0 0 10px rgba(234, 61, 86, 0.15);
  }
  100% {
    -webkit-box-shadow: 0 0 0 0 rgba(234, 61, 86, 0);
  }
}

@keyframes mkdfPulsesmall {
  0% {
    -webkit-box-shadow: 0 0 0 0 currentColor;
    box-shadow: 0 0 0 0 currentColor;
  }
  70% {
    -webkit-box-shadow: 0 0 0 10px rgba(234, 61, 86, 0);
    box-shadow: 0 0 0 10px rgba(234, 61, 86, 0);
  }
  100% {
    -webkit-box-shadow: 0 0 0 0 rgba(234, 61, 86, 0);
    box-shadow: 0 0 0 0 rgba(234, 61, 86, 0);
  }
}

@-webkit-keyframes mkdfPulsesmallfirst {
  0% {
    -webkit-box-shadow: 0 0 0 0 #ea3d56;
  }
  70% {
    -webkit-box-shadow: 0 0 0 10px rgba(234, 61, 86, 0.15);
  }
  100% {
    -webkit-box-shadow: 0 0 0 0 rgba(234, 61, 86, 0);
  }
}

@keyframes mkdfPulsesmallfirst {
  0% {
    -webkit-box-shadow: 0 0 0 0 #ea3d56;
    box-shadow: 0 0 0 0 #ea3d56;
  }
  70% {
    -webkit-box-shadow: 0 0 0 10px rgba(234, 61, 86, 0);
    box-shadow: 0 0 0 10px rgba(234, 61, 86, 0);
  }
  100% {
    -webkit-box-shadow: 0 0 0 0 rgba(234, 61, 86, 0);
    box-shadow: 0 0 0 0 rgba(234, 61, 86, 0);
  }
}

/* ==========================================================================
   Custom Post Types styles
   ========================================================================== */
/* ==========================================================================
   Portfolio Single page style - begin
   ========================================================================== */
.mkdf-portfolio-single-holder {
  position: relative;
  display: inline-block;
  width: 100%;
  vertical-align: middle;
  margin: 0 0 50px;
}

.mkdf-portfolio-single-holder .mkdf-ps-image-holder {
  position: relative;
  display: inline-block;
  width: 100%;
  vertical-align: middle;
}

.mkdf-portfolio-single-holder .mkdf-ps-image-holder .mkdf-ps-image:not(.mkdf-item-space) {
  position: relative;
  display: inline-block;
  width: 100%;
  vertical-align: middle;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}

.mkdf-portfolio-single-holder .mkdf-ps-image-holder .mkdf-ps-image a, .mkdf-portfolio-single-holder .mkdf-ps-image-holder .mkdf-ps-image img {
  position: relative;
  display: block;
}

.mkdf-portfolio-single-holder .mkdf-ps-info-holder {
  position: relative;
  display: inline-block;
  width: 100%;
  vertical-align: middle;
}

.mkdf-portfolio-single-holder .mkdf-ps-info-holder .mkdf-ps-info-item {
  position: relative;
  display: inline-block;
  width: 100%;
  vertical-align: middle;
  margin: 0 0 10px;
}

.mkdf-portfolio-single-holder .mkdf-ps-info-holder .mkdf-ps-info-item.mkdf-ps-content-item {
  margin: 15px 0 70px;
}

.mkdf-portfolio-single-holder .mkdf-ps-info-holder .mkdf-ps-info-item.mkdf-ps-social-share {
  border-top: 1px solid rgba(225, 225, 225, 0.3);
  padding: 20px 0 0;
}

.mkdf-portfolio-single-holder .mkdf-ps-info-holder .mkdf-ps-info-item.mkdf-ps-social-share span {
  color: #fff;
  padding: 7px;
  border-radius: 50%;
}

.mkdf-portfolio-single-holder .mkdf-ps-info-holder .mkdf-ps-info-item.mkdf-ps-social-share span.social_facebook {
  background: #3b5998;
}

.mkdf-portfolio-single-holder .mkdf-ps-info-holder .mkdf-ps-info-item.mkdf-ps-social-share span.social_twitter {
  background: #55acee;
}

.mkdf-portfolio-single-holder .mkdf-ps-info-holder .mkdf-ps-info-item.mkdf-ps-social-share span.social_linkedin {
  background: #007bb5;
}

.mkdf-portfolio-single-holder .mkdf-ps-info-holder .mkdf-ps-info-item.mkdf-ps-social-share span.social_instagram {
  background: #cd486b;
}

.mkdf-portfolio-single-holder .mkdf-ps-info-holder .mkdf-ps-info-item.mkdf-ps-social-share span.social_pinterest {
  background: #cb2027;
}

.mkdf-portfolio-single-holder .mkdf-ps-info-holder .mkdf-ps-info-item.mkdf-ps-social-share span.social_tumblr {
  background: #32506d;
}

.mkdf-portfolio-single-holder .mkdf-ps-info-holder .mkdf-ps-info-item.mkdf-ps-social-share span.social_googleplus {
  background: #dd4b39;
}

.mkdf-portfolio-single-holder .mkdf-ps-info-holder .mkdf-ps-info-item.mkdf-ps-social-share span.fa-vk {
  background: #45668e;
}

.mkdf-portfolio-single-holder .mkdf-ps-info-holder .mkdf-ps-info-item p, .mkdf-portfolio-single-holder .mkdf-ps-info-holder .mkdf-ps-info-item a {
  margin: 0;
}

.mkdf-portfolio-single-holder .mkdf-ps-info-holder .mkdf-ps-info-item h6, .mkdf-portfolio-single-holder .mkdf-ps-info-holder .mkdf-ps-info-item p {
  display: inline;
}

.mkdf-portfolio-single-holder .mkdf-ps-info-holder .mkdf-ps-info-title,
.mkdf-portfolio-single-holder .mkdf-ps-info-holder .mkdf-ps-title {
  margin: 0;
}

/* ==========================================================================
   Portfolio Single page style - end
   ========================================================================== */
/* ==========================================================================
   Portfolio Single page specific style for types - begin
   ========================================================================== */
.mkdf-portfolio-single-holder .mkdf-ps-image-holder.mkdf-grid-masonry-list .mkdf-ps-image a {
  height: 100%;
}

/* ==========================================================================
   Portfolio Single page specific style for types - end
   ========================================================================== */
/* ==========================================================================
   Portfolio Single - Gallery layout style - begin
   ========================================================================== */
.mkdf-portfolio-single-holder.mkdf-ps-gallery-layout .mkdf-ps-image-holder {
  width: 100%;
}

.mkdf-portfolio-single-holder.mkdf-ps-gallery-layout .mkdf-ps-image-holder .mkdf-ps-image {
  float: left;
}

.mkdf-portfolio-single-holder.mkdf-ps-gallery-layout > .mkdf-grid-row {
  margin-top: 40px;
}

/* ==========================================================================
   Portfolio Single - Gallery layout style - end
   ========================================================================== */
/* ==========================================================================
   Portfolio Single - Huge Images layout style - begin
   ========================================================================== */
.mkdf-portfolio-single-holder.mkdf-ps-huge-images-layout {
  padding: 0 4%;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}

.mkdf-portfolio-single-holder.mkdf-ps-huge-images-layout .mkdf-ps-image-holder {
  margin: 0 0 40px;
}

.mkdf-portfolio-single-holder.mkdf-ps-huge-images-layout .mkdf-ps-image-holder .mkdf-ps-image {
  margin: 0 0 30px;
}

.mkdf-portfolio-single-holder.mkdf-ps-huge-images-layout .mkdf-ps-image-holder .mkdf-ps-image:last-child {
  margin: 0;
}

@media only screen and (max-width: 1200px) {
  .mkdf-portfolio-single-holder.mkdf-ps-huge-images-layout {
    padding: 0 40px;
  }
}

@media only screen and (max-width: 768px) {
  .mkdf-portfolio-single-holder.mkdf-ps-huge-images-layout {
    padding: 0 30px;
  }
}

/* ==========================================================================
   Portfolio Single - Huge Images layout style - end
   ========================================================================== */
/* ==========================================================================
   Portfolio Single - Images layout style - begin
   ========================================================================== */
.mkdf-portfolio-single-holder.mkdf-ps-images-layout .mkdf-ps-image-holder {
  margin: 0 0 40px;
}

.mkdf-portfolio-single-holder.mkdf-ps-images-layout .mkdf-ps-image-holder .mkdf-ps-image {
  margin: 0 0 30px;
}

.mkdf-portfolio-single-holder.mkdf-ps-images-layout .mkdf-ps-image-holder .mkdf-ps-image:last-child {
  margin: 0;
}

/* ==========================================================================
   Portfolio Single - Images layout style - end
   ========================================================================== */
/* ==========================================================================
   Portfolio Single - Masonry layout style - begin
   ========================================================================== */
.mkdf-portfolio-single-holder.mkdf-ps-masonry-layout > .mkdf-grid-row {
  margin-top: 40px;
}

/* ==========================================================================
   Portfolio Single - Masonry layout style - end
   ========================================================================== */
/* ==========================================================================
   Portfolio Single - Slider layout style - begin
   ========================================================================== */
.mkdf-portfolio-single-holder.mkdf-ps-slider-layout .mkdf-ps-image-holder {
  margin: 0 0 40px;
}

.mkdf-portfolio-single-holder.mkdf-ps-slider-layout .mkdf-ps-image-holder .mkdf-ps-image-inner {
  visibility: hidden;
}

.mkdf-portfolio-single-holder.mkdf-ps-slider-layout .mkdf-ps-image-holder .mkdf-ps-image img {
  width: 100%;
}

/* ==========================================================================
   Portfolio Single - Slider layout style - end
   ========================================================================== */
/* ==========================================================================
   Portfolio Single - Small Gallery layout style - begin
   ========================================================================== */
.mkdf-portfolio-single-holder.mkdf-ps-small-gallery-layout .mkdf-ps-image-holder {
  width: 100%;
}

.mkdf-portfolio-single-holder.mkdf-ps-small-gallery-layout .mkdf-ps-image-holder .mkdf-ps-image {
  float: left;
}

.mkdf-portfolio-single-holder.mkdf-ps-small-gallery-layout .mkdf-ps-content-item {
  margin: 0 0 30px;
}

/* ==========================================================================
   Portfolio Single - Small Gallery layout style - end
   ========================================================================== */
/* ==========================================================================
   Portfolio Single - Small Images layout style - begin
   ========================================================================== */
.mkdf-portfolio-single-holder.mkdf-ps-small-images-layout .mkdf-ps-image-holder .mkdf-ps-image {
  margin: 0 0 30px;
}

.mkdf-portfolio-single-holder.mkdf-ps-small-images-layout .mkdf-ps-image-holder .mkdf-ps-image:last-child {
  margin: 0;
}

.mkdf-portfolio-single-holder.mkdf-ps-small-images-layout .mkdf-ps-content-item {
  margin: 0 0 30px;
}

/* ==========================================================================
   Portfolio Single - Small Images layout style - end
   ========================================================================== */
/* ==========================================================================
   Portfolio Single - Small Masonry layout style - begin
   ========================================================================== */
.mkdf-portfolio-single-holder.mkdf-ps-small-masonry-layout .mkdf-ps-content-item {
  margin: 0 0 30px;
}

/* ==========================================================================
   Portfolio Single - Small Masonry layout style - end
   ========================================================================== */
/* ==========================================================================
   Portfolio Single - Small Slider layout style - begin
   ========================================================================== */
.mkdf-portfolio-single-holder.mkdf-ps-small-slider-layout .mkdf-ps-image-holder .mkdf-ps-image-inner {
  visibility: hidden;
}

.mkdf-portfolio-single-holder.mkdf-ps-small-slider-layout .mkdf-ps-image-holder .mkdf-ps-image-inner .owl-stage-outer {
  width: calc(100% - 1px);
}

.mkdf-portfolio-single-holder.mkdf-ps-small-slider-layout .mkdf-ps-image-holder .mkdf-ps-image img {
  width: 100%;
}

.mkdf-portfolio-single-holder.mkdf-ps-small-slider-layout .mkdf-ps-content-item {
  margin: 0 0 30px;
}

/* ==========================================================================
   Portfolio Single - Small Slider layout style - end
   ========================================================================== */
/* ==========================================================================
   Portfolio Single navigation style - begin
   ========================================================================== */
.mkdf-ps-navigation {
  position: relative;
  display: table;
  width: 100%;
  vertical-align: middle;
  padding: 0;
  margin: 44px 0 0;
  clear: both;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}

.mkdf-ps-full-width-custom-layout .mkdf-ps-navigation {
  padding: 0 40px;
}

.mkdf-ps-navigation .mkdf-ps-back-btn {
  position: absolute;
  top: 50%;
  left: 50%;
  display: inline-block;
  vertical-align: middle;
  -webkit-transform: translateX(-50%) translateY(-50%);
  -ms-transform: translateX(-50%) translateY(-50%);
  transform: translateX(-50%) translateY(-50%);
}

.mkdf-ps-navigation .mkdf-ps-back-btn a {
  position: relative;
  display: inline-block;
  margin: 0;
  padding: 0;
  vertical-align: middle;
  cursor: pointer;
  font-size: 23px;
  line-height: 1;
}

.mkdf-ps-navigation .mkdf-ps-back-btn a span {
  display: block;
  line-height: inherit;
}

.mkdf-ps-navigation .mkdf-ps-back-btn a span:before, .mkdf-ps-navigation .mkdf-ps-back-btn a span:after {
  display: block;
  line-height: 14px;
}

.mkdf-ps-navigation .mkdf-ps-back-btn a span:after {
  content: "\e0a6";
}

.mkdf-ps-navigation .mkdf-ps-prev,
.mkdf-ps-navigation .mkdf-ps-next {
  position: relative;
  display: table-cell;
  vertical-align: middle;
  width: 49%;
  padding: 0;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}

.mkdf-ps-navigation .mkdf-ps-prev a,
.mkdf-ps-navigation .mkdf-ps-next a {
  position: relative;
  display: inline-block;
  vertical-align: middle;
  font-size: 46px;
  line-height: 55px;
}

.mkdf-ps-navigation .mkdf-ps-prev a .mkdf-ps-nav-mark,
.mkdf-ps-navigation .mkdf-ps-next a .mkdf-ps-nav-mark {
  position: relative;
  display: inline-block;
  vertical-align: top;
}

.mkdf-ps-navigation .mkdf-ps-prev a .mkdf-ps-nav-mark:before,
.mkdf-ps-navigation .mkdf-ps-next a .mkdf-ps-nav-mark:before {
  display: block;
  line-height: inherit;
}

.mkdf-ps-navigation .mkdf-ps-prev a .mkdf-ps-nav-mark {
  left: -14px;
}

.mkdf-ps-navigation .mkdf-ps-next {
  text-align: right;
}

.mkdf-ps-navigation .mkdf-ps-next a .mkdf-ps-nav-mark {
  right: -14px;
}

/* ==========================================================================
   Portfolio Single navigation style - end
   ========================================================================== */
/* ==========================================================================
   Portfolio Single navigation style - begin
   ========================================================================== */
.mkdf-ps-related-posts-holder {
  position: relative;
  display: inline-block;
  width: 100%;
  vertical-align: middle;
  margin: 80px 0 60px;
  clear: both;
}

.mkdf-ps-related-posts-holder .mkdf-ps-related-posts {
  margin: 0 -15px;
}

.mkdf-ps-related-posts-holder .mkdf-ps-related-post {
  position: relative;
  display: inline-block;
  vertical-align: middle;
  width: 25%;
  float: left;
  padding: 0 15px;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}

@media only screen and (max-width: 1024px) {
  .mkdf-ps-related-posts-holder .mkdf-ps-related-post {
    width: 50%;
    margin: 0 0 30px;
  }
  .mkdf-ps-related-posts-holder .mkdf-ps-related-post:nth-child(2n+1) {
    clear: both;
  }
}

@media only screen and (max-width: 680px) {
  .mkdf-ps-related-posts-holder .mkdf-ps-related-post {
    width: 100%;
  }
}

.mkdf-ps-related-posts-holder .mkdf-ps-related-post .mkdf-pli-image-hover {
  position: absolute;
  display: block;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  padding: 20px;
  opacity: 0;
  text-align: center;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  background-color: #ffc40e;
  color: #fff;
  font-size: 72px;
  -webkit-transition: opacity 0.2s ease-in-out;
  transition: opacity 0.2s ease-in-out;
}

.mkdf-ps-related-posts-holder .mkdf-ps-related-post .mkdf-pli-image-hover .mkdf-pli-image-hover-table {
  display: table;
  height: 100%;
  width: 100%;
}

.mkdf-ps-related-posts-holder .mkdf-ps-related-post .mkdf-pli-image-hover .mkdf-pli-image-hover-table i {
  display: table-cell;
  vertical-align: middle;
}

.mkdf-ps-related-posts-holder .mkdf-ps-related-post:hover .mkdf-pli-image-hover {
  opacity: 1;
  cursor: pointer;
}

.mkdf-ps-related-posts-holder .mkdf-ps-related-image {
  position: relative;
  display: inline-block;
  width: 100%;
  vertical-align: middle;
  overflow: hidden;
}

.mkdf-ps-related-posts-holder .mkdf-ps-related-image a, .mkdf-ps-related-posts-holder .mkdf-ps-related-image img {
  display: block;
}

.mkdf-ps-related-posts-holder .mkdf-ps-related-text {
  position: relative;
  display: inline-block;
  width: 100%;
  vertical-align: middle;
  margin: 20px 0 0;
}

.mkdf-ps-related-posts-holder .mkdf-ps-related-text .mkdf-ps-related-title {
  margin: 0;
}

.mkdf-ps-related-posts-holder .mkdf-ps-related-text .mkdf-ps-related-categories {
  margin: 6px 0 0;
}

/* ==========================================================================
   Portfolio Single navigation style - end
   ========================================================================== */
/* ==========================================================================
   Portfolio Category List shortcode style - begin
   ========================================================================== */
.mkdf-portfolio-category-list-holder {
  position: relative;
  display: inline-block;
  width: 100%;
  vertical-align: middle;
}

.touch .mkdf-portfolio-category-list-holder article {
  cursor: pointer;
}

.mkdf-portfolio-category-list-holder article:hover .mkdf-pcli-text-holder {
  opacity: 1;
}

.mkdf-portfolio-category-list-holder article .mkdf-pcl-item-inner {
  position: relative;
  display: inline-block;
  width: 100%;
  vertical-align: middle;
  overflow: hidden;
}

.mkdf-portfolio-category-list-holder article .mkdf-pcli-image {
  position: relative;
  display: inline-block;
  width: 100%;
  vertical-align: middle;
}

.mkdf-portfolio-category-list-holder article .mkdf-pcli-image img {
  display: block;
  width: 100%;
}

.mkdf-portfolio-category-list-holder article .mkdf-pcli-text-holder {
  position: absolute;
  display: block;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  padding: 20px;
  background-color: rgba(27, 44, 88, 0.6);
  opacity: 0;
  text-align: center;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  -webkit-transition: opacity 0.2s ease-in-out;
  transition: opacity 0.2s ease-in-out;
}

.mkdf-portfolio-category-list-holder article .mkdf-pcli-text-wrapper {
  position: relative;
  display: table;
  table-layout: fixed;
  height: 100%;
  width: 100%;
}

.mkdf-portfolio-category-list-holder article .mkdf-pcli-text {
  position: relative;
  display: table-cell;
  height: 100%;
  width: 100%;
  vertical-align: middle;
}

.mkdf-portfolio-category-list-holder article .mkdf-pcli-title {
  margin: 0;
  color: #fff;
}

.mkdf-portfolio-category-list-holder article .mkdf-pcli-excerpt {
  margin: 3px 0 0;
  color: #fff;
}

.mkdf-portfolio-category-list-holder article .mkdf-pcli-link {
  position: absolute;
  display: block;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
}

/* ==========================================================================
   Portfolio Category List shortcode style - end
   ========================================================================== */
/* ==========================================================================
   Portfolio shortcode style - begin
   ========================================================================== */
.mkdf-portfolio-list-holder {
  position: relative;
  display: inline-block;
  width: 100%;
  vertical-align: middle;
  /***** Article Global Style - begin *****/
  /***** Article Global Style - end *****/
  /***** Specific Global Style - begin *****/
  /***** Specific Global Style - end *****/
  /***** Portfolio Types - end *****/
  /***** Additional Features - begin *****/
  /***** Additional Features - end *****/
}

.mkdf-portfolio-list-holder.mkdf-diagonal-layout.mkdf-five-columns article:nth-child(6) {
  margin-top: 66px;
}

@media screen and (max-width: 1024px) {
  .mkdf-portfolio-list-holder.mkdf-diagonal-layout.mkdf-five-columns article:nth-child(6) {
    margin-top: 0;
  }
}

.mkdf-portfolio-list-holder.mkdf-diagonal-layout.mkdf-five-columns article:nth-child(5) {
  margin-top: 132px;
}

@media screen and (max-width: 768px) {
  .mkdf-portfolio-list-holder.mkdf-diagonal-layout.mkdf-five-columns article:nth-child(5) {
    margin-top: 0;
  }
}

.mkdf-portfolio-list-holder.mkdf-diagonal-layout.mkdf-five-columns article:nth-child(4) {
  margin-top: 198px;
}

@media screen and (max-width: 680px) {
  .mkdf-portfolio-list-holder.mkdf-diagonal-layout.mkdf-five-columns article:nth-child(4) {
    margin-top: 0;
  }
}

.mkdf-portfolio-list-holder.mkdf-diagonal-layout.mkdf-five-columns article:nth-child(3) {
  margin-top: 264px;
}

@media screen and (max-width: 680px) {
  .mkdf-portfolio-list-holder.mkdf-diagonal-layout.mkdf-five-columns article:nth-child(3) {
    margin-top: 0;
  }
}

.mkdf-portfolio-list-holder.mkdf-diagonal-layout.mkdf-five-columns .mkdf-pl-inner.mkdf-masonry-list-wrapper {
  opacity: 0;
  -webkit-transition: opacity 0.22s cubic-bezier(0.23, 1, 0.32, 1), -webkit-transform 0s cubic-bezier(0.23, 1, 0.32, 1);
  transition: opacity 0.22s cubic-bezier(0.23, 1, 0.32, 1), -webkit-transform 0s cubic-bezier(0.23, 1, 0.32, 1);
  transition: opacity 0.22s cubic-bezier(0.23, 1, 0.32, 1), transform 0s cubic-bezier(0.23, 1, 0.32, 1);
  transition: opacity 0.22s cubic-bezier(0.23, 1, 0.32, 1), transform 0s cubic-bezier(0.23, 1, 0.32, 1), -webkit-transform 0s cubic-bezier(0.23, 1, 0.32, 1);
}

.mkdf-portfolio-list-holder.mkdf-diagonal-layout.mkdf-five-columns .mkdf-pl-inner.mkdf-masonry-list-wrapper .mkdf-pl-item .mkdf-pl-item-inner {
  opacity: 0;
  -webkit-transform: scale(0.6);
  -ms-transform: scale(0.6);
  transform: scale(0.6);
  -webkit-transition: -webkit-transform 0.6s cubic-bezier(0.18, 0.89, 0.32, 1), opacity 0.2s ease-out;
  -webkit-transition: opacity 0.2s ease-out, -webkit-transform 0.4s cubic-bezier(0.18, 0.89, 0.32, 1);
  transition: opacity 0.2s ease-out, -webkit-transform 0.4s cubic-bezier(0.18, 0.89, 0.32, 1);
  transition: transform 0.4s cubic-bezier(0.18, 0.89, 0.32, 1), opacity 0.2s ease-out;
  transition: transform 0.4s cubic-bezier(0.18, 0.89, 0.32, 1), opacity 0.2s ease-out, -webkit-transform 0.4s cubic-bezier(0.18, 0.89, 0.32, 1);
}

.mkdf-portfolio-list-holder.mkdf-diagonal-layout.mkdf-five-columns .mkdf-pl-inner.mkdf-masonry-list-wrapper .mkdf-pl-item .mkdf-pl-item-inner.mkdf-appeared {
  opacity: 1;
  -webkit-transform: scale(1);
  -ms-transform: scale(1);
  transform: scale(1);
}

.touch .mkdf-portfolio-list-holder article {
  cursor: pointer;
}

.mkdf-portfolio-list-holder article .mkdf-pl-item-inner {
  position: relative;
  display: inline-block;
  width: 100%;
  vertical-align: middle;
}

.mkdf-portfolio-list-holder article .mkdf-pli-image {
  position: relative;
  display: inline-block;
  width: 100%;
  vertical-align: middle;
}

.mkdf-portfolio-list-holder article .mkdf-pli-image img {
  display: block;
  width: 100%;
}

.mkdf-portfolio-list-holder article .mkdf-pli-image .mkdf-pli-image-hover {
  position: absolute;
  display: block;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  padding: 20px;
  opacity: 0;
  text-align: center;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  color: #fff;
  font-size: 72px;
  -webkit-transition: opacity 0.2s ease-in-out;
  transition: opacity 0.2s ease-in-out;
}

.mkdf-portfolio-list-holder article .mkdf-pli-image .mkdf-pli-image-hover .mkdf-pli-image-hover-table {
  display: table;
  height: 100%;
  width: 100%;
}

.mkdf-portfolio-list-holder article .mkdf-pli-image .mkdf-pli-image-hover .mkdf-pli-image-hover-table i {
  display: table-cell;
  vertical-align: middle;
  -webkit-transition: all 0.4s ease-in-out;
  transition: all 0.4s ease-in-out;
}

.mkdf-portfolio-list-holder article:hover .mkdf-pli-image .mkdf-pli-image-hover {
  opacity: 1;
}

.mkdf-portfolio-list-holder article:hover .mkdf-pli-image .mkdf-pli-image-hover i {
  -webkit-transform: rotate(90deg);
  -ms-transform: rotate(90deg);
  transform: rotate(90deg);
}

.mkdf-portfolio-list-holder article .mkdf-pli-link {
  position: absolute;
  display: block;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
}

.mkdf-portfolio-list-holder article .mkdf-pli-text-wrapper {
  position: relative;
  display: table;
  table-layout: fixed;
  height: 100%;
  width: 100%;
}

.mkdf-portfolio-list-holder article .mkdf-pli-text {
  position: relative;
  display: table-cell;
  height: 100%;
  width: 100%;
  vertical-align: middle;
}

.mkdf-portfolio-list-holder article .mkdf-pli-text .mkdf-pli-title {
  margin: 0;
  line-height: 1.5em;
}

.mkdf-portfolio-list-holder article .mkdf-pli-text .mkdf-pli-category-holder {
  position: relative;
  display: block;
}

.mkdf-portfolio-list-holder article .mkdf-pli-text .mkdf-pli-category-holder a {
  position: relative;
  display: inline-block;
  vertical-align: middle;
  padding: 0 6px 0 0;
  margin: 0 3px 0 0;
  color: #0c2c5866;
  z-index: 8;
  font-size: 12px;
  font-family: "Josefin Sans", sans-serif;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.1em;
}

.mkdf-portfolio-list-holder article .mkdf-pli-text .mkdf-pli-category-holder a:after {
  position: absolute;
  top: 0;
  right: -4px;
  content: '/';
  color: #1b2c58;
  opacity: 0.4;
  font-size: 14px;
  line-height: inherit;
}

.mkdf-portfolio-list-holder article .mkdf-pli-text .mkdf-pli-category-holder a:last-child {
  margin: 0;
  padding: 0;
}

.mkdf-portfolio-list-holder article .mkdf-pli-text .mkdf-pli-category-holder a:last-child:after {
  display: none;
}

.mkdf-portfolio-list-holder article .mkdf-pli-text .mkdf-pli-category-holder a:hover {
  color: #ea3d56;
}

.mkdf-portfolio-list-holder article .mkdf-pli-text .mkdf-pli-category-holder a:hover:after {
  color: #1b2c58;
  opacity: 0.4;
}

.mkdf-portfolio-list-holder article .mkdf-pli-text .mkdf-pli-excerpt {
  margin: 10px 0 0;
}

.mkdf-portfolio-list-holder.mkdf-pl-has-shadow article .mkdf-pli-image {
  -webkit-box-shadow: 0 16px 46px 0 rgba(0, 0, 0, 0.3);
  box-shadow: 0 16px 46px 0 rgba(0, 0, 0, 0.3);
}

.mkdf-portfolio-list-holder.mkdf-pl-has-filter .mkdf-pl-inner {
  overflow: hidden;
}

.mkdf-portfolio-list-holder.mkdf-pl-no-content .mkdf-pli-text-holder {
  display: none;
}

.mkdf-portfolio-list-holder.mkdf-pl-masonry.mkdf-fixed-masonry-items article .mkdf-pl-item-inner,
.mkdf-portfolio-list-holder.mkdf-pl-masonry.mkdf-fixed-masonry-items article .mkdf-pli-image {
  height: 100%;
}

.mkdf-portfolio-list-holder.mkdf-pl-has-animation article {
  opacity: 0;
  -webkit-transform: translateY(80px);
  -ms-transform: translateY(80px);
  transform: translateY(80px);
  -webkit-transition: opacity 0.8s cubic-bezier(0.34, 0.52, 0.57, 1.04), transform 0.8s cubic-bezier(0.34, 0.52, 0.57, 1.04);
  -webkit-transition: opacity 0.8s cubic-bezier(0.34, 0.52, 0.57, 1.04), -webkit-transform 0.8s cubic-bezier(0.34, 0.52, 0.57, 1.04);
  transition: opacity 0.8s cubic-bezier(0.34, 0.52, 0.57, 1.04), -webkit-transform 0.8s cubic-bezier(0.34, 0.52, 0.57, 1.04);
  transition: opacity 0.8s cubic-bezier(0.34, 0.52, 0.57, 1.04), transform 0.8s cubic-bezier(0.34, 0.52, 0.57, 1.04);
  transition: opacity 0.8s cubic-bezier(0.34, 0.52, 0.57, 1.04), transform 0.8s cubic-bezier(0.34, 0.52, 0.57, 1.04), -webkit-transform 0.8s cubic-bezier(0.34, 0.52, 0.57, 1.04);
}

.mkdf-portfolio-list-holder.mkdf-pl-has-animation article.mkdf-item-show {
  opacity: 1;
  -webkit-transform: translateY(0);
  -ms-transform: translateY(0);
  transform: translateY(0);
}

.mkdf-portfolio-list-holder.mkdf-pl-has-animation article.mkdf-item-show.mkdf-item-shown {
  -webkit-transition: none;
  transition: none;
}

.touch .mkdf-portfolio-list-holder.mkdf-pl-has-animation article {
  opacity: 1;
  -webkit-transform: translateY(0);
  -ms-transform: translateY(0);
  transform: translateY(0);
}

/* ==========================================================================
   Portfolio shortcode style - end
   ========================================================================== */
/* ==========================================================================
   Portfolio filter style - begin
   ========================================================================== */
.mkdf-pl-filter-holder {
  position: relative;
  display: inline-block;
  width: 100%;
  vertical-align: middle;
  margin: 0 0 30px;
  text-align: center;
}

.mkdf-pl-filter-holder ul {
  position: relative;
  display: inline-block;
  vertical-align: middle;
  margin: 0;
  padding: 12px 29px;
  list-style: none;
  border: 2px solid #edeeef;
}

.mkdf-pl-filter-holder ul li {
  position: relative;
  display: inline-block;
  vertical-align: middle;
  margin: 0;
  padding: 0 15px;
  cursor: pointer;
}

@media only screen and (max-width: 1024px) {
  .mkdf-pl-filter-holder ul li {
    padding: 0 10px;
  }
}

.mkdf-pl-filter-holder ul li span {
  position: relative;
  display: inline-block;
  vertical-align: middle;
  color: #1b2c58;
  font-family: "Josefin Sans", sans-serif;
  font-size: 20px;
  line-height: 22px;
  white-space: nowrap;
  -webkit-transition: color 0.2s ease-out;
  transition: color 0.2s ease-out;
}

.mkdf-pl-filter-holder ul li.mkdf-pl-current span, .mkdf-pl-filter-holder ul li:hover span {
  color: #ea3d56;
}

/* ==========================================================================
   Portfolio filter style - end
   ========================================================================== */
/* ==========================================================================
   Portfolio standard pagination style - begin
   ========================================================================== */
.mkdf-portfolio-list-holder.mkdf-pl-pag-standard .mkdf-pl-inner {
  opacity: 1;
  -webkit-transition: opacity 0.2s ease-out;
  transition: opacity 0.2s ease-out;
}

.mkdf-portfolio-list-holder.mkdf-pl-pag-standard.mkdf-pl-pag-standard-animate .mkdf-pl-inner {
  opacity: 0;
}

.mkdf-pl-standard-pagination {
  position: relative;
  display: inline-block;
  width: 100%;
  vertical-align: top;
  margin: 40px 0 0;
  clear: both;
}

.mkdf-pl-standard-pagination ul {
  position: relative;
  display: inline-block;
  width: 100%;
  vertical-align: top;
  padding: 0;
  margin: 0;
  list-style: none;
  text-align: center;
}

.mkdf-pl-standard-pagination ul li {
  position: relative;
  display: inline-block;
  vertical-align: top;
  margin: 0 12px;
}

.mkdf-pl-standard-pagination ul li a {
  position: relative;
  display: inline-block;
  vertical-align: top;
  margin: 0;
  padding: 0;
}

.mkdf-pl-standard-pagination ul li.mkdf-pag-active a {
  color: #ea3d56;
}

.mkdf-pl-standard-pagination ul li.mkdf-pag-prev, .mkdf-pl-standard-pagination ul li.mkdf-pag-next, .mkdf-pl-standard-pagination ul li.mkdf-pag-first, .mkdf-pl-standard-pagination ul li.mkdf-pag-last {
  margin: 0 2px;
}

.mkdf-pl-standard-pagination ul li.mkdf-pag-prev a, .mkdf-pl-standard-pagination ul li.mkdf-pag-next a, .mkdf-pl-standard-pagination ul li.mkdf-pag-first a, .mkdf-pl-standard-pagination ul li.mkdf-pag-last a {
  font-size: 24px;
}

.mkdf-pl-standard-pagination ul li.mkdf-pag-prev a span, .mkdf-pl-standard-pagination ul li.mkdf-pag-next a span, .mkdf-pl-standard-pagination ul li.mkdf-pag-first a span, .mkdf-pl-standard-pagination ul li.mkdf-pag-last a span {
  display: block;
  line-height: inherit;
}

.mkdf-pl-standard-pagination ul li.mkdf-pag-prev a span:before, .mkdf-pl-standard-pagination ul li.mkdf-pag-next a span:before, .mkdf-pl-standard-pagination ul li.mkdf-pag-first a span:before, .mkdf-pl-standard-pagination ul li.mkdf-pag-last a span:before {
  display: block;
  line-height: inherit;
}

.mkdf-pl-standard-pagination ul li.mkdf-pag-prev a {
  opacity: 0;
}

.mkdf-pl-standard-pagination ul li.mkdf-pag-next a {
  opacity: 1;
}

/* ==========================================================================
   Portfolio standard pagination style - end
   ========================================================================== */
/* ==========================================================================
   Portfolio load more pagination style - begin
   ========================================================================== */
.mkdf-pl-load-more-holder {
  position: relative;
  display: inline-block;
  width: 100%;
  vertical-align: middle;
}

.mkdf-pl-load-more-holder .mkdf-pl-load-more {
  margin: 32px 0 0;
  text-align: center;
}

/* ==========================================================================
   Portfolio load more pagination style - end
   ========================================================================== */
/* ==========================================================================
   Portfolio loading element style - begin
   ========================================================================== */
.mkdf-pl-loading {
  position: relative;
  display: none;
  width: 100%;
  margin: 40px 0 20px;
  color: #1b2c58;
  text-align: center;
}

.mkdf-pl-loading.mkdf-filter-trigger {
  position: absolute;
  top: 250px;
  left: 0;
}

.mkdf-pl-loading.mkdf-standard-pag-trigger {
  position: absolute;
  top: 50px;
  left: 0;
}

.mkdf-pl-has-filter .mkdf-pl-loading.mkdf-standard-pag-trigger {
  top: 150px;
}

.mkdf-pl-loading.mkdf-showing {
  display: block;
}

.mkdf-pl-loading > div {
  position: relative;
  display: inline-block;
  vertical-align: middle;
  width: 14px;
  height: 14px;
  margin: 0 3px;
  background-color: #1b2c58;
  border-radius: 100%;
  -webkit-animation: sk-bouncedelay 1.4s infinite ease-in-out both;
  animation: sk-bouncedelay 1.4s infinite ease-in-out both;
}

.mkdf-pl-loading .mkdf-pl-loading-bounce1 {
  -webkit-animation-delay: -0.32s;
  animation-delay: -0.32s;
}

.mkdf-pl-loading .mkdf-pl-loading-bounce2 {
  -webkit-animation-delay: -0.16s;
  animation-delay: -0.16s;
}

@-webkit-keyframes sk-bouncedelay {
  0%, 80%, 100% {
    -webkit-transform: scale(0);
  }
  40% {
    -webkit-transform: scale(1);
  }
}

@keyframes sk-bouncedelay {
  0%, 80%, 100% {
    -webkit-transform: scale(0);
    transform: scale(0);
  }
  40% {
    -webkit-transform: scale(1);
    transform: scale(1);
  }
}

/* ==========================================================================
   Portfolio list responsive - start
   ========================================================================== */
@media (min-width: 768px) and (max-width: 1024px) {
  .mkdf-portfolio-list-holder.mkdf-grid-list.mkdf-three-columns .mkdf-item-space,
  .mkdf-portfolio-list-holder.mkdf-grid-list.mkdf-three-columns .mkdf-masonry-grid-sizer {
    width: 50% !important;
  }
  .mkdf-portfolio-list-holder.mkdf-grid-list.mkdf-three-columns .mkdf-item-space:nth-child(2n+1) {
    clear: both !important;
  }
}

/* ==========================================================================
   Portfolio list responsive - end
   ========================================================================== */
/* ==========================================================================
   Portfolio loading element style - end
   ========================================================================== */
/* ==========================================================================
   Portfolio Item Layout - Standard Shader style - begin
   ========================================================================== */
.mkdf-portfolio-list-holder.mkdf-pl-boxed.mkdf-light-skin .mkdf-pli-excerpt,
.mkdf-portfolio-list-holder.mkdf-pl-boxed.mkdf-light-skin .mkdf-pli-title {
  color: #fff;
}

.mkdf-portfolio-list-holder.mkdf-pl-boxed .mkdf-pl-item-inner {
  border: 2px solid rgba(225, 225, 225, 0.2);
  padding: 20px;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  -webkit-transition: all 0.3s ease-out;
  transition: all 0.3s ease-out;
}

.mkdf-portfolio-list-holder.mkdf-pl-boxed .mkdf-pl-item-inner:hover {
  border: 2px solid rgba(225, 225, 225, 0.4);
}

.mkdf-portfolio-list-holder.mkdf-pl-boxed .mkdf-pli-text-holder {
  position: relative;
  display: inline-block;
  width: 100%;
  vertical-align: middle;
  margin: 29px 0 0;
}

.mkdf-portfolio-list-holder.mkdf-pl-boxed article .mkdf-pli-text .mkdf-pli-category-holder {
  margin: 20px 0 0;
  display: inline-block;
  width: 60%;
}

.mkdf-portfolio-list-holder.mkdf-pl-boxed article .mkdf-pli-text .mkdf-pli-category-holder a {
  color: #0c2c5866;
  font-weight: 700;
  font-size: 12px;
  letter-spacing: 0.01em;
}

.mkdf-portfolio-list-holder.mkdf-pl-boxed article .mkdf-pli-text .mkdf-pli-category-holder a:after {
  font-size: 12px;
  color: #0c2c5866;
}

.mkdf-portfolio-list-holder.mkdf-pl-boxed article .mkdf-pli-text .mkdf-pli-category-holder a:hover {
  color: #ea3d56;
}

.mkdf-portfolio-list-holder.mkdf-pl-boxed article .mkdf-pli-text .mkdf-pli-social-share {
  display: inline-block;
  width: 40%;
  text-align: right;
  color: #fff;
  float: right;
  margin: 20px 0 0;
}

.mkdf-portfolio-list-holder.mkdf-pl-boxed article .mkdf-pli-text .mkdf-pli-social-share ul li a {
  color: rgba(255, 255, 255, 0.4);
  z-index: 8;
  position: relative;
}

.mkdf-portfolio-list-holder.mkdf-pl-boxed article .mkdf-pli-text .mkdf-pli-social-share ul li a:hover {
  color: white;
}

/* ==========================================================================
   Portfolio Item Layout - Standard Shader style - end
   ========================================================================== */
/* ==========================================================================
   Portfolio Item Layout - Gallery Overlay style - begin
   ========================================================================== */
.mkdf-portfolio-list-holder.mkdf-pl-gallery-overlay.mkdf-pl-has-shadow .mkdf-pl-item-inner {
  -webkit-box-shadow: 0px 16px 46px 0px rgba(182, 40, 68, 0.52);
  box-shadow: 0px 16px 46px 0px rgba(182, 40, 68, 0.52);
}

.mkdf-portfolio-list-holder.mkdf-pl-gallery-overlay article:hover .mkdf-pli-text-holder {
  opacity: 1;
}

.mkdf-portfolio-list-holder.mkdf-pl-gallery-overlay article .mkdf-pl-item-inner {
  overflow: hidden;
}

/* ==========================================================================
   Portfolio Item Layout - Gallery Overlay style - end
   ========================================================================== */
/* ==========================================================================
   Portfolio Item Layout - Gallery Slide From Image Bottom style - begin
   ========================================================================== */
.mkdf-portfolio-list-holder.mkdf-pl-gallery-slide-from-image-bottom.mkdf-pl-has-shadow .mkdf-pl-item-inner {
  -webkit-box-shadow: 0px 16px 46px 0px rgba(182, 40, 68, 0.52);
  box-shadow: 0px 16px 46px 0px rgba(182, 40, 68, 0.52);
}

.mkdf-portfolio-list-holder.mkdf-pl-gallery-slide-from-image-bottom article:hover .mkdf-pli-text-holder {
  -webkit-transform: translateY(0);
  -ms-transform: translateY(0);
  transform: translateY(0);
}

.mkdf-portfolio-list-holder.mkdf-pl-gallery-slide-from-image-bottom article:hover .mkdf-pli-text-wrapper {
  -webkit-transform: translateY(0);
  -ms-transform: translateY(0);
  transform: translateY(0);
}

.mkdf-portfolio-list-holder.mkdf-pl-gallery-slide-from-image-bottom article .mkdf-pl-item-inner {
  overflow: hidden;
}

.mkdf-portfolio-list-holder.mkdf-pl-gallery-slide-from-image-bottom .mkdf-pli-text-holder {
  position: absolute;
  display: block;
  width: 100%;
  height: auto;
  bottom: 0;
  left: 0;
  padding: 15px 20px 10px;
  background-color: #fff;
  overflow: hidden;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  -webkit-transform: translateY(100%);
  -ms-transform: translateY(100%);
  transform: translateY(100%);
  -webkit-transition: -webkit-transform 0.4s ease-in-out;
  transition: -webkit-transform 0.4s ease-in-out;
  transition: transform 0.4s ease-in-out;
  transition: transform 0.4s ease-in-out, -webkit-transform 0.4s ease-in-out;
}

.mkdf-portfolio-list-holder.mkdf-pl-gallery-slide-from-image-bottom .mkdf-pli-text-wrapper {
  -webkit-transform: translateY(-200%);
  -ms-transform: translateY(-200%);
  transform: translateY(-200%);
  -webkit-transition: -webkit-transform 0.4s ease-in-out;
  transition: -webkit-transform 0.4s ease-in-out;
  transition: transform 0.4s ease-in-out;
  transition: transform 0.4s ease-in-out, -webkit-transform 0.4s ease-in-out;
}

/* ==========================================================================
   Portfolio Item Layout - Gallery Slide From Image Bottom style - end
   ========================================================================== */
/* ==========================================================================
   Portfolio Item Layout - Standard Shader style - begin
   ========================================================================== */
.mkdf-portfolio-list-holder.mkdf-pl-standard-shader .mkdf-pli-text-holder {
  position: relative;
  display: inline-block;
  width: 100%;
  vertical-align: middle;
  margin: 15px 0 10px;
}

/* ==========================================================================
   Portfolio Item Layout - Standard Shader style - end
   ========================================================================== */
/* ==========================================================================
   Portfolio Item Layout - Standard Switch Images style - begin
   ========================================================================== */
.mkdf-portfolio-list-holder.mkdf-pl-standard-switch-images article .mkdf-pli-image img {
  -webkit-transition: opacity 0.2s ease-in-out;
  transition: opacity 0.2s ease-in-out;
}

.mkdf-portfolio-list-holder.mkdf-pl-standard-switch-images article .mkdf-pli-image img:nth-child(1) {
  opacity: 1;
}

.mkdf-portfolio-list-holder.mkdf-pl-standard-switch-images article .mkdf-pli-image img:nth-child(2) {
  position: absolute;
  display: block;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  opacity: 0;
}

.mkdf-portfolio-list-holder.mkdf-pl-standard-switch-images article.mkdf-pl-has-switch-image:hover .mkdf-pli-image img:nth-child(1) {
  opacity: 1;
}

.mkdf-portfolio-list-holder.mkdf-pl-standard-switch-images article.mkdf-pl-has-switch-image:hover .mkdf-pli-image img:nth-child(2) {
  opacity: 1;
}

.mkdf-portfolio-list-holder.mkdf-pl-standard-switch-images .mkdf-pli-text-holder {
  position: relative;
  display: inline-block;
  width: 100%;
  vertical-align: middle;
  margin: 15px 0 0;
}

/* ==========================================================================
   Portfolio Item Layout - Standard Switch Images style - end
   ========================================================================== */
/* ==========================================================================
   Portfolio Project Info shortcode style - begin
   ========================================================================== */
.mkdf-portfolio-project-info {
  position: relative;
  display: inline-block;
  vertical-align: middle;
}

.mkdf-portfolio-project-info .mkdf-ppi-label {
  margin: 0;
  padding: 0;
}

.mkdf-portfolio-project-info > div {
  position: relative;
  display: inline-block;
  vertical-align: middle;
}

.mkdf-portfolio-project-info > div a {
  position: relative;
  display: inline-block;
  vertical-align: middle;
  margin: 0 5px 0 0;
}

.mkdf-portfolio-project-info > div a:last-child {
  margin: 0;
}

.mkdf-portfolio-project-info .mkdf-ppi-title {
  position: relative;
  display: inline-block;
  vertical-align: middle;
  margin: 0;
}

.mkdf-portfolio-project-info .mkdf-ppi-image {
  position: relative;
  display: inline-block;
  vertical-align: middle;
  margin: 0;
}

.mkdf-portfolio-project-info .mkdf-ppi-image img {
  display: block;
}

/* ==========================================================================
   Portfolio Project Info shortcode style - end
   ========================================================================== */
/* ==========================================================================
   Portfolio Slider shortcode style - begin
   ========================================================================== */
.mkdf-portfolio-slider-holder {
  position: relative;
  display: inline-block;
  width: 100%;
  vertical-align: middle;
}

.mkdf-portfolio-slider-holder .mkdf-portfolio-list-holder.mkdf-nav-light-skin .owl-nav .owl-prev,
.mkdf-portfolio-slider-holder .mkdf-portfolio-list-holder.mkdf-nav-light-skin .owl-nav .owl-next {
  color: #fff;
}

.mkdf-portfolio-slider-holder .mkdf-portfolio-list-holder.mkdf-nav-light-skin .owl-nav .owl-prev:hover,
.mkdf-portfolio-slider-holder .mkdf-portfolio-list-holder.mkdf-nav-light-skin .owl-nav .owl-next:hover {
  color: #ea3d56;
}

.mkdf-portfolio-slider-holder .mkdf-portfolio-list-holder.mkdf-nav-dark-skin .owl-nav .owl-prev,
.mkdf-portfolio-slider-holder .mkdf-portfolio-list-holder.mkdf-nav-dark-skin .owl-nav .owl-next {
  color: #1b2c58;
}

.mkdf-portfolio-slider-holder .mkdf-portfolio-list-holder.mkdf-nav-dark-skin .owl-nav .owl-prev:hover,
.mkdf-portfolio-slider-holder .mkdf-portfolio-list-holder.mkdf-nav-dark-skin .owl-nav .owl-next:hover {
  color: #ea3d56;
}

.mkdf-portfolio-slider-holder .mkdf-portfolio-list-holder.mkdf-pag-light-skin .owl-dots .owl-dot span {
  background-color: rgba(255, 255, 255, 0.2);
}

.mkdf-portfolio-slider-holder .mkdf-portfolio-list-holder.mkdf-pag-light-skin .owl-dots .owl-dot.active span, .mkdf-portfolio-slider-holder .mkdf-portfolio-list-holder.mkdf-pag-light-skin .owl-dots .owl-dot:hover span {
  background-color: #ea3d56;
}

.mkdf-portfolio-slider-holder .mkdf-portfolio-list-holder.mkdf-pag-dark-skin .owl-dots .owl-dot span {
  background-color: rgba(27, 44, 88, 0.2);
}

.mkdf-portfolio-slider-holder .mkdf-portfolio-list-holder.mkdf-pag-dark-skin .owl-dots .owl-dot.active span, .mkdf-portfolio-slider-holder .mkdf-portfolio-list-holder.mkdf-pag-dark-skin .owl-dots .owl-dot:hover span {
  background-color: #ea3d56;
}

.mkdf-portfolio-slider-holder .mkdf-portfolio-list-holder.mkdf-pag-on-slider .owl-nav .owl-prev,
.mkdf-portfolio-slider-holder .mkdf-portfolio-list-holder.mkdf-pag-on-slider .owl-nav .owl-next {
  -webkit-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  transform: translateY(-50%);
}

.mkdf-portfolio-slider-holder .mkdf-portfolio-list-holder.mkdf-pag-on-slider .owl-dots {
  position: absolute;
  left: 0;
  bottom: 20px;
  width: 100%;
  margin: 0;
}

/* ==========================================================================
   Portfolio Slider shortcode style - end
   ========================================================================== */
/* ==========================================================================
   Testimonials standard style - begin
   ========================================================================== */
.mkdf-testimonials-holder {
  position: relative;
  display: inline-block;
  width: 100%;
  vertical-align: middle;
}

.mkdf-testimonials-holder .mkdf-testimonials,
.mkdf-testimonials-holder .mkdf-testimonial-content,
.mkdf-testimonials-holder .mkdf-testimonial-text-holder {
  position: relative;
  display: inline-block;
  width: 100%;
  vertical-align: middle;
}

.mkdf-testimonials-holder .mkdf-testimonials {
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}

.mkdf-testimonials-holder .mkdf-testimonials .mkdf-testimonials-icon {
  position: absolute;
  display: inline-block;
  left: 50%;
  top: -80px;
  -webkit-transform: translateX(-50%);
  -ms-transform: translateX(-50%);
  transform: translateX(-50%);
}

.mkdf-testimonials-holder .mkdf-testimonial-image img {
  width: auto !important;
  border-radius: 5em;
}

.mkdf-testimonials-holder.mkdf-testimonials-standard {
  text-align: center;
  /* Light/Dark styles */
}

.mkdf-testimonials-holder.mkdf-testimonials-standard .mkdf-testimonial-content {
  max-width: 875px;
}

.mkdf-testimonials-holder.mkdf-testimonials-standard .mkdf-testimonial-image {
  position: relative;
  display: inline-block;
  width: 100%;
  vertical-align: middle;
  margin: 25px 0 0;
}

.mkdf-testimonials-holder.mkdf-testimonials-standard .mkdf-testimonial-image img {
  display: block;
  margin: 0 auto;
  border: 4px solid #fff;
  border-radius: 50%;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}

.mkdf-testimonials-holder.mkdf-testimonials-standard .mkdf-testimonial-title {
  font-size: 55px;
  font-weight: 600;
  margin: 0 0 39px;
}

.mkdf-testimonials-holder.mkdf-testimonials-standard .mkdf-testimonial-text {
  margin: 0 0 20px;
  font-size: 18px;
  color: #4e4e4e;
  line-height: 34px;
}

.mkdf-testimonials-holder.mkdf-testimonials-standard .mkdf-testimonial-author {
  margin-top: 43px;
}

.mkdf-testimonials-holder.mkdf-testimonials-standard .mkdf-testimonial-author > .mkdf-testimonials-author-name {
  font-size: 26px;
  font-weight: 600;
  color: #ea3d56;
  margin: 25px 0 2px;
}

.mkdf-testimonials-holder.mkdf-testimonials-standard .mkdf-testimonial-author > .mkdf-testimonials-author-job {
  font-size: 14px;
  font-weight: 700;
  color: #1b2c58;
  margin: 25px 0 2px;
  letter-spacing: .1em;
  text-transform: uppercase;
  font-family: "Josefin Sans", sans-serif;
}

.mkdf-testimonials-holder.mkdf-testimonials-standard.mkdf-testimonials-light .mkdf-testimonial-title,
.mkdf-testimonials-holder.mkdf-testimonials-standard.mkdf-testimonials-light .mkdf-testimonial-text,
.mkdf-testimonials-holder.mkdf-testimonials-standard.mkdf-testimonials-light .mkdf-testimonial-author {
  color: #fff;
}

.mkdf-testimonials-holder.mkdf-testimonials-standard.mkdf-testimonials-light .mkdf-testimonial-author > .mkdf-testimonials-author-name {
  color: #fff;
}

.mkdf-testimonials-holder.mkdf-testimonials-standard.mkdf-testimonials-light .mkdf-testimonial-author > .mkdf-testimonials-author-job {
  color: rgba(255, 255, 255, 0.6);
}

.mkdf-testimonials-holder.mkdf-testimonials-standard.mkdf-testimonials-light .owl-dots .owl-dot span {
  border: 2px solid rgba(255, 255, 255, 0.5);
}

.mkdf-testimonials-holder.mkdf-testimonials-standard.mkdf-testimonials-light .owl-dots .owl-dot:hover span, .mkdf-testimonials-holder.mkdf-testimonials-standard.mkdf-testimonials-light .owl-dots .owl-dot.active span {
  background-color: #fff;
  border-color: #fff;
}

.mkdf-testimonials-holder.mkdf-testimonials-standard.mkdf-testimonials-light .mkdf-owl-slider .owl-nav .owl-next > span, .mkdf-testimonials-holder.mkdf-testimonials-standard.mkdf-testimonials-light .mkdf-owl-slider .owl-nav .owl-prev > span {
  color: #fff;
}

@media (max-width: 1024px) {
  .mkdf-testimonials-holder.mkdf-testimonials-standard .mkdf-owl-slider .owl-nav .owl-next > span, .mkdf-testimonials-holder.mkdf-testimonials-standard .mkdf-owl-slider .owl-nav .owl-prev > span {
    display: none;
  }
}

/* ==========================================================================
   Testimonials standard style - end
   ========================================================================== */

/*# sourceMappingURL=../../../../plugins/boostup-core/assets/css/custom-post-types-map.css.map */
