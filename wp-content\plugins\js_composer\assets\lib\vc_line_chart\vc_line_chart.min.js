/*!
 * WPBakery Page Builder v6.0.0 (https://wpbakery.com)
 * Copyright 2011-2023 <PERSON>, WPBakery
 * License: Commercial. More details: http://go.wpbakery.com/licensing
 */

// jscs:disable
// jshint ignore: start

!function($){"use strict";$.fn.vcLineChart=function(){var vcwaypoint=void 0!==$.fn.vcwaypoint;return this.each(function(){var data,gradient,chart,i,$this=$(this),ctx=$this.find("canvas")[0].getContext("2d"),options={showTooltips:$this.data("vcTooltips"),animation:{duration:800,easing:$this.data("vcAnimation")||"easeOutQuart"},datasetFill:!0,scaleLabel:function(object){return" "+object.value},responsive:!0,plugins:{}};for($this.data("vcLegend")||(options.plugins.legend={display:!1}),$this.data("vcTooltips")||(options.plugins.tooltip={enabled:!1}),$this.data("chart")&&($this.data("chart").destroy(),$this.removeData("animated")),data=$this.data("vcValues"),ctx.canvas.width=$this.width(),ctx.canvas.height=$this.width(),i=data.datasets.length-1;0<=i;i--)Array.isArray(data.datasets[i].backgroundColor)&&((gradient=ctx.createLinearGradient(0,0,0,ctx.canvas.height)).addColorStop(0,data.datasets[i].backgroundColor[0]),gradient.addColorStop(1,data.datasets[i].backgroundColor[1]),data.datasets[i].backgroundColor=gradient);function addchart(){var type;$this.data("animated")||(type="line","bar"===$this.data("vcType")&&(type="bar"),chart=new Chart(ctx,{type:type,data:data,options:options}),$this.data("vcChartId",chart.id),$this.data("chart",chart),$this.data("animated",!0))}vcwaypoint?$this.vcwaypoint($.proxy(addchart,$this),{offset:"85%"}):addchart()}),this},"function"!=typeof window.vc_line_charts&&(window.vc_line_charts=function(model_id){var selector=".vc_line-chart";$(selector=void 0!==model_id?'[data-model-id="'+model_id+'"] '+selector:selector).vcLineChart()}),$(document).ready(function(){window.vc_iframe||vc_line_charts()})}(jQuery);