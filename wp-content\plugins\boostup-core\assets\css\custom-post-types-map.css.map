{"version": 3, "sources": ["custom-post-types-map.scss", "custom-post-types-map.css", "../../../../../themes/boostup/assets/css/scss/_mixins.scss", "../../../../../themes/boostup/assets/css/scss/_variables.scss", "../../../post-types/portfolio/assets/css/scss/default/single/_portfolio-single.scss", "../../../post-types/portfolio/assets/css/scss/default/single/layout-collections/_gallery.scss", "../../../post-types/portfolio/assets/css/scss/default/single/layout-collections/_huge-images.scss", "../../../post-types/portfolio/assets/css/scss/default/single/layout-collections/_images.scss", "../../../post-types/portfolio/assets/css/scss/default/single/layout-collections/_masonry.scss", "../../../post-types/portfolio/assets/css/scss/default/single/layout-collections/_slider.scss", "../../../post-types/portfolio/assets/css/scss/default/single/layout-collections/_small-gallery.scss", "../../../post-types/portfolio/assets/css/scss/default/single/layout-collections/_small-images.scss", "../../../post-types/portfolio/assets/css/scss/default/single/layout-collections/_small-masonry.scss", "../../../post-types/portfolio/assets/css/scss/default/single/layout-collections/_small-slider.scss", "../../../post-types/portfolio/assets/css/scss/default/single/parts/_navigation.scss", "../../../post-types/portfolio/assets/css/scss/default/single/parts/_related-posts.scss", "../../../post-types/portfolio/shortcodes/portfolio-category-list/assets/css/scss/default/_portfolio-category-list.scss", "../../../post-types/portfolio/shortcodes/portfolio-list/assets/css/scss/default/_portfolio-list.scss", "../../../post-types/portfolio/shortcodes/portfolio-list/assets/css/scss/default/layout-collections/_boxed.scss", "../../../post-types/portfolio/shortcodes/portfolio-list/assets/css/scss/default/layout-collections/_gallery-overlay.scss", "../../../post-types/portfolio/shortcodes/portfolio-list/assets/css/scss/default/layout-collections/_gallery-slide-from-image-bottom.scss", "../../../post-types/portfolio/shortcodes/portfolio-list/assets/css/scss/default/layout-collections/_standard-shader.scss", "../../../post-types/portfolio/shortcodes/portfolio-list/assets/css/scss/default/layout-collections/_standard-switch-images.scss", "../../../post-types/portfolio/shortcodes/portfolio-project-info/assets/css/scss/default/_portfolio-project-info.scss", "../../../post-types/portfolio/shortcodes/portfolio-slider/assets/css/scss/default/_portfolio-slider.scss", "../../../post-types/testimonials/shortcodes/testimonials/assets/css/scss/default/_testimonials-standard.scss"], "names": [], "mappings": "AAAA;;+ECE+E;ACuf9E;EACG;IAxUA,+BAyUsC;IAvUtC,uBAuUsC;IArVtC,4BAsVoC;IApVpC,oBAoVoC;EDjftC;ECmfE;IA5UA,+BA6UsC;IA3UtC,uBA2UsC;IAzVtC,4BA0VoC;IAxVpC,oBAwVoC;ED7etC;AACF;ACqeC;EACG;IAxUA,+BAyUsC;IAvUtC,uBAuUsC;IArVtC,4BAsVoC;IApVpC,oBAoVoC;EDjftC;ECmfE;IA5UA,+BA6UsC;IA3UtC,uBA2UsC;IAzVtC,4BA0VoC;IAxVpC,oBAwVoC;ED7etC;AACF;;AC+eA;EACG;IAEC,wCAAgC;IAAhC,gCAAgC;ED5elC;EC8eA;IAEI,mDCteoB;IDsepB,2CCteoB;EFNxB;EC8eA;IAEI,gDC1eoB;ID0epB,wCC1eoB;EFFxB;AACF;;AC8eA;EACE;IAEE,wCAAgC;IAAhC,gCAAgC;ED3elC;EC6eA;IAEI,mDCpfoB;IDofpB,2CCpfoB;EFSxB;EC6eA;IAEI,gDCxfoB;IDwfpB,wCCxfoB;EFaxB;AACF;;AC6eA;EACE;IACE,wCAAwC;ED1e1C;EC4eA;IACI,sDChgBoB;EFsBxB;EC4eA;IACI,gDCngBoB;EFyBxB;AACF;;AC4eA;EACE;IAEE,wCAAgC;IAAhC,gCAAgC;EDzelC;EC2eA;IAEI,mDC7gBoB;ID6gBpB,2CC7gBoB;EFoCxB;EC2eA;IAEI,gDCjhBoB;IDihBpB,wCCjhBoB;EFwCxB;AACF;;AC2eA;EACE;IACE,mCCthBsB;EF8CxB;EC0eA;IACI,sDCzhBoB;EFiDxB;EC0eA;IACI,gDC5hBoB;EFoDxB;AACF;;AC0eA;EACE;IAEE,mCCliBsB;IDkiBtB,2BCliBsB;EF2DxB;ECyeA;IAEI,mDCtiBoB;IDsiBpB,2CCtiBoB;EF+DxB;ECyeA;IAEI,gDC1iBoB;ID0iBpB,wCC1iBoB;EFmExB;AACF;;ADlGA;;+ECsG+E;AG5G/E;;+EH+G+E;AG3G/E;EFCI,kBAAkB;EAClB,qBAAqB;EACrB,WAAW;EACX,sBAJmD;EEEtD,gBAAgB;AHgHjB;;AGlHA;EFCI,kBAAkB;EAClB,qBAAqB;EACrB,WAAW;EACX,sBAJmD;ADyHvD;;AGzHA;EFCI,kBAAkB;EAClB,qBAAqB;EACrB,WAAW;EACX,sBAJmD;EEWhD,8BAAsB;EAAtB,sBAAsB;AHsH7B;;AGjIA;EAeO,kBAAkB;EAClB,cAAc;AHsHrB;;AGtIA;EFCI,kBAAkB;EAClB,qBAAqB;EACrB,WAAW;EACX,sBAJmD;AD6IvD;;AG7IA;EFCI,kBAAkB;EAClB,qBAAqB;EACrB,WAAW;EACX,sBAJmD;EEmEpD,gBAAgB;AHkFnB;;AGrJA;EA4BI,mBAAmB;AH6HvB;;AGzJA;EAgCI,8CDa+B;ECZ/B,iBAAiB;AH6HrB;;AG9JA;EAoCK,WAAW;EACX,YAAY;EACZ,kBAAkB;AH8HvB;;AGpKA;EAyCM,mBAAmB;AH+HzB;;AGxKA;EA4CM,mBAAmB;AHgIzB;;AG5KA;EA+CM,mBAAmB;AHiIzB;;AGhLA;EAkDM,mBAAmB;AHkIzB;;AGpLA;EAqDM,mBAAmB;AHmIzB;;AGxLA;EAwDM,mBAAmB;AHoIzB;;AG5LA;EA2DM,mBAAmB;AHqIzB;;AGhMA;EA8DM,mBAAmB;AHsIzB;;AGpMA;EAsEI,SAAS;AHkIb;;AGxMA;EA0EI,eAAe;AHkInB;;AG5MA;;EAgFG,SAAS;AHiIZ;;AG5HA;;+EHgI+E;AG5H/E;;+EH+H+E;AG3H/E;EASK,YAAY;AHqHjB;;AG/GA;;+EHmH+E;AInO/E;;+EJsO+E;AIlO/E;EAKS,WAAW;AJgOpB;;AIrOA;EAQU,WAAW;AJiOrB;;AIzOA;EAeM,gBAAgB;AJ8NtB;;AI1NA;;+EJ8N+E;AKrP/E;;+ELwP+E;AKpP/E;EAGQ,aAAa;EAChB,8BAAsB;EAAtB,sBAAsB;ALoP3B;;AKxPA;EAOY,gBAAgB;ALqP5B;;AK5PA;EAUgB,gBAAgB;ALsPhC;;AKhQA;EAaoB,SAAS;ALuP7B;;ACqYI;EIzoBJ;IAqBM,eAAe;ELoPnB;AACF;;AC2YI;EIrpBJ;IAyBM,eAAe;ELsPnB;AACF;;AKnPA;;+ELuP+E;AMxR/E;;+EN2R+E;AMvR/E;EAKY,gBAAgB;ANqR5B;;AM1RA;EAQgB,gBAAgB;ANsRhC;;AM9RA;EAWoB,SAAS;ANuR7B;;AM/QA;;+ENmR+E;AO1S/E;;+EP6S+E;AOzS/E;EAKY,gBAAgB;APuS5B;;AOlSA;;+EPsS+E;AQpT/E;;+ERuT+E;AQnT/E;EAKG,gBAAgB;ARiTnB;;AQtTA;EAQI,kBAAkB;ARkTtB;;AQ1TA;EAcK,WAAW;ARgThB;;AQ1SA;;+ER8S+E;AStU/E;;+ETyU+E;ASrU/E;EAKY,WAAW;ATmUvB;;ASxUA;EAQgB,WAAW;AToU3B;;AS5UA;EAcY,gBAAgB;ATkU5B;;AS9TA;;+ETkU+E;AUxV/E;;+EV2V+E;AUvV/E;EAOgB,gBAAgB;AVmVhC;;AU1VA;EAUoB,SAAS;AVoV7B;;AU9VA;EAiBY,gBAAgB;AViV5B;;AU7UA;;+EViV+E;AW1W/E;;+EX6W+E;AWzW/E;EAKY,gBAAgB;AXuW5B;;AWlWA;;+EXsW+E;AYpX/E;;+EZuX+E;AYnX/E;EAOI,kBAAkB;AZ+WtB;;AYtXA;EAUK,uBAAuB;AZgX5B;;AY1XA;EAiBK,WAAW;AZ6WhB;;AY9XA;EAuBG,gBAAgB;AZ2WnB;;AYvWA;;+EZ2W+E;Aa1Y/E;;+Eb6Y+E;AazY/E;EACC,kBAAkB;EAClB,cAAc;EACd,WAAW;EACX,sBAAsB;EACtB,UAAU;EACV,gBAAgB;EAChB,WAAW;EACR,8BAAsB;EAAtB,sBAAsB;Ab2Y1B;;AazYI;EACI,eAAe;Ab4YvB;;AavZA;EAeE,kBAAkB;EAClB,QAAQ;EACR,SAAS;EACT,qBAAqB;EACrB,sBAAsB;EZ+IpB,oDY9IsD;EZgJtD,gDYhJsD;EZgJtD,4CYhJsD;Ab8Y1D;;AalaA;EAuBG,kBAAkB;EAClB,qBAAqB;EACrB,SAAS;EACT,UAAU;EACV,sBAAsB;EACtB,eAAe;EACf,eAAe;EACf,cAAc;Ab+YjB;;Aa7aA;EAiCI,cAAc;EACd,oBAAoB;AbgZxB;;AalbA;EAsCK,cAAc;EACd,iBAAiB;AbgZtB;;AavbA;EA2CK,gBAAgB;AbgZrB;;Aa3bA;;EAmDE,kBAAkB;EAClB,mBAAmB;EACnB,sBAAsB;EACtB,UAAU;EACV,UAAU;EACV,8BAAsB;EAAtB,sBAAsB;Ab6YxB;;AarcA;;EA2DG,kBAAkB;EAClB,qBAAqB;EACrB,sBAAsB;EACtB,eAAe;EACf,iBAAiB;Ab+YpB;;Aa9cA;;EAkEI,kBAAkB;EAClB,qBAAqB;EACrB,mBAAmB;AbiZvB;;AardA;;EAuEK,cAAc;EACd,oBAAoB;AbmZzB;;Aa3dA;EAmFI,WAAW;Ab4Yf;;Aa/dA;EAyFE,iBAAiB;Ab0YnB;;AaneA;EA8FI,YAAY;AbyYhB;;AapYA;;+EbwY+E;Ac/e/E;;+Edkf+E;Ac9e/E;EbCI,kBAAkB;EAClB,qBAAqB;EACrB,WAAW;EACX,sBAJmD;EaEtD,mBAAmB;EACnB,WAAW;AdmfZ;;ActfA;EAME,eAAe;AdofjB;;Ac1fA;EAUE,kBAAkB;EAClB,qBAAqB;EACrB,sBAAsB;EACtB,UAAU;EACV,WAAW;EACX,eAAe;EACf,8BAAsB;EAAtB,sBAAsB;AdofxB;;AC2II;Ea/oBJ;IAmBG,UAAU;IACV,gBAAgB;EdsfjB;Ec1gBF;IAuBI,WAAW;Edsfb;AACF;;AC6II;Ea3pBJ;IA4BG,WAAW;EdufZ;AACF;;AcphBA;EbQI,kBAAkB;EAClB,cAAc;EACd,WAAW;EACX,YAAY;EACZ,MAAM;EACN,OAAO;EamBR,aAAa;EACb,UAAU;EACV,kBAAkB;EAClB,8BAAsB;EAAtB,sBAAsB;EACtB,yBZA8B;EYC9B,WAAW;EACX,eAAe;EbgHd,4Ca/G8C;EbiH9C,oCajH8C;AdggBlD;;AcviBA;EA0CI,cAAc;EACd,YAAY;EACZ,WAAW;AdigBf;;Ac7iBA;EA+CK,mBAAmB;EACnB,sBAAsB;AdkgB3B;;AcljBA;EAyDK,UAAU;EACV,eAAe;Ad6fpB;;AcvjBA;EbCI,kBAAkB;EAClB,qBAAqB;EACrB,WAAW;EACX,sBAJmD;EasErD,gBAAgB;AdyflB;;Ac/jBA;EAoEG,cAAc;Ad+fjB;;AcnkBA;EbCI,kBAAkB;EAClB,qBAAqB;EACrB,WAAW;EACX,sBAJmD;Ea2ErD,gBAAgB;AdggBlB;;Ac3kBA;EA8EG,SAAS;AdigBZ;;Ac/kBA;EAkFG,eAAe;AdigBlB;;Ac7fA;;+EdigB+E;Ae3lB/E;;+Ef8lB+E;Ae1lB/E;EdCI,kBAAkB;EAClB,qBAAqB;EACrB,WAAW;EACX,sBAJmD;ADgmBvD;;Ae3lBK;EACC,eAAe;Af8lBrB;;AepmBA;EAYO,UAAU;Af4lBjB;;AexmBA;EdCI,kBAAkB;EAClB,qBAAqB;EACrB,WAAW;EACX,sBAJmD;EckBjD,gBAAgB;Af8lBtB;;AehnBA;EdCI,kBAAkB;EAClB,qBAAqB;EACrB,WAAW;EACX,sBAJmD;ADunBvD;;AevnBA;EAyBO,cAAc;EACd,WAAW;AfkmBlB;;Ae5nBA;EdQI,kBAAkB;EAClB,cAAc;EACd,WAAW;EACX,YAAY;EACZ,MAAM;EACN,OAAO;EcmBL,aAAa;EACb,uCbKyB;EaJzB,UAAU;EACV,kBAAkB;EAClB,8BAAsB;EAAtB,sBAAsB;EdkHxB,4CcjHiD;EdmHjD,oCcnHiD;AfwmBrD;;Ae7oBA;EdqHI,kBAAkB;EAClB,cAAc;EACd,mBAAmB;EACnB,YAAY;EACZ,WAAW;AD4hBf;;AerpBA;Ed6HI,kBAAkB;EAClB,mBAAmB;EACnB,YAAY;EACZ,WAAW;EACX,sBAL8C;ADiiBlD;;Ae7pBA;EAiDM,SAAS;EACT,WAAW;AfgnBjB;;AelqBA;EAsDM,eAAe;EACf,WAAW;AfgnBjB;;AevqBA;EdQI,kBAAkB;EAClB,cAAc;EACd,WAAW;EACX,YAAY;EACZ,MAAM;EACN,OAAO;ADmqBX;;AehnBA;;+EfonB+E;AgBxrB/E;;+EhB2rB+E;AgBvrB/E;EfCI,kBAAkB;EAClB,qBAAqB;EACrB,WAAW;EACX,sBAJmD;EeGtD,yCAAA;EAkNA,uCAAA;EAEA,0CAAA;EAyBA,wCAAA;EAgBA,kCAAA;EAEA,wCAAA;EAyBA,sCAAA;AhByaD;;AgBpsBA;EAUI,gBAAgB;AhB8rBpB;;AgB5rBI;EAZJ;IAaK,aAAa;EhBgsBhB;AACF;;AgB9sBA;EAkBI,iBAAiB;AhBgsBrB;;AgB9rBI;EApBJ;IAqBK,aAAa;EhBksBhB;AACF;;AgBxtBA;EA0BI,iBAAiB;AhBksBrB;;AgBhsBI;EA5BJ;IA6BK,aAAa;EhBosBhB;AACF;;AgBluBA;EAkCI,iBAAiB;AhBosBrB;;AgBlsBI;EApCJ;IAqCK,aAAa;EhBssBhB;AACF;;AgB5uBA;EA6CK,UAAU;EACV,qHAAoH;EACpH,6GAAoG;EAApG,qGAAoG;EAApG,0JAAoG;AhBmsBzG;;AgBlvBA;EAsDQ,UAAU;Ef4Gd,6Be3GoC;Ef6GpC,yBe7GoC;Ef6GpC,qBe7GoC;EAChC,mGAAiG;EACjG,mGAAiF;EAAjF,2FAAiF;EAAjF,mFAAiF;EAAjF,6IAAiF;AhBksBzF;;AgB3vBA;EAoEU,UAAU;Ef8FhB,2Be7FqC;Ef+FrC,uBe/FqC;Ef+FrC,mBe/FqC;AhB6rBzC;;AgB7qBK;EACC,eAAe;AhBgrBrB;;AgBtwBA;EfCI,kBAAkB;EAClB,qBAAqB;EACrB,WAAW;EACX,sBAJmD;AD6wBvD;;AgB7wBA;EfCI,kBAAkB;EAClB,qBAAqB;EACrB,WAAW;EACX,sBAJmD;ADoxBvD;;AgBpxBA;EAiGO,cAAc;EACd,WAAW;AhBurBlB;;AgBzxBA;EfQI,kBAAkB;EAClB,cAAc;EACd,WAAW;EACX,YAAY;EACZ,MAAM;EACN,OAAO;Ee0FP,aAAa;EACb,UAAU;EACV,kBAAkB;EAClB,8BAAsB;EAAtB,sBAAsB;EACtB,WAAW;EACX,eAAe;Ef0Cf,4CezC+C;Ef2C/C,oCe3C+C;AhB8rBnD;;AgB3yBA;EAgHK,cAAc;EACd,YAAY;EACZ,WAAW;AhB+rBhB;;AgBjzBA;EAqHM,mBAAmB;EACnB,sBAAsB;EfgCxB,wCe/B6C;EfiC7C,gCejC6C;AhBksBjD;;AgBzzBA;EAkIK,UAAU;AhB2rBf;;AgB7zBA;EAqIM,gCAAwB;EAAxB,4BAAwB;EAAxB,wBAAwB;AhB4rB9B;;AgBj0BA;EfQI,kBAAkB;EAClB,cAAc;EACd,WAAW;EACX,YAAY;EACZ,MAAM;EACN,OAAO;AD6zBX;;AgB10BA;EfqHI,kBAAkB;EAClB,cAAc;EACd,mBAAmB;EACnB,YAAY;EACZ,WAAW;ADytBf;;AgBl1BA;Ef6HI,kBAAkB;EAClB,mBAAmB;EACnB,YAAY;EACZ,WAAW;EACX,sBAL8C;AD8tBlD;;AgB11BA;EAuJI,SAAS;EACT,kBAAkB;AhBusBtB;;AgB/1BA;EA4JO,kBAAkB;EAClB,cAAc;AhBusBrB;;AgBp2BA;EAiKQ,kBAAkB;EAClB,qBAAqB;EACrB,sBAAsB;EACtB,kBAAkB;EAClB,iBAAiB;EACpB,gBAAgB;EACb,UAAU;EACb,eAAe;EACf,uCd5IyC;Ec6IzC,gBAAgB;EAChB,yBAAyB;EACzB,qBAAqB;AhBusB1B;;AgBn3BA;EAgLS,kBAAkB;EAClB,MAAM;EACN,WAAW;EACX,YAAY;EACZ,cdnJ2B;EcoJ9B,YAAY;EACT,eAAe;EACf,oBAAoB;AhBusB7B;;AgB93BA;EA2LS,SAAS;EACT,UAAU;AhBusBnB;;AgBn4BA;EA+LU,aAAa;AhBwsBvB;;AgBv4BA;EAoMM,cdpKoB;AF22B1B;;AgB34BA;EAuMU,cdtK0B;EcuK7B,YAAY;AhBwsBnB;;AgBh5BA;EAgNO,gBAAgB;AhBosBvB;;AgBp5BA;EA8NI,oDAAwC;EAAxC,4CAAwC;AhB0rB5C;;AgBx5BA;EAsOG,gBAAgB;AhBsrBnB;;AgB55BA;EA6OG,aAAa;AhBmrBhB;;AgBh6BA;;EA0Pc,YAAY;AhB2qB1B;;AgBr6BA;EAuQG,UAAU;EfrGT,mCesGsC;EfpGtC,+BeoGsC;EfpGtC,2BeoGsC;EflHtC,0HemH2H;EfjH3H,kIeiH2H;EfjH3H,0HeiH2H;EfjH3H,kHeiH2H;EfjH3H,+KeiH2H;AhBsqB/H;;AgB/6BA;EA4QI,UAAU;Ef1GV,gCe2GoC;EfzGpC,4BeyGoC;EfzGpC,wBeyGoC;AhByqBxC;;AgBt7BA;EfsJI,wBe0H6B;EfxH7B,gBewH6B;AhB4qBjC;;AgBxqBG;EACC,UAAU;EfnHV,gCeoHoC;EflHpC,4BekHoC;EflHpC,wBekHoC;AhB6qBxC;;AgBrqBA;;+EhByqB+E;AgBrqB/E;;+EhBwqB+E;AgBpqB/E;EfrSI,kBAAkB;EAClB,qBAAqB;EACrB,WAAW;EACX,sBAJmD;EewSnD,gBAAgB;EAChB,kBAAkB;AhByqBtB;;AgB5qBA;EAMQ,kBAAkB;EAClB,qBAAqB;EACrB,sBAAsB;EACtB,SAAS;EACT,kBAAkB;EAClB,gBAAgB;EACtB,yBAAyB;AhB0qB3B;;AgBtrBA;EAeY,kBAAkB;EAClB,qBAAqB;EACrB,sBAAsB;EACtB,SAAS;EACZ,eAAe;EACZ,eAAe;AhB2qB3B;;ACtVI;EezWJ;IAuBU,eAAe;EhB6qBvB;AACF;;AgBrsBA;EA2BgB,kBAAkB;EAClB,qBAAqB;EACrB,sBAAsB;EACtB,cd9Re;Ec+R3B,uCdxS0C;EcyS9B,eAAe;EACf,iBAAiB;EACjB,mBAAmB;EflL/B,uCemLsD;EfjLtD,+BeiLsD;AhBgrB1D;;AgBntBA;EA0CoB,cdhTM;AF69B1B;;AgBvqBA;;+EhB2qB+E;AgBvqB/E;;+EhB0qB+E;AgBtqB/E;EAKG,UAAU;Ef7MT,yCe8M2C;Ef5M3C,iCe4M2C;AhBsqB/C;;AgB5qBA;EAYI,UAAU;AhBoqBd;;AgB9pBA;Ef/WI,kBAAkB;EAClB,qBAAqB;EACrB,WAAW;EACX,mBAuSqC;EACrC,gBAAgB;EAChB,WAAW;AD0uBf;;ACxuBI;EA9SA,kBAAkB;EAClB,qBAAqB;EACrB,WAAW;EACX,mBA4SyC;EACrC,UAAU;EACV,SAAS;EACT,gBAAgB;EAChB,kBAAkB;AD8uB1B;;AC5uBQ;EACI,kBAAkB;EAClB,qBAAqB;EACrB,mBAAmB;EACnB,cAAc;AD+uB1B;;AC7uBY;EACI,kBAAkB;EAClB,qBAAqB;EACrB,mBAAmB;EACnB,SAAS;EACT,UAAU;ADgvB1B;;AC3uBgB;EACI,cCvSM;AFqhC1B;;AC1uBY;EAII,aAAa;AD0uB7B;;ACxuBgB;EACI,eAAe;AD2uBnC;;ACzuBoB;EACI,cAAc;EACd,oBAAoB;AD4uB5C;;AC1uBwB;EACI,cAAc;EACd,oBAAoB;AD6uBhD;;ACnuBoB;EACI,UAAU;ADsuBlC;;AChuBoB;EACI,UAAU;ADmuBlC;;AgB5tBA;;+EhBguB+E;AgB5tB/E;;+EhB+tB+E;AgB3tB/E;Ef1XI,kBAAkB;EAClB,qBAAqB;EACrB,WAAW;EACX,sBAJmD;AD4lCvD;;AgBjuBA;EAIK,gBAAgB;EAChB,kBAAkB;AhBiuBvB;;AgB9tBA;;+EhBkuB+E;AgB9tB/E;;+EhBiuB+E;AgB7tB/E;EACC,kBAAkB;EAClB,aAAa;EACb,WAAW;EACX,mBAAmB;EACnB,cd1W8B;Ec2W9B,kBAAkB;AhB+tBnB;;AgBruBA;EASE,kBAAkB;EAClB,UAAU;EACV,OAAO;AhBguBT;;AgB3uBA;EAeE,kBAAkB;EAClB,SAAS;EACT,OAAO;AhBguBT;;AgB9tBK;EACC,UAAU;AhBiuBhB;;AgBrvBA;EAyBE,cAAc;AhBguBhB;;AgBzvBA;EA6BE,kBAAkB;EAClB,qBAAqB;EACrB,sBAAsB;EACtB,WAAW;EACX,YAAY;EACZ,aAAa;EACb,yBdxY6B;EcyY7B,mBAAmB;EfvQjB,gEewQkE;EftQlE,wDesQkE;AhBkuBtE;;AgBvwBA;EAyCE,+BAA+B;EAE/B,uBAAuB;AhBkuBzB;;AgB7wBA;EA+CE,+BAA+B;EAE/B,uBAAuB;AhBkuBzB;;AgB9tBA;EACC;IACC,2BAA2B;EhBiuB3B;EgB/tBD;IACC,2BAA6B;EhBiuB7B;AACF;;AgBrtBA;EACC;IACC,2BAA2B;IAC3B,mBAAmB;EhBiuBnB;EgB/tBD;IACC,2BAA6B;IAC7B,mBAAqB;EhBiuBrB;AACF;;AgB9tBA;;+EhBkuB+E;AgB9tB/E;EAEC;;IAGI,qBAAqB;EhB8tBxB;EgB1tBD;IACC,sBAAsB;EhB4tBtB;AACF;;AgBvtBA;;+EhB2tB+E;AgBvtB/E;;+EhB0tB+E;AiBntC/E;;+EjBstC+E;AiBltC/E;;EAQI,WAAW;AjB8sCf;;AiBttCA;EAaG,0CAAsC;EACtC,aAAa;EACb,8BAAsB;EAAtB,sBAAsB;EhBuIrB,qCgBtIuC;EhBwIvC,6BgBxIuC;AjB+sC3C;;AiB/tCA;EAmBI,0CAAsC;AjBgtC1C;;AiBnuCA;EhBCI,kBAAkB;EAClB,qBAAqB;EACrB,WAAW;EACX,sBAJmD;EgByBpD,gBAAgB;AjBktCnB;;AiB3uCA;EAiCK,gBAAgB;EAChB,qBAAqB;EACrB,UAAU;AjB8sCf;;AiBjvCA;EAsCM,gBAAgB;EAChB,gBAAgB;EAChB,eAAe;EACf,sBAAsB;AjB+sC5B;;AiBxvCA;EA4CO,eAAe;EACZ,gBAAgB;AjBgtC1B;;AiB7vCA;EAiDO,cfjBmB;AFiuC1B;;AiBjwCA;EAuDK,qBAAqB;EACrB,UAAU;EACV,iBAAiB;EACjB,WAAW;EACX,YAAY;EACZ,gBAAgB;AjB8sCrB;;AiB1wCA;EAiEQ,+BAA6B;EAC7B,UAAU;EACV,kBAAkB;AjB6sC1B;;AiBhxCA;EAsES,YAA2B;AjB8sCpC;;AiBnsCA;;+EjBusC+E;AkB5xC/E;;+ElB+xC+E;AkB3xC/E;EAOI,6DAAqD;EAArD,qDAAqD;AlBuxCzD;;AkB9xCA;EAgBK,UAAU;AlBkxCf;;AkBlyCA;EAqBI,gBAAgB;AlBixCpB;;AkB3wCA;;+ElB+wC+E;AmB9yC/E;;+EnBizC+E;AmB7yC/E;EAOI,6DAAqD;EAArD,qDAAqD;AnByyCzD;;AmBhzCA;ElBkKI,gCkBlJqC;ElBoJrC,4BkBpJqC;ElBoJrC,wBkBpJqC;AnBsyCzC;;AmBtzCA;ElBkKI,gCkB9IqC;ElBgJrC,4BkBhJqC;ElBgJrC,wBkBhJqC;AnBwyCzC;;AmB5zCA;EAyBI,gBAAgB;AnBuyCpB;;AmBh0CA;EA8BG,kBAAkB;EAClB,cAAc;EACd,WAAW;EACX,YAAY;EACZ,SAAS;EACT,OAAO;EACP,uBAAuB;EACvB,sBAAsB;EACtB,gBAAgB;EAChB,8BAAsB;EAAtB,sBAAsB;ElB2HrB,mCkB1HsC;ElB4HtC,+BkB5HsC;ElB4HtC,2BkB5HsC;ElBoHtC,sDkBnH+C;ElBqH/C,8CkBrH+C;ElBqH/C,sCkBrH+C;ElBqH/C,0EkBrH+C;AnB0yCnD;;AmBn1CA;ElBkKI,oCkBrHuC;ElBuHvC,gCkBvHuC;ElBuHvC,4BkBvHuC;ElB+GvC,sDkB9G+C;ElBgH/C,8CkBhH+C;ElBgH/C,sCkBhH+C;ElBgH/C,0EkBhH+C;AnB8yCnD;;AmB1yCA;;+EnB8yC+E;AoBp2C/E;;+EpBu2C+E;AoBn2C/E;EnBCI,kBAAkB;EAClB,qBAAqB;EACrB,WAAW;EACX,sBAJmD;EmBMpD,mBAAmB;ApBo2CtB;;AoBh2CA;;+EpBo2C+E;AqBl3C/E;;+ErBq3C+E;AqBj3C/E;EpBsJI,4CoB7IgD;EpB+IhD,oCoB/IgD;ArB62CpD;;AqBt3CA;EAYwB,UAAU;ArB82ClC;;AqB13CA;EpBQI,kBAAkB;EAClB,cAAc;EACd,WAAW;EACX,YAAY;EACZ,MAAM;EACN,OAAO;EoBIL,UAAU;ArBm3ChB;;AqBp4CA;EA+BgC,UAAU;ArBy2C1C;;AqBx4CA;EAmCQ,UAAU;ArBy2ClB;;AqB54CA;EpBCI,kBAAkB;EAClB,qBAAqB;EACrB,WAAW;EACX,sBAJmD;EoB6CpD,gBAAgB;ArBu2CnB;;AqBn2CA;;+ErBu2C+E;AsB55C/E;;+EtB+5C+E;AsB35C/E;EACI,kBAAkB;EACrB,qBAAqB;EACrB,sBAAsB;AtB65CvB;;AsBh6CA;EAME,SAAS;EACT,UAAU;AtB85CZ;;AsBr6CA;EAWE,kBAAkB;EAClB,qBAAqB;EACrB,sBAAsB;AtB85CxB;;AsB36CA;EAgBG,kBAAkB;EAClB,qBAAqB;EACrB,sBAAsB;EACtB,iBAAiB;AtB+5CpB;;AsBl7CA;EAsBI,SAAS;AtBg6Cb;;AsBt7CA;EA4BE,kBAAkB;EAClB,qBAAqB;EACrB,sBAAsB;EACtB,SAAS;AtB85CX;;AsB77CA;EAmCE,kBAAkB;EAClB,qBAAqB;EACrB,sBAAsB;EACtB,SAAS;AtB85CX;;AsBp8CA;EAyCG,cAAc;AtB+5CjB;;AsB35CA;;+EtB+5C+E;AuBh9C/E;;+EvBm9C+E;AuB/8C/E;EtBCI,kBAAkB;EAClB,qBAAqB;EACrB,WAAW;EACX,sBAJmD;ADq9CvD;;AuBr9CA;;EAWK,WrBsCoB;AFy6CzB;;AuB19CA;;EAcM,crBkBoB;AF+7C1B;;AuB/9CA;;EA0BK,crBY0B;AF87C/B;;AuBp+CA;;EA6BM,crBGoB;AFy8C1B;;AuBz+CA;EA0CM,0CrBOmB;AF47CzB;;AuB7+CA;EAiDO,yBrBjBmB;AFi9C1B;;AuBj/CA;EA+DM,uCrBzByB;AF+8C/B;;AuBr/CA;EAsEO,yBrBtCmB;AFy9C1B;;AuBz/CA;;EtBkKI,mCsB/EwC;EtBiFxC,+BsBjFwC;EtBiFxC,2BsBjFwC;AvB66C5C;;AuBhgDA;EAwFI,kBAAkB;EAClB,OAAO;EACP,YAAY;EACZ,WAAW;EACX,SAAS;AvB46Cb;;AuBv6CA;;+EvB26C+E;AwBhhD/E;;+ExBmhD+E;AwB/gD/E;EvBCI,kBAAkB;EAClB,qBAAqB;EACrB,WAAW;EACX,sBAJmD;ADqhDvD;;AwBrhDA;;;EvBCI,kBAAkB;EAClB,qBAAqB;EACrB,WAAW;EACX,sBAJmD;AD8hDvD;;AwB9hDA;EAUQ,8BAAsB;EAAtB,sBAAsB;AxBwhD9B;;AwBliDA;EAaY,kBAAkB;EAClB,qBAAqB;EACrB,SAAS;EACT,UAAU;EvBkJlB,mCuBhJ+C;EvBkJ/C,+BuBlJ+C;EvBkJ/C,2BuBlJ+C;AxB0hDnD;;AwB5iDA;EAyBY,sBAAsB;EACtB,kBAAkB;AxBuhD9B;;AwBjjDA;EA+BQ,kBAAkB;EAuDlB,sBAAA;AxBg+CR;;AwBtjDA;EAkCY,gBAAgB;AxBwhD5B;;AwB1jDA;EvBCI,kBAAkB;EAClB,qBAAqB;EACrB,WAAW;EACX,sBAJmD;EuBuC3C,gBAAgB;AxB2hD5B;;AwBlkDA;EA0CgB,cAAc;EACd,cAAc;EACd,sBAAsB;EACtB,kBAAkB;EAClB,8BAAsB;EAAtB,sBAAsB;AxB4hDtC;;AwB1kDA;EAmDY,eAAe;EACf,gBAAgB;EAChB,gBAAgB;AxB2hD5B;;AwBhlDA;EAyDY,gBAAgB;EAChB,eAAe;EACf,cAAc;EACd,iBAAiB;AxB2hD7B;;AwBvlDA;EAgEY,gBAAgB;AxB2hD5B;;AwB3lDA;EAmEgB,eAAe;EACf,gBAAgB;EAChB,ctBrCU;EsBsCV,kBAAkB;AxB4hDlC;;AwBlmDA;EA0EgB,eAAe;EACf,gBAAgB;EAChB,ctB3CoB;EsB4CpB,kBAAkB;EAClB,oBAAoB;EACpB,yBAAyB;EACzB,uCtBnD8B;AF+kD9C;;AwB5mDA;;;EA4FgB,WAAW;AxBshD3B;;AwBlnDA;EAkGoB,WAAW;AxBohD/B;;AwBtnDA;EAqGoB,+BAAgB;AxBqhDpC;;AwB1nDA;EA8GwB,0CAA2B;AxBghDnD;;AwB9nDA;EAqH4B,sBAAsB;EACtB,kBAAkB;AxB6gD9C;;AwBnoDA;EAmI4B,WAAW;AxBogDvC;;AwB5/CQ;EA3IR;IAqJ4B,aAAa;ExBu/CvC;AACF;;AwB/+CA;;+ExBm/C+E", "file": "../../../../boostup-core/assets/css/scss/custom-post-types-map.css", "sourcesContent": ["/* ==========================================================================\n   Global partials\n   ========================================================================== */\n@import '../../../../../themes/boostup/assets/css/scss/variables';\n@import '../../../../../themes/boostup/assets/css/scss/mixins';\n\n/* ==========================================================================\n   Custom Post Types styles\n   ========================================================================== */\n@import \"D:/Projects/boostup/wp-content/plugins/boostup-core/post-types/portfolio/assets/css/scss/default/single/_portfolio-single.scss\";\n@import \"D:/Projects/boostup/wp-content/plugins/boostup-core/post-types/portfolio/assets/css/scss/default/single/layout-collections/_gallery.scss\";\n@import \"D:/Projects/boostup/wp-content/plugins/boostup-core/post-types/portfolio/assets/css/scss/default/single/layout-collections/_huge-images.scss\";\n@import \"D:/Projects/boostup/wp-content/plugins/boostup-core/post-types/portfolio/assets/css/scss/default/single/layout-collections/_images.scss\";\n@import \"D:/Projects/boostup/wp-content/plugins/boostup-core/post-types/portfolio/assets/css/scss/default/single/layout-collections/_masonry.scss\";\n@import \"D:/Projects/boostup/wp-content/plugins/boostup-core/post-types/portfolio/assets/css/scss/default/single/layout-collections/_slider.scss\";\n@import \"D:/Projects/boostup/wp-content/plugins/boostup-core/post-types/portfolio/assets/css/scss/default/single/layout-collections/_small-gallery.scss\";\n@import \"D:/Projects/boostup/wp-content/plugins/boostup-core/post-types/portfolio/assets/css/scss/default/single/layout-collections/_small-images.scss\";\n@import \"D:/Projects/boostup/wp-content/plugins/boostup-core/post-types/portfolio/assets/css/scss/default/single/layout-collections/_small-masonry.scss\";\n@import \"D:/Projects/boostup/wp-content/plugins/boostup-core/post-types/portfolio/assets/css/scss/default/single/layout-collections/_small-slider.scss\";\n@import \"D:/Projects/boostup/wp-content/plugins/boostup-core/post-types/portfolio/assets/css/scss/default/single/parts/_navigation.scss\";\n@import \"D:/Projects/boostup/wp-content/plugins/boostup-core/post-types/portfolio/assets/css/scss/default/single/parts/_related-posts.scss\";\n@import \"D:/Projects/boostup/wp-content/plugins/boostup-core/post-types/portfolio/shortcodes/portfolio-category-list/assets/css/scss/default/_portfolio-category-list.scss\";\n@import \"D:/Projects/boostup/wp-content/plugins/boostup-core/post-types/portfolio/shortcodes/portfolio-list/assets/css/scss/default/_portfolio-list.scss\";\n@import \"D:/Projects/boostup/wp-content/plugins/boostup-core/post-types/portfolio/shortcodes/portfolio-list/assets/css/scss/default/layout-collections/_boxed.scss\";\n@import \"D:/Projects/boostup/wp-content/plugins/boostup-core/post-types/portfolio/shortcodes/portfolio-list/assets/css/scss/default/layout-collections/_gallery-overlay.scss\";\n@import \"D:/Projects/boostup/wp-content/plugins/boostup-core/post-types/portfolio/shortcodes/portfolio-list/assets/css/scss/default/layout-collections/_gallery-slide-from-image-bottom.scss\";\n@import \"D:/Projects/boostup/wp-content/plugins/boostup-core/post-types/portfolio/shortcodes/portfolio-list/assets/css/scss/default/layout-collections/_standard-shader.scss\";\n@import \"D:/Projects/boostup/wp-content/plugins/boostup-core/post-types/portfolio/shortcodes/portfolio-list/assets/css/scss/default/layout-collections/_standard-switch-images.scss\";\n@import \"D:/Projects/boostup/wp-content/plugins/boostup-core/post-types/portfolio/shortcodes/portfolio-project-info/assets/css/scss/default/_portfolio-project-info.scss\";\n@import \"D:/Projects/boostup/wp-content/plugins/boostup-core/post-types/portfolio/shortcodes/portfolio-slider/assets/css/scss/default/_portfolio-slider.scss\";\n@import \"D:/Projects/boostup/wp-content/plugins/boostup-core/post-types/testimonials/shortcodes/testimonials/assets/css/scss/default/_testimonials-standard.scss\";", "/* ==========================================================================\n   Global partials\n   ========================================================================== */\n@keyframes animate-btn-line {\n  0% {\n    -webkit-transform-origin: 0 50%;\n    -moz-transform-origin: 0 50%;\n    transform-origin: 0 50%;\n    -webkit-transform: scaleX(0);\n    -moz-transform: scaleX(0);\n    transform: scaleX(0);\n  }\n  100% {\n    -webkit-transform-origin: 0 50%;\n    -moz-transform-origin: 0 50%;\n    transform-origin: 0 50%;\n    -webkit-transform: scaleX(1);\n    -moz-transform: scaleX(1);\n    transform: scaleX(1);\n  }\n}\n\n@-webkit-keyframes mkdfPulsebig {\n  0% {\n    -moz-box-shadow: 0 0 0 0 currentColor;\n    box-shadow: 0 0 0 0 currentColor;\n  }\n  70% {\n    -moz-box-shadow: 0 0 0 20px rgba(234, 61, 86, 0.3);\n    box-shadow: 0 0 0 20px rgba(234, 61, 86, 0);\n  }\n  100% {\n    -moz-box-shadow: 0 0 0 0 rgba(234, 61, 86, 0);\n    box-shadow: 0 0 0 0 rgba(234, 61, 86, 0);\n  }\n}\n\n@keyframes mkdfPulsebig {\n  0% {\n    -moz-box-shadow: 0 0 0 0 currentColor;\n    box-shadow: 0 0 0 0 currentColor;\n  }\n  70% {\n    -moz-box-shadow: 0 0 0 20px rgba(234, 61, 86, 0.3);\n    box-shadow: 0 0 0 20px rgba(234, 61, 86, 0);\n  }\n  100% {\n    -moz-box-shadow: 0 0 0 0 rgba(234, 61, 86, 0);\n    box-shadow: 0 0 0 0 rgba(234, 61, 86, 0);\n  }\n}\n\n@-webkit-keyframes mkdfPulsesmall {\n  0% {\n    -webkit-box-shadow: 0 0 0 0 currentColor;\n  }\n  70% {\n    -webkit-box-shadow: 0 0 0 10px rgba(234, 61, 86, 0.15);\n  }\n  100% {\n    -webkit-box-shadow: 0 0 0 0 rgba(234, 61, 86, 0);\n  }\n}\n\n@keyframes mkdfPulsesmall {\n  0% {\n    -moz-box-shadow: 0 0 0 0 currentColor;\n    box-shadow: 0 0 0 0 currentColor;\n  }\n  70% {\n    -moz-box-shadow: 0 0 0 10px rgba(234, 61, 86, 0.15);\n    box-shadow: 0 0 0 10px rgba(234, 61, 86, 0);\n  }\n  100% {\n    -moz-box-shadow: 0 0 0 0 rgba(234, 61, 86, 0);\n    box-shadow: 0 0 0 0 rgba(234, 61, 86, 0);\n  }\n}\n\n@-webkit-keyframes mkdfPulsesmallfirst {\n  0% {\n    -webkit-box-shadow: 0 0 0 0 #ea3d56;\n  }\n  70% {\n    -webkit-box-shadow: 0 0 0 10px rgba(234, 61, 86, 0.15);\n  }\n  100% {\n    -webkit-box-shadow: 0 0 0 0 rgba(234, 61, 86, 0);\n  }\n}\n\n@keyframes mkdfPulsesmallfirst {\n  0% {\n    -moz-box-shadow: 0 0 0 0 #ea3d56;\n    box-shadow: 0 0 0 0 #ea3d56;\n  }\n  70% {\n    -moz-box-shadow: 0 0 0 10px rgba(234, 61, 86, 0.15);\n    box-shadow: 0 0 0 10px rgba(234, 61, 86, 0);\n  }\n  100% {\n    -moz-box-shadow: 0 0 0 0 rgba(234, 61, 86, 0);\n    box-shadow: 0 0 0 0 rgba(234, 61, 86, 0);\n  }\n}\n\n/* ==========================================================================\n   Custom Post Types styles\n   ========================================================================== */\n/* ==========================================================================\n   Portfolio Single page style - begin\n   ========================================================================== */\n.mkdf-portfolio-single-holder {\n  position: relative;\n  display: inline-block;\n  width: 100%;\n  vertical-align: middle;\n  margin: 0 0 50px;\n}\n\n.mkdf-portfolio-single-holder .mkdf-ps-image-holder {\n  position: relative;\n  display: inline-block;\n  width: 100%;\n  vertical-align: middle;\n}\n\n.mkdf-portfolio-single-holder .mkdf-ps-image-holder .mkdf-ps-image:not(.mkdf-item-space) {\n  position: relative;\n  display: inline-block;\n  width: 100%;\n  vertical-align: middle;\n  box-sizing: border-box;\n}\n\n.mkdf-portfolio-single-holder .mkdf-ps-image-holder .mkdf-ps-image a, .mkdf-portfolio-single-holder .mkdf-ps-image-holder .mkdf-ps-image img {\n  position: relative;\n  display: block;\n}\n\n.mkdf-portfolio-single-holder .mkdf-ps-info-holder {\n  position: relative;\n  display: inline-block;\n  width: 100%;\n  vertical-align: middle;\n}\n\n.mkdf-portfolio-single-holder .mkdf-ps-info-holder .mkdf-ps-info-item {\n  position: relative;\n  display: inline-block;\n  width: 100%;\n  vertical-align: middle;\n  margin: 0 0 10px;\n}\n\n.mkdf-portfolio-single-holder .mkdf-ps-info-holder .mkdf-ps-info-item.mkdf-ps-content-item {\n  margin: 15px 0 70px;\n}\n\n.mkdf-portfolio-single-holder .mkdf-ps-info-holder .mkdf-ps-info-item.mkdf-ps-social-share {\n  border-top: 1px solid rgba(225, 225, 225, 0.3);\n  padding: 20px 0 0;\n}\n\n.mkdf-portfolio-single-holder .mkdf-ps-info-holder .mkdf-ps-info-item.mkdf-ps-social-share span {\n  color: #fff;\n  padding: 7px;\n  border-radius: 50%;\n}\n\n.mkdf-portfolio-single-holder .mkdf-ps-info-holder .mkdf-ps-info-item.mkdf-ps-social-share span.social_facebook {\n  background: #3b5998;\n}\n\n.mkdf-portfolio-single-holder .mkdf-ps-info-holder .mkdf-ps-info-item.mkdf-ps-social-share span.social_twitter {\n  background: #55acee;\n}\n\n.mkdf-portfolio-single-holder .mkdf-ps-info-holder .mkdf-ps-info-item.mkdf-ps-social-share span.social_linkedin {\n  background: #007bb5;\n}\n\n.mkdf-portfolio-single-holder .mkdf-ps-info-holder .mkdf-ps-info-item.mkdf-ps-social-share span.social_instagram {\n  background: #cd486b;\n}\n\n.mkdf-portfolio-single-holder .mkdf-ps-info-holder .mkdf-ps-info-item.mkdf-ps-social-share span.social_pinterest {\n  background: #cb2027;\n}\n\n.mkdf-portfolio-single-holder .mkdf-ps-info-holder .mkdf-ps-info-item.mkdf-ps-social-share span.social_tumblr {\n  background: #32506d;\n}\n\n.mkdf-portfolio-single-holder .mkdf-ps-info-holder .mkdf-ps-info-item.mkdf-ps-social-share span.social_googleplus {\n  background: #dd4b39;\n}\n\n.mkdf-portfolio-single-holder .mkdf-ps-info-holder .mkdf-ps-info-item.mkdf-ps-social-share span.fa-vk {\n  background: #45668e;\n}\n\n.mkdf-portfolio-single-holder .mkdf-ps-info-holder .mkdf-ps-info-item p, .mkdf-portfolio-single-holder .mkdf-ps-info-holder .mkdf-ps-info-item a {\n  margin: 0;\n}\n\n.mkdf-portfolio-single-holder .mkdf-ps-info-holder .mkdf-ps-info-item h6, .mkdf-portfolio-single-holder .mkdf-ps-info-holder .mkdf-ps-info-item p {\n  display: inline;\n}\n\n.mkdf-portfolio-single-holder .mkdf-ps-info-holder .mkdf-ps-info-title,\n.mkdf-portfolio-single-holder .mkdf-ps-info-holder .mkdf-ps-title {\n  margin: 0;\n}\n\n/* ==========================================================================\n   Portfolio Single page style - end\n   ========================================================================== */\n/* ==========================================================================\n   Portfolio Single page specific style for types - begin\n   ========================================================================== */\n.mkdf-portfolio-single-holder .mkdf-ps-image-holder.mkdf-grid-masonry-list .mkdf-ps-image a {\n  height: 100%;\n}\n\n/* ==========================================================================\n   Portfolio Single page specific style for types - end\n   ========================================================================== */\n/* ==========================================================================\n   Portfolio Single - Gallery layout style - begin\n   ========================================================================== */\n.mkdf-portfolio-single-holder.mkdf-ps-gallery-layout .mkdf-ps-image-holder {\n  width: 100%;\n}\n\n.mkdf-portfolio-single-holder.mkdf-ps-gallery-layout .mkdf-ps-image-holder .mkdf-ps-image {\n  float: left;\n}\n\n.mkdf-portfolio-single-holder.mkdf-ps-gallery-layout > .mkdf-grid-row {\n  margin-top: 40px;\n}\n\n/* ==========================================================================\n   Portfolio Single - Gallery layout style - end\n   ========================================================================== */\n/* ==========================================================================\n   Portfolio Single - Huge Images layout style - begin\n   ========================================================================== */\n.mkdf-portfolio-single-holder.mkdf-ps-huge-images-layout {\n  padding: 0 4%;\n  box-sizing: border-box;\n}\n\n.mkdf-portfolio-single-holder.mkdf-ps-huge-images-layout .mkdf-ps-image-holder {\n  margin: 0 0 40px;\n}\n\n.mkdf-portfolio-single-holder.mkdf-ps-huge-images-layout .mkdf-ps-image-holder .mkdf-ps-image {\n  margin: 0 0 30px;\n}\n\n.mkdf-portfolio-single-holder.mkdf-ps-huge-images-layout .mkdf-ps-image-holder .mkdf-ps-image:last-child {\n  margin: 0;\n}\n\n@media only screen and (max-width: 1200px) {\n  .mkdf-portfolio-single-holder.mkdf-ps-huge-images-layout {\n    padding: 0 40px;\n  }\n}\n\n@media only screen and (max-width: 768px) {\n  .mkdf-portfolio-single-holder.mkdf-ps-huge-images-layout {\n    padding: 0 30px;\n  }\n}\n\n/* ==========================================================================\n   Portfolio Single - Huge Images layout style - end\n   ========================================================================== */\n/* ==========================================================================\n   Portfolio Single - Images layout style - begin\n   ========================================================================== */\n.mkdf-portfolio-single-holder.mkdf-ps-images-layout .mkdf-ps-image-holder {\n  margin: 0 0 40px;\n}\n\n.mkdf-portfolio-single-holder.mkdf-ps-images-layout .mkdf-ps-image-holder .mkdf-ps-image {\n  margin: 0 0 30px;\n}\n\n.mkdf-portfolio-single-holder.mkdf-ps-images-layout .mkdf-ps-image-holder .mkdf-ps-image:last-child {\n  margin: 0;\n}\n\n/* ==========================================================================\n   Portfolio Single - Images layout style - end\n   ========================================================================== */\n/* ==========================================================================\n   Portfolio Single - Masonry layout style - begin\n   ========================================================================== */\n.mkdf-portfolio-single-holder.mkdf-ps-masonry-layout > .mkdf-grid-row {\n  margin-top: 40px;\n}\n\n/* ==========================================================================\n   Portfolio Single - Masonry layout style - end\n   ========================================================================== */\n/* ==========================================================================\n   Portfolio Single - Slider layout style - begin\n   ========================================================================== */\n.mkdf-portfolio-single-holder.mkdf-ps-slider-layout .mkdf-ps-image-holder {\n  margin: 0 0 40px;\n}\n\n.mkdf-portfolio-single-holder.mkdf-ps-slider-layout .mkdf-ps-image-holder .mkdf-ps-image-inner {\n  visibility: hidden;\n}\n\n.mkdf-portfolio-single-holder.mkdf-ps-slider-layout .mkdf-ps-image-holder .mkdf-ps-image img {\n  width: 100%;\n}\n\n/* ==========================================================================\n   Portfolio Single - Slider layout style - end\n   ========================================================================== */\n/* ==========================================================================\n   Portfolio Single - Small Gallery layout style - begin\n   ========================================================================== */\n.mkdf-portfolio-single-holder.mkdf-ps-small-gallery-layout .mkdf-ps-image-holder {\n  width: 100%;\n}\n\n.mkdf-portfolio-single-holder.mkdf-ps-small-gallery-layout .mkdf-ps-image-holder .mkdf-ps-image {\n  float: left;\n}\n\n.mkdf-portfolio-single-holder.mkdf-ps-small-gallery-layout .mkdf-ps-content-item {\n  margin: 0 0 30px;\n}\n\n/* ==========================================================================\n   Portfolio Single - Small Gallery layout style - end\n   ========================================================================== */\n/* ==========================================================================\n   Portfolio Single - Small Images layout style - begin\n   ========================================================================== */\n.mkdf-portfolio-single-holder.mkdf-ps-small-images-layout .mkdf-ps-image-holder .mkdf-ps-image {\n  margin: 0 0 30px;\n}\n\n.mkdf-portfolio-single-holder.mkdf-ps-small-images-layout .mkdf-ps-image-holder .mkdf-ps-image:last-child {\n  margin: 0;\n}\n\n.mkdf-portfolio-single-holder.mkdf-ps-small-images-layout .mkdf-ps-content-item {\n  margin: 0 0 30px;\n}\n\n/* ==========================================================================\n   Portfolio Single - Small Images layout style - end\n   ========================================================================== */\n/* ==========================================================================\n   Portfolio Single - Small Masonry layout style - begin\n   ========================================================================== */\n.mkdf-portfolio-single-holder.mkdf-ps-small-masonry-layout .mkdf-ps-content-item {\n  margin: 0 0 30px;\n}\n\n/* ==========================================================================\n   Portfolio Single - Small Masonry layout style - end\n   ========================================================================== */\n/* ==========================================================================\n   Portfolio Single - Small Slider layout style - begin\n   ========================================================================== */\n.mkdf-portfolio-single-holder.mkdf-ps-small-slider-layout .mkdf-ps-image-holder .mkdf-ps-image-inner {\n  visibility: hidden;\n}\n\n.mkdf-portfolio-single-holder.mkdf-ps-small-slider-layout .mkdf-ps-image-holder .mkdf-ps-image-inner .owl-stage-outer {\n  width: calc(100% - 1px);\n}\n\n.mkdf-portfolio-single-holder.mkdf-ps-small-slider-layout .mkdf-ps-image-holder .mkdf-ps-image img {\n  width: 100%;\n}\n\n.mkdf-portfolio-single-holder.mkdf-ps-small-slider-layout .mkdf-ps-content-item {\n  margin: 0 0 30px;\n}\n\n/* ==========================================================================\n   Portfolio Single - Small Slider layout style - end\n   ========================================================================== */\n/* ==========================================================================\n   Portfolio Single navigation style - begin\n   ========================================================================== */\n.mkdf-ps-navigation {\n  position: relative;\n  display: table;\n  width: 100%;\n  vertical-align: middle;\n  padding: 0;\n  margin: 44px 0 0;\n  clear: both;\n  box-sizing: border-box;\n}\n\n.mkdf-ps-full-width-custom-layout .mkdf-ps-navigation {\n  padding: 0 40px;\n}\n\n.mkdf-ps-navigation .mkdf-ps-back-btn {\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  display: inline-block;\n  vertical-align: middle;\n  -webkit-transform: translateX(-50%) translateY(-50%);\n  -moz-transform: translateX(-50%) translateY(-50%);\n  transform: translateX(-50%) translateY(-50%);\n}\n\n.mkdf-ps-navigation .mkdf-ps-back-btn a {\n  position: relative;\n  display: inline-block;\n  margin: 0;\n  padding: 0;\n  vertical-align: middle;\n  cursor: pointer;\n  font-size: 23px;\n  line-height: 1;\n}\n\n.mkdf-ps-navigation .mkdf-ps-back-btn a span {\n  display: block;\n  line-height: inherit;\n}\n\n.mkdf-ps-navigation .mkdf-ps-back-btn a span:before, .mkdf-ps-navigation .mkdf-ps-back-btn a span:after {\n  display: block;\n  line-height: 14px;\n}\n\n.mkdf-ps-navigation .mkdf-ps-back-btn a span:after {\n  content: \"\\e0a6\";\n}\n\n.mkdf-ps-navigation .mkdf-ps-prev,\n.mkdf-ps-navigation .mkdf-ps-next {\n  position: relative;\n  display: table-cell;\n  vertical-align: middle;\n  width: 49%;\n  padding: 0;\n  box-sizing: border-box;\n}\n\n.mkdf-ps-navigation .mkdf-ps-prev a,\n.mkdf-ps-navigation .mkdf-ps-next a {\n  position: relative;\n  display: inline-block;\n  vertical-align: middle;\n  font-size: 46px;\n  line-height: 55px;\n}\n\n.mkdf-ps-navigation .mkdf-ps-prev a .mkdf-ps-nav-mark,\n.mkdf-ps-navigation .mkdf-ps-next a .mkdf-ps-nav-mark {\n  position: relative;\n  display: inline-block;\n  vertical-align: top;\n}\n\n.mkdf-ps-navigation .mkdf-ps-prev a .mkdf-ps-nav-mark:before,\n.mkdf-ps-navigation .mkdf-ps-next a .mkdf-ps-nav-mark:before {\n  display: block;\n  line-height: inherit;\n}\n\n.mkdf-ps-navigation .mkdf-ps-prev a .mkdf-ps-nav-mark {\n  left: -14px;\n}\n\n.mkdf-ps-navigation .mkdf-ps-next {\n  text-align: right;\n}\n\n.mkdf-ps-navigation .mkdf-ps-next a .mkdf-ps-nav-mark {\n  right: -14px;\n}\n\n/* ==========================================================================\n   Portfolio Single navigation style - end\n   ========================================================================== */\n/* ==========================================================================\n   Portfolio Single navigation style - begin\n   ========================================================================== */\n.mkdf-ps-related-posts-holder {\n  position: relative;\n  display: inline-block;\n  width: 100%;\n  vertical-align: middle;\n  margin: 80px 0 60px;\n  clear: both;\n}\n\n.mkdf-ps-related-posts-holder .mkdf-ps-related-posts {\n  margin: 0 -15px;\n}\n\n.mkdf-ps-related-posts-holder .mkdf-ps-related-post {\n  position: relative;\n  display: inline-block;\n  vertical-align: middle;\n  width: 25%;\n  float: left;\n  padding: 0 15px;\n  box-sizing: border-box;\n}\n\n@media only screen and (max-width: 1024px) {\n  .mkdf-ps-related-posts-holder .mkdf-ps-related-post {\n    width: 50%;\n    margin: 0 0 30px;\n  }\n  .mkdf-ps-related-posts-holder .mkdf-ps-related-post:nth-child(2n+1) {\n    clear: both;\n  }\n}\n\n@media only screen and (max-width: 680px) {\n  .mkdf-ps-related-posts-holder .mkdf-ps-related-post {\n    width: 100%;\n  }\n}\n\n.mkdf-ps-related-posts-holder .mkdf-ps-related-post .mkdf-pli-image-hover {\n  position: absolute;\n  display: block;\n  width: 100%;\n  height: 100%;\n  top: 0;\n  left: 0;\n  padding: 20px;\n  opacity: 0;\n  text-align: center;\n  box-sizing: border-box;\n  background-color: #ffc40e;\n  color: #fff;\n  font-size: 72px;\n  -webkit-transition: opacity 0.2s ease-in-out;\n  -moz-transition: opacity 0.2s ease-in-out;\n  transition: opacity 0.2s ease-in-out;\n}\n\n.mkdf-ps-related-posts-holder .mkdf-ps-related-post .mkdf-pli-image-hover .mkdf-pli-image-hover-table {\n  display: table;\n  height: 100%;\n  width: 100%;\n}\n\n.mkdf-ps-related-posts-holder .mkdf-ps-related-post .mkdf-pli-image-hover .mkdf-pli-image-hover-table i {\n  display: table-cell;\n  vertical-align: middle;\n}\n\n.mkdf-ps-related-posts-holder .mkdf-ps-related-post:hover .mkdf-pli-image-hover {\n  opacity: 1;\n  cursor: pointer;\n}\n\n.mkdf-ps-related-posts-holder .mkdf-ps-related-image {\n  position: relative;\n  display: inline-block;\n  width: 100%;\n  vertical-align: middle;\n  overflow: hidden;\n}\n\n.mkdf-ps-related-posts-holder .mkdf-ps-related-image a, .mkdf-ps-related-posts-holder .mkdf-ps-related-image img {\n  display: block;\n}\n\n.mkdf-ps-related-posts-holder .mkdf-ps-related-text {\n  position: relative;\n  display: inline-block;\n  width: 100%;\n  vertical-align: middle;\n  margin: 20px 0 0;\n}\n\n.mkdf-ps-related-posts-holder .mkdf-ps-related-text .mkdf-ps-related-title {\n  margin: 0;\n}\n\n.mkdf-ps-related-posts-holder .mkdf-ps-related-text .mkdf-ps-related-categories {\n  margin: 6px 0 0;\n}\n\n/* ==========================================================================\n   Portfolio Single navigation style - end\n   ========================================================================== */\n/* ==========================================================================\n   Portfolio Category List shortcode style - begin\n   ========================================================================== */\n.mkdf-portfolio-category-list-holder {\n  position: relative;\n  display: inline-block;\n  width: 100%;\n  vertical-align: middle;\n}\n\n.touch .mkdf-portfolio-category-list-holder article {\n  cursor: pointer;\n}\n\n.mkdf-portfolio-category-list-holder article:hover .mkdf-pcli-text-holder {\n  opacity: 1;\n}\n\n.mkdf-portfolio-category-list-holder article .mkdf-pcl-item-inner {\n  position: relative;\n  display: inline-block;\n  width: 100%;\n  vertical-align: middle;\n  overflow: hidden;\n}\n\n.mkdf-portfolio-category-list-holder article .mkdf-pcli-image {\n  position: relative;\n  display: inline-block;\n  width: 100%;\n  vertical-align: middle;\n}\n\n.mkdf-portfolio-category-list-holder article .mkdf-pcli-image img {\n  display: block;\n  width: 100%;\n}\n\n.mkdf-portfolio-category-list-holder article .mkdf-pcli-text-holder {\n  position: absolute;\n  display: block;\n  width: 100%;\n  height: 100%;\n  top: 0;\n  left: 0;\n  padding: 20px;\n  background-color: rgba(27, 44, 88, 0.6);\n  opacity: 0;\n  text-align: center;\n  box-sizing: border-box;\n  -webkit-transition: opacity 0.2s ease-in-out;\n  -moz-transition: opacity 0.2s ease-in-out;\n  transition: opacity 0.2s ease-in-out;\n}\n\n.mkdf-portfolio-category-list-holder article .mkdf-pcli-text-wrapper {\n  position: relative;\n  display: table;\n  table-layout: fixed;\n  height: 100%;\n  width: 100%;\n}\n\n.mkdf-portfolio-category-list-holder article .mkdf-pcli-text {\n  position: relative;\n  display: table-cell;\n  height: 100%;\n  width: 100%;\n  vertical-align: middle;\n}\n\n.mkdf-portfolio-category-list-holder article .mkdf-pcli-title {\n  margin: 0;\n  color: #fff;\n}\n\n.mkdf-portfolio-category-list-holder article .mkdf-pcli-excerpt {\n  margin: 3px 0 0;\n  color: #fff;\n}\n\n.mkdf-portfolio-category-list-holder article .mkdf-pcli-link {\n  position: absolute;\n  display: block;\n  width: 100%;\n  height: 100%;\n  top: 0;\n  left: 0;\n}\n\n/* ==========================================================================\n   Portfolio Category List shortcode style - end\n   ========================================================================== */\n/* ==========================================================================\n   Portfolio shortcode style - begin\n   ========================================================================== */\n.mkdf-portfolio-list-holder {\n  position: relative;\n  display: inline-block;\n  width: 100%;\n  vertical-align: middle;\n  /***** Article Global Style - begin *****/\n  /***** Article Global Style - end *****/\n  /***** Specific Global Style - begin *****/\n  /***** Specific Global Style - end *****/\n  /***** Portfolio Types - end *****/\n  /***** Additional Features - begin *****/\n  /***** Additional Features - end *****/\n}\n\n.mkdf-portfolio-list-holder.mkdf-diagonal-layout.mkdf-five-columns article:nth-child(6) {\n  margin-top: 66px;\n}\n\n@media screen and (max-width: 1024px) {\n  .mkdf-portfolio-list-holder.mkdf-diagonal-layout.mkdf-five-columns article:nth-child(6) {\n    margin-top: 0;\n  }\n}\n\n.mkdf-portfolio-list-holder.mkdf-diagonal-layout.mkdf-five-columns article:nth-child(5) {\n  margin-top: 132px;\n}\n\n@media screen and (max-width: 768px) {\n  .mkdf-portfolio-list-holder.mkdf-diagonal-layout.mkdf-five-columns article:nth-child(5) {\n    margin-top: 0;\n  }\n}\n\n.mkdf-portfolio-list-holder.mkdf-diagonal-layout.mkdf-five-columns article:nth-child(4) {\n  margin-top: 198px;\n}\n\n@media screen and (max-width: 680px) {\n  .mkdf-portfolio-list-holder.mkdf-diagonal-layout.mkdf-five-columns article:nth-child(4) {\n    margin-top: 0;\n  }\n}\n\n.mkdf-portfolio-list-holder.mkdf-diagonal-layout.mkdf-five-columns article:nth-child(3) {\n  margin-top: 264px;\n}\n\n@media screen and (max-width: 680px) {\n  .mkdf-portfolio-list-holder.mkdf-diagonal-layout.mkdf-five-columns article:nth-child(3) {\n    margin-top: 0;\n  }\n}\n\n.mkdf-portfolio-list-holder.mkdf-diagonal-layout.mkdf-five-columns .mkdf-pl-inner.mkdf-masonry-list-wrapper {\n  opacity: 0;\n  -webkit-transition: opacity 0.22s cubic-bezier(0.23, 1, 0.32, 1), -webkit-transform 0s cubic-bezier(0.23, 1, 0.32, 1);\n  transition: opacity 0.22s cubic-bezier(0.23, 1, 0.32, 1), transform 0s cubic-bezier(0.23, 1, 0.32, 1);\n}\n\n.mkdf-portfolio-list-holder.mkdf-diagonal-layout.mkdf-five-columns .mkdf-pl-inner.mkdf-masonry-list-wrapper .mkdf-pl-item .mkdf-pl-item-inner {\n  opacity: 0;\n  -webkit-transform: scale(0.6);\n  -moz-transform: scale(0.6);\n  transform: scale(0.6);\n  -webkit-transition: -webkit-transform 0.6s cubic-bezier(0.18, 0.89, 0.32, 1), opacity 0.2s ease-out;\n  transition: transform 0.4s cubic-bezier(0.18, 0.89, 0.32, 1), opacity 0.2s ease-out;\n}\n\n.mkdf-portfolio-list-holder.mkdf-diagonal-layout.mkdf-five-columns .mkdf-pl-inner.mkdf-masonry-list-wrapper .mkdf-pl-item .mkdf-pl-item-inner.mkdf-appeared {\n  opacity: 1;\n  -webkit-transform: scale(1);\n  -moz-transform: scale(1);\n  transform: scale(1);\n}\n\n.touch .mkdf-portfolio-list-holder article {\n  cursor: pointer;\n}\n\n.mkdf-portfolio-list-holder article .mkdf-pl-item-inner {\n  position: relative;\n  display: inline-block;\n  width: 100%;\n  vertical-align: middle;\n}\n\n.mkdf-portfolio-list-holder article .mkdf-pli-image {\n  position: relative;\n  display: inline-block;\n  width: 100%;\n  vertical-align: middle;\n}\n\n.mkdf-portfolio-list-holder article .mkdf-pli-image img {\n  display: block;\n  width: 100%;\n}\n\n.mkdf-portfolio-list-holder article .mkdf-pli-image .mkdf-pli-image-hover {\n  position: absolute;\n  display: block;\n  width: 100%;\n  height: 100%;\n  top: 0;\n  left: 0;\n  padding: 20px;\n  opacity: 0;\n  text-align: center;\n  box-sizing: border-box;\n  color: #fff;\n  font-size: 72px;\n  -webkit-transition: opacity 0.2s ease-in-out;\n  -moz-transition: opacity 0.2s ease-in-out;\n  transition: opacity 0.2s ease-in-out;\n}\n\n.mkdf-portfolio-list-holder article .mkdf-pli-image .mkdf-pli-image-hover .mkdf-pli-image-hover-table {\n  display: table;\n  height: 100%;\n  width: 100%;\n}\n\n.mkdf-portfolio-list-holder article .mkdf-pli-image .mkdf-pli-image-hover .mkdf-pli-image-hover-table i {\n  display: table-cell;\n  vertical-align: middle;\n  -webkit-transition: all 0.4s ease-in-out;\n  -moz-transition: all 0.4s ease-in-out;\n  transition: all 0.4s ease-in-out;\n}\n\n.mkdf-portfolio-list-holder article:hover .mkdf-pli-image .mkdf-pli-image-hover {\n  opacity: 1;\n}\n\n.mkdf-portfolio-list-holder article:hover .mkdf-pli-image .mkdf-pli-image-hover i {\n  transform: rotate(90deg);\n}\n\n.mkdf-portfolio-list-holder article .mkdf-pli-link {\n  position: absolute;\n  display: block;\n  width: 100%;\n  height: 100%;\n  top: 0;\n  left: 0;\n}\n\n.mkdf-portfolio-list-holder article .mkdf-pli-text-wrapper {\n  position: relative;\n  display: table;\n  table-layout: fixed;\n  height: 100%;\n  width: 100%;\n}\n\n.mkdf-portfolio-list-holder article .mkdf-pli-text {\n  position: relative;\n  display: table-cell;\n  height: 100%;\n  width: 100%;\n  vertical-align: middle;\n}\n\n.mkdf-portfolio-list-holder article .mkdf-pli-text .mkdf-pli-title {\n  margin: 0;\n  line-height: 1.5em;\n}\n\n.mkdf-portfolio-list-holder article .mkdf-pli-text .mkdf-pli-category-holder {\n  position: relative;\n  display: block;\n}\n\n.mkdf-portfolio-list-holder article .mkdf-pli-text .mkdf-pli-category-holder a {\n  position: relative;\n  display: inline-block;\n  vertical-align: middle;\n  padding: 0 6px 0 0;\n  margin: 0 3px 0 0;\n  color: #0c2c5866;\n  z-index: 8;\n  font-size: 12px;\n  font-family: \"Josefin Sans\", sans-serif;\n  font-weight: 700;\n  text-transform: uppercase;\n  letter-spacing: 0.1em;\n}\n\n.mkdf-portfolio-list-holder article .mkdf-pli-text .mkdf-pli-category-holder a:after {\n  position: absolute;\n  top: 0;\n  right: -4px;\n  content: '/';\n  color: #1b2c58;\n  opacity: 0.4;\n  font-size: 14px;\n  line-height: inherit;\n}\n\n.mkdf-portfolio-list-holder article .mkdf-pli-text .mkdf-pli-category-holder a:last-child {\n  margin: 0;\n  padding: 0;\n}\n\n.mkdf-portfolio-list-holder article .mkdf-pli-text .mkdf-pli-category-holder a:last-child:after {\n  display: none;\n}\n\n.mkdf-portfolio-list-holder article .mkdf-pli-text .mkdf-pli-category-holder a:hover {\n  color: #ea3d56;\n}\n\n.mkdf-portfolio-list-holder article .mkdf-pli-text .mkdf-pli-category-holder a:hover:after {\n  color: #1b2c58;\n  opacity: 0.4;\n}\n\n.mkdf-portfolio-list-holder article .mkdf-pli-text .mkdf-pli-excerpt {\n  margin: 10px 0 0;\n}\n\n.mkdf-portfolio-list-holder.mkdf-pl-has-shadow article .mkdf-pli-image {\n  box-shadow: 0 16px 46px 0 rgba(0, 0, 0, 0.3);\n}\n\n.mkdf-portfolio-list-holder.mkdf-pl-has-filter .mkdf-pl-inner {\n  overflow: hidden;\n}\n\n.mkdf-portfolio-list-holder.mkdf-pl-no-content .mkdf-pli-text-holder {\n  display: none;\n}\n\n.mkdf-portfolio-list-holder.mkdf-pl-masonry.mkdf-fixed-masonry-items article .mkdf-pl-item-inner,\n.mkdf-portfolio-list-holder.mkdf-pl-masonry.mkdf-fixed-masonry-items article .mkdf-pli-image {\n  height: 100%;\n}\n\n.mkdf-portfolio-list-holder.mkdf-pl-has-animation article {\n  opacity: 0;\n  -webkit-transform: translateY(80px);\n  -moz-transform: translateY(80px);\n  transform: translateY(80px);\n  -webkit-transition: opacity 0.8s cubic-bezier(0.34, 0.52, 0.57, 1.04), transform 0.8s cubic-bezier(0.34, 0.52, 0.57, 1.04);\n  -moz-transition: opacity 0.8s cubic-bezier(0.34, 0.52, 0.57, 1.04), transform 0.8s cubic-bezier(0.34, 0.52, 0.57, 1.04);\n  transition: opacity 0.8s cubic-bezier(0.34, 0.52, 0.57, 1.04), transform 0.8s cubic-bezier(0.34, 0.52, 0.57, 1.04);\n}\n\n.mkdf-portfolio-list-holder.mkdf-pl-has-animation article.mkdf-item-show {\n  opacity: 1;\n  -webkit-transform: translateY(0);\n  -moz-transform: translateY(0);\n  transform: translateY(0);\n}\n\n.mkdf-portfolio-list-holder.mkdf-pl-has-animation article.mkdf-item-show.mkdf-item-shown {\n  -webkit-transition: none;\n  -moz-transition: none;\n  transition: none;\n}\n\n.touch .mkdf-portfolio-list-holder.mkdf-pl-has-animation article {\n  opacity: 1;\n  -webkit-transform: translateY(0);\n  -moz-transform: translateY(0);\n  transform: translateY(0);\n}\n\n/* ==========================================================================\n   Portfolio shortcode style - end\n   ========================================================================== */\n/* ==========================================================================\n   Portfolio filter style - begin\n   ========================================================================== */\n.mkdf-pl-filter-holder {\n  position: relative;\n  display: inline-block;\n  width: 100%;\n  vertical-align: middle;\n  margin: 0 0 30px;\n  text-align: center;\n}\n\n.mkdf-pl-filter-holder ul {\n  position: relative;\n  display: inline-block;\n  vertical-align: middle;\n  margin: 0;\n  padding: 12px 29px;\n  list-style: none;\n  border: 2px solid #edeeef;\n}\n\n.mkdf-pl-filter-holder ul li {\n  position: relative;\n  display: inline-block;\n  vertical-align: middle;\n  margin: 0;\n  padding: 0 15px;\n  cursor: pointer;\n}\n\n@media only screen and (max-width: 1024px) {\n  .mkdf-pl-filter-holder ul li {\n    padding: 0 10px;\n  }\n}\n\n.mkdf-pl-filter-holder ul li span {\n  position: relative;\n  display: inline-block;\n  vertical-align: middle;\n  color: #1b2c58;\n  font-family: \"Josefin Sans\", sans-serif;\n  font-size: 20px;\n  line-height: 22px;\n  white-space: nowrap;\n  -webkit-transition: color 0.2s ease-out;\n  -moz-transition: color 0.2s ease-out;\n  transition: color 0.2s ease-out;\n}\n\n.mkdf-pl-filter-holder ul li.mkdf-pl-current span, .mkdf-pl-filter-holder ul li:hover span {\n  color: #ea3d56;\n}\n\n/* ==========================================================================\n   Portfolio filter style - end\n   ========================================================================== */\n/* ==========================================================================\n   Portfolio standard pagination style - begin\n   ========================================================================== */\n.mkdf-portfolio-list-holder.mkdf-pl-pag-standard .mkdf-pl-inner {\n  opacity: 1;\n  -webkit-transition: opacity 0.2s ease-out;\n  -moz-transition: opacity 0.2s ease-out;\n  transition: opacity 0.2s ease-out;\n}\n\n.mkdf-portfolio-list-holder.mkdf-pl-pag-standard.mkdf-pl-pag-standard-animate .mkdf-pl-inner {\n  opacity: 0;\n}\n\n.mkdf-pl-standard-pagination {\n  position: relative;\n  display: inline-block;\n  width: 100%;\n  vertical-align: top;\n  margin: 40px 0 0;\n  clear: both;\n}\n\n.mkdf-pl-standard-pagination ul {\n  position: relative;\n  display: inline-block;\n  width: 100%;\n  vertical-align: top;\n  padding: 0;\n  margin: 0;\n  list-style: none;\n  text-align: center;\n}\n\n.mkdf-pl-standard-pagination ul li {\n  position: relative;\n  display: inline-block;\n  vertical-align: top;\n  margin: 0 12px;\n}\n\n.mkdf-pl-standard-pagination ul li a {\n  position: relative;\n  display: inline-block;\n  vertical-align: top;\n  margin: 0;\n  padding: 0;\n}\n\n.mkdf-pl-standard-pagination ul li.mkdf-pag-active a {\n  color: #ea3d56;\n}\n\n.mkdf-pl-standard-pagination ul li.mkdf-pag-prev, .mkdf-pl-standard-pagination ul li.mkdf-pag-next, .mkdf-pl-standard-pagination ul li.mkdf-pag-first, .mkdf-pl-standard-pagination ul li.mkdf-pag-last {\n  margin: 0 2px;\n}\n\n.mkdf-pl-standard-pagination ul li.mkdf-pag-prev a, .mkdf-pl-standard-pagination ul li.mkdf-pag-next a, .mkdf-pl-standard-pagination ul li.mkdf-pag-first a, .mkdf-pl-standard-pagination ul li.mkdf-pag-last a {\n  font-size: 24px;\n}\n\n.mkdf-pl-standard-pagination ul li.mkdf-pag-prev a span, .mkdf-pl-standard-pagination ul li.mkdf-pag-next a span, .mkdf-pl-standard-pagination ul li.mkdf-pag-first a span, .mkdf-pl-standard-pagination ul li.mkdf-pag-last a span {\n  display: block;\n  line-height: inherit;\n}\n\n.mkdf-pl-standard-pagination ul li.mkdf-pag-prev a span:before, .mkdf-pl-standard-pagination ul li.mkdf-pag-next a span:before, .mkdf-pl-standard-pagination ul li.mkdf-pag-first a span:before, .mkdf-pl-standard-pagination ul li.mkdf-pag-last a span:before {\n  display: block;\n  line-height: inherit;\n}\n\n.mkdf-pl-standard-pagination ul li.mkdf-pag-prev a {\n  opacity: 0;\n}\n\n.mkdf-pl-standard-pagination ul li.mkdf-pag-next a {\n  opacity: 1;\n}\n\n/* ==========================================================================\n   Portfolio standard pagination style - end\n   ========================================================================== */\n/* ==========================================================================\n   Portfolio load more pagination style - begin\n   ========================================================================== */\n.mkdf-pl-load-more-holder {\n  position: relative;\n  display: inline-block;\n  width: 100%;\n  vertical-align: middle;\n}\n\n.mkdf-pl-load-more-holder .mkdf-pl-load-more {\n  margin: 32px 0 0;\n  text-align: center;\n}\n\n/* ==========================================================================\n   Portfolio load more pagination style - end\n   ========================================================================== */\n/* ==========================================================================\n   Portfolio loading element style - begin\n   ========================================================================== */\n.mkdf-pl-loading {\n  position: relative;\n  display: none;\n  width: 100%;\n  margin: 40px 0 20px;\n  color: #1b2c58;\n  text-align: center;\n}\n\n.mkdf-pl-loading.mkdf-filter-trigger {\n  position: absolute;\n  top: 250px;\n  left: 0;\n}\n\n.mkdf-pl-loading.mkdf-standard-pag-trigger {\n  position: absolute;\n  top: 50px;\n  left: 0;\n}\n\n.mkdf-pl-has-filter .mkdf-pl-loading.mkdf-standard-pag-trigger {\n  top: 150px;\n}\n\n.mkdf-pl-loading.mkdf-showing {\n  display: block;\n}\n\n.mkdf-pl-loading > div {\n  position: relative;\n  display: inline-block;\n  vertical-align: middle;\n  width: 14px;\n  height: 14px;\n  margin: 0 3px;\n  background-color: #1b2c58;\n  border-radius: 100%;\n  -webkit-animation: sk-bouncedelay 1.4s infinite ease-in-out both;\n  -moz-animation: sk-bouncedelay 1.4s infinite ease-in-out both;\n  animation: sk-bouncedelay 1.4s infinite ease-in-out both;\n}\n\n.mkdf-pl-loading .mkdf-pl-loading-bounce1 {\n  -webkit-animation-delay: -0.32s;\n  -moz-animation-delay: -0.32s;\n  animation-delay: -0.32s;\n}\n\n.mkdf-pl-loading .mkdf-pl-loading-bounce2 {\n  -webkit-animation-delay: -0.16s;\n  -moz-animation-delay: -0.16s;\n  animation-delay: -0.16s;\n}\n\n@-webkit-keyframes sk-bouncedelay {\n  0%, 80%, 100% {\n    -webkit-transform: scale(0);\n  }\n  40% {\n    -webkit-transform: scale(1);\n  }\n}\n\n@-moz-keyframes sk-bouncedelay {\n  0%, 80%, 100% {\n    -moz-transform: scale(0);\n  }\n  40% {\n    -moz-transform: scale(1);\n  }\n}\n\n@keyframes sk-bouncedelay {\n  0%, 80%, 100% {\n    -webkit-transform: scale(0);\n    transform: scale(0);\n  }\n  40% {\n    -webkit-transform: scale(1);\n    transform: scale(1);\n  }\n}\n\n/* ==========================================================================\n   Portfolio list responsive - start\n   ========================================================================== */\n@media (min-width: 768px) and (max-width: 1024px) {\n  .mkdf-portfolio-list-holder.mkdf-grid-list.mkdf-three-columns .mkdf-item-space,\n  .mkdf-portfolio-list-holder.mkdf-grid-list.mkdf-three-columns .mkdf-masonry-grid-sizer {\n    width: 50% !important;\n  }\n  .mkdf-portfolio-list-holder.mkdf-grid-list.mkdf-three-columns .mkdf-item-space:nth-child(2n+1) {\n    clear: both !important;\n  }\n}\n\n/* ==========================================================================\n   Portfolio list responsive - end\n   ========================================================================== */\n/* ==========================================================================\n   Portfolio loading element style - end\n   ========================================================================== */\n/* ==========================================================================\n   Portfolio Item Layout - Standard Shader style - begin\n   ========================================================================== */\n.mkdf-portfolio-list-holder.mkdf-pl-boxed.mkdf-light-skin .mkdf-pli-excerpt,\n.mkdf-portfolio-list-holder.mkdf-pl-boxed.mkdf-light-skin .mkdf-pli-title {\n  color: #fff;\n}\n\n.mkdf-portfolio-list-holder.mkdf-pl-boxed .mkdf-pl-item-inner {\n  border: 2px solid rgba(225, 225, 225, 0.2);\n  padding: 20px;\n  box-sizing: border-box;\n  -webkit-transition: all 0.3s ease-out;\n  -moz-transition: all 0.3s ease-out;\n  transition: all 0.3s ease-out;\n}\n\n.mkdf-portfolio-list-holder.mkdf-pl-boxed .mkdf-pl-item-inner:hover {\n  border: 2px solid rgba(225, 225, 225, 0.4);\n}\n\n.mkdf-portfolio-list-holder.mkdf-pl-boxed .mkdf-pli-text-holder {\n  position: relative;\n  display: inline-block;\n  width: 100%;\n  vertical-align: middle;\n  margin: 29px 0 0;\n}\n\n.mkdf-portfolio-list-holder.mkdf-pl-boxed article .mkdf-pli-text .mkdf-pli-category-holder {\n  margin: 20px 0 0;\n  display: inline-block;\n  width: 60%;\n}\n\n.mkdf-portfolio-list-holder.mkdf-pl-boxed article .mkdf-pli-text .mkdf-pli-category-holder a {\n  color: #0c2c5866;\n  font-weight: 700;\n  font-size: 12px;\n  letter-spacing: 0.01em;\n}\n\n.mkdf-portfolio-list-holder.mkdf-pl-boxed article .mkdf-pli-text .mkdf-pli-category-holder a:after {\n  font-size: 12px;\n  color: #0c2c5866;\n}\n\n.mkdf-portfolio-list-holder.mkdf-pl-boxed article .mkdf-pli-text .mkdf-pli-category-holder a:hover {\n  color: #ea3d56;\n}\n\n.mkdf-portfolio-list-holder.mkdf-pl-boxed article .mkdf-pli-text .mkdf-pli-social-share {\n  display: inline-block;\n  width: 40%;\n  text-align: right;\n  color: #fff;\n  float: right;\n  margin: 20px 0 0;\n}\n\n.mkdf-portfolio-list-holder.mkdf-pl-boxed article .mkdf-pli-text .mkdf-pli-social-share ul li a {\n  color: rgba(255, 255, 255, 0.4);\n  z-index: 8;\n  position: relative;\n}\n\n.mkdf-portfolio-list-holder.mkdf-pl-boxed article .mkdf-pli-text .mkdf-pli-social-share ul li a:hover {\n  color: white;\n}\n\n/* ==========================================================================\n   Portfolio Item Layout - Standard Shader style - end\n   ========================================================================== */\n/* ==========================================================================\n   Portfolio Item Layout - Gallery Overlay style - begin\n   ========================================================================== */\n.mkdf-portfolio-list-holder.mkdf-pl-gallery-overlay.mkdf-pl-has-shadow .mkdf-pl-item-inner {\n  box-shadow: 0px 16px 46px 0px rgba(182, 40, 68, 0.52);\n}\n\n.mkdf-portfolio-list-holder.mkdf-pl-gallery-overlay article:hover .mkdf-pli-text-holder {\n  opacity: 1;\n}\n\n.mkdf-portfolio-list-holder.mkdf-pl-gallery-overlay article .mkdf-pl-item-inner {\n  overflow: hidden;\n}\n\n/* ==========================================================================\n   Portfolio Item Layout - Gallery Overlay style - end\n   ========================================================================== */\n/* ==========================================================================\n   Portfolio Item Layout - Gallery Slide From Image Bottom style - begin\n   ========================================================================== */\n.mkdf-portfolio-list-holder.mkdf-pl-gallery-slide-from-image-bottom.mkdf-pl-has-shadow .mkdf-pl-item-inner {\n  box-shadow: 0px 16px 46px 0px rgba(182, 40, 68, 0.52);\n}\n\n.mkdf-portfolio-list-holder.mkdf-pl-gallery-slide-from-image-bottom article:hover .mkdf-pli-text-holder {\n  -webkit-transform: translateY(0);\n  -moz-transform: translateY(0);\n  transform: translateY(0);\n}\n\n.mkdf-portfolio-list-holder.mkdf-pl-gallery-slide-from-image-bottom article:hover .mkdf-pli-text-wrapper {\n  -webkit-transform: translateY(0);\n  -moz-transform: translateY(0);\n  transform: translateY(0);\n}\n\n.mkdf-portfolio-list-holder.mkdf-pl-gallery-slide-from-image-bottom article .mkdf-pl-item-inner {\n  overflow: hidden;\n}\n\n.mkdf-portfolio-list-holder.mkdf-pl-gallery-slide-from-image-bottom .mkdf-pli-text-holder {\n  position: absolute;\n  display: block;\n  width: 100%;\n  height: auto;\n  bottom: 0;\n  left: 0;\n  padding: 15px 20px 10px;\n  background-color: #fff;\n  overflow: hidden;\n  box-sizing: border-box;\n  -webkit-transform: translateY(100%);\n  -moz-transform: translateY(100%);\n  transform: translateY(100%);\n  -webkit-transition: -webkit-transform 0.4s ease-in-out;\n  -moz-transition: -moz-transform 0.4s ease-in-out;\n  transition: transform 0.4s ease-in-out;\n}\n\n.mkdf-portfolio-list-holder.mkdf-pl-gallery-slide-from-image-bottom .mkdf-pli-text-wrapper {\n  -webkit-transform: translateY(-200%);\n  -moz-transform: translateY(-200%);\n  transform: translateY(-200%);\n  -webkit-transition: -webkit-transform 0.4s ease-in-out;\n  -moz-transition: -moz-transform 0.4s ease-in-out;\n  transition: transform 0.4s ease-in-out;\n}\n\n/* ==========================================================================\n   Portfolio Item Layout - Gallery Slide From Image Bottom style - end\n   ========================================================================== */\n/* ==========================================================================\n   Portfolio Item Layout - Standard Shader style - begin\n   ========================================================================== */\n.mkdf-portfolio-list-holder.mkdf-pl-standard-shader .mkdf-pli-text-holder {\n  position: relative;\n  display: inline-block;\n  width: 100%;\n  vertical-align: middle;\n  margin: 15px 0 10px;\n}\n\n/* ==========================================================================\n   Portfolio Item Layout - Standard Shader style - end\n   ========================================================================== */\n/* ==========================================================================\n   Portfolio Item Layout - Standard Switch Images style - begin\n   ========================================================================== */\n.mkdf-portfolio-list-holder.mkdf-pl-standard-switch-images article .mkdf-pli-image img {\n  -webkit-transition: opacity 0.2s ease-in-out;\n  -moz-transition: opacity 0.2s ease-in-out;\n  transition: opacity 0.2s ease-in-out;\n}\n\n.mkdf-portfolio-list-holder.mkdf-pl-standard-switch-images article .mkdf-pli-image img:nth-child(1) {\n  opacity: 1;\n}\n\n.mkdf-portfolio-list-holder.mkdf-pl-standard-switch-images article .mkdf-pli-image img:nth-child(2) {\n  position: absolute;\n  display: block;\n  width: 100%;\n  height: 100%;\n  top: 0;\n  left: 0;\n  opacity: 0;\n}\n\n.mkdf-portfolio-list-holder.mkdf-pl-standard-switch-images article.mkdf-pl-has-switch-image:hover .mkdf-pli-image img:nth-child(1) {\n  opacity: 1;\n}\n\n.mkdf-portfolio-list-holder.mkdf-pl-standard-switch-images article.mkdf-pl-has-switch-image:hover .mkdf-pli-image img:nth-child(2) {\n  opacity: 1;\n}\n\n.mkdf-portfolio-list-holder.mkdf-pl-standard-switch-images .mkdf-pli-text-holder {\n  position: relative;\n  display: inline-block;\n  width: 100%;\n  vertical-align: middle;\n  margin: 15px 0 0;\n}\n\n/* ==========================================================================\n   Portfolio Item Layout - Standard Switch Images style - end\n   ========================================================================== */\n/* ==========================================================================\n   Portfolio Project Info shortcode style - begin\n   ========================================================================== */\n.mkdf-portfolio-project-info {\n  position: relative;\n  display: inline-block;\n  vertical-align: middle;\n}\n\n.mkdf-portfolio-project-info .mkdf-ppi-label {\n  margin: 0;\n  padding: 0;\n}\n\n.mkdf-portfolio-project-info > div {\n  position: relative;\n  display: inline-block;\n  vertical-align: middle;\n}\n\n.mkdf-portfolio-project-info > div a {\n  position: relative;\n  display: inline-block;\n  vertical-align: middle;\n  margin: 0 5px 0 0;\n}\n\n.mkdf-portfolio-project-info > div a:last-child {\n  margin: 0;\n}\n\n.mkdf-portfolio-project-info .mkdf-ppi-title {\n  position: relative;\n  display: inline-block;\n  vertical-align: middle;\n  margin: 0;\n}\n\n.mkdf-portfolio-project-info .mkdf-ppi-image {\n  position: relative;\n  display: inline-block;\n  vertical-align: middle;\n  margin: 0;\n}\n\n.mkdf-portfolio-project-info .mkdf-ppi-image img {\n  display: block;\n}\n\n/* ==========================================================================\n   Portfolio Project Info shortcode style - end\n   ========================================================================== */\n/* ==========================================================================\n   Portfolio Slider shortcode style - begin\n   ========================================================================== */\n.mkdf-portfolio-slider-holder {\n  position: relative;\n  display: inline-block;\n  width: 100%;\n  vertical-align: middle;\n}\n\n.mkdf-portfolio-slider-holder .mkdf-portfolio-list-holder.mkdf-nav-light-skin .owl-nav .owl-prev,\n.mkdf-portfolio-slider-holder .mkdf-portfolio-list-holder.mkdf-nav-light-skin .owl-nav .owl-next {\n  color: #fff;\n}\n\n.mkdf-portfolio-slider-holder .mkdf-portfolio-list-holder.mkdf-nav-light-skin .owl-nav .owl-prev:hover,\n.mkdf-portfolio-slider-holder .mkdf-portfolio-list-holder.mkdf-nav-light-skin .owl-nav .owl-next:hover {\n  color: #ea3d56;\n}\n\n.mkdf-portfolio-slider-holder .mkdf-portfolio-list-holder.mkdf-nav-dark-skin .owl-nav .owl-prev,\n.mkdf-portfolio-slider-holder .mkdf-portfolio-list-holder.mkdf-nav-dark-skin .owl-nav .owl-next {\n  color: #1b2c58;\n}\n\n.mkdf-portfolio-slider-holder .mkdf-portfolio-list-holder.mkdf-nav-dark-skin .owl-nav .owl-prev:hover,\n.mkdf-portfolio-slider-holder .mkdf-portfolio-list-holder.mkdf-nav-dark-skin .owl-nav .owl-next:hover {\n  color: #ea3d56;\n}\n\n.mkdf-portfolio-slider-holder .mkdf-portfolio-list-holder.mkdf-pag-light-skin .owl-dots .owl-dot span {\n  background-color: rgba(255, 255, 255, 0.2);\n}\n\n.mkdf-portfolio-slider-holder .mkdf-portfolio-list-holder.mkdf-pag-light-skin .owl-dots .owl-dot.active span, .mkdf-portfolio-slider-holder .mkdf-portfolio-list-holder.mkdf-pag-light-skin .owl-dots .owl-dot:hover span {\n  background-color: #ea3d56;\n}\n\n.mkdf-portfolio-slider-holder .mkdf-portfolio-list-holder.mkdf-pag-dark-skin .owl-dots .owl-dot span {\n  background-color: rgba(27, 44, 88, 0.2);\n}\n\n.mkdf-portfolio-slider-holder .mkdf-portfolio-list-holder.mkdf-pag-dark-skin .owl-dots .owl-dot.active span, .mkdf-portfolio-slider-holder .mkdf-portfolio-list-holder.mkdf-pag-dark-skin .owl-dots .owl-dot:hover span {\n  background-color: #ea3d56;\n}\n\n.mkdf-portfolio-slider-holder .mkdf-portfolio-list-holder.mkdf-pag-on-slider .owl-nav .owl-prev,\n.mkdf-portfolio-slider-holder .mkdf-portfolio-list-holder.mkdf-pag-on-slider .owl-nav .owl-next {\n  -webkit-transform: translateY(-50%);\n  -moz-transform: translateY(-50%);\n  transform: translateY(-50%);\n}\n\n.mkdf-portfolio-slider-holder .mkdf-portfolio-list-holder.mkdf-pag-on-slider .owl-dots {\n  position: absolute;\n  left: 0;\n  bottom: 20px;\n  width: 100%;\n  margin: 0;\n}\n\n/* ==========================================================================\n   Portfolio Slider shortcode style - end\n   ========================================================================== */\n/* ==========================================================================\n   Testimonials standard style - begin\n   ========================================================================== */\n.mkdf-testimonials-holder {\n  position: relative;\n  display: inline-block;\n  width: 100%;\n  vertical-align: middle;\n}\n\n.mkdf-testimonials-holder .mkdf-testimonials,\n.mkdf-testimonials-holder .mkdf-testimonial-content,\n.mkdf-testimonials-holder .mkdf-testimonial-text-holder {\n  position: relative;\n  display: inline-block;\n  width: 100%;\n  vertical-align: middle;\n}\n\n.mkdf-testimonials-holder .mkdf-testimonials {\n  box-sizing: border-box;\n}\n\n.mkdf-testimonials-holder .mkdf-testimonials .mkdf-testimonials-icon {\n  position: absolute;\n  display: inline-block;\n  left: 50%;\n  top: -80px;\n  -webkit-transform: translateX(-50%);\n  -moz-transform: translateX(-50%);\n  transform: translateX(-50%);\n}\n\n.mkdf-testimonials-holder .mkdf-testimonial-image img {\n  width: auto !important;\n  border-radius: 5em;\n}\n\n.mkdf-testimonials-holder.mkdf-testimonials-standard {\n  text-align: center;\n  /* Light/Dark styles */\n}\n\n.mkdf-testimonials-holder.mkdf-testimonials-standard .mkdf-testimonial-content {\n  max-width: 875px;\n}\n\n.mkdf-testimonials-holder.mkdf-testimonials-standard .mkdf-testimonial-image {\n  position: relative;\n  display: inline-block;\n  width: 100%;\n  vertical-align: middle;\n  margin: 25px 0 0;\n}\n\n.mkdf-testimonials-holder.mkdf-testimonials-standard .mkdf-testimonial-image img {\n  display: block;\n  margin: 0 auto;\n  border: 4px solid #fff;\n  border-radius: 50%;\n  box-sizing: border-box;\n}\n\n.mkdf-testimonials-holder.mkdf-testimonials-standard .mkdf-testimonial-title {\n  font-size: 55px;\n  font-weight: 600;\n  margin: 0 0 39px;\n}\n\n.mkdf-testimonials-holder.mkdf-testimonials-standard .mkdf-testimonial-text {\n  margin: 0 0 20px;\n  font-size: 18px;\n  color: #4e4e4e;\n  line-height: 34px;\n}\n\n.mkdf-testimonials-holder.mkdf-testimonials-standard .mkdf-testimonial-author {\n  margin-top: 43px;\n}\n\n.mkdf-testimonials-holder.mkdf-testimonials-standard .mkdf-testimonial-author > .mkdf-testimonials-author-name {\n  font-size: 26px;\n  font-weight: 600;\n  color: #ea3d56;\n  margin: 25px 0 2px;\n}\n\n.mkdf-testimonials-holder.mkdf-testimonials-standard .mkdf-testimonial-author > .mkdf-testimonials-author-job {\n  font-size: 14px;\n  font-weight: 700;\n  color: #1b2c58;\n  margin: 25px 0 2px;\n  letter-spacing: .1em;\n  text-transform: uppercase;\n  font-family: \"Josefin Sans\", sans-serif;\n}\n\n.mkdf-testimonials-holder.mkdf-testimonials-standard.mkdf-testimonials-light .mkdf-testimonial-title,\n.mkdf-testimonials-holder.mkdf-testimonials-standard.mkdf-testimonials-light .mkdf-testimonial-text,\n.mkdf-testimonials-holder.mkdf-testimonials-standard.mkdf-testimonials-light .mkdf-testimonial-author {\n  color: #fff;\n}\n\n.mkdf-testimonials-holder.mkdf-testimonials-standard.mkdf-testimonials-light .mkdf-testimonial-author > .mkdf-testimonials-author-name {\n  color: #fff;\n}\n\n.mkdf-testimonials-holder.mkdf-testimonials-standard.mkdf-testimonials-light .mkdf-testimonial-author > .mkdf-testimonials-author-job {\n  color: rgba(255, 255, 255, 0.6);\n}\n\n.mkdf-testimonials-holder.mkdf-testimonials-standard.mkdf-testimonials-light .owl-dots .owl-dot span {\n  border: 2px solid rgba(255, 255, 255, 0.5);\n}\n\n.mkdf-testimonials-holder.mkdf-testimonials-standard.mkdf-testimonials-light .owl-dots .owl-dot:hover span, .mkdf-testimonials-holder.mkdf-testimonials-standard.mkdf-testimonials-light .owl-dots .owl-dot.active span {\n  background-color: #fff;\n  border-color: #fff;\n}\n\n.mkdf-testimonials-holder.mkdf-testimonials-standard.mkdf-testimonials-light .mkdf-owl-slider .owl-nav .owl-next > span, .mkdf-testimonials-holder.mkdf-testimonials-standard.mkdf-testimonials-light .mkdf-owl-slider .owl-nav .owl-prev > span {\n  color: #fff;\n}\n\n@media (max-width: 1024px) {\n  .mkdf-testimonials-holder.mkdf-testimonials-standard .mkdf-owl-slider .owl-nav .owl-next > span, .mkdf-testimonials-holder.mkdf-testimonials-standard .mkdf-owl-slider .owl-nav .owl-prev > span {\n    display: none;\n  }\n}\n\n/* ==========================================================================\n   Testimonials standard style - end\n   ========================================================================== */\n", "////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////\n\n// layout mixins - start\n\n@mixin mkdfRelativeHolderLayout($vertical-align: middle) {\n    position: relative;\n    display: inline-block;\n    width: 100%;\n    vertical-align: $vertical-align;\n}\n\n@mixin mkdfAbsoluteHolderLayout() {\n    position: absolute;\n    display: block;\n    width: 100%;\n    height: 100%;\n    top: 0;\n    left: 0;\n}\n\n@mixin mkdfFlexBoxLayout($position: null, $align-items: null, $justify-content: null) {\n    @if ($position) {\n        position: $position;\n    }\n    \n    @include mkdfFlexLayout();\n    \n    @if ($align-items) {\n        @include mkdfFlexAlignItems($align-items);\n    }\n    \n    @if ($justify-content) {\n        @include mkdfFlexJustifyContent($justify-content);\n    }\n}\n\n@mixin mkdfFlexContainer($align-items: null, $justify-content: null, $flex-direction: null, $flex-wrap: null, $align-content: null) {\n\t@include mkdfFlexBoxLayout(null, $align-items, $justify-content);\n\t\n\t@if ($flex-direction) {\n\t\tflex-direction: $flex-direction;\n\t}\n\t\n\t@if ($flex-wrap) {\n\t\tflex-wrap: $flex-wrap;\n\t}\n\t\n\t@if ($align-content) {\n\t\talign-content: $align-content;\n\t}\n}\n\n@mixin mkdfFlexLayout() {\n    display: -webkit-box;\n    display: -webkit-flex;\n    display: -ms-flexbox;\n    display: flex;\n}\n\n@mixin mkdfInlineFlexLayout() {\n    display: -webkit-inline-flex;\n    display: -ms-inline-flexbox;\n    display: inline-flex;\n}\n\n@mixin mkdfFlexItem($order: 0, $flex-grow: 0, $flex-shrink: 1, $flex-basis: auto) {\n    order: $order;\n    flex-grow: $flex-grow;\n    flex-shrink: $flex-shrink;\n    flex-basis: $flex-basis;\n}\n\n@mixin mkdfFlexAlignItems($align-items) {\n    $older-align-items: $align-items;\n    \n    @if ($align-items == 'flex-start') {\n        $older-align-items: start;\n    } @else if ($align-items == 'flex-end') {\n        $older-align-items: end;\n    }\n    \n    -webkit-box-align: $older-align-items;\n    -webkit-align-items: $align-items;\n    -ms-flex-align: $older-align-items;\n    align-items: $align-items;\n}\n@mixin mkdfDefaultTransition($transition-param...) {\n    $transitions_each: ('-webkit-transition', '-moz-transition', 'transition');\n    $string: '';\n\n    @each $var in $transition-param{\n        @if $string == '' {\n            $string : $var $default-transition-duration $default-easing-function\n        } @else {\n            $string : $string, $var $default-transition-duration $default-easing-function\n        }\n    }\n\n\n    @each $transition in $transitions_each{\n        #{$transition}: $string;\n    }\n}\n@mixin mkdfFlexJustifyContent($justify-content) {\n    $older-justify-content: $justify-content;\n    \n    @if ($justify-content == 'flex-start') {\n        $older-justify-content: start;\n    } @else if ($justify-content == 'flex-end') {\n        $older-justify-content: end;\n    } @else if ($justify-content == 'space-between') {\n        $older-justify-content: justify;\n    }\n    \n    -webkit-box-pack: $older-justify-content;\n    -webkit-justify-content: $justify-content;\n    -ms-flex-pack: $older-justify-content;\n    justify-content: $justify-content;\n}\n\n@mixin mkdfTableLayout() {\n    position: relative;\n    display: table;\n    table-layout: fixed;\n    height: 100%;\n    width: 100%;\n}\n\n@mixin mkdfTableCellLayout($vertical-align: middle) {\n    position: relative;\n    display: table-cell;\n    height: 100%;\n    width: 100%;\n    vertical-align: $vertical-align;\n}\n\n@mixin mkdfTypographyLayout($important : null) {\n    color: inherit $important;\n    font-family: inherit $important;\n    font-size: inherit $important;\n    font-weight: inherit $important;\n    font-style: inherit $important;\n    line-height: inherit $important;\n    letter-spacing: inherit $important;\n    text-transform: inherit $important;\n}\n\n// layout mixins - end\n\n////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////\n\n// transition mixins - start\n\n@mixin mkdfTransition($transition-param...) {\n    -webkit-transition: $transition-param;\n    -moz-transition: $transition-param;\n    transition: $transition-param;\n}\n\n@mixin mkdfTransitionTransform($transition-param...) {\n    -webkit-transition: -webkit-transform $transition-param;\n    -moz-transition: -moz-transform $transition-param;\n    transition: transform $transition-param;\n}\n\n@mixin mkdfTransform($transform-param...) {\n    -webkit-transform: $transform-param;\n    -moz-transform: $transform-param;\n    transform: $transform-param;\n}\n\n@mixin mkdfAnimation($animation-param...) {\n    -webkit-animation: $animation-param;\n    -moz-animation: $animation-param;\n    animation: $animation-param;\n}\n\n@mixin mkdfTransformOrigin($animation-param...) {\n    -webkit-transform-origin: $animation-param;\n    -moz-transform-origin: $animation-param;\n    transform-origin: $animation-param;\n}\n@mixin mkdfPulse {\n\n}\n\n// transition mixins - end\n\n////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////\n\n// checkbox mixins - start\n\n$checkbox-size: 15px;\n$checkbox-border-width: 1px;\n\n%checkbox-style {\n    position: relative;\n    margin: 8px 0;\n    line-height: 1;\n\n    input[type=checkbox] {\n        width: $checkbox-size;\n        height: $checkbox-size;\n        max-height: $checkbox-size;\n        position: relative;\n        display: inline-block;\n        vertical-align: top;\n        top: 0;\n        left: 0;\n        margin: 0;\n    }\n\n    input[type=checkbox] + label {\n        position: absolute;\n        top: 0;\n        left: 0;\n        display: inline-block;\n        line-height: 0;\n        pointer-events: none;\n        cursor: pointer;\n    }\n\n    input[type=checkbox] + label span.mkdf-label-text {\n        display: inline-block;\n        padding-left: 10px;\n        line-height: $checkbox-size;\n        color: $default-heading-color;\n    }\n\n    input[type=checkbox] + label .mkdf-label-view {\n        display: inline-block;\n        vertical-align: top;\n        width: $checkbox-size;\n        height: $checkbox-size;\n        background-color: $default-background-color;\n        border: $checkbox-border-width solid $default-border-color;\n        border-radius: 2px;\n        cursor: pointer;\n        box-sizing: border-box;\n\n        &:hover {\n            cursor: pointer;\n        }\n    }\n\n    input[type=checkbox] + label .mkdf-label-view:after {\n        content: '';\n        position: absolute;\n        top: ($checkbox-size - ($checkbox-size / 2 - $checkbox-border-width)) / 2;\n        left: ($checkbox-size - ($checkbox-size / 2 - $checkbox-border-width)) / 2;\n        width: $checkbox-size / 2 - $checkbox-border-width;\n        height: $checkbox-size / 2 - $checkbox-border-width;\n        background-color: $first-main-color;\n        opacity: 0;\n        @include mkdfTransition(opacity 0.3s ease-in-out);\n    }\n\n    input[type=checkbox]:checked + label .mkdf-label-view:after {\n        opacity: 1;\n    }\n}\n\n// checkbox mixins - end\n\n////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////\n\n// common mixins - start\n\n@mixin mkdfBckImageStyle() {\n    background-size: cover;\n    background-repeat: no-repeat;\n    background-position: center center;\n}\n\n@mixin mkdfImageOverlayHoverStyle($with-hover: true) {\n    \n    @if ($with-hover) {\n        \n        &:hover {\n            \n            &:after {\n                opacity: 1;\n            }\n        }\n\n        &:after {\n            @include mkdfAbsoluteHolderLayout();\n            content: '';\n            background-color: rgba($first-main-color, .4);\n            opacity: 0;\n            @include mkdfTransition(opacity .2s ease-in-out);\n        }\n\n    } @else {\n        @include mkdfAbsoluteHolderLayout();\n        content: '';\n        background-color: rgba($first-main-color, .4);\n        opacity: 0;\n        @include mkdfTransition(opacity .2s ease-in-out);\n    }\n}\n\n@mixin mkdfStandardPaginationStyle($list_type: null) {\n    @include mkdfRelativeHolderLayout(top);\n    margin: 40px 0 0;\n    clear: both;\n\n    ul {\n        @include mkdfRelativeHolderLayout(top);\n        padding: 0;\n        margin: 0;\n        list-style: none;\n        text-align: center;\n\n        li {\n            position: relative;\n            display: inline-block;\n            vertical-align: top;\n            margin: 0 12px;\n\n            a {\n                position: relative;\n                display: inline-block;\n                vertical-align: top;\n                margin: 0;\n                padding: 0;\n            }\n\n            &.mkdf-pag-active {\n                \n                a {\n                    color: $first-main-color;\n                }\n            }\n\n            &.mkdf-pag-prev,\n            &.mkdf-pag-next,\n            &.mkdf-pag-first,\n            &.mkdf-pag-last {\n                margin: 0 2px;\n\n                a {\n                    font-size: 24px;\n\n                    span {\n                        display: block;\n                        line-height: inherit;\n\n                        &:before {\n                            display: block;\n                            line-height: inherit;\n                        }\n                    }\n                }\n            }\n\n            @if ($list_type == 'shortcode') {\n                \n                &.mkdf-pag-prev {\n                    \n                    a {\n                        opacity: 0;\n                    }\n                }\n\n                &.mkdf-pag-next {\n                    \n                    a {\n                        opacity: 1;\n                    }\n                }\n\n            } @else if ($list_type == 'shop') {\n                span {\n                    position: relative;\n                    display: inline-block;\n                    vertical-align: top;\n                    margin: 0;\n                    padding: 0;\n                    color: $first-main-color;\n                }\n\n                a {\n                    \n                    &.next,\n                    &.prev {\n                        font-size: 0;\n                        line-height: 0;\n\n                        &:before {\n                            display: block;\n                            font-family: 'ElegantIcons'; // same icon pack as in our templates for pagination\n                            font-size: 24px;\n                            line-height: 26px;\n                            -webkit-font-smoothing: antialiased;\n                            -moz-osx-font-smoothing: grayscale;\n                        }\n                    }\n\n                    &.prev {\n                        margin-right: -10px;\n\n                        &:before {\n                            content: \"\\34\";\n                        }\n                    }\n\n                    &.next {\n                        margin-left: -10px;\n\n                        &:before {\n                            content: \"\\35\";\n                        }\n                    }\n                }\n            }\n        }\n    }\n}\n\n@mixin mkdfButtonDefaultStyle() {\n    position: relative;\n    display: inline-block;\n    vertical-align: middle;\n    width: auto;\n    margin: 0;\n    font-family: $default-text-font;\n    font-size: 12px;\n    line-height: 2em;\n    letter-spacing: 0.16em;\n    font-weight: 700;\n    text-transform: uppercase;\n    outline: none;\n    box-sizing: border-box;\n    @include mkdfTransition(color .2s ease-in-out, background-color .2s ease-in-out, border-color .2s ease-in-out);\n}\n\n@mixin mkdfButtonSize($size: medium) {\n    \n    @if ($size == 'small') {\n        padding: 11px 24px;\n\n    } @else if ($size == 'medium') {\n        padding: 13px 34px 11px;\n\n\n    } @else if ($size == 'large') {\n        padding: 13px 43px 11px;\n\n    } @else if ($size == 'huge') {\n        padding: 21px 60px 16px;\n        font-size:14px;\n    }\n}\n\n@mixin mkdfButtonTransparentColor() {\n    color: $default-text-color;\n    background-color: transparent;\n}\n\n@mixin mkdfButtonSolidColor($important: null) {\n    color: #fff $important;\n    background-color: $first-main-color $important;\n    border: 1px solid transparent $important;\n}\n\n@mixin mkdfButtonSolidHoverColor($important: null) {\n    color: #fff $important;\n    background-color: $first-main-color $important;\n    box-shadow: none !important;\n}\n\n@mixin mkdfButtonOutlineColor($important: null) {\n    color: $first-main-color $important;\n    background-color: transparent $important;\n    border: 1px solid $first-main-color $important;\n}\n\n@mixin mkdfButtonOutlineHoverColor($important: null) {\n    color: #fff $important;\n    background-color: $first-main-color $important;\n    border-color: $first-main-color $important;\n}\n\n@mixin mkdfPlaceholder {\n    &::-webkit-input-placeholder {\n        @content\n    }\n\n    &:-moz-placeholder {\n        @content;\n\n        opacity: 1;\n    }\n    &::-moz-placeholder {\n        @content;\n\n        opacity: 1;\n    }\n    &:-ms-input-placeholder {\n        @content\n    }\n}\n\n\n @keyframes animate-btn-line {\n    0% {\n        @include mkdfTransformOrigin(0 50%);\n        @include mkdfTransform(scaleX(0));\n    }\n    100% {\n        @include mkdfTransformOrigin(0 50%);\n        @include mkdfTransform(scaleX(1));\n    }\n}\n@-webkit-keyframes mkdfPulsebig {\n   0% {\n    -moz-box-shadow: 0 0 0 0 currentColor;\n    box-shadow: 0 0 0 0 currentColor;\n  }\n  70% {\n      -moz-box-shadow: 0 0 0 20px rgba($first-main-color, 0.3);\n      box-shadow: 0 0 0 20px rgba($first-main-color, 0);\n  }\n  100% {\n      -moz-box-shadow: 0 0 0 0 rgba($first-main-color, 0);\n      box-shadow: 0 0 0 0 rgba($first-main-color, 0);\n  }\n}\n@keyframes mkdfPulsebig {\n  0% {\n    -moz-box-shadow: 0 0 0 0 currentColor;\n    box-shadow: 0 0 0 0 currentColor;\n  }\n  70% {\n      -moz-box-shadow: 0 0 0 20px rgba($first-main-color, 0.3);\n      box-shadow: 0 0 0 20px rgba($first-main-color, 0);\n  }\n  100% {\n      -moz-box-shadow: 0 0 0 0 rgba($first-main-color, 0);\n      box-shadow: 0 0 0 0 rgba($first-main-color, 0);\n  }\n}\n@-webkit-keyframes mkdfPulsesmall {\n  0% {\n    -webkit-box-shadow: 0 0 0 0 currentColor;\n  }\n  70% {\n      -webkit-box-shadow: 0 0 0 10px rgba($first-main-color, 0.15);\n  }\n  100% {\n      -webkit-box-shadow: 0 0 0 0 rgba($first-main-color, 0);\n  }\n}\n@keyframes mkdfPulsesmall {\n  0% {\n    -moz-box-shadow: 0 0 0 0 currentColor;\n    box-shadow: 0 0 0 0 currentColor;\n  }\n  70% {\n      -moz-box-shadow: 0 0 0 10px rgba($first-main-color, 0.15);\n      box-shadow: 0 0 0 10px rgba($first-main-color, 0);\n  }\n  100% {\n      -moz-box-shadow: 0 0 0 0 rgba($first-main-color, 0);\n      box-shadow: 0 0 0 0 rgba($first-main-color, 0);\n  }\n}\n@-webkit-keyframes mkdfPulsesmallfirst {\n  0% {\n    -webkit-box-shadow: 0 0 0 0 $first-main-color;\n  }\n  70% {\n      -webkit-box-shadow: 0 0 0 10px rgba($first-main-color, 0.15);\n  }\n  100% {\n      -webkit-box-shadow: 0 0 0 0 rgba($first-main-color, 0);\n  }\n}\n@keyframes mkdfPulsesmallfirst {\n  0% {\n    -moz-box-shadow: 0 0 0 0 $first-main-color;\n    box-shadow: 0 0 0 0 $first-main-color;\n  }\n  70% {\n      -moz-box-shadow: 0 0 0 10px rgba($first-main-color, 0.15);\n      box-shadow: 0 0 0 10px rgba($first-main-color, 0);\n  }\n  100% {\n      -moz-box-shadow: 0 0 0 0 rgba($first-main-color, 0);\n      box-shadow: 0 0 0 0 rgba($first-main-color, 0);\n  }\n}\n\n// common mixins - end\n\n////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////\n\n// mixins styles - start\n\n%input-style {\n    position: relative;\n    width: 100%;\n    margin: 0 0 $input-margin;\n    padding: $input-vertical-padding $input-horizontal-padding;\n    font-family: $default-text-font;\n    font-size: 16px;\n    font-weight: inherit;\n    color: #a2a3a3;\n    background-color: transparent;\n    border: 2px solid $default-border-color;\n    border-radius: 0;\n    outline: 0;\n    cursor: pointer;\n    -webkit-appearance: none;\n    box-sizing: border-box;\n    @include mkdfTransition(border-color 0.2s ease-in-out);\n\n    &:focus {\n        color: $default-heading-color;\n        \n    }\n\n    @include mkdfPlaceholder {\n        color: inherit;\n    }\n}\n\n// mixins styles - end\n\n////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////\n\n//media query mixins - start\n\n@mixin laptop-landscape-large {\n    @media only screen and (max-width: map-get($breakpoints, laptop-landscape-large)) {\n        @content;\n    }\n}\n\n@mixin laptop-landscape-mac {\n    @media only screen and (max-width: map-get($breakpoints, laptop-landscape-mac)) {\n        @content;\n    }\n}\n\n@mixin laptop-landscape-medium {\n    @media only screen and (max-width: map-get($breakpoints, laptop-landscape-medium)) {\n        @content;\n    }\n}\n\n@mixin laptop-landscape {\n    @media only screen and (max-width: map-get($breakpoints, laptop-landscape)) {\n        @content;\n    }\n}\n\n@mixin ipad-landscape {\n    @media only screen and (max-width: map-get($breakpoints, ipad-landscape)) {\n        @content;\n    }\n}\n\n@mixin ipad-portrait {\n    @media only screen and (max-width: map-get($breakpoints, ipad-portrait)) {\n        @content;\n    }\n}\n\n@mixin phone-landscape {\n    @media only screen and (max-width: map-get($breakpoints, phone-landscape)) {\n        @content;\n    }\n}\n\n@mixin phone-portrait {\n    @media only screen and (max-width: map-get($breakpoints, phone-portrait)) {\n        @content;\n    }\n}\n\n@mixin smaller-phone-portrait {\n    @media only screen and (max-width: map-get($breakpoints, smaller-phone-portrait)) {\n        @content;\n    }\n}\n\n// media query mixins - end\n\n// animation mixin - start\n\n@mixin keyframes($name) {\n    @-webkit-keyframes #{$name} {\n        @content;\n    }\n\n    @keyframes #{$name} {\n        @content;\n    }\n}\n\n@mixin animation($name, $duration, $repeat, $timing, $delay) {\n    -webkit-animation-name: $name;\n    -webkit-animation-duration: $duration;\n    -webkit-animation-iteration-count: $repeat;\n    -webkit-animation-timing-function: $timing;\n    -webkit-animation-delay: $delay;\n    -webkit-animation-fill-mode: forwards; // this prevents the animation from restarting!\n\n    animation-name: $name;\n    animation-duration: $duration;\n    animation-iteration-count: $repeat;\n    animation-timing-function: $timing;\n    animation-delay: $delay;\n    animation-fill-mode: forwards; // this prevents the animation from restarting!\n}\n\n// animation mixin - end\n\n////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////\n\n// heading mixins - start\n\n@mixin mkdfDefaultHeadingStyle() {\n    @include mkdfHeadingStyle();\n    font-weight: 600;\n    margin: 25px 0;\n    letter-spacing: -0.025em;\n    font-family: $default-text-font;\n    -ms-word-wrap: break-word;\n    word-wrap: break-word;\n    \n    a {\n        @include mkdfTypographyLayout();\n        \n        &:hover {\n            color: $first-main-color;\n        }\n    }\n}\n\n@mixin mkdfHeadingStyle($with-heading: null, $with-color: true) {\n    \n    @if ($with-color) {\n        color: $default-heading-color;\n    }\n    \n    @if ($with-heading == 'h1') {\n        @include mkdfH1();\n    } @else if ($with-heading == 'h2') {\n        @include mkdfH2();\n    } @else if ($with-heading == 'h3') {\n        @include mkdfH3();\n    } @else if ($with-heading == 'h4') {\n        @include mkdfH4();\n    } @else if ($with-heading == 'h5') {\n        @include mkdfH5();\n    } @else if ($with-heading == 'h6') {\n        @include mkdfH6();\n    }\n}\n\n@mixin mkdfBody() {\n    font-family: $additional-text-font;\n    font-size: 16px;\n    font-weight: 400;\n    line-height: 30px;\n    color: $default-text-color;\n    background-color: $default-background-color;\n    -webkit-font-smoothing: antialiased;\n}\n\n@mixin mkdfH1() {\n    font-size: 55px;\n    line-height: 1.23em;\n}\n\n@mixin mkdfH2() {\n    font-size: 40px;\n    line-height: 1.25em;\n}\n\n@mixin mkdfH3() {\n    font-size: 36px;\n    line-height: 1.16em;\n}\n\n@mixin mkdfH4() {\n    font-size: 30px;\n    line-height: 1.2em;\n}\n\n@mixin mkdfH5() {\n    font-size: 26px;\n    line-height: 1.23em;\n}\n\n@mixin mkdfH6() {\n    font-size: 20px;\n    line-height: 1.3em;\n    letter-spacing: 0;\n}\n\n// heading mixins - end\n\n////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////\n\n@mixin mkdfBlockquote($important : null) {\n    @include mkdfRelativeHolderLayout();\n    margin: 10px 0 $important;\n    padding: 20px 40px $important;\n    font-size: 18px $important;\n    line-height: 30px $important;\n    quotes: none;\n    box-sizing: border-box;\n    border: none $important;\n    color: $default-text-color $important;\n\n    > * {\n        @include mkdfTypographyLayout();\n        margin: 0;\n    }\n\n    &:after,\n    &:before{\n        content: '';\n    }\n\n    //&:before {\n    //    content: \"\\7b\";\n    //    font-family: \"ElegantIcons\";\n    //    font-size: 60px;\n    //    color: $first-main-color;\n    //    position: absolute;\n    //    top:50%;\n    //    @include mkdfTransform(translateY(-50%));\n    //}\n\n    cite,\n    .wp-block-quote__citation,\n    .wp-block-pullquote__citation,\n    footer {\n        display: block $important;\n        margin-top: 10px $important;\n        text-align: inherit $important;\n        font-size: 14px $important;\n        line-height: 1.3em $important;\n        letter-spacing: 0 $important;\n        font-style: normal  $important;\n        font-weight: 400 $important;\n        text-transform: none $important;\n    }\n\n    cite{\n        padding-left: 10%;\n        display: inline-block $important;\n    }\n\n}\n\n", "$breakpoints: (\n        laptop-landscape-large: 1440px,\n        laptop-landscape-mac: 1366px,\n        laptop-landscape-medium: 1280px,\n        laptop-landscape: 1200px,\n        ipad-landscape: 1024px,\n        ipad-portrait: 768px,\n        phone-landscape: 680px,\n        phone-portrait: 480px,\n        smaller-phone-portrait: 320px\n);\n\n$grid-width: 1100px;\n$grid-width-laptop-landscape: 950px;\n$grid-width-ipad-landscape: 768px;\n$grid-width-ipad-portrait: 600px;\n$grid-width-phone-landscape: 420px;\n$grid-width-phone-portrait: 300px;\n$grid-width-smaller-phone-portrait: 90%;\n\n$grid-width-boxed: 1150px;\n$grid-width-laptop-landscape-boxed: 1000px;\n$grid-width-ipad-landscape-boxed: 818px;\n$grid-width-ipad-portrait-boxed: 650px;\n$grid-width-phone-landscape-boxed: 470px;\n$grid-width-phone-portrait-boxed: 350px;\n$grid-width-smaller-phone-portrait-boxed: 92%;\n\n$grid-width-1300: 1300px;\n$grid-width-1200: 1200px;\n$grid-width-1000: 1000px;\n$grid-width-800: 800px;\n\n$default-text-font: '<PERSON><PERSON>', sans-serif;\n$additional-text-font: '<PERSON><PERSON><PERSON>', sans-serif;\n\n$first-main-color: #ea3d56;\n$first-main-color-dark-blue: #1b2c58;\n$first-main-color-medium-blue: #3745a5;\n$first-main-color-ligh-blue: #1f75ff;\n$first-main-color-yellow: #ffc40e;\n$first-main-color-green: #56c4c5;\n$default-heading-color: #1b2c58;\n$default-text-color: #868890;\n$shadow-color: inherit;\n\n\n$default-background-color: #fff;\n$additional-background-color: #f6f6f6;\n$default-border-color: rgba(#e1e1e1, 0.3);\n$default-border-radius: 4px;\n$default-box-shadow: 0 0 4.85px 0.15px rgba(#000, 0.09);\n\n$header-light-color: #fff;\n$header-light-hover-color: $first-main-color;\n$header-dark-color: $default-heading-color;\n$header-dark-hover-color: $first-main-color;\n\n// input elements\n$input-height: 50px;\n$sselect-input-height: $input-height;\n$input-vertical-padding: 22px;\n$input-horizontal-padding: 16px;\n$input-margin: 18px;\n\n// responsive breakpoints\n$laptop-landscape-large-plus-pixel: 1441px;\n$laptop-landscape-large: 1440px;\n$laptop-landscape-mac-plus-pixel: 1367px;\n$laptop-landscape-mac: 1366px;\n$laptop-landscape-medium-plus-pixel: 1281px;\n$laptop-landscape-medium: 1280px;\n$laptop-landscape-plus-pixel: 1201px;\n$laptop-landscape: 1200px;\n$ipad-landscape-plus-pixel: 1025px;\n$ipad-landscape: 1024px;\n$ipad-portrait-plus-pixel: 769px;\n$ipad-portrait: 768px;\n$phone-landscape-plus-pixel: 681px;\n$phone-landscape: 680px;\n$phone-portrait-plus-pixel: 481px;\n$phone-portrait: 480px;\n$smaller-phone-portrait-plus-pixel: 321px;\n$smaller-phone-portrait: 320px;\n\n$default-easing-function: ease;\n$default-transition-duration: .5s;", "/* ==========================================================================\n   Portfolio Single page style - begin\n   ========================================================================== */\n\n.mkdf-portfolio-single-holder {\n\t@include mkdfRelativeHolderLayout();\n\tmargin: 0 0 50px;\n\t\n    .mkdf-ps-image-holder {\n\t    @include mkdfRelativeHolderLayout();\n\t    \n\t    .mkdf-ps-image {\n\t\t    \n\t\t    &:not(.mkdf-item-space) {\n\t\t\t    @include mkdfRelativeHolderLayout();\n\t\t\t    box-sizing: border-box;\n\t\t    }\n\t\t    \n\t\t    a, img {\n\t\t\t    position: relative;\n\t\t\t    display: block;\n\t\t    }\n\t    }\n    }\n\t\n\t.mkdf-ps-info-holder {\n\t\t@include mkdfRelativeHolderLayout();\n\t\t\n\t\t.mkdf-ps-info-item {\n\t\t\t@include mkdfRelativeHolderLayout();\n\n\t\t\t&.mkdf-ps-content-item {\n\t\t\t\tmargin: 15px 0 70px;\n\t\t\t}\n\n\t\t\t&.mkdf-ps-social-share {\n\t\t\t\tborder-top: 1px solid $default-border-color;\n\t\t\t\tpadding: 20px 0 0;\n\n\t\t\t\tspan {\n\t\t\t\t\tcolor: #fff;\n\t\t\t\t\tpadding: 7px;\n\t\t\t\t\tborder-radius: 50%;\n\n\t\t\t\t\t&.social_facebook {\n\t\t\t\t\t\tbackground: #3b5998;\n\t\t\t\t\t}\n\t\t\t\t\t&.social_twitter {\n\t\t\t\t\t\tbackground: #55acee;\n\t\t\t\t\t}\n\t\t\t\t\t&.social_linkedin {\n\t\t\t\t\t\tbackground: #007bb5;\n\t\t\t\t\t}\n\t\t\t\t\t&.social_instagram {\n\t\t\t\t\t\tbackground: #cd486b;\n\t\t\t\t\t}\n\t\t\t\t\t&.social_pinterest {\n\t\t\t\t\t\tbackground: #cb2027;\n\t\t\t\t\t}\n\t\t\t\t\t&.social_tumblr {\n\t\t\t\t\t\tbackground: #32506d;\n\t\t\t\t\t}\n\t\t\t\t\t&.social_googleplus {\n\t\t\t\t\t\tbackground: #dd4b39;\n\t\t\t\t\t}\n\t\t\t\t\t&.fa-vk {\n\t\t\t\t\t\tbackground: #45668e;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tmargin: 0 0 10px;\n\t\t\t\n\t\t\tp, a {\n\t\t\t\tmargin: 0;\n\t\t\t}\n\n\t\t\th6, p {\n\t\t\t\tdisplay: inline;\n\t\t\t}\n\t\t}\n\t\t\n\t\t.mkdf-ps-info-title,\n\t\t.mkdf-ps-title {\n\t\t\tmargin: 0;\n\t\t}\n\t}\n}\n\n/* ==========================================================================\n   Portfolio Single page style - end\n   ========================================================================== */\n\n/* ==========================================================================\n   Portfolio Single page specific style for types - begin\n   ========================================================================== */\n\n.mkdf-portfolio-single-holder {\n\t\n\t.mkdf-ps-image-holder {\n\t\t\n\t\t&.mkdf-grid-masonry-list {\n\t\t\t\n\t\t\t.mkdf-ps-image {\n\t\t\t\t\n\t\t\t\ta {\n\t\t\t\t\theight: 100%;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n}\n/* ==========================================================================\n   Portfolio Single page specific style for types - end\n   ========================================================================== */", "/* ==========================================================================\n   Portfolio Single - Gallery layout style - begin\n   ========================================================================== */\n\n.mkdf-portfolio-single-holder {\n    \n    &.mkdf-ps-gallery-layout {\n        \n        .mkdf-ps-image-holder {\n\t        width: 100%;\n\t\n\t        .mkdf-ps-image {\n\t\t        float: left;\n\t\t        \n\t\t       \n\t        }\n        }\n\t\n\t    > .mkdf-grid-row {\n\t\t    margin-top: 40px;\n\t    }\n    }\n}\n/* ==========================================================================\n   Portfolio Single - Gallery layout style - end\n   ========================================================================== */", "/* ==========================================================================\n   Portfolio Single - Huge Images layout style - begin\n   ========================================================================== */\n\n.mkdf-portfolio-single-holder {\n    \n    &.mkdf-ps-huge-images-layout {\n        padding: 0 4%;\n\t    box-sizing: border-box;\n\t    \n        .mkdf-ps-image-holder {\n            margin: 0 0 40px;\n        \n            .mkdf-ps-image {\n                margin: 0 0 30px;\n            \n                &:last-child {\n                    margin: 0;\n                }\n\n               \n            }\n        }\n\t    \n\t    @include laptop-landscape {\n\t\t    padding: 0 40px;\n\t    }\n\t\n\t    @include ipad-portrait {\n\t\t    padding: 0 30px;\n\t    }\n    }\n}\n/* ==========================================================================\n   Portfolio Single - Huge Images layout style - end\n   ========================================================================== */", "/* ==========================================================================\n   Portfolio Single - Images layout style - begin\n   ========================================================================== */\n\n.mkdf-portfolio-single-holder {\n    \n    &.mkdf-ps-images-layout {\n    \n        .mkdf-ps-image-holder {\n            margin: 0 0 40px;\n\t        \n            .mkdf-ps-image {\n                margin: 0 0 30px;\n                \n                &:last-child {\n                    margin: 0;\n                }\n\n                \n            }\n        }\n    }\n}\n/* ==========================================================================\n   Portfolio Single - Images layout style - end\n   ========================================================================== */", "/* ==========================================================================\n   Portfolio Single - Masonry layout style - begin\n   ========================================================================== */\n\n.mkdf-portfolio-single-holder {\n    \n    &.mkdf-ps-masonry-layout {\n        \n        > .mkdf-grid-row {\n            margin-top: 40px;\n        }\n       \n    }\n}\n/* ==========================================================================\n   Portfolio Single - Masonry layout style - end\n   ========================================================================== */", "/* ==========================================================================\n   Portfolio Single - Slider layout style - begin\n   ========================================================================== */\n\n.mkdf-portfolio-single-holder {\n\t\n\t&.mkdf-ps-slider-layout {\n\t\t\n\t\t.mkdf-ps-image-holder {\n\t\t\tmargin: 0 0 40px;\n\t\t\t\n\t\t\t.mkdf-ps-image-inner {\n\t\t\t\tvisibility: hidden;\n\t\t\t}\n\t\t\t\n\t\t\t.mkdf-ps-image {\n\t\t\t\t\n\t\t\t\timg {\n\t\t\t\t\twidth: 100%;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n}\n/* ==========================================================================\n   Portfolio Single - Slider layout style - end\n   ========================================================================== */", "/* ==========================================================================\n   Portfolio Single - Small Gallery layout style - begin\n   ========================================================================== */\n\n.mkdf-portfolio-single-holder {\n    \n    &.mkdf-ps-small-gallery-layout {\n    \n        .mkdf-ps-image-holder {\n            width: 100%;\n        \n            .mkdf-ps-image {\n                float: left;\n            \n            }\n        }\n    \n        .mkdf-ps-content-item {\n            margin: 0 0 30px;\n        }\n    }\n}\n/* ==========================================================================\n   Portfolio Single - Small Gallery layout style - end\n   ========================================================================== */", "/* ==========================================================================\n   Portfolio Single - Small Images layout style - begin\n   ========================================================================== */\n\n.mkdf-portfolio-single-holder {\n    \n    &.mkdf-ps-small-images-layout {\n    \n        .mkdf-ps-image-holder {\n        \n            .mkdf-ps-image {\n                margin: 0 0 30px;\n            \n                &:last-child {\n                    margin: 0;\n                }\n\n            }\n        }\n    \n        .mkdf-ps-content-item {\n            margin: 0 0 30px;\n        }\n    }\n}\n/* ==========================================================================\n   Portfolio Single - Small Images layout style - end\n   ========================================================================== */", "/* ==========================================================================\n   Portfolio Single - Small Masonry layout style - begin\n   ========================================================================== */\n\n.mkdf-portfolio-single-holder {\n    \n    &.mkdf-ps-small-masonry-layout {\n    \n        .mkdf-ps-content-item {\n            margin: 0 0 30px;\n        }\n\n    }\n}\n/* ==========================================================================\n   Portfolio Single - Small Masonry layout style - end\n   ========================================================================== */", "/* ==========================================================================\n   Portfolio Single - Small Slider layout style - begin\n   ========================================================================== */\n\n.mkdf-portfolio-single-holder {\n\t\n\t&.mkdf-ps-small-slider-layout {\n\t\t\n\t\t.mkdf-ps-image-holder {\n\t\t\t\n\t\t\t.mkdf-ps-image-inner {\n\t\t\t\tvisibility: hidden;\n\n\t\t\t\t.owl-stage-outer {\n\t\t\t\t\twidth: calc(100% - 1px);\n\t\t\t\t}\n\t\t\t}\n\t\t\t\n\t\t\t.mkdf-ps-image {\n\t\t\t\t\n\t\t\t\timg {\n\t\t\t\t\twidth: 100%;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\t\n\t\t.mkdf-ps-content-item {\n\t\t\tmargin: 0 0 30px;\n\t\t}\n\t}\n}\n/* ==========================================================================\n   Portfolio Single - Small Slider layout style - end\n   ========================================================================== */", "/* ==========================================================================\n   Portfolio Single navigation style - begin\n   ========================================================================== */\n\n.mkdf-ps-navigation {\n\tposition: relative;\n\tdisplay: table;\n\twidth: 100%;\n\tvertical-align: middle;\n\tpadding: 0;\n\tmargin: 44px 0 0;\n\tclear: both;\n    box-sizing: border-box;\n\n    .mkdf-ps-full-width-custom-layout & {\n        padding: 0 40px;\n    }\n\t\n\t.mkdf-ps-back-btn {\n\t\tposition: absolute;\n\t\ttop: 50%;\n\t\tleft: 50%;\n\t\tdisplay: inline-block;\n\t\tvertical-align: middle;\n\t\t@include mkdfTransform(translateX(-50%) translateY(-50%));\n\t\t\n\t\ta {\n\t\t\tposition: relative;\n\t\t\tdisplay: inline-block;\n\t\t\tmargin: 0;\n\t\t\tpadding: 0;\n\t\t\tvertical-align: middle;\n\t\t\tcursor: pointer;\n\t\t\tfont-size: 23px;\n\t\t\tline-height: 1;\n\t\t\t\n\t\t\tspan {\n\t\t\t\tdisplay: block;\n\t\t\t\tline-height: inherit;\n\t\t\t\t\n\t\t\t\t&:before,\n\t\t\t\t&:after {\n\t\t\t\t\tdisplay: block;\n\t\t\t\t\tline-height: 14px;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t&:after {\n\t\t\t\t\tcontent: \"\\e0a6\";\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\t\n\t.mkdf-ps-prev,\n\t.mkdf-ps-next {\n\t\tposition: relative;\n\t\tdisplay: table-cell;\n\t\tvertical-align: middle;\n\t\twidth: 49%;\n\t\tpadding: 0;\n\t\tbox-sizing: border-box;\n\t\t\n\t\ta {\n\t\t\tposition: relative;\n\t\t\tdisplay: inline-block;\n\t\t\tvertical-align: middle;\n\t\t\tfont-size: 46px;\n\t\t\tline-height: 55px;\n\t\t\t\n\t\t\t.mkdf-ps-nav-mark {\n\t\t\t\tposition: relative;\n\t\t\t\tdisplay: inline-block;\n\t\t\t\tvertical-align: top;\n\t\t\t\t\n\t\t\t\t&:before {\n\t\t\t\t\tdisplay: block;\n\t\t\t\t\tline-height: inherit;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\t\n\t.mkdf-ps-prev {\n\t\t\n\t\ta {\n\t\t\t\n\t\t\t.mkdf-ps-nav-mark {\n\t\t\t\tleft: -14px;\n\t\t\t}\n\t\t}\n\t}\n\t\n\t.mkdf-ps-next {\n\t\ttext-align: right;\n\t\t\n\t\ta {\n\t\t\t\n\t\t\t.mkdf-ps-nav-mark {\n\t\t\t\tright: -14px;\n\t\t\t}\n\t\t}\n\t}\n}\n/* ==========================================================================\n   Portfolio Single navigation style - end\n   ========================================================================== */", "/* ==========================================================================\n   Portfolio Single navigation style - begin\n   ========================================================================== */\n\n.mkdf-ps-related-posts-holder {\n\t@include mkdfRelativeHolderLayout();\n\tmargin: 80px 0 60px;\n\tclear: both;\n\t\n\t.mkdf-ps-related-posts {\n\t\tmargin: 0 -15px;\n\t}\n\t\n\t.mkdf-ps-related-post {\n\t\tposition: relative;\n\t\tdisplay: inline-block;\n\t\tvertical-align: middle;\n\t\twidth: 25%;\n\t\tfloat: left;\n\t\tpadding: 0 15px;\n\t\tbox-sizing: border-box;\n\t\t\n\t\t@include ipad-landscape {\n\t\t\twidth: 50%;\n\t\t\tmargin: 0 0 30px;\n\t\t\t\n\t\t\t&:nth-child(2n+1) {\n\t\t\t\tclear: both;\n\t\t\t}\n\t\t}\n\t\t\n\t\t@include phone-landscape {\n\t\t\twidth: 100%;\n\t\t}\n\t\t.mkdf-pli-image-hover {\n\t\t\t@include mkdfAbsoluteHolderLayout();\n\t\t\tpadding: 20px;\n\t\t\topacity: 0;\n\t\t\ttext-align: center;\n\t\t\tbox-sizing: border-box;\n\t\t\tbackground-color: $first-main-color-yellow;\n\t\t\tcolor: #fff;\n\t\t\tfont-size: 72px;\n\t\t\t@include mkdfTransition(opacity .2s ease-in-out);\n\n\t\t\t.mkdf-pli-image-hover-table{\n\t\t\t\tdisplay: table;\n\t\t\t\theight: 100%;\n\t\t\t\twidth: 100%;\n\n\t\t\t\ti{\n\t\t\t\t\tdisplay: table-cell;\n\t\t\t\t\tvertical-align: middle;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\t&:hover{\n\n\t\t\t\n\n\t\t\t\t.mkdf-pli-image-hover{\n\t\t\t\t\topacity: 1;\n\t\t\t\t\tcursor: pointer;\n\t\t\t\t}\n\t\t\t\n\t\t}\n\t}\n\t\n\t.mkdf-ps-related-image {\n\t\t@include mkdfRelativeHolderLayout();\n\t\t\n\t\ta, img {\n\t\t\tdisplay: block;\n\t\t}\n\t\toverflow: hidden;\n\t}\n\t\n\t.mkdf-ps-related-text {\n\t\t@include mkdfRelativeHolderLayout();\n\t\tmargin: 20px 0 0;\n\t\t\n\t\t.mkdf-ps-related-title {\n\t\t\tmargin: 0;\n\t\t}\n\t\t\n\t\t.mkdf-ps-related-categories {\n\t\t\tmargin: 6px 0 0;\n\t\t}\n\t}\n}\n/* ==========================================================================\n   Portfolio Single navigation style - end\n   ========================================================================== */", "/* ==========================================================================\n   Portfolio Category List shortcode style - begin\n   ========================================================================== */\n\n.mkdf-portfolio-category-list-holder {\n    @include mkdfRelativeHolderLayout();\n\n    article {\n\n\t    .touch & {\n\t\t    cursor: pointer;\n\t    }\n\t\n\t    &:hover {\n\t\t\n\t\t    .mkdf-pcli-text-holder {\n\t\t\t    opacity: 1;\n\t\t    }\n\t    }\n\n\t    .mkdf-pcl-item-inner {\n\t\t    @include mkdfRelativeHolderLayout();\n\t\t    overflow: hidden;\n\t    }\n\t    \n\t    .mkdf-pcli-image {\n\t\t    @include mkdfRelativeHolderLayout();\n\t\t    \n\t\t    img {\n\t\t\t    display: block;\n\t\t\t    width: 100%;\n\t\t    }\n\t    }\n\t\n\t    .mkdf-pcli-text-holder {\n\t\t    @include mkdfAbsoluteHolderLayout();\n\t\t    padding: 20px;\n\t\t    background-color: rgba($default-heading-color, .6);\n\t\t    opacity: 0;\n\t\t    text-align: center;\n\t\t    box-sizing: border-box;\n\t\t    @include mkdfTransition(opacity .2s ease-in-out);\n\t    }\n\n\t    .mkdf-pcli-text-wrapper {\n\t        @include mkdfTableLayout();\n\t    }\n\n\t    .mkdf-pcli-text {\n\t\t    @include mkdfTableCellLayout();\n\t    }\n\t\n\t    .mkdf-pcli-title {\n\t\t    margin: 0;\n\t\t    color: #fff;\n\t    }\n\t    \n\t    .mkdf-pcli-excerpt {\n\t\t    margin: 3px 0 0;\n\t\t    color: #fff;\n\t    }\n\t\n\t    .mkdf-pcli-link {\n\t\t    @include mkdfAbsoluteHolderLayout();\n\t    }\n    }\n}\n\n/* ==========================================================================\n   Portfolio Category List shortcode style - end\n   ========================================================================== */", "/* ==========================================================================\n   Portfolio shortcode style - begin\n   ========================================================================== */\n\n.mkdf-portfolio-list-holder {\n    @include mkdfRelativeHolderLayout();\n\n\t/***** Article Global Style - begin *****/\n\n\t&.mkdf-diagonal-layout.mkdf-five-columns{\n\n\t\tarticle{\n\n\t\t\t&:nth-child(6){\n\t\t\t\tmargin-top: 66px;\n\n\t\t\t\t@media screen and (max-width: 1024px){\n\t\t\t\t\tmargin-top: 0;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t&:nth-child(5){\n\t\t\t\tmargin-top: 132px;\n\n\t\t\t\t@media screen and (max-width: 768px){\n\t\t\t\t\tmargin-top: 0;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t&:nth-child(4){\n\t\t\t\tmargin-top: 198px;\n\n\t\t\t\t@media screen and (max-width: 680px){\n\t\t\t\t\tmargin-top: 0;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t&:nth-child(3){\n\t\t\t\tmargin-top: 264px;\n\n\t\t\t\t@media screen and (max-width: 680px){\n\t\t\t\t\tmargin-top: 0;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\t.mkdf-pl-inner {\n\n\t\t\t&.mkdf-masonry-list-wrapper {\n\t\t\t\t\t\n\t\t\t\t\topacity: 0;\n\t\t\t\t\t-webkit-transition: opacity .22s cubic-bezier(0.23, 1, 0.32, 1), -webkit-transform 0s cubic-bezier(0.23, 1, 0.32, 1);\n\t\t\t\t\ttransition: opacity .22s cubic-bezier(0.23, 1, 0.32, 1), transform 0s cubic-bezier(0.23, 1, 0.32, 1);\n\t\t\t\t\t\n\t\t\t\t\t\n\t\t\t\t\t.mkdf-pl-item  {\n\n\t\t\t\t\t\t\t.mkdf-pl-item-inner {\n\n\t\t\t\t\t\t\t\topacity: 0;\n\t\t\t\t\t\t\t\t@include mkdfTransform(scale(.6));\n\t\t\t\t\t\t\t\t-webkit-transition: -webkit-transform .6s cubic-bezier(0.18, 0.89, 0.32, 1), opacity .2s ease-out;\n\t\t\t\t\t\t\t\ttransition: transform .4s cubic-bezier(0.18, 0.89, 0.32, 1), opacity .2s ease-out;\n\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\n\n\t\t\t\t\t\t\t\n\n\t\t\t\t\t\t\t\t.mkdf-pl-item-inner {\n\n\t\t\t\t\t\t\t\t\t&.mkdf-appeared {\n\n\t\t\t\t\t\t\t\t\t\topacity: 1;\n\t\t\t\t\t\t\t\t\t\t@include mkdfTransform(scale(1));\n\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\n\n\t\t\t\t\t\t\t\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t}\n\t\t}\n\t}\n\n    article {\n\n\t    .touch & {\n\t\t    cursor: pointer;\n\t    }\n\t\t\n\t    .mkdf-pl-item-inner {\n\t\t    @include mkdfRelativeHolderLayout();\n\t    }\n\t    \n\t    .mkdf-pli-image {\n\t\t    @include mkdfRelativeHolderLayout();\n\t\t    \n\t\t    img {\n\t\t\t    display: block;\n\t\t\t    width: 100%;\n\t\t    }\n\n\t\t\t.mkdf-pli-image-hover {\n\t\t\t\t@include mkdfAbsoluteHolderLayout();\n\t\t\t\tpadding: 20px;\n\t\t\t\topacity: 0;\n\t\t\t\ttext-align: center;\n\t\t\t\tbox-sizing: border-box;\n\t\t\t\tcolor: #fff;\n\t\t\t\tfont-size: 72px;\n\t\t\t\t@include mkdfTransition(opacity .2s ease-in-out);\n\n\t\t\t\t.mkdf-pli-image-hover-table{\n\t\t\t\t\tdisplay: table;\n\t\t\t\t\theight: 100%;\n\t\t\t\t\twidth: 100%;\n\n\t\t\t\t\ti{\n\t\t\t\t\t\tdisplay: table-cell;\n\t\t\t\t\t\tvertical-align: middle;\n\t\t\t\t\t\t@include mkdfTransition(all .4s ease-in-out);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t    }\n\n\t\t&:hover{\n\n\t\t\t.mkdf-pli-image{\n\n\t\t\t\t.mkdf-pli-image-hover{\n\t\t\t\t\topacity: 1;\n\n\t\t\t\t\ti {\n\t\t\t\t\t\ttransform: rotate(90deg);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t    .mkdf-pli-link {\n\t\t    @include mkdfAbsoluteHolderLayout();\n\t    }\n\n\t    .mkdf-pli-text-wrapper {\n\t        @include mkdfTableLayout();\n\t    }\n\n\t    .mkdf-pli-text {\n\t\t    @include mkdfTableCellLayout();\n\n\t\t    .mkdf-pli-title {\n\t\t\t\tmargin: 0;\n\t\t\t\tline-height: 1.5em;\n\t\t    }\n\n\t\t    .mkdf-pli-category-holder {\n\t\t\t    position: relative;\n\t\t\t    display: block;\n\t\t\t   \n\n\t\t\t    a {\n\t\t\t\t    position: relative;\n\t\t\t\t    display: inline-block;\n\t\t\t\t    vertical-align: middle;\n\t\t\t\t    padding: 0 6px 0 0;\n\t\t\t\t    margin: 0 3px 0 0;\n\t\t\t\t\tcolor: #0c2c5866;\n\t\t\t\t    z-index: 8;\n\t\t\t\t\tfont-size: 12px;\n\t\t\t\t\tfont-family: $default-text-font;\n\t\t\t\t\tfont-weight: 700;\n\t\t\t\t\ttext-transform: uppercase;\n\t\t\t\t\tletter-spacing: 0.1em;\n\n\t\t\t\t    \n\t\t\t\t    &:after {\n\t\t\t\t\t    position: absolute;\n\t\t\t\t\t    top: 0;\n\t\t\t\t\t    right: -4px;\n\t\t\t\t\t    content: '/';\n\t\t\t\t\t    color: $first-main-color-dark-blue;\n\t\t\t\t\t\topacity: 0.4;\n\t\t\t\t\t    font-size: 14px;\n\t\t\t\t\t    line-height: inherit;\n\t\t\t\t    }\n\n\t\t\t\t    &:last-child {\n\t\t\t\t\t    margin: 0;\n\t\t\t\t\t    padding: 0;\n\t\t\t\t\t    \n\t\t\t\t\t    &:after {\n\t\t\t\t\t\t    display: none;\n\t\t\t\t\t    }\n\t\t\t\t    }\n\n\t\t\t\t\t&:hover{\n\t\t\t\t\t\tcolor: $first-main-color;\n\n\t\t\t\t\t\t\t&:after {\n\t\t\t\t\t\t    color: $first-main-color-dark-blue;\n\t\t\t\t\t\t\topacity: 0.4;\n\t\t\t\t\t\t    \n\t\t\t\t\t    }\n\t\t\t\t\t}\n\t\t\t    }\n\t\t    }\n\n\t\t    .mkdf-pli-excerpt {\n\t\t\t    margin: 10px 0 0;\n\t\t    }\n\t    }\n    }\n\n\t/***** Article Global Style - end *****/\n\t\n\t/***** Specific Global Style - begin *****/\n\t\n\t&.mkdf-pl-has-shadow {\n\t\t\n\t\tarticle {\n\t\t\t\n\t\t\t.mkdf-pli-image {\n\t\t\t\tbox-shadow: 0 16px 46px 0 rgba(0,0,0,.3);\n\t\t\t}\n\t\t}\n\t}\n\t\n\t&.mkdf-pl-has-filter {\n\t\t\n\t\t.mkdf-pl-inner {\n\t\t\toverflow: hidden;\n\t\t}\n\t}\n\t\n\t&.mkdf-pl-no-content {\n\t\t\n\t\t.mkdf-pli-text-holder {\n\t\t\tdisplay: none;\n\t\t}\n\t}\n\t/***** Specific Global Style - end *****/\n\t\n\t&.mkdf-pl-masonry {\n\n        &.mkdf-fixed-masonry-items {\n\t        \n            article {\n\t            \n                .mkdf-pl-item-inner,\n                .mkdf-pli-image {\n\t\t            height: 100%;\n\t            }\n            }\n        }\n\t}\n\n\t/***** Portfolio Types - end *****/\n\n\t/***** Additional Features - begin *****/\n\n\t&.mkdf-pl-has-animation {\n\n\t\tarticle {\n\t\t\topacity: 0;\n\t\t\t@include mkdfTransform(translateY(80px));\n\t\t\t@include mkdfTransition(opacity .8s cubic-bezier(0.34, 0.52, 0.57, 1.04), transform .8s cubic-bezier(0.34, 0.52, 0.57, 1.04));\n\n\t\t\t&.mkdf-item-show {\n\t\t\t\topacity: 1;\n\t\t\t\t@include mkdfTransform(translateY(0));\n\n\t\t\t\t&.mkdf-item-shown {\n\t\t\t\t\t@include mkdfTransition(none);\n\t\t\t\t}\n\t\t\t}\n\t\t\t\n\t\t\t.touch & {\n\t\t\t\topacity: 1;\n\t\t\t\t@include mkdfTransform(translateY(0));\n\t\t\t}\n\t\t}\n\t}\n\n\t/***** Additional Features - end *****/\n}\n\n/* ==========================================================================\n   Portfolio shortcode style - end\n   ========================================================================== */\n\n/* ==========================================================================\n   Portfolio filter style - begin\n   ========================================================================== */\n\n.mkdf-pl-filter-holder {\n    @include mkdfRelativeHolderLayout();\n    margin: 0 0 30px;\n    text-align: center;\n\n    ul {\n        position: relative;\n        display: inline-block;\n        vertical-align: middle;\n        margin: 0;\n        padding: 12px 29px;\n        list-style: none;\n\t\tborder: 2px solid #edeeef;\n\n        li {\n            position: relative;\n            display: inline-block;\n            vertical-align: middle;\n            margin: 0;\n\t        padding: 0 15px;\n            cursor: pointer;\n\n\t        @include ipad-landscape {\n\t\t        padding: 0 10px;\n\t        }\n\n            span {\n                position: relative;\n                display: inline-block;\n                vertical-align: middle;\n                color: $default-heading-color;\n\t\t\t\tfont-family: $default-text-font;\n                font-size: 20px;\n                line-height: 22px;\n                white-space: nowrap;\n                @include mkdfTransition(color .2s ease-out);\n            }\n\n            &.mkdf-pl-current,\n            &:hover {\n\n                span {\n                    color: $first-main-color;\n                }\n            }\n        }\n    }\n}\n/* ==========================================================================\n   Portfolio filter style - end\n   ========================================================================== */\n\n/* ==========================================================================\n   Portfolio standard pagination style - begin\n   ========================================================================== */\n\n.mkdf-portfolio-list-holder {\n\t\n\t&.mkdf-pl-pag-standard {\n\t\t\n\t\t.mkdf-pl-inner {\n\t\t\topacity: 1;\n\t\t\t@include mkdfTransition(opacity .2s ease-out);\n\t\t}\n\t\t\n\t\t&.mkdf-pl-pag-standard-animate {\n\t\t\t\n\t\t\t.mkdf-pl-inner {\n\t\t\t\topacity: 0;\n\t\t\t}\n\t\t}\n\t}\n}\n\n.mkdf-pl-standard-pagination {\n\t@include mkdfStandardPaginationStyle('shortcode');\n}\n/* ==========================================================================\n   Portfolio standard pagination style - end\n   ========================================================================== */\n\n/* ==========================================================================\n   Portfolio load more pagination style - begin\n   ========================================================================== */\n\n.mkdf-pl-load-more-holder {\n\t@include mkdfRelativeHolderLayout();\n\n    .mkdf-pl-load-more {\n\t    margin: 32px 0 0;\n\t    text-align: center;\n    }\n}\n/* ==========================================================================\n   Portfolio load more pagination style - end\n   ========================================================================== */\n\n/* ==========================================================================\n   Portfolio loading element style - begin\n   ========================================================================== */\n\n.mkdf-pl-loading {\n\tposition: relative;\n\tdisplay: none;\n\twidth: 100%;\n\tmargin: 40px 0 20px;\n\tcolor: $default-heading-color;\n\ttext-align: center;\n\n\t&.mkdf-filter-trigger {\n\t\tposition: absolute;\n\t\ttop: 250px;\n\t\tleft: 0;\n\t}\n\t\n    &.mkdf-standard-pag-trigger {\n\t\tposition: absolute;\n\t\ttop: 50px;\n\t\tleft: 0;\n\t\n\t    .mkdf-pl-has-filter & {\n\t\t    top: 150px;\n\t    }\n    }\n\n\t&.mkdf-showing {\n\t\tdisplay: block;\n\t}\n\n\t> div {\n\t\tposition: relative;\n\t\tdisplay: inline-block;\n\t\tvertical-align: middle;\n\t\twidth: 14px;\n\t\theight: 14px;\n\t\tmargin: 0 3px;\n\t\tbackground-color: $default-heading-color;\n\t\tborder-radius: 100%;\n\t\t@include mkdfAnimation(sk-bouncedelay 1.4s infinite ease-in-out both);\n\t}\n\n\t.mkdf-pl-loading-bounce1 {\n\t\t-webkit-animation-delay: -0.32s;\n\t\t-moz-animation-delay: -0.32s;\n\t\tanimation-delay: -0.32s;\n\t}\n\n\t.mkdf-pl-loading-bounce2 {\n\t\t-webkit-animation-delay: -0.16s;\n\t\t-moz-animation-delay: -0.16s;\n\t\tanimation-delay: -0.16s;\n\t}\n}\n\n@-webkit-keyframes sk-bouncedelay {\n\t0%, 80%, 100% {\n\t\t-webkit-transform: scale(0);\n\t}\n\t40% {\n\t\t-webkit-transform: scale(1.0);\n\t}\n}\n\n@-moz-keyframes sk-bouncedelay {\n\t0%, 80%, 100% {\n\t\t-moz-transform: scale(0);\n\t}\n\t40% {\n\t\t-moz-transform: scale(1.0);\n\t}\n}\n\n@keyframes sk-bouncedelay {\n\t0%, 80%, 100% {\n\t\t-webkit-transform: scale(0);\n\t\ttransform: scale(0);\n\t}\n\t40% {\n\t\t-webkit-transform: scale(1.0);\n\t\ttransform: scale(1.0);\n\t}\n}\n\n/* ==========================================================================\n   Portfolio list responsive - start\n   ========================================================================== */\n\n@media (min-width: 768px) and (max-width: 1024px) {\n\n\t.mkdf-portfolio-list-holder.mkdf-grid-list.mkdf-three-columns .mkdf-item-space, \n\t.mkdf-portfolio-list-holder.mkdf-grid-list.mkdf-three-columns .mkdf-masonry-grid-sizer {\n\n\t    width: 50% !important;\n\t    \n\t\n\t}\n\t.mkdf-portfolio-list-holder.mkdf-grid-list.mkdf-three-columns .mkdf-item-space:nth-child(2n+1) {\n\t\tclear: both !important;\n\t}\n\t\n\t\t\n}\n\n/* ==========================================================================\n   Portfolio list responsive - end\n   ========================================================================== */\n   \n/* ==========================================================================\n   Portfolio loading element style - end\n   ========================================================================== */", "/* ==========================================================================\n   Portfolio Item Layout - Standard Shader style - begin\n   ========================================================================== */\n\n.mkdf-portfolio-list-holder {\n\t\n\t&.mkdf-pl-boxed {\n\n\t\t&.mkdf-light-skin {\n\n\t\t\t.mkdf-pli-excerpt,\n\t\t\t.mkdf-pli-title {\n\t\t\t\tcolor: #fff;\n\t\t\t}\n\t\t}\n\n\t\t.mkdf-pl-item-inner{\n\t\t\tborder: 2px solid rgba(225,225,225,.2);\n\t\t\tpadding: 20px;\n\t\t\tbox-sizing: border-box;\n\t\t\t@include mkdfTransition(all .3s ease-out);\n\n\t\t\t&:hover {\n\t\t\t\tborder: 2px solid rgba(225,225,225,.4);\n\t\t\t}\n\t\t}\n\n\t\t.mkdf-pli-text-holder {\n\t\t\t@include mkdfRelativeHolderLayout();\n\t\t\tmargin: 29px 0 0;\n\t\t}\n\n\t\tarticle {\n\n\t\t\t.mkdf-pli-text {\n\n\t\t\t\t.mkdf-pli-category-holder {\n\t\t\t\t\tmargin: 20px 0 0;\n\t\t\t\t\tdisplay: inline-block;\n\t\t\t\t\twidth: 60%;\n\n\t\t\t\t\ta {\n\t\t\t\t\t\tcolor: #0c2c5866;\n\t\t\t\t\t\tfont-weight: 700;\n\t\t\t\t\t\tfont-size: 12px;\n\t\t\t\t\t\tletter-spacing: 0.01em;\n\n\t\t\t\t\t\t&:after {\n\t\t\t\t\t\t\tfont-size: 12px;\n\t\t\t\t\t\t    color: #0c2c5866;\n\t\t\t\t\t    }\n\n\t\t\t\t\t\t&:hover {\n\t\t\t\t\t\t\tcolor: $first-main-color;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t.mkdf-pli-social-share {\n\t\t\t\t\tdisplay: inline-block;\n\t\t\t\t\twidth: 40%;\n\t\t\t\t\ttext-align: right;\n\t\t\t\t\tcolor: #fff;\n\t\t\t\t\tfloat: right;\n\t\t\t\t\tmargin: 20px 0 0;\n\n\t\t\t\t\tul {\n\t\t\t\t\t\tli {\n\t\t\t\t\t\t\ta {\n\t\t\t\t\t\t\t\tcolor: rgba(255,255,255, 0.4);\n\t\t\t\t\t\t\t\tz-index: 8;\n\t\t\t\t\t\t\t\tposition: relative;\n\n\t\t\t\t\t\t\t\t&:hover{\n\t\t\t\t\t\t\t\t\tcolor: rgba(255,255,255, 1);\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t}\n}\n/* ==========================================================================\n   Portfolio Item Layout - Standard Shader style - end\n   ========================================================================== */", "/* ==========================================================================\n   Portfolio Item Layout - Gallery Overlay style - begin\n   ========================================================================== */\n\n.mkdf-portfolio-list-holder {\n\t\n\t&.mkdf-pl-gallery-overlay {\n\t\t\n\t\t&.mkdf-pl-has-shadow {\n\t\t\t\n\t\t\t.mkdf-pl-item-inner {\n\t\t\t\tbox-shadow: 0px 16px 46px 0px rgba(182, 40, 68, 0.52);\n\t\t\t}\n\t\t}\n\t\t\n\t\tarticle {\n\t\t\t\n\t\t\t&:hover {\n\t\t\t\t\n\t\t\t\t.mkdf-pli-text-holder {\n\t\t\t\t\topacity: 1;\n\t\t\t\t}\n\t\t\t}\n\t\t\t\n\t\t\t.mkdf-pl-item-inner {\n\t\t\t\toverflow: hidden;\n\t\t\t}\n\n\t\t}\n\t}\n}\n/* ==========================================================================\n   Portfolio Item Layout - Gallery Overlay style - end\n   ========================================================================== */", "/* ==========================================================================\n   Portfolio Item Layout - Gallery Slide From Image Bottom style - begin\n   ========================================================================== */\n\n.mkdf-portfolio-list-holder {\n\t\n\t&.mkdf-pl-gallery-slide-from-image-bottom {\n\t\t\n\t\t&.mkdf-pl-has-shadow {\n\t\t\t\n\t\t\t.mkdf-pl-item-inner {\n\t\t\t\tbox-shadow: 0px 16px 46px 0px rgba(182, 40, 68, 0.52);\n\t\t\t}\n\t\t}\n\t\t\n\t\tarticle {\n\t\t\t\n\t\t\t&:hover {\n\t\t\t\t\n\t\t\t\t.mkdf-pli-text-holder {\n\t\t\t\t\t@include mkdfTransform(translateY(0));\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t.mkdf-pli-text-wrapper {\n\t\t\t\t\t@include mkdfTransform(translateY(0));\n\t\t\t\t}\n\t\t\t}\n\t\t\t\n\t\t\t.mkdf-pl-item-inner {\n\t\t\t\toverflow: hidden;\n\t\t\t}\n\t\t}\n\t\t\n\t\t.mkdf-pli-text-holder {\n\t\t\tposition: absolute;\n\t\t\tdisplay: block;\n\t\t\twidth: 100%;\n\t\t\theight: auto;\n\t\t\tbottom: 0;\n\t\t\tleft: 0;\n\t\t\tpadding: 15px 20px 10px;\n\t\t\tbackground-color: #fff;\n\t\t\toverflow: hidden;\n\t\t\tbox-sizing: border-box;\n\t\t\t@include mkdfTransform(translateY(100%));\n\t\t\t@include mkdfTransitionTransform(.4s ease-in-out);\n\t\t}\n\t\t\n\t\t.mkdf-pli-text-wrapper {\n\t\t\t@include mkdfTransform(translateY(-200%));\n\t\t\t@include mkdfTransitionTransform(.4s ease-in-out);\n\t\t}\n\t}\n}\n/* ==========================================================================\n   Portfolio Item Layout - Gallery Slide From Image Bottom style - end\n   ========================================================================== */", "/* ==========================================================================\n   Portfolio Item Layout - Standard Shader style - begin\n   ========================================================================== */\n\n.mkdf-portfolio-list-holder {\n\t\n\t&.mkdf-pl-standard-shader {\n\t\t\n\t\t.mkdf-pli-text-holder {\n\t\t\t@include mkdfRelativeHolderLayout();\n\t\t\tmargin: 15px 0 10px;\n\t\t}\n\t}\n}\n/* ==========================================================================\n   Portfolio Item Layout - Standard Shader style - end\n   ========================================================================== */", "/* ==========================================================================\n   Portfolio Item Layout - Standard Switch Images style - begin\n   ========================================================================== */\n\n.mkdf-portfolio-list-holder {\n\t\n\t&.mkdf-pl-standard-switch-images {\n\t\t\n\t\tarticle {\n\t\t\t\n\t\t\t.mkdf-pli-image {\n\t\t\t\t\n\t\t\t\timg {\n\t\t\t\t\t@include mkdfTransition(opacity .2s ease-in-out);\n\n                    &:nth-child(1) {\n                        opacity: 1;\n                    }\n\n                    &:nth-child(2) {\n\t\t\t\t\t\t@include mkdfAbsoluteHolderLayout();\n\t\t\t\t\t\topacity: 0;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\t\n\t\t\t&.mkdf-pl-has-switch-image {\n\t\t\t\t\n\t\t\t\t&:hover {\n\t\t\t\t\t\n\t\t\t\t\t.mkdf-pli-image {\n\t\t\t\t\t\t\n\t\t\t\t\t\timg {\n\n                            &:nth-child(1) {\n                                opacity: 1;\n                            }\n\n                            &:nth-child(2) {\n\t\t\t\t\t\t\t\topacity: 1;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\t\n\t\t.mkdf-pli-text-holder {\n\t\t\t@include mkdfRelativeHolderLayout();\n\t\t\tmargin: 15px 0 0;\n\t\t}\n\t}\n}\n/* ==========================================================================\n   Portfolio Item Layout - Standard Switch Images style - end\n   ========================================================================== */", "/* ==========================================================================\n   Portfolio Project Info shortcode style - begin\n   ========================================================================== */\n\n.mkdf-portfolio-project-info {\n    position: relative;\n\tdisplay: inline-block;\n\tvertical-align: middle;\n\n\t.mkdf-ppi-label {\n\t\tmargin: 0;\n\t\tpadding: 0;\n\t}\n\n\t> div {\n\t\tposition: relative;\n\t\tdisplay: inline-block;\n\t\tvertical-align: middle;\n\n\t\ta {\n\t\t\tposition: relative;\n\t\t\tdisplay: inline-block;\n\t\t\tvertical-align: middle;\n\t\t\tmargin: 0 5px 0 0;\n\n\t\t\t&:last-child {\n\t\t\t\tmargin: 0;\n\t\t\t}\n\t\t}\n\t}\n\n\t.mkdf-ppi-title {\n\t\tposition: relative;\n\t\tdisplay: inline-block;\n\t\tvertical-align: middle;\n\t\tmargin: 0;\n\t}\n\t\n\t.mkdf-ppi-image {\n\t\tposition: relative;\n\t\tdisplay: inline-block;\n\t\tvertical-align: middle;\n\t\tmargin: 0;\n\t\t\n\t\timg {\n\t\t\tdisplay: block;\n\t\t}\n\t}\n}\n/* ==========================================================================\n   Portfolio Project Info shortcode style - end\n   ========================================================================== */", "/* ==========================================================================\n   Portfolio Slider shortcode style - begin\n   ========================================================================== */\n\n.mkdf-portfolio-slider-holder {\n    @include mkdfRelativeHolderLayout();\n\n\t.mkdf-portfolio-list-holder {\n\n\t\t&.mkdf-nav-light-skin {\n\n\t\t\t.owl-nav {\n\n\t\t\t\t.owl-prev,\n\t\t\t\t.owl-next {\n\t\t\t\t\tcolor: $header-light-color;\n\t\t\t\t\t\n\t\t\t\t\t&:hover {\n\t\t\t\t\t\tcolor: $header-light-hover-color;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t&.mkdf-nav-dark-skin {\n\n\t\t\t.owl-nav {\n\n\t\t\t\t.owl-prev,\n\t\t\t\t.owl-next {\n\t\t\t\t\tcolor: $header-dark-color;\n\t\t\t\t\t\n\t\t\t\t\t&:hover {\n\t\t\t\t\t\tcolor: $header-dark-hover-color;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t&.mkdf-pag-light-skin {\n\n\t\t\t.owl-dots {\n\n\t\t\t\t.owl-dot {\n\n\t\t\t\t\tspan {\n\t\t\t\t\t\tbackground-color: rgba($header-light-color, .2);\n\t\t\t\t\t}\n\n\t\t\t\t\t&.active,\n\t\t\t\t\t&:hover {\n\n\t\t\t\t\t\tspan {\n\t\t\t\t\t\t\tbackground-color: $header-light-hover-color;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t&.mkdf-pag-dark-skin {\n\n\t\t\t.owl-dots {\n\n\t\t\t\t.owl-dot {\n\n\t\t\t\t\tspan {\n\t\t\t\t\t\tbackground-color: rgba($header-dark-color, .2);\n\t\t\t\t\t}\n\n\t\t\t\t\t&.active,\n\t\t\t\t\t&:hover {\n\n\t\t\t\t\t\tspan {\n\t\t\t\t\t\t\tbackground-color: $header-dark-hover-color;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t&.mkdf-pag-on-slider {\n\n\t\t\t.owl-nav {\n\n\t\t\t\t.owl-prev,\n\t\t\t\t.owl-next {\n\t\t\t\t\t@include mkdfTransform(translateY(-50%));\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t.owl-dots {\n\t\t\t\tposition: absolute;\n\t\t\t\tleft: 0;\n\t\t\t\tbottom: 20px;\n\t\t\t\twidth: 100%;\n\t\t\t\tmargin: 0;\n\t\t\t}\n\t\t}\n\t}\n}\n/* ==========================================================================\n   Portfolio Slider shortcode style - end\n   ========================================================================== */", "/* ==========================================================================\n   Testimonials standard style - begin\n   ========================================================================== */\n\n.mkdf-testimonials-holder {\n    @include mkdfRelativeHolderLayout();\n\n    .mkdf-testimonials,\n    .mkdf-testimonial-content,\n    .mkdf-testimonial-text-holder {\n        @include mkdfRelativeHolderLayout();\n    }\n\n    .mkdf-testimonials {\n        box-sizing: border-box;\n\n        .mkdf-testimonials-icon{\n            position: absolute;\n            display: inline-block;\n            left: 50%;\n            top: -80px;\n\n            @include mkdfTransform(translateX(-50%));\n        }\n    }\n\n    .mkdf-testimonial-image {\n\n        img {\n            width: auto !important;\n            border-radius: 5em;\n        }\n    }\n    \n    &.mkdf-testimonials-standard {\n        text-align: center;\n\n        .mkdf-testimonial-content{\n            max-width: 875px;\n        }\n\n        .mkdf-testimonial-image {\n            @include mkdfRelativeHolderLayout();\n            margin: 25px 0 0;\n\n            img {\n                display: block;\n                margin: 0 auto;\n                border: 4px solid #fff;\n                border-radius: 50%;\n                box-sizing: border-box;\n            }\n        }\n\n        .mkdf-testimonial-title{\n            font-size: 55px;\n            font-weight: 600;\n            margin: 0 0 39px;\n        }\n\n        .mkdf-testimonial-text {\n            margin: 0 0 20px;\n            font-size: 18px;\n            color: #4e4e4e;\n            line-height: 34px;\n        }\n\n        .mkdf-testimonial-author {\n            margin-top: 43px;\n\n            > .mkdf-testimonials-author-name{\n                font-size: 26px;\n                font-weight: 600;\n                color: $first-main-color;\n                margin: 25px 0 2px;\n            }\n\n            > .mkdf-testimonials-author-job{\n                font-size: 14px;\n                font-weight: 700;\n                color: $first-main-color-dark-blue;\n                margin: 25px 0 2px;\n                letter-spacing: .1em;\n                text-transform: uppercase;\n                font-family: $default-text-font;\n            }\n\n\n        }\n\n        /* Light/Dark styles */\n        &.mkdf-testimonials-light {\n            \n            .mkdf-testimonial-title,\n            .mkdf-testimonial-text,\n            .mkdf-testimonial-author {\n                color: #fff;\n            }\n\n            .mkdf-testimonial-author {\n\n                > .mkdf-testimonials-author-name {\n                    color: #fff;\n                }\n                > .mkdf-testimonials-author-job{\n                    color: rgba(#fff,0.6);\n                }\n            }\n\n            .owl-dots {\n                \n                .owl-dot {\n                    \n                    span {\n                        border: 2px solid rgba(#fff, .5);\n                    }\n                    \n                    &:hover,\n                    &.active {\n\n                        span {\n                            background-color: #fff;\n                            border-color: #fff;\n                        }\n                    }\n                }\n            }\n            .mkdf-owl-slider {\n\n                .owl-nav {\n\n                    .owl-next,.owl-prev {\n\n                        > span {\n\n                            color: #fff;\n                        }\n                    }\n                }\n           \n            }\n        }\n\n        @media (max-width: 1024px) {\n\n            .mkdf-owl-slider {\n\n                .owl-nav {\n\n                    .owl-next,.owl-prev {\n\n                        > span {\n\n                            display: none;\n                        }\n                    }\n                }\n           \n            }\n        }\n    }\n}\n/* ==========================================================================\n   Testimonials standard style - end\n   ========================================================================== */"]}