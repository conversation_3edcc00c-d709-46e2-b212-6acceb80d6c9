<?php
$rand = rand(0, 1000);
$link_class = !empty($play_button_hover_image) ? 'mkdf-vb-has-hover-image' : '';
?>
<div class="mkdf-video-button-holder <?php echo esc_attr($holder_classes); ?> " >
	<div class="mkdf-video-button-image">
		<?php echo wp_get_attachment_image($video_image, 'full'); ?>

        <?php if(!empty($play_button_image)) { ?>
            <a class="mkdf-video-button-play-image <?php echo esc_attr($link_class); ?>" href="<?php echo esc_url($video_link); ?>" data-rel="prettyPhoto[video_button_pretty_photo_<?php echo esc_attr($rand); ?>]">
			<span class="mkdf-video-button-play-inner">
				<?php echo wp_get_attachment_image($play_button_image, 'full'); ?>
                <?php if(!empty($play_button_hover_image)) { ?>
                    <?php echo wp_get_attachment_image($play_button_hover_image, 'full'); ?>
                <?php } ?>
			</span>
            </a>
        <?php } else { ?>
            <a class="mkdf-video-button-play" <?php echo boostup_mikado_get_inline_style($play_button_styles); ?> href="<?php echo esc_url($video_link); ?>" data-rel="prettyPhoto[video_button_pretty_photo_<?php echo esc_attr($rand); ?>]">
			<span class="mkdf-video-button-play-inner <?php echo esc_attr($button_classes); ?>">
                <span class="icon-basic-video" <?php echo boostup_mikado_get_inline_style($circle_style); ?> >

                    <span class="icon-basic-animation" <?php echo boostup_mikado_get_inline_style($circle_animation_style); ?> ></span>
                    
                </span>
				
			</span>
            </a>
        <?php } ?>
	</div>

    <?php if ($video_text !== ''){ ?>
        <div class="mkdf-video-button-text"  <?php echo boostup_mikado_get_inline_style($text_box_style); ?>  >
            <<?php echo esc_attr($title_tag); ?> class="mkdf-video-button-title"><?php echo wp_kses_post($title); ?></<?php echo esc_attr($title_tag); ?>>
            <p><?php echo esc_html($video_text); ?></p>
        </div>
        <div class="mkdf-video-button-text-shadow-holder" <?php echo boostup_mikado_get_inline_style($text_shadow_style); ?> >

        </div>
    <?php } ?>
</div>