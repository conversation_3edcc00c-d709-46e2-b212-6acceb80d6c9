{"version": 3, "sources": ["shortcodes-map.scss", "shortcodes-map.css", "../../../../../themes/boostup/assets/css/scss/_mixins.scss", "../../../../../themes/boostup/assets/css/scss/_variables.scss", "../../../shortcodes/accordions/assets/css/scss/default/_accordions.scss", "../../../shortcodes/banner/assets/css/scss/default/_banner.scss", "../../../shortcodes/button/assets/css/scss/default/_button.scss", "../../../shortcodes/call-to-action/assets/css/scss/default/_call-to-action.scss", "../../../shortcodes/clients-carousel/assets/css/scss/default/_clients-carousel.scss", "../../../shortcodes/clients-grid/assets/css/scss/default/_clients-grid.scss", "../../../shortcodes/countdown/assets/css/scss/default/_countdown.scss", "../../../shortcodes/counter/assets/css/scss/default/_counter.scss", "../../../shortcodes/custom-font/assets/css/scss/default/_custom-font.scss", "../../../shortcodes/dropcaps/assets/css/scss/default/_dropcaps.scss", "../../../shortcodes/elements-holder/assets/css/scss/default/_elements-holder.scss", "../../../shortcodes/google-map/assets/css/scss/default/_google-map.scss", "../../../shortcodes/icon-list-item/assets/css/scss/default/_icon-list-item.scss", "../../../shortcodes/icon-with-text/assets/css/scss/default/_icon-with-text.scss", "../../../shortcodes/icon/assets/css/scss/default/_icon.scss", "../../../shortcodes/image-gallery/assets/css/scss/default/_image-gallery.scss", "../../../shortcodes/image-with-text/assets/css/scss/default/_image-with-text.scss", "../../../shortcodes/pie-chart/assets/css/scss/default/_pie-chart.scss", "../../../shortcodes/pricing-table/assets/css/scss/default/_pricing-tables.scss", "../../../shortcodes/process/assets/css/scss/default/_process.scss", "../../../shortcodes/progress-bar/assets/css/scss/default/_progress-bar.scss", "../../../shortcodes/roadmap/assets/css/scss/default/_roadmap.scss", "../../../shortcodes/section-title/assets/css/scss/default/_section-title.scss", "../../../shortcodes/separator/assets/css/scss/default/_separator.scss", "../../../shortcodes/single-image/assets/css/scss/default/_single-image.scss", "../../../shortcodes/social-share/assets/css/scss/default/_social-share.scss", "../../../shortcodes/tabs/assets/css/scss/default/_tabs.scss", "../../../shortcodes/team/assets/css/scss/default/_team.scss", "../../../shortcodes/text-marquee/assets/css/scss/default/text-marquee.scss", "../../../shortcodes/video-button/assets/css/scss/default/_video-button.scss", "../../../shortcodes/workflow/assets/css/scss/default/_workflow.scss"], "names": [], "mappings": "AAAA;;+ECE+E;ACuf9E;EACG;IAxUA,+BAyUsC;IAvUtC,uBAuUsC;IArVtC,4BAsVoC;IApVpC,oBAoVoC;EDjftC;ECmfE;IA5UA,+BA6UsC;IA3UtC,uBA2UsC;IAzVtC,4BA0VoC;IAxVpC,oBAwVoC;ED7etC;AACF;ACqeC;EACG;IAxUA,+BAyUsC;IAvUtC,uBAuUsC;IArVtC,4BAsVoC;IApVpC,oBAoVoC;EDjftC;ECmfE;IA5UA,+BA6UsC;IA3UtC,uBA2UsC;IAzVtC,4BA0VoC;IAxVpC,oBAwVoC;ED7etC;AACF;;AC+eA;EACG;IAEC,wCAAgC;IAAhC,gCAAgC;ED5elC;EC8eA;IAEI,mDCteoB;IDsepB,2CCteoB;EFNxB;EC8eA;IAEI,gDC1eoB;ID0epB,wCC1eoB;EFFxB;AACF;;AC8eA;EACE;IAEE,wCAAgC;IAAhC,gCAAgC;ED3elC;EC6eA;IAEI,mDCpfoB;IDofpB,2CCpfoB;EFSxB;EC6eA;IAEI,gDCxfoB;IDwfpB,wCCxfoB;EFaxB;AACF;;AC6eA;EACE;IACE,wCAAwC;ED1e1C;EC4eA;IACI,sDChgBoB;EFsBxB;EC4eA;IACI,gDCngBoB;EFyBxB;AACF;;AC4eA;EACE;IAEE,wCAAgC;IAAhC,gCAAgC;EDzelC;EC2eA;IAEI,mDC7gBoB;ID6gBpB,2CC7gBoB;EFoCxB;EC2eA;IAEI,gDCjhBoB;IDihBpB,wCCjhBoB;EFwCxB;AACF;;AC2eA;EACE;IACE,mCCthBsB;EF8CxB;EC0eA;IACI,sDCzhBoB;EFiDxB;EC0eA;IACI,gDC5hBoB;EFoDxB;AACF;;AC0eA;EACE;IAEE,mCCliBsB;IDkiBtB,2BCliBsB;EF2DxB;ECyeA;IAEI,mDCtiBoB;IDsiBpB,2CCtiBoB;EF+DxB;ECyeA;IAEI,gDC1iBoB;ID0iBpB,wCC1iBoB;EFmExB;AACF;;ADlGA;;+ECsG+E;AG5G/E;;+EH+G+E;AG3G/E;EFCI,kBAAkB;EAClB,qBAAqB;EACrB,WAAW;EACX,sBAJmD;ADiHvD;;AGjHA;EAIQ,kBAAkB;EAClB,eAAe;EACf,SAAS;EACT,8BAAsB;EAAtB,sBAAsB;EF2J1B,kCE1J0C;EF4J1C,0BE5J0C;EAC5C,0CAAuC;AHmHzC;;AG5HA;EAYM,cAAc;EACd,oBAAoB;AHoH1B;;AGjIA;EAiBY,kBAAkB;EAClB,QAAQ;EACX,QAAQ;EACL,WAAW;EACd,YAAY;EACZ,gBAAgB;EAChB,eAAe;EACf,iBAAiB;EACd,kBAAkB;EFyI1B,mCExI+C;EF0I/C,+BE1I+C;EF0I/C,2BE1I+C;EF4H/C,0CE3HsD;EF6HtD,kCE7HsD;AHwH1D;;AGnJA;EA8BU,kBAAkB;EAClB,cAAc;EACd,WAAW;EACX,YAAY;EACZ,kBAAkB;EAClB,oBAAoB;EFmH1B,yCElHkD;EFoHlD,iCEpHkD;AH2HtD;;AG/JA;EAuCW,cAAc;EACd,oBAAoB;AH4H/B;;AGpKA;EAiDU,cAAc;EFiHpB,iCEhH4C;EFkH5C,6BElH4C;EFkH5C,yBElH4C;AHyHhD;;AG3KA;EAqDiB,WAAW;EACX,cAAc;EACd,WAAW;EACX,WAAW;EACX,kBAAkB;EAClB,OAAO;EACP,YAAY;EACZ,8BAA8B;EF0F3C,0CEzF2D;EF2F3D,kCE3F2D;AH4H/D;;AGzLA;EAqEY,WAAW;AHwHvB;;AG7LA;EAkFY,SAAS;AH+GrB;;AGjMA;EA4FQ,SAAS;EACf,0CDhDiC;ECiDjC,gBAAgB;AHyGlB;;AGvMA;EAiGM,SAAS;AH0Gf;;AG3MA;EAyGG,WAAW;AHsGd;;AG/MA;EA4GI,WAAW;AHuGf;;AGnNA;EAiHG,gBAAgB;EAChB,4BAA4B;AHsG/B;;AGxNA;EAqHI,SAAS;AHuGb;;AG5NA;EAyHI,WAAW;AHuGf;;AGhOA;EA6HI,mBAAmB;AHuGvB;;AGpOA;EAkIG,4BAA4B;AHsG/B;;AGxOA;EAuIE,iDD1FiC;AF+LnC;;AG5OA;EA0IG,yBAAyB;EACzB,8CD9FgC;AFoMnC;;AGjPA;EA+IG,iCAAiC;AHsGpC;;AGrPA;EAkJI,sCDrG+B;AF4MnC;;AGzPA;EAuJG,oBAAoB;AHsGvB;;AGlGA;;+EHsG+E;AIrQ/E;;+EJwQ+E;AIpQ/E;EHCI,kBAAkB;EAClB,qBAAqB;EACrB,WAAW;EACX,sBAJmD;AD0QvD;;AIvQC;EACC,eAAe;AJ0QjB;;AI9QA;EAYI,UAAU;AJsQd;;AIlRA;EAiBG,UAAU;EHqIT,yCGpI2C;EHsI3C,iCGtI2C;AJuQ/C;;AIzRA;EAyBG,aAAa;AJoQhB;;AI7RA;EAgCG,kBAAkB;EAClB,kBAAkB;AJiQrB;;AIlSA;EHCI,kBAAkB;EAClB,qBAAqB;EACrB,WAAW;EACX,sBAJmD;ADySvD;;AIzSA;EAyCG,cAAc;AJoQjB;;AI7SA;EHQI,kBAAkB;EAClB,cAAc;EACd,WAAW;EACX,YAAY;EACZ,MAAM;EACN,OAAO;EGkCT,aAAa;EACb,uCFV6B;EEW7B,8BAAsB;EAAtB,sBAAsB;AJwQxB;;AC4VI;EGrpBJ;IAoDG,aAAa;EJ0Qd;AACF;;AI/TA;EHqHI,kBAAkB;EAClB,cAAc;EACd,mBAAmB;EACnB,YAAY;EACZ,WAAW;AD8Mf;;AIvUA;EA6DE,kBAAkB;EAClB,mBAAmB;EACnB,YAAY;EACZ,WAAW;EACX,sBAAsB;AJ8QxB;;AI/UA;EAqEE,eAAe;EACf,WAAW;AJ8Qb;;AIpVA;EA0EE,SAAS;EACT,WAAW;AJ8Qb;;AIzVA;EA8EG,gBAAgB;AJ+QnB;;AI7VA;EAmFE,kBAAkB;EAClB,qBAAqB;EACrB,mBAAmB;EACnB,gBAAgB;EAChB,WAAW;EACX,gBAAgB;EAChB,UAAU;EHyER,gCGxEkC;EH0ElC,wBG1EkC;AJgRtC;;AI1WA;EA+FI,WAAW;AJ+Qf;;AI9WA;EAoGG,kBAAkB;EAClB,qBAAqB;EACrB,mBAAmB;EACnB,WAAW;AJ8Qd;;AIrXA;EA0GI,cAAc;AJ+QlB;;AIzXA;EA+GG,kBAAkB;EAClB,MAAM;EACN,OAAO;EACP,WAAW;EACX,YAAY;EACZ,qBAAqB;EACrB,mBAAmB;EACnB,mBAAmB;EACnB,gBAAgB;EH+Bf,0CG9B4C;EHgC5C,kCGhC4C;AJgRhD;;AIxYA;EA2HI,cF3FsB;AF4W1B;;AI5YA;;EAiIG,kBAAkB;EAClB,qBAAqB;EACrB,mBAAmB;AJgRtB;;AInZA;EAuIG,iBAAiB;EACjB,eAAe;AJgRlB;;AIxZA;EA4IG,eAAe;EACf,oBAAoB;AJgRvB;;AI7ZA;EHQI,kBAAkB;EAClB,cAAc;EACd,WAAW;EACX,YAAY;EACZ,MAAM;EACN,OAAO;EGsIT,UAAU;AJoRZ;;AIjRA;;+EJqR+E;AK/a/E;;+ELkb+E;AK9a/E;EJiaI,kBAAkB;EAClB,qBAAqB;EACrB,sBAAsB;EACtB,WAAW;EACX,SAAS;EACT,uCCzY0C;ED0Y1C,eAAe;EACf,gBAAgB;EAChB,sBAAsB;EACtB,gBAAgB;EAChB,yBAAyB;EACzB,aAAa;EACb,8BAAsB;EAAtB,sBAAsB;EAvRtB,4GAwR6G;EAtR7G,oGAsR6G;EASzG,uBAAuB;EIpb3B,eAAe;AL+bnB;;AKlcA;EAMQ,qBAAqB;EACrB,cHgCoB;EG/BpB,6BAA6B;EAC7B,SAAS;EACT,sBAAsB;ALgc9B;;AK1cA;EAaY,qBAAqB;EACrB,sBAAsB;EACtB,iBAAiB;ALic7B;;AKhdA;EAsBgB,iBAAiB;AL8bjC;;AKpdA;EA2BgB,kBAAkB;AL6blC;;AKxdA;EAiCoB,WAAW;EACX,kBAAkB;EAClB,cAAc;EACd,UAAU;EACV,WAAW;EACX,WAAW;EACX,8BAA8B;EJ2H9C,4BI1HgD;EJ4HhD,wBI5HgD;EJ4HhD,oBI5HgD;EJsIhD,+BIrIkD;EJuIlD,2BIvIkD;EJuIlD,uBIvIkD;EAClC,kDAAkD;EAClD,0CAAkC;EAAlC,kCAAkC;EAAlC,kEAAkC;AL+btD;;AK1eA;EJ8KI,kCItHyD;EJwHzD,8BIxHyD;EJwHzD,0BIxHyD;EJ0GzD,4BIzGoD;EJ2GpD,wBI3GoD;EJ2GpD,oBI3GoD;EJ+GpD,sDI9G4E;EJgH5E,8CIhH4E;AL4bhF;;AKtfA;EJycI,WAAW;EACX,yBC1asB;ED2atB,6BAA6B;EArT7B,qCI/E4C;EJiF5C,6BIjF4C;ALwbhD;;AK/fA;EA4EY,sBAAsB;ALublC;;AKngBA;EAmFY,oCAAoC;ALobhD;;AKvgBA;EAyFY,gCAAgC;ALkb5C;;AK3gBA;EA8FY,mCAA2B;EAA3B,2BAA2B;ALibvC;;AK/gBA;EJqdI,cCrbsB;EDsbtB,6BAA6B;EAC7B,yBCvbsB;AFqf1B;;AKrhBA;EAuGY,sBAAsB;ALkblC;;AKzhBA;EA4GY,oCAA8C;ALib1D;;AK7hBA;EAiHY,gCAA0C;ALgbtD;;AKjiBA;EJobQ,kBAAkB;ADiH1B;;AKriBA;EJ2bQ,uBAAuB;AD8G/B;;AKziBA;EJ8bQ,uBAAuB;EACvB,eAAc;AD+GtB;;AK9iBA;;EAqIY,kBAAkB;EAClB,qBAAqB;EACrB,sBAAsB;EACtB,kBAAkB;EAClB,eAAe;EACf,oBAAoB;AL8ahC;;AKxjBA;;EA6IgB,cAAc;EACd,oBAAoB;ALgbpC;;AK3aA;;+EL+a+E;AMtkB/E;;+ENykB+E;AMrkB/E;ELCI,kBAAkB;EAClB,qBAAqB;EACrB,WAAW;EACX,sBAJmD;EKwBtD,iCAAA;EA0CA,+BAAA;EAEA,kCAAA;EA2CA,gCAAA;ANgeD;;AM/kBA;;EAKE,kBAAkB;EAClB,qBAAqB;EACrB,sBAAsB;AN+kBxB;;AMtlBA;EAaG,SAAS;AN6kBZ;;AM1lBA;EAoBG,mBAAmB;AN0kBtB;;AM9lBA;EA6BG,cAAc;ANqkBjB;;AMlmBA;EAmCI,WAAW;ANmkBf;;AMtmBA;;EAyCG,mBAAmB;EACnB,8BAAsB;EAAtB,sBAAsB;ANkkBzB;;AM5mBA;EA8CG,iBAAiB;ANkkBpB;;AMhnBA;EAqDG,kBAAkB;AN+jBrB;;AMpnBA;;EA0DG,WAAW;AN+jBd;;AMznBA;EA8DG,gBAAgB;AN+jBnB;;AM7nBA;;EA0EG,UAAU;ANwjBb;;AMloBA;EAiFG,yBAAyB;ANqjB5B;;AMtoBA;EAqFG,yBAAyB;ANqjB5B;;AM1oBA;EA4FG,UAAU;ANkjBb;;AM9oBA;EAgGG,UAAU;ANkjBb;;AMlpBA;EAuGG,UAAU;AN+iBb;;AMtpBA;EA2GG,UAAU;AN+iBb;;AMziBA;;+EN6iB+E;AOlqB/E;;+EPqqB+E;AOjqB/E;ENCI,kBAAkB;EAClB,qBAAqB;EACrB,WAAW;EACX,sBAJmD;EMwBtD,gCAAA;EAqHA,8BAAA;AP4hBD;;AOzqBA;ENCI,kBAAkB;EAClB,qBAAqB;EACrB,WAAW;EACX,sBAJmD;ADgrBvD;;AOhrBA;ENCI,kBAAkB;EAClB,qBAAqB;EACrB,WAAW;EACX,sBAJmD;ADurBvD;;AO7qBE;EACC,eAAe;APgrBlB;;AO3rBA;EAeG,kBAAkB;EAClB,cAAc;APgrBjB;;AOhsBA;EAoBE,kBAAkB;EAClB,qBAAqB;EACrB,mBAAmB;APgrBrB;;AOtsBA;EAgCG,oBAAa;EAAb,oBAAa;EAAb,aAAa;EACV,yBAAmB;EAAnB,sBAAmB;EAAnB,mBAAmB;EACnB,wBAAuB;EAAvB,qBAAuB;EAAvB,uBAAuB;AP0qB7B;;AO5sBA;EAuCK,UAAU;APyqBf;;AOhtBA;EA2CK,UAAU;APyqBf;;AOptBA;EAgDI,kBAAkB;EAClB,cAAc;EACd,WAAW;EACX,cAAc;EACd,UAAU;EACV,gCAAgC;ENiGhC,0CMhG6C;ENkG7C,kCMlG6C;AP0qBjD;;AOhuBA;EA0DI,kBAAkB;EAClB,MAAM;EACN,UAAU;EACV,WAAW;EACX,UAAU;EACP,gCAAgC;ENuFnC,0CMtF6C;ENwF7C,kCMxF6C;AP4qBjD;;AO5uBA;EAuEG,oBAAa;EAAb,oBAAa;EAAb,aAAa;EACV,yBAAmB;EAAnB,sBAAmB;EAAnB,mBAAmB;EACnB,wBAAuB;EAAvB,qBAAuB;EAAvB,uBAAuB;APyqB7B;;AOlvBA;EA8EK,YAAY;APwqBjB;;AOtvBA;EAqFI,kBAAkB;EAClB,cAAc;EACd,WAAW;EACX,cAAc;EACd,UAAU;EACV,gCAAgC;EN4DhC,0CM3D6C;EN6D7C,kCM7D6C;APuqBjD;;AOlwBA;EA+FI,kBAAkB;EAClB,MAAM;EACN,UAAU;EACV,WAAW;EACX,UAAU;EACP,gCAAgC;ENkDnC,0CMjD6C;ENmD7C,kCMnD6C;APyqBjD;;AO9wBA;EA6GG,gBAAgB;APqqBnB;;AOlxBA;ENkKI,oCMhDyC;ENkDzC,gCMlDyC;ENkDzC,4BMlDyC;APsqB7C;;AOxxBA;ENkKI,qCM5C0C;EN8C1C,iCM9C0C;EN8C1C,6BM9C0C;APwqB9C;;AO9xBA;EA2HI,kBAAkB;EAClB,cAAc;EACd,WAAW;EACX,cAAc;EN8Bd,+CM7ByC;EN+BzC,uCM/ByC;EN+BzC,+BM/ByC;EN+BzC,4DM/ByC;APyqB7C;;AOxyBA;EAmII,kBAAkB;EAClB,MAAM;EACN,SAAS;EACT,WAAW;EN4BX,wCM3B4C;EN6B5C,oCM7B4C;EN6B5C,gCM7B4C;ENqB5C,+CMpByC;ENsBzC,uCMtByC;ENsBzC,+BMtByC;ENsBzC,4DMtByC;AP6qB7C;;AOtqBA;;+EP0qB+E;AQ7zB/E;;+ERg0B+E;AQ5zB/E;EPCI,kBAAkB;EAClB,qBAAqB;EACrB,WAAW;EACX,sBAJmD;EOEtD,kBAAkB;ARi0BnB;;AQn0BA;EAKE,gBAAgB;ARk0BlB;;AQv0BA;EASE,iBAAiB;ARk0BnB;;AQ30BA;EAaE,kBAAkB;EAClB,qBAAqB;EACrB,mBAAmB;ARk0BrB;;AQ9zBA;;+ERk0B+E;ASz1B/E;;+ET41B+E;ASx1B/E;ERCI,kBAAkB;EAClB,qBAAqB;EACrB,WAAW;EACX,sBAJmD;AD81BvD;;AS91BA;;EAWK,WAAW;ATw1BhB;;ASn2BA;EAkBE,cAAc;ATq1BhB;;ASv2BA;ERCI,kBAAkB;EAClB,qBAAqB;EACrB,WAAW;EACX,sBAJmD;EQuBrD,kBAAkB;EAClB,WAAW;ATw1Bb;;ASh3BA;EAgCK,WAH2B;ATu1BhC;;ASp3BA;EAgCK,UAH2B;AT21BhC;;ASx3BA;EAgCK,gBAH2B;AT+1BhC;;AS53BA;EAgCK,UAH2B;ATm2BhC;;ASh4BA;EAgCK,UAH2B;ATu2BhC;;ASp4BA;EAgCK,gBAH2B;AT22BhC;;ASx4BA;ERCI,kBAAkB;EAClB,qBAAqB;EACrB,WAAW;EACX,sBAJmD;EQuCpD,cAAc;EACd,8BAAsB;EAAtB,sBAAsB;ATy2BzB;;ASj5BA;EA2CI,kBAAkB;EAClB,cAAc;EACd,cPP2B;EOQ3B,uCPjB0C;EOkB1C,wBAAwB;EACxB,eAAe;EACf,gBAAgB;EAChB,gBAAgB;AT02BpB;;AS55BA;EAsDI,cAAc;EACd,uCP1B0C;EO2B1C,eAAe;EACf,gBAAgB;EAChB,eAAe;EACZ,sBAAsB;EACzB,yBAAyB;AT02B7B;;ASr2BA;;+ETy2B+E;AU96B/E;;+EVi7B+E;AU76B/E;ETCI,kBAAkB;EAClB,qBAAqB;EACrB,WAAW;EACX,sBAJmD;ESEnD,UAAU;EToJV,yCSnJ6C;ETqJ7C,iCSrJ6C;EAChD,kBAAkB;AVo7BnB;;AUx7BA;EAOE,kBAAkB;EAClB,qBAAqB;EACrB,sBAAsB;AVq7BxB;;AU97BA;EAaK,WAAW;EACX,gCAAgC;EAChC,sBAAsB;EACtB,cRsB0B;EQrB7B,uCRY4C;EQXzC,eAAe;EAClB,wBAAwB;EACrB,gBAAgB;EAChB,gBAAgB;EAChB,gBAAgB;AVq7BrB;;AU38BA;EA0BK,gBAAgB;EACnB,eAAc;EACd,uBAAuB;AVq7BzB;;AUj9BA;EAgCK,gBAAgB;AVq7BrB;;AUl7BA;;+EVs7B+E;AW79B/E;;+EXg+B+E;AW59B/E;EAGE,QAAQ;EACR,mBAAmB;AX49BrB;;AWh+BA;EAQE,qBAAqB;AX49BvB;;AWp+BA;EAWG,aAAa;AX69BhB;;AWx+BA;EAeG,qBAAqB;EACrB,UAAU;EACV,sCAAsC;EACtC,8BAA8B;AX69BjC;;AW19BE;EACC;IACC,UAAU;IACV,0BAA0B;EX69B5B;EW39BC;IACC,UAAU;IACV,wBAAwB;EX69B1B;EW39BC;IACC,UAAU;IACV,0BAA0B;EX69B5B;AACF;;AW19BE;EACC;IACC,UAAU;IACV,0BAA0B;EX69B5B;EW39BC;IACC,UAAU;IACV,wBAAwB;EX69B1B;EW39BC;IACC,UAAU;IACV,0BAA0B;EX69B5B;AACF;;AWz9BA;;+EX69B+E;AYrhC/E;;+EZwhC+E;AYphC/E;EACI,kBAAkB;EAClB,qBAAqB;EACrB,mBAAmB;EACnB,WAAW;EACX,uCVwB0C;EUvB1C,iBAAiB;EACjB,eAAe;EACf,WAAW;EACX,gBAAgB;EAChB,kBAAkB;EAClB,kBAAkB;AZshCtB;;AYjiCA;EAcQ,YAAY;EACZ,WAAW;EACX,uCVasC;EUZtC,eAAe;EACf,iBAAiB;EACjB,gBAAgB;EAChB,WAAW;EACX,yBViBuB;EUhBvB,oBAAoB;EACpB,8BAAsB;EAAtB,sBAAsB;AZuhC9B;;AY9iCA;EA4BQ,kBAAkB;AZshC1B;;AYnhCA;;+EZuhC+E;Aa1jC/E;;+Eb6jC+E;AazjC/E;EACC,WAAW;EACX,cAAc;EACd,mBAAmB;Ab2jCpB;;Aa9jCA;EAME,YAAY;Ab4jCd;;AalkCA;EAYG,WAAW;Ab0jCd;;AatkCA;EAuBI,UAHyB;AbsjC7B;;Aa1kCA;EAuBI,gBAHyB;Ab0jC7B;;Aa9kCA;EAuBI,UAHyB;Ab8jC7B;;AallCA;EAuBI,UAHyB;AbkkC7B;;AatlCA;EAuBI,gBAHyB;AbskC7B;;Aa1lCA;EA6BE,mBAAmB;EACnB,sBAAsB;EACtB,YAAY;EACZ,2BAA2B;EAC3B,sBAAsB;AbikCxB;;AalmCA;EAoCG,mBAAmB;AbkkCtB;;AatmCA;EAwCG,sBAAsB;AbkkCzB;;Aa1mCA;EA4CG,kBAAkB;AbkkCrB;;Aa9mCA;EAgDG,iBAAiB;AbkkCpB;;AalnCA;EAoDG,WAAW;AbkkCd;;AatnCA;EAyDE,eAAe;AbikCjB;;Aa9jCA;;+EbkkC+E;AcloC/E;;+EdqoC+E;AcjoC/E;EbCI,kBAAkB;EAClB,qBAAqB;EACrB,WAAW;EACX,sBAJmD;ADuoCvD;;AcvoCA;EAIE,kBAAkB;EAClB,SAAS;EACT,UAAU;EACV,cAAc;EACd,eAAe;EACf,iBAAiB;EACjB,cZ6B0B;EY5B1B,sBAAsB;EACtB,YAAY;EACZ,8BAAsB;EAAtB,sBAAsB;AduoCxB;;AcppCA;EAgBG,cZsB4B;AFknC/B;;AcxpCA;EAqBE,cAAc;EACd,WAAW;EACX,aAAa;AduoCf;;Ac9pCA;;;EA4BG,WAAW;EACX,cAAc;AdwoCjB;;AcrqCA;EAiCG,eAAe;AdwoClB;;AczqCA;EAsCE,aAAa;AduoCf;;Ac7qCA;EA0CE,kBAAkB;EAClB,MAAM;EACN,OAAO;EACP,WAAW;EACX,YAAY;EACZ,aAAa;EACb,aAAa;AduoCf;;AcnoCA;;+EduoC+E;Ae/rC/E;;+EfksC+E;Ae9rC/E;EACI,kBAAkB;EAClB,cAAc;EACd,mBAAmB;EACnB,YAAY;EACZ,WAAW;EACX,kBAAkB;AfgsCtB;;AetsCA;;EAUE,kBAAkB;EAClB,mBAAmB;EACnB,mBAAmB;AfisCrB;;Ae7sCA;EAgBK,SAAS;AfisCd;;AejtCA;EAmBM,kBAAkB;EAClB,qBAAqB;EACrB,mBAAmB;EACnB,cbgByB;EafzB,eAAe;EACf,oBAAoB;AfksC1B;;Ae1tCA;EA2BO,cAAc;EACd,oBAAoB;AfmsC3B;;Ae/tCA;EAkCK,UAAU;EACV,mBAAmB;EACnB,8BAAsB;EAAtB,sBAAsB;AfisC3B;;Ae9rCA;;+EfksC+E;AgB7uC/E;;+EhBgvC+E;AgB5uC/E;EfCI,kBAAkB;EAClB,qBAAqB;EACrB,WAAW;EACX,sBAJmD;EAsJnD,qCepJqC;EfsJrC,6BetJqC;AhBmvCzC;;AgBrvCA;EAKE,eAAe;AhBovCjB;;AgBzvCA;EfsJI,wCe5I0C;Ef8I1C,gCe9I0C;AhBqvC9C;;AgB/vCA;EAcG,kBAAkB;EAClB,qBAAqB;EACrB,sBAAsB;AhBqvCzB;;AgBrwCA;EAoBG,cAAc;AhBqvCjB;;AgBzwCA;EAwBI,cAAc;AhBqvClB;;AgB7wCA;EAgCE,SAAS;EACT,kBAAkB;AhBivCpB;;AgBlxCA;EAoCG,kBAAkB;EAClB,qBAAqB;EACrB,mBAAmB;AhBkvCtB;;AgBxxCA;EA2CE,cAAc;AhBivChB;;AgB5xCA;EA+CE,aAAa;EACb,gBAAgB;AhBivClB;;AgBjyCA;EAqDI,yBAA+C;AhBgvCnD;;AgBryCA;EA2DG,qCAAqC;EfuGpC,qCetG8C;EfwG9C,iCexG8C;EfwG9C,6BexG8C;AhBgvClD;;AgB5yCA;EAiEE,WAAW;AhB+uCb;;AgBhzCA;;EAqEG,mBAAmB;EACnB,mBAAmB;AhBgvCtB;;AgBtzCA;EA0EG,kBAAkB;EAClB,QAAQ;EACR,oBAAoB;AhBgvCvB;;AgB5zCA;EA+EI,eAAe;AhBivCnB;;AgBh0CA;EAoFG,mBAAmB;EACnB,sBAAsB;AhBgvCzB;;AgBr0CA;;EA8FG,kBAAkB;EAClB,mBAAmB;EACnB,sBAAsB;AhB4uCzB;;AgB50CA;EfsJI,wBehD4B;EfkD5B,gBelD4B;AhB4uChC;;AgBl1CA;EA0GI,eAAe;AhB4uCnB;;AgBt1CA;EA+GG,mBAAmB;AhB2uCtB;;AgB11CA;EAoHE,kBAAkB;AhB0uCpB;;AgB91CA;EAuHG,iBAAiB;AhB2uCpB;;AgBl2CA;EA4HE,WAAU;EACV,aAAY;EACZ,kBAAkB;EAClB,8BAAqB;EAArB,sBAAqB;EACrB,gBAAgB;EfsBd,qCerBsC;EfuBtC,6BevBsC;AhB4uC1C;;AgB72CA;EAsIG,eAAe;EACf,gBAAgB;EAChB,uBAAuB;AhB2uC1B;;AgBn3CA;EA6IE,WAAU;EACV,aAAY;EACZ,kBAAkB;EAClB,8BAAqB;EAArB,sBAAqB;EACrB,gBAAgB;AhB0uClB;;AC9vBI;Ee7nBJ;IAqJI,uCAAuC;EhB2uCzC;AACF;;AgBj4CA;EA0JG,eAAe;EACf,gBAAgB;EAChB,uBAAuB;AhB2uC1B;;AgBvuCA;;+EhB2uC+E;AiB/4C/E;;+EjBk5C+E;AiB94C/E;EACI,kBAAkB;EAClB,qBAAqB;EACrB,sBAAsB;EACzB,kBAAkB;AjBg5CnB;;AiBp5CA;EAQQ,UAAU;EACV,WAAW;EACX,gBAAgB;EAChB,kBAAkB;EAClB,yBfoBkB;EDsHtB,sFgBzI4F;EhB2I5F,8EgB3I4F;AjBk5ChG;;AiB/5CA;EAgBS,kBAAkB;EAClB,qBAAqB;EACrB,mBAAmB;EACnB,WAAW;EACX,YAAY;AjBm5CrB;;AiBv6CA;EAwBY,WAAW;EACX,oBAAoB;AjBm5ChC;;AiB56CA;EA8BQ,kBAAkB;AjBk5C1B;;AiBh7CA;EAyCK,cAAc;EACd,oBAAoB;EhB4GrB,2CgB3GkD;EhB6GlD,mCgB7GkD;AjB64CtD;;AiBx7CA;EA8CM,cAAc;EACd,oBAAoB;AjB84C1B;;AiB77CA;EAmDQ,gBAAgB;AjB84CxB;;AiBj8CA;EhBkKI,gCgB5G4C;EhB8G5C,4BgB9G4C;EhB8G5C,wBgB9G4C;EhBsG5C,+CC3E0B;ED6E1B,uCC7E0B;ED6E1B,+BC7E0B;ED6E1B,4DC7E0B;AFy3C9B;;AiB18CA;EA0DY,kBAAkB;EAClB,SAAS;EACT,OAAO;EACP,WAAW;EACX,YAAY;EhBoGpB,gCgBnG4C;EhBqG5C,4BgBrG4C;EhBqG5C,wBgBrG4C;EhB6F5C,+CC3E0B;ED6E1B,uCC7E0B;ED6E1B,+BC7E0B;ED6E1B,4DC7E0B;AFu4C9B;;AiBx9CA;EhBkKI,oCgB5FoD;EhB8FpD,gCgB9FoD;EhB8FpD,4BgB9FoD;AjBw5CxD;;AiB99CA;EhBkKI,oCgBxFoD;EhB0FpD,gCgB1FoD;EhB0FpD,4BgB1FoD;AjB05CxD;;AiBp5CA;EACC,kBAAkB;EAClB,qBAAqB;EACrB,sBAAsB;EhB+EnB,2BgB9E+B;EhBgF/B,uBgBhF+B;EhBgF/B,mBgBhF+B;EhBkE/B,+CgBjEmD;EhBmEnD,uDgBnEmD;EhBmEnD,+CgBnEmD;EhBmEnD,uCgBnEmD;EhBmEnD,4EgBnEmD;AjB25CvD;;AiBh6CA;EhBkFI,2BgB1EmC;EhB4EnC,uBgB5EmC;EhB4EnC,mBgB5EmC;AjB85CvC;;AiB15CA;EACI,uBAAuB;EACvB,kBAAkB;EAClB,oBAAoB;AjB65CxB;;AiB15CA;EACI,cAAc;AjB65ClB;;AiB15CA;EACI,cAAc;AjB65ClB;;AiB15CA;EACI,cAAc;AjB65ClB;;AiB15CA;EACI,cAAc;AjB65ClB;;AiB35CA;;+EjB+5C+E;AkBphD/E;;+ElBuhD+E;AkBnhD/E;EjBCI,kBAAkB;EAClB,qBAAqB;EACrB,WAAW;EACX,sBAJmD;EiBwGtD,gDAAA;EAgBA,8CAAA;EAEA,+CAAA;EA8CA,6CAAA;EAEA,4CAAA;EA2DA,0CAAA;EAEA,wCAAA;EA4BA,sCAAA;EAEA,6CAAA;EA8BA,2CAAA;AlBgwCD;;ACx4BI;EiB3pBJ;IAMG,aAAa;ElBkiDd;AACF;;AkBziDA;EAUG,oCAA4B;EAA5B,4BAA4B;EjBwJ3B,8CiBvJiD;EjByJjD,0CiBzJiD;EjByJjD,sCiBzJiD;AlBqiDrD;;AkBhjDA;EAcI,UAAU;AlBsiDd;;AkBpjDA;EAkBI,eAAe;EACf,WAAW;EACX,WAAW;EACX,YAAY;EACZ,iBAAiB;EACjB,yBhBSsB;EgBRtB,kBAAkB;EAClB,sDAAkD;EAElD,8CAA0C;AlBsiD9C;;AkBjkDA;EAgCG,SAAS;AlBqiDZ;;AkBrkDA;EAqCG,UAAU;AlBoiDb;;AkBzkDA;EA2CE,aAAa;AlBkiDf;;ACl7BI;EiB3pBJ;IA8CG,cAAc;ElBoiDf;AACF;;AkBnlDA;EAqDG,mBAAmB;AlBkiDtB;;AkBhiDG;EAvDH;IAwDI,mBAAmB;ElBoiDrB;AACF;;AkBliDG;EA3DH;IA4DI,kBAAkB;ElBsiDpB;AACF;;AkBpiDG;EA/DH;IAgEI,cAAc;ElBwiDhB;AACF;;AkBzmDA;EAwEG,yDhBzB6C;EgByB7C,iDhBzB6C;AF8jDhD;;AkB7mDA;EA+EI,iBAAiB;AlBkiDrB;;AkBjnDA;EAmFI,yDhBpC4C;EgBoC5C,iDhBpC4C;AFskDhD;;AkBrnDA;EA2FG,kBAAkB;EAClB,cAAc;AlB8hDjB;;AkB1nDA;EjBCI,kBAAkB;EAClB,qBAAqB;EACrB,WAAW;EACX,sBAJmD;ADioDvD;;AkBjoDA;EjBCI,kBAAkB;EAClB,qBAAqB;EACrB,WAAW;EACX,sBAJmD;ADwoDvD;;AkBxoDA;;EAkHK,YAAY;AlB2hDjB;;ACz3CY;EACI,UAAU;AD43C1B;;ACx3CQ;EAjRJ,kBAAkB;EAClB,cAAc;EACd,WAAW;EACX,YAAY;EACZ,MAAM;EACN,OAAO;EA8QC,WAAW;EACX,wCC5Pc;ED6Pd,UAAU;EAvIlB,4CAwIuD;EAtIvD,oCAsIuD;ADk4C3D;;AkBhqDA;EAoIK,mBAAmB;AlBgiDxB;;AC34CQ;EiBjJH,mBAAmB;AlBgiDxB;;AkBxqDA;EjBsJI,wCiBR4C;EjBU5C,gCiBV4C;AlBgiDhD;;AkB9qDA;EAiJM,aAAa;AlBiiDnB;;AkBlrDA;EAsJK,0DAA8C;EAA9C,kDAA8C;AlBgiDnD;;AkBtrDA;EA6JG,+BAA2B;EAC3B,SAAS;AlB6hDZ;;AkB3rDA;EAmKG,8BAA6B;AlB4hDhC;;AkB/rDA;EAgLG,kBAAkB;AlBmhDrB;;AkBnsDA;EjBQI,kBAAkB;EAClB,cAAc;EACd,WAAW;EACX,YAAY;EACZ,MAAM;EACN,OAAO;EiBwKG,WAAW;EACX,yBAAyB;EACzB,UAAU;EjBjCpB,4CiBkCyD;EjBhCzD,oCiBgCyD;AlB0hD7D;;AkBltDA;EA6Lc,cAAc;EACpB,yBAAyB;EACzB,kBAAkB;EAClB,eAAe;EACf,WAAW;EACX,UAAU;EACV,kBAAkB;EAClB,8BAAsB;EAAtB,sBAAsB;EACtB,qBAAqB;EACrB,sBAAsB;EACtB,QAAQ;EACR,UAAU;EjBlDd,wCiBmD8C;EjBjD9C,gCiBiD8C;EAC1C,iCAAyB;EAAzB,6BAAyB;EAAzB,yBAAyB;EACzB,WAAW;EACX,YAAY;EACZ,iBAAiB;EACjB,cAAc;AlB2hDtB;;AkBzuDA;EAwNM,UAAU;AlBqhDhB;;AkB7uDA;EA2NM,UAAU;EjBzDZ,gCiB0DuC;EjBxDvC,4BiBwDuC;EjBxDvC,wBiBwDuC;AlBwhD3C;;AkBvgDG;EACC,eAAe;AlB0gDnB;;AkBxvDA;EjBkKI,8BiBkFmC;EjBhFnC,0BiBgFmC;EjBhFnC,sBiBgFmC;AlB0gDvC;;AkB9vDA;EAyPI,gBAAgB;AlBygDpB;;AkBlwDA;EjBkKI,2BiB2F+B;EjBzF/B,uBiByF+B;EjBzF/B,mBiByF+B;EjBjG/B,sDiBkGgD;EjBhGhD,8CiBgGgD;EjBhGhD,sCiBgGgD;EjBhGhD,0EiBgGgD;AlB6gDpD;;AkB3wDA;EA0QG,gBAAgB;AlBqgDnB;;AkBngDG;EACC,eAAe;AlBsgDnB;;AkBnxDA;EAmRK,4BAA4B;EAC5B,YAAY;AlBogDjB;;AkBxxDA;EAyRI,2CAA2C;EAC3C,+BAA+B;EAC/B,4BAA4B;EAC5B,YAAY;EACZ,uBAAuB;EjBvIvB,wCiBwI2C;EjBtI3C,gCiBsI2C;AlBqgD/C;;AkB7/CA;;+ElBigD+E;AmB3yD/E;;+EnB8yD+E;AmB1yD/E;ElBCI,kBAAkB;EAClB,qBAAqB;EACrB,WAAW;EACX,sBAJmD;EAsJnD,wCkBpJ4C;ElBsJ5C,gCkBtJ4C;EAyD/C,+CAAA;EAYA,6CAAA;EAEA,4CAAA;EAYA,0CAAA;EAEA,wCAAA;EAyBA,sCAAA;EAEA,6CAAA;EA8BA,2CAAA;AnB2qDD;;AmB3zDA;EASG,0DAA8C;EAA9C,kDAA8C;AnBszDjD;;AmB/zDA;ElBCI,kBAAkB;EAClB,qBAAqB;EACrB,WAAW;EACX,sBAJmD;EkBgBrD,WAAW;AnBuzDb;;AmBv0DA;EAmBG,kBAAkB;EAClB,cAAc;ElBkIb,iCkBjImC;ElBmInC,yBkBnImC;AnB0zDvC;;AmB/0DA;EAyBG,kBAAiB;EACjB,YAAY;EACZ,UAAU;EACV,UAAU;EACV,eAAe;ElByHd,qCkBxHuC;ElB0HvC,6BkB1HuC;AnB4zD3C;;AmB11DA;EAuCI,oDAA4C;EAA5C,4CAA4C;AnBuzDhD;;AmB91DA;ElBCI,kBAAkB;EAClB,qBAAqB;EACrB,WAAW;EACX,sBAJmD;ADq2DvD;;AmBr2DA;EAmDE,gBAAgB;AnBszDlB;;AmBz2DA;EAuDE,gBAAgB;EAChB,kBAAkB;AnBszDpB;;AmB92DA;EAgEY,qCAAqC;ElBkG7C,qCkBjGiD;ElBmGjD,iCkBnGiD;ElBmGjD,6BkBnGiD;AnBozDrD;;ACjmDY;EACI,UAAU;ADomD1B;;AChmDQ;EAjRJ,kBAAkB;EAClB,cAAc;EACd,WAAW;EACX,YAAY;EACZ,MAAM;EACN,OAAO;EA8QC,WAAW;EACX,wCC5Pc;ED6Pd,UAAU;EAvIlB,4CAwIuD;EAtIvD,oCAsIuD;AD0mD3D;;AmBx4DA;EA4FG,gBAAgB;AnBgzDnB;;AmB9yDG;EACC,eAAe;AnBizDnB;;AmBh5DA;ElBkKI,8BkB7DmC;ElB+DnC,0BkB/DmC;ElB+DnC,sBkB/DmC;AnBizDvC;;AmBt5DA;ElBkKI,2BkBxD+B;ElB0D/B,uBkB1D+B;ElB0D/B,mBkB1D+B;ElBkD/B,sDkBjDgD;ElBmDhD,8CkBnDgD;ElBmDhD,sCkBnDgD;ElBmDhD,0EkBnDgD;AnBozDpD;;AmB/5DA;EAuHG,gBAAgB;AnB4yDnB;;AmB1yDG;EACC,eAAe;AnB6yDnB;;AmBv6DA;EAgIK,4BAA4B;EAC5B,YAAY;AnB2yDjB;;AmB56DA;EAsII,2CAA2C;EAC3C,+BAA+B;EAC/B,4BAA4B;EAC5B,YAAY;EACZ,uBAAuB;ElBYvB,wCkBX2C;ElBa3C,gCkBb2C;AnB4yD/C;;AmBnyDA,wCAAA;AAGC;EAIC,qHAAoH;EACpH,6GAAoG;EAApG,qGAAoG;EAApG,0JAAoG;AnBiyDtG;;AmBtyDC;EAWG,UAAU;ElBAV,6BkBCgC;ElBChC,yBkBDgC;ElBChC,qBkBDgC;EAChC,mGAAiG;EACjG,mGAAiF;EAAjF,2FAAiF;EAAjF,mFAAiF;EAAjF,6IAAiF;AnBiyDrF;;AmB/yDC;EAkBI,UAAU;ElBPX,2BkBQgC;ElBNhC,uBkBMgC;ElBNhC,mBkBMgC;AnBmyDpC;;AmBtxDI,sCAAA;AACJ;;+EnB0xD+E;AoBt9D/E;;+EpBy9D+E;AoBr9D/E;EnBCI,kBAAkB;EAClB,qBAAqB;EACrB,WAAW;EACX,sBAJmD;EmBEtD,UAAU;EnBoJP,wCmBnJwC;EnBqJxC,gCmBrJwC;ApB49D5C;;AoB/9DA;EAMQ,kBAAkB;EAClB,cAAc;EACd,aAAa;EACb,YAAY;EACZ,kBAAkB;EACrB,kBAAkB;EAClB,cAAc;ApB69DnB;;AoBz+DA;EAeY,kBAAkB;EAClB,MAAM;EACN,OAAO;ApB89DnB;;AoB/+DA;EAqBY,kBAAkB;EAClB,qBAAqB;EACrB,sBAAsB;EACtB,clBcmB;EkBbtB,eAAe;EACf,oBAAoB;EACpB,gBAAgB;EACb,uClBCkC;AF69D9C;;AoB1/DA;EA+BgB,kBAAkB;EAClB,YAAY;EACf,eAAe;ApB+9D5B;;AoBhgEA;EnBCI,kBAAkB;EAClB,qBAAqB;EACrB,WAAW;EACX,sBAJmD;EmBwC/C,kBAAkB;EAClB,gBAAgB;ApBg+DxB;;AoBzgEA;EA6CM,SAAS;ApBg+Df;;AoB7gEA;EAiDM,eAAe;EACT,uClBrBkC;EkBsBlC,eAAe;EACf,gBAAgB;EAChB,sBAAsB;ApBg+DlC;;AoB59DA;;+EpBg+D+E;AqB7hE/E;;+ErBgiE+E;AqB5hE/E;EpBCI,kBAAkB;EAClB,qBAAqB;EACrB,WAAW;EACX,sBAJmD;ADkiEvD;;AqB7hEI;EADJ;IAEQ,4BAA4B;ErBiiElC;EqBniEF;IAKY,4BAA4B;ErBiiEtC;AACF;;AqBviEA;EpBHI,kBAAkB;EAClB,qBAAqB;EACrB,WAAW;EACX,sBAJmD;EoBe/C,0CAAuC;EACvC,8BAAsB;EAAtB,sBAAsB;ArBoiE9B;;AqBhjEA;EAeY,gBAAgB;EAChB,SAAS;EACT,UAAU;ArBqiEtB;;AqBtjEA;EAoBgB,SAAS;EACT,kBAAkB;EAClB,8BAAsB;EAAtB,sBAAsB;ArBsiEtC;;AqB5jEA;EAyBoB,kBAAkB;EAClB,kBAAkB;EAClB,aAAa;EACb,cnBMW;EmBLX,eAAe;EACf,iBAAiB;EACjB,gBAAgB;ArBuiEpC;;AqBtkEA;EAkCwB,cAAc;EACd,uCnBVsB;EmBWtB,cnBRE;EmBSF,eAAe;EACf,gBAAgB;EAChB,oBAAoB;EACpB,yBAAyB;EACzB,oBAAoB;ArBwiE5C;;AqBjlEA;EA6CwB,cAAc;EACd,eAAe;EACf,uCnBtBsB;EmBuBtB,uBAAuB;EACvB,gBAAgB;EACnB,oBAAoB;EACpB,8BAAsB;EAAtB,sBAAsB;ArBwiE3C;;AqB3lEA;EA0DwB,gBAAgB;EAChB,SAAS;EACT,UAAU;EACV,oBAAa;EAAb,oBAAa;EAAb,aAAa;EACb,4BAAsB;EAAtB,6BAAsB;EAAtB,0BAAsB;EAAtB,sBAAsB;ArBqiE9C;;AqBnmEA;EAiE4B,aAAa;EACb,iBAAiB;EACjB,8CnB1BO;EmB2BP,eAAe;EACf,gBAAgB;EAChB,cnBzCQ;EmB0CR,uBAAuB;ArBsiEnD;;AqB7mEA;EA0EgC,iDnBjCG;AFwkEnC;;AqBjnEA;EAiFoB,kBAAkB;EACjC,uBAAuB;EACR,iDnB1Ce;AF8kEnC;;AqBvnEA;EAsFwB,kBAAkB;EAClB,qBAAqB;EACrB,sBAAsB;EACtB,eAAe;EACf,gBAAgB;EAChB,gBAAgB;EAChB,cnB1DO;EmB2DP,uCnBpEsB;AFymE9C;;AqBloEA;EAiGwB,kBAAkB;EAClB,qBAAqB;EACrB,sBAAsB;EACtB,eAAe;EACf,gBAAgB;EAChB,gBAAgB;EAChB,cnBrEO;EmBsEP,uCnB/EsB;AFonE9C;;AqB7oEA;EA4GwB,kBAAkB;EAClB,cAAc;EACd,eAAe;EACf,gBAAgB;EAChB,gBAAgB;EAChB,+BAAgB;EAChB,uCnBzFsB;EmB0FtB,oBAAoB;EACpB,gBAAgB;ArBqiExC;;AqBzpEA;EAyHoB,UAAU;ArBoiE9B;;AqB7pEA;EA4HwB,eAAe;EACf,WAAW;EACX,uBAAuB;EACvB,cnBlGY;AFuoEpC;;AqBpqEA;EAkI4B,sBAAsB;ArBsiElD;;AqB7hEA;EpBmBI,oCoBlBwC;EpBoBxC,gCoBpBwC;EpBoBxC,4BoBpBwC;EACxC,WAAW;ArBkiEf;;AqBhiEI;EAJJ;IpBmBI,uBoBd+B;IpBgB/B,mBoBhB+B;IpBgB/B,eoBhB+B;ErBsiEjC;AACF;;AqB5iEA;EASQ,yBAAyB;EACzB,cAAc;EACd,eAAe;EACf,gBAAgB;EAChB,uBAAuB;EACvB,cAAc;ArBuiEtB;;AqBriEQ;EAhBR;IAiBY,WAAW;IACX,SAAS;ErByiEnB;AACF;;AqB5jEA;EA0BoB,aAAoB;ArBsiExC;;AqBhkEA;EA6BwB,iBAAiB;ArBuiEzC;;AqBpkEA;EAkCoB,UAAU;ArBsiE9B;;AqBxkEA;EAqCwB,eAAe;EACf,gBAAgB;ArBuiExC;;AqB/hEA;;+ErBmiE+E;AqB/hE5E;EAEK,qHAAoH;EACpH,6GAAoG;EAApG,qGAAoG;EAApG,0JAAoG;ArBgiE5G;;AqBniEG;EpB3CC,wCoBiDgD;EpB/ChD,gCoB+CgD;EAC5C,UAAU;EACV,mGAAiG;EACjG,mGAAiF;EAAjF,2FAAiF;EAAjF,mFAAiF;EAAjF,6IAAiF;ArBmiEzF;;AqB5iEG;EAcS,UAAU;ArBkiEtB;;AqBhjEG;EAkBS,YAAY;ArBkiExB;;AqB7hEG;;+ErBiiE4E;AqB9hE/E;;+ErBiiE+E;AsBhwE/E;;+EtBmwE+E;AsB/vE/E;ErBCI,kBAAkB;EAClB,qBAAqB;EACrB,WAAW;EACX,sBAJmD;ADqwEvD;;AsBrwEA;EAWK,UALwB;AtBmwE7B;;AsBzwEA;EAkBK,WAZwB;AtBuwE7B;;AsB7wEA;EAuBI,UAjByB;AtB2wE7B;;AsBjxEA;EAWK,gBALwB;AtB+wE7B;;AsBrxEA;EAkBK,iBAZwB;AtBmxE7B;;AsBzxEA;EAuBI,gBAjByB;AtBuxE7B;;AsB7xEA;EAWK,UALwB;AtB2xE7B;;AsBjyEA;EAkBK,WAZwB;AtB+xE7B;;AsBryEA;EAuBI,UAjByB;AtBmyE7B;;AsBzyEA;EA+BG,UAAU;ErBmIT,2BqBlI8B;ErBoI9B,uBqBpI8B;ErBoI9B,mBqBpI8B;AtBgxElC;;AsBhzEA;EAsCI,WAAW;AtB8wEf;;AsBpzEA;EA6CI,YAAY;AtB2wEhB;;AsBxzEA;EAkDG,UAAU;AtB0wEb;;AsB5zEA;ErBCI,kBAAkB;EAClB,qBAAqB;EACrB,WAAW;EACX,sBAJmD;EqBwDrD,WAAW;AtB4wEb;;AsBp0EA;EA2DG,WAAW;AtB6wEd;;AsBx0EA;EA+DG,QAAQ;EACR,SAAS;EACT,QAAQ;EACR,WAAW;ErBoFV,wCqBnFyC;ErBqFzC,gCqBrFyC;AtB+wE7C;;AsBl1EA;EAwEE,kBAAkB;EAClB,SAAS;EACT,OAAO;EACP,aAAa;EACb,WAAW;EACX,YAAY;AtB8wEd;;AsB31EA;EAgFG,SAAS;EACT,SAAS;EACT,UAAU;EACV,SAAS;ErBmER,yCqBlE0C;ErBoE1C,iCqBpE0C;AtBixE9C;;AsBr2EA;EAyFE,kBAAkB;EAClB,qBAAqB;EACrB,mBAAmB;EACnB,kBAAkB;AtBgxEpB;;AsB52EA;EAiGI,aAAa;AtB+wEjB;;AsBh3EA;EA0GK,8BAA0B;EAE1B,sBAAkB;AtB0wEvB;;AsBt3EA;EAgHK,8BAA0B;EAE1B,sBAAkB;AtB0wEvB;;AsB53EA;EA0GK,4BAA0B;EAE1B,oBAAkB;AtBsxEvB;;AsBl4EA;EAgHK,8BAA0B;EAE1B,sBAAkB;AtBsxEvB;;AsBx4EA;EA0GK,8BAA0B;EAE1B,sBAAkB;AtBkyEvB;;AsB94EA;EAgHK,8BAA0B;EAE1B,sBAAkB;AtBkyEvB;;AsBp5EA;EAyHE,kBAAkB;EAClB,qBAAqB;EACrB,mBAAmB;EACnB,WAAW;EACX,YAAY;EACZ,eAAe;EACf,iBAAiB;EACjB,gBAAgB;EAChB,WAAW;EACX,yBpBlGwB;EoBmGxB,mBAAmB;EACnB,UAAU;EACV,gEAAgE;EAEhE,wDAAgD;EAAhD,gDAAgD;EAAhD,4EAAgD;ErB2B9C,6BqB1B8B;ErB4B9B,yBqB5B8B;ErB4B9B,qBqB5B8B;AtBiyElC;;AsBz6EA;EA4IE,kBAAkB;EAClB,yBpB7GwB;AF84E1B;;AsB96EA;EAiJE,eAAe;AtBiyEjB;;AsBl7EA;EAqJE,kBAAkB;EAClB,qBAAqB;EACrB,mBAAmB;EACnB,WAAW;EACX,eAAe;EACf,UAAU;EACV,kBAAkB;EAClB,8BAAsB;EAAtB,sBAAsB;ErBNpB,qCqBOsC;ErBLtC,6BqBKsC;AtBmyE1C;;AsBh8EA;EAkKI,8BAA0B;EAE1B,sBAAkB;AtBkyEtB;;AsBt8EA;EAkKI,4BAA0B;EAE1B,oBAAkB;AtBwyEtB;;AsB58EA;EAkKI,8BAA0B;EAE1B,sBAAkB;AtB8yEtB;;AsBl9EA;ErBCI,kBAAkB;EAClB,qBAAqB;EACrB,WAAW;EACX,sBAJmD;EqB2KrD,mBAAmB;AtB+yErB;;AsB19EA;EA+KE,SAAS;AtB+yEX;;AsB99EA;EAmLE,gBAAgB;AtB+yElB;;AsB5yEA;;+EtBgzE+E;AuB1+E/E;;+EvB6+E+E;AuBz+E/E;EtBCI,kBAAkB;EAClB,qBAAqB;EACrB,WAAW;EACX,sBAJmD;AD++EvD;;AuB/+EA;EAIQ,WAAW;EACX,YAAY;AvB++EpB;;AuBp/EA;EAQY,kBAAkB;EAClB,OAAO;EACP,WAAW;EACX,SAAS;EtBuJjB,mCsBtJ+C;EtBwJ/C,+BsBxJ+C;EtBwJ/C,2BsBxJ+C;AvBk/EnD;;AuB9/EA;EAiBQ,kBAAkB;EAClB,kBAAkB;AvBi/E1B;;AuBngFA;EAqBY,kBAAkB;EAClB,qBAAqB;EACrB,sBAAsB;EACtB,YAAY;AvBk/ExB;;AuB1gFA;EA6BQ,kBAAkB;EAClB,QAAQ;EACR,YAAY;EACZ,WAAW;EACd,qBAAqB;EACrB,sBAAsB;EACtB,UAAU;EACV,WAAW;AvBi/EhB;;AuBrhFA;EAuCY,YAAY;AvBk/ExB;;AuBzhFA;EA4CQ,kBAAkB;EAClB,WAAW;EACX,gBAAgB;EAChB,yBAAyB;AvBi/EjC;;AuBhiFA;EAkDY,WAAW;EACX,eAAe;EACf,gBAAgB;EAChB,yBrBrBc;AFugF1B;;AuB9+EA;;+EvBk/E+E;AwB/iF/E;;+ExBkjF+E;AwB9iF/E;EvBCI,kBAAkB;EAClB,qBAAqB;EACrB,WAAW;EACX,sBAJmD;EuBEtD,gBAAgB;EAChB,gBAAgB;AxBmjFjB;;AwBtjFA;EAME,gBAAgB;AxBojFlB;;AwB1jFA;EAUE,kBAAkB;EAClB,SAAS;EACT,WAAW;EACX,yBtB+BmC;AFqhFrC;;AwBjkFA;;EAiBG,kBAAkB;EAClB,QAAQ;EACR,eAAe;EACf,ctBYuB;EsBXvB,eAAe;EvB6Id,mCuB5IsC;EvB8ItC,+BuB9IsC;EvB8ItC,2BuB9IsC;EACvC,WAAW;AxBujFd;;AwB9kFA;EA2BG,UAAU;EACV,yBAAyB;AxBujF5B;;AwBnlFA;EvBkKI,iCuBnIqC;EvBqIrC,6BuBrIqC;EvBqIrC,yBuBrIqC;AxB0jFzC;;AwBzlFA;EAoCG,WAAW;EACX,yBAAyB;AxByjF5B;;AwB9lFA;EvBsJI,wCuB5GyC;EvB8GzC,gCuB9GyC;AxB0jF7C;;AwBpmFA;EA8CE,kBAAkB;EAClB,WAAW;EACX,kBAAkB;EvBkHhB,oCuBjHsC;EvBmHtC,gCuBnHsC;EvBmHtC,4BuBnHsC;AxB4jF1C;;AwB7mFA;EAoDG,YAAY;AxB6jFf;;AwBjnFA;;EAyDG,qBAAqB;EACrB,sBAAsB;EACtB,QAAQ;EACR,WAAW;EACX,yBAAyB;EACzB,kBAAkB;EAClB,SAAS;AxB6jFZ;;AwB5nFA;EAkEM,OAAO;AxB8jFb;;AwBhoFA;EAqEM,uBAAuB;AxB+jF7B;;AwBpoFA;EAyEG,qBAAqB;EACrB,sBAAsB;EACtB,WAAW;EACX,YAAY;EACZ,kBAAkB;EAClB,yBtBlCkC;EsBmClC,iDtB/CuB;EsB+CvB,yCtB/CuB;AF8mF1B;;AwB9oFA;EAmFG,kBAAkB;EAClB,OAAO;EACP,WAAW;AxB+jFd;;AwBppFA;EAwFI,ctBlD2B;EsBmD3B,eAAe;EACf,uCtB7D0C;EsB8D1C,gBAAgB;AxBgkFpB;;AwB3pFA;EAgGG,kBAAkB;EAClB,QAAQ;EACR,UAAU;EACV,gBAAgB;EAChB,kBAAkB;EAClB,8BAAsB;EAAtB,sBAAsB;EACtB,uDAA+C;EAA/C,+CAA+C;EAC/C,sBAAsB;EACtB,kBAAkB;EAClB,WAAW;AxB+jFd;;AwB7jFG;EA3GH;IA4GI,aAAa;ExBikFf;AACF;;AwB9qFA;EAgHI,gBAAgB;AxBkkFpB;;ACjhEI;EuBjqBJ;IAkHK,kBAAkB;ExBqkFrB;AACF;;ACvhEI;EuBjqBJ;IAyHK,kBAAkB;ExBokFrB;AACF;;AwB9rFA;EA8HI,WAAW;EACX,kBAAkB;EAClB,SAAS;EACT,UAAU;EACV,YAAY;EACZ,yBAAyB;EvB+BzB,mCuB9BuC;EvBgCvC,+BuBhCuC;EvBgCvC,2BuBhCuC;EACvC,WAAW;AxBskFf;;AwB3sFA;EA2II,SAAS;AxBokFb;;AwB/sFA;EA+II,YAAY;AxBokFhB;;AwBntFA;EAkJK,SAAS;AxBqkFd;;AwBvtFA;EAyJI,YAAY;AxBkkFhB;;AwB3tFA;EA6JI,SAAS;AxBkkFb;;AwB/tFA;EAgKK,YAAY;AxBmkFjB;;AwBnuFA;EAuKI,yBtBvIsB;EsBwItB,OAAO;AxBgkFX;;AwBxuFA;;EA+KI,yBtB/IsB;AF6sF1B;;AwB7uFA;EAmMG,sBAAsB;AxB8iFzB;;AwBjvFA;EA4MI,yBtB5KsB;AFqtF1B;;AwBrvFA;EA+MK,yBtB/KqB;AFytF1B;;AwBzvFA;EAmNK,WAAW;AxB0iFhB;;AwB7vFA;EAuNK,WAAW;AxB0iFhB;;AwBjwFA;;EA6NI,wCtB7LsB;AFsuF1B;;AwBtwFA;EAiOI,sBAAsB;EACtB,iDtBlMsB;EsBkMtB,yCtBlMsB;AF2uF1B;;AwB3wFA;EAwOK,yBtBxMqB;AF+uF1B;;AwB/wFA;;EA+OK,yBtB/MqB;AFovF1B;;AwBpxFA;EAqPG,ctBrNuB;AFwvF1B;;AwBxxFA;EAyPG,wCtBzNuB;AF4vF1B;;AwB5xFA;;EA6PI,ctB7NsB;AFiwF1B;;AwBjyFA;EAwQI,sBAAsB;AxB6hF1B;;AwBryFA;EA2QK,sBAAsB;AxB8hF3B;;AwBzyFA;;EAiRI,qBAAqB;EACrB,sBAAsB;EACtB,QAAQ;EACR,WAAW;EACX,0CAAuC;AxB6hF3C;;AwBlzFA;EAyRI,yBtBtPgC;EsBuPhC,8CAAsC;EAAtC,sCAAsC;AxB6hF1C;;AwBvzFA;EAgSK,sBAAsB;AxB2hF3B;;AwB3zFA;;EAuSK,sBAAsB;AxByhF3B;;AwBh0FA;EA6SG,WAAW;AxBuhFd;;AwBp0FA;EAiTG,0CAAuC;AxBuhF1C;;AwBx0FA;;EAqTI,WAAW;AxBwhFf;;AwBjhFA;;+ExBqhF+E;AyBr1F/E;;+EzBw1F+E;AyBp1F/E;ExBCI,kBAAkB;EAClB,qBAAqB;EACrB,WAAW;EACX,sBAJmD;EwBEtD,8BAAsB;EAAtB,sBAAsB;AzBy1FvB;;AyB31FA;EAcK,cAA+B;AzBi1FpC;;AyB/1FA;;EAmBK,cAA+B;AzBi1FpC;;AyBp2FA;EAcK,eAA+B;AzB01FpC;;AyBx2FA;;EAmBK,eAA+B;AzB01FpC;;AyB72FA;EAcK,eAA+B;AzBm2FpC;;AyBj3FA;;EAmBK,eAA+B;AzBm2FpC;;AyBt3FA;;EA0BG,kBAAkB;EAClB,qBAAqB;EACrB,sBAAsB;EACtB,UAAU;EACV,WAAW;EACX,SAAS;EACT,8BAAsB;EAAtB,sBAAsB;AzBi2FzB;;AyBj4FA;EAsCI,iBAAiB;AzB+1FrB;;AyBr4FA;EA0CI,gBAAgB;AzB+1FpB;;AyBz4FA;EAiDI,YAAY;EACZ,gBAAgB;AzB41FpB;;AyB94FA;EAsDI,iBAAiB;AzB41FrB;;AyBl5FA;EA4DK,cAAc;EACjB,SAAS;AzB01FX;;AyBv5FA;EAgEM,gBAAgB;AzB21FtB;;AyB35FA;EAoEM,gBAAgB;AzB21FtB;;AyB/5FA;EAyEK,cAAc;EACX,eAAe;EACrB,eAAe;EACf,mBAAmB;AzB01FrB;;AyBv1FA;;+EzB21F+E;A0B96F/E;;+E1Bi7F+E;A0B76F/E;EACI,kBAAkB;EAClB,YAAY;EACZ,YAAY;EACZ,gBAAgB;A1B+6FpB;;A0Bn7FA;EAOQ,kBAAkB;A1Bg7F1B;;A0Bv7FA;EAWQ,gBAAgB;A1Bg7FxB;;A0B37FA;EAeQ,iBAAiB;A1Bg7FzB;;A0B/7FA;EAqBY,sBAAsB;A1B86FlC;;A0Bz6FA;EACI,kBAAkB;EAClB,qBAAqB;EACrB,sBAAsB;EACtB,gCxBEsB;EwBDtB,cAAc;A1B46FlB;;A0B16FA;;+E1B86F+E;A2Bn9F/E;;+E3Bs9F+E;A2Bl9F/E;E1BCI,kBAAkB;EAClB,qBAAqB;EACrB,WAAW;EACX,sBAJmD;E0BuCtD,6CAAA;EAEA,4CAAA;EAYA,0CAAA;EAEA,wCAAA;EAyBA,sCAAA;EAEA,6CAAA;EA8BA,2CAAA;EAEA,0CAAA;EAqCA,wCAAA;A3B00FD;;A2Bj+FA;EAMG,yDzByC6C;EyBzC7C,iDzByC6C;AFs7FhD;;A2Br+FA;EAaG,0CzBgCgC;EyB/BvB,aAAa;A3B49FzB;;A2B1+FA;E1BCI,kBAAkB;EAClB,qBAAqB;EACrB,WAAW;EACX,sBAJmD;ADi/FvD;;A2Bj/FA;EAsBG,kBAAkB;EAClB,cAAc;A3B+9FjB;;ACluFY;EACI,UAAU;ADquF1B;;ACjuFQ;EAjRJ,kBAAkB;EAClB,cAAc;EACd,WAAW;EACX,YAAY;EACZ,MAAM;EACN,OAAO;EA8QC,WAAW;EACX,wCC5Pc;ED6Pd,UAAU;EAvIlB,4CAwIuD;EAtIvD,oCAsIuD;AD2uF3D;;ACrvFY;EACI,UAAU;ADwvF1B;;ACpvFQ;EAjRJ,kBAAkB;EAClB,cAAc;EACd,WAAW;EACX,YAAY;EACZ,MAAM;EACN,OAAO;EA8QC,WAAW;EACX,wCC5Pc;ED6Pd,UAAU;EAvIlB,4CAwIuD;EAtIvD,oCAsIuD;AD8vF3D;;A2B5hGA;EA4DG,gBAAgB;A3Bo+FnB;;A2Bl+FG;EACC,eAAe;A3Bq+FnB;;A2BpiGA;E1BkKI,8B0B7FmC;E1B+FnC,0B0B/FmC;E1B+FnC,sB0B/FmC;A3Bq+FvC;;A2B1iGA;E1BkKI,2B0BxF+B;E1B0F/B,uB0B1F+B;E1B0F/B,mB0B1F+B;E1BkF/B,sD0BjFgD;E1BmFhD,8C0BnFgD;E1BmFhD,sC0BnFgD;E1BmFhD,0E0BnFgD;A3Bw+FpD;;A2BnjGA;EAuFG,gBAAgB;A3Bg+FnB;;A2B99FG;EACC,eAAe;A3Bi+FnB;;A2B3jGA;EAgGK,4BAA4B;EAC5B,YAAY;A3B+9FjB;;A2BhkGA;EAsGI,2CAA2C;EAC3C,+BAA+B;EAC/B,4BAA4B;EAC5B,YAAY;EACZ,uBAAuB;E1B4CvB,wC0B3C2C;E1B6C3C,gC0B7C2C;A3Bg+F/C;;A2B3kGA;EAuHG,gBAAgB;EAChB,cAAc;EACd,4BAA4B;EAC5B,6BAA6B;EAC7B,qBAAqB;E1B2BpB,4C0B1B8C;E1B4B9C,oC0B5B8C;A3B09FlD;;A2BtlGA;EA+HI,+BAA+B;A3B29FnC;;A2Bx9FG;EACC,eAAe;A3B29FnB;;A2B9lGA;EAuII,WAAW;EACX,cAAc;A3B29FlB;;ACp9EI;E0B/oBJ;IA4II,UAAU;IACV,gBAAgB;E3B49FlB;E2BzmGF;IAgJK,gBAAgB;IAChB,eAAe;E3B49FlB;AACF;;A2Br9FA;;+E3By9F+E;A4BtnG/E;;+E5BynG+E;A4BrnG/E;EACC,kBAAkB;EAClB,qBAAqB;EACrB,mBAAmB;A5BunGpB;;A4B1nGA;EAME,kBAAkB;EAClB,qBAAqB;EACrB,mBAAmB;EACnB,kBAAkB;A5BwnGpB;;A4BjoGA;EAaE,kBAAkB;EAClB,qBAAqB;EACrB,mBAAmB;EACnB,gBAAgB;EAChB,UAAU;EACV,SAAS;A5BwnGX;;A4B1oGA;EAsBE,kBAAkB;EAClB,qBAAqB;EACrB,mBAAmB;EACnB,UAAU;EACV,SAAS;A5BwnGX;;A4BlpGA;EA6BG,eAAe;A5BynGlB;;A4BtpGA;EAoCG,kBAAkB;A5BsnGrB;;A4B1pGA;EAuCI,eAAe;A5BunGnB;;A4B9pGA;EA+CG,kBAAkB;A5BmnGrB;;A4BlqGA;EAkDI,eAAe;A5BonGnB;;A4BtqGA;EA2DE,kBAAkB;EAClB,qBAAqB;EACrB,sBAAsB;A5B+mGxB;;A4B5qGA;EAkEI,UAAU;EACV,mBAAmB;EACnB,eAAe;EAEf,0EAAA;A5B6mGJ;;A4BnrGA;EA2EM,8BAFkB;EAIlB,sBAJkB;A5BgnGxB;;A4BzrGA;EA2EM,8BAFkB;EAIlB,sBAJkB;A5BsnGxB;;A4B/rGA;EA2EM,8BAFkB;EAIlB,sBAJkB;A5B4nGxB;;A4BrsGA;EA2EM,8BAFkB;EAIlB,sBAJkB;A5BkoGxB;;A4B3sGA;EA2EM,8BAFkB;EAIlB,sBAJkB;A5BwoGxB;;A4BjtGA;EA2EM,8BAFkB;EAIlB,sBAJkB;A5B8oGxB;;A4BvtGA;EAoFG,cAAc;A5BuoGjB;;A4B3tGA;EAuFI,qBAAqB;EACrB,mBAAmB;EACnB,iBAAiB;A5BwoGrB;;A4BjuGA;EA8FG,kBAAkB;EAClB,kBAAkB;EAClB,YAAY;A5BuoGf;;A4BvuGA;EAmGI,kBAAkB;EAClB,cAAc;EACd,YAAY;EACZ,SAAS;EACT,qBAAqB;A5BwoGzB;;A4B/uGA;EA2GI,kBAAkB;EAClB,cAAc;EACd,kBAAkB;EAClB,kBAAkB;EAClB,gBAAgB;EAChB,UAAU;EACV,8BAAsB;EAAtB,sBAAsB;E3BqCtB,mE2BpCqE;E3BsCrE,2D2BtCqE;A5B0oGzE;;A4B5vGA;E3BsJI,uE2BjC0E;E3BmC1E,+D2BnC0E;A5B6oG9E;;A4BlwGA;EAyHK,cAAc;EACd,oBAAoB;A5B6oGzB;;A4BvwGA;EAkII,SAAS;EACT,OAAO;A5ByoGX;;A4B5wGA;EAsIK,WA/EmB;EAgFnB,YA/EoB;EAgFpB,iBAhFoB;EAiFpB,0C1B5F8B;AFsuGnC;;A4BnxGA;EA4IM,gBAAgB;A5B2oGtB;;A4BvxGA;EAgJM,yBAAyB;EACzB,WAAW;A5B2oGjB;;A4B5xGA;EAqJM,yBAAyB;EACzB,WAAW;A5B2oGjB;;A4BjyGA;EA0JM,yBAAyB;EACzB,WAAW;A5B2oGjB;;A4BtyGA;EA+JM,yBAAyB;EACzB,WAAW;A5B2oGjB;;A4B3yGA;EAoKM,yBAAyB;EACzB,WAAW;A5B2oGjB;;A4BhzGA;EAyKM,yBAAyB;EACzB,WAAW;A5B2oGjB;;A4BrzGA;EA8KM,yBAAyB;EACzB,WAAW;A5B2oGjB;;A4B1zGA;EAmLM,eAAe;EACf,c1B7IsB;E0B8ItB,sBAAsB;A5B2oG5B;;A4Bh0GA;EA0LO,aAAQ;A5B0oGf;;A4Bp0GA;EA0LO,aAAQ;A5B8oGf;;A4Bx0GA;EA0LO,aAAQ;A5BkpGf;;A4B50GA;EA0LO,cAAQ;A5BspGf;;A4Bh1GA;EA0LO,cAAQ;A5B0pGf;;A4Bp1GA;EA0LO,cAAQ;A5B8pGf;;A4Bx1GA;EA0LO,cAAQ;A5BkqGf;;A4B51GA;EAoMI,MAAM;EACN,QAAQ;A5B4pGZ;;A4Bj2GA;EAwMK,qBAAqC;A5B6pG1C;;A4Br2GA;EA4MO,SAAM;A5B6pGb;;A4Bz2GA;EA4MO,UAAM;A5BiqGb;;A4B72GA;EA4MO,UAAM;A5BqqGb;;A4Bj3GA;EA4MO,UAAM;A5ByqGb;;A4Br3GA;EA4MO,WAAM;A5B6qGb;;A4Bz3GA;EA4MO,WAAM;A5BirGb;;A4B73GA;EA4MO,WAAM;A5BqrGb;;A4Bj4GA;EAsNI,MAAM;EACN,OAAO;A5B+qGX;;A4Bt4GA;EA0NK,qBAAqC;A5BgrG1C;;A4B14GA;EA8NO,UAAO;A5BgrGd;;A4B94GA;EA8NO,WAAO;A5BorGd;;A4Bl5GA;EA8NO,WAAO;A5BwrGd;;A4Bt5GA;EA8NO,WAAO;A5B4rGd;;A4B15GA;EA8NO,YAAO;A5BgsGd;;A4B95GA;EA8NO,YAAO;A5BosGd;;A4Bl6GA;EA8NO,YAAO;A5BwsGd;;A4B/rGA;;+E5BmsG+E;A6B96G/E;;+E7Bi7G+E;A6B76G/E;E5BCI,kBAAkB;EAClB,qBAAqB;EACrB,WAAW;EACX,sBAJmD;ADm7GvD;;A6Bn7GA;E5BCI,kBAAkB;EAClB,qBAAqB;EACrB,WAAW;EACX,sBAJmD;E4BKrD,SAAS;EACT,UAAU;EACV,gBAAgB;EAChB,iD3BqCiC;AFi5GnC;;A6B97GA;EAWG,WAAW;EACX,SAAS;EACT,UAAU;EACV,6BAA6B;A7Bu7GhC;;A6Br8GA;EAiBI,kBAAkB;EAClB,qBAAqB;EACrB,sBAAsB;EACtB,8BAAsB;EAAtB,sBAAsB;E5BkItB,iG4BjIkG;E5BmIlG,yF4BnIkG;EAClG,eAAe;EACf,uC3BM0C;E2BL1C,YAAY;A7B07GhB;;A6Bl9GA;EA6BK,SAAS;EACT,WAAW;EACX,kBAAkB;EAClB,YAAY;EACZ,OAAO;EACP,qBAAqB;E5BoHtB,oC4BnHyC;E5BqHzC,4B4BrHyC;A7B27G7C;;A6B99GA;EAwCK,UAAU;A7B07Gf;;A6Bl+GA;EA8CQ,8BAAsB;EAAtB,sBAAsB;E5B7C1B,kBAAkB;EAClB,qBAAqB;EACrB,WAAW;EACX,sBAJmD;AD0+GvD;;A6B1+GA;EAkDG,SAAS;A7B47GZ;;A6B9+GA;EA6DK,kBAAkB;EAClB,iBAAiB;EACjB,gBAAgB;EAChB,mBAAmB;EACnB,c3B3B0B;E2B4B1B,mBAAmB;EACnB,oCAAoC;A7Bq7GzC;;A6Bx/GA;EAwEK,oCAAoC;EACpC,UAAU;A7Bo7Gf;;A6B7/GA;EA+EG,gBAAgB;A7Bk7GnB;;A6BjgHA;EAwFI,kBAAkB;A7B66GtB;;A6BrgHA;EA2FK,kBAAkB;EAClB,iBAAiB;EACjB,gBAAgB;EAChB,mBAAmB;EACnB,WAAW;EACX,yB3B1D0B;E2B2D1B,mBAAmB;A7B86GxB;;A6B/gHA;;EAsGK,WAAW;EACX,yB3BvEqB;AFq/G1B;;A6BrhHA;EA2GK,SAAS;A7B86Gd;;A6BzhHA;EAiHG,gBAAgB;A7B46GnB;;A6B7hHA;EAwHG,iD3B3EgC;AFo/GnC;;A6BjiHA;EA2HI,kBAAkB;A7B06GtB;;A6BriHA;EA8HK,eAAe;EACf,eAAe;EACf,iBAAiB;EACjB,gBAAgB;EAChB,c3B5F0B;AFugH/B;;A6B7iHA;;EAuIK,c3BvGqB;AFkhH1B;;A6BljHA;EA2IK,SAAS;A7B26Gd;;A6BtjHA;EAiJG,eAAe;A7By6GlB;;A6B1jHA;EAsJE,cAAc;A7Bw6GhB;;A6B9jHA;EAyJG,mBAAmB;EACnB,mBAAmB;EACnB,YAAY;EACZ,YAAY;EACZ,gD3BhHgC;E2BiHhC,gBAAgB;EAChB,8BAAsB;EAAtB,sBAAsB;A7By6GzB;;A6BxkHA;EAkKI,cAAc;EACd,WAAW;A7B06Gf;;A6B7kHA;EAsKK,eAAe;EACf,iBAAiB;EACjB,gBAAgB;EAChB,cAAc;EACd,c3BpI0B;E2BqI1B,oBAAoB;A7B26GzB;;A6BtlHA;;EAiLK,c3BjJqB;E2BkJrB,mCAAmC;A7B06GxC;;A6B5lHA;EAsLK,SAAS;A7B06Gd;;A6BhmHA;EA4LG,mBAAmB;EACnB,mBAAmB;EACnB,yBAAyB;EACzB,YAAY;EACZ,mBAAmB;EACnB,8BAAsB;EAAtB,sBAAsB;A7Bw6GzB;;A6Bn6GA;EACC,UAAU;EACV,oBAAoB;EACpB,kBAAkB;EAClB,cAAc;EACd,gBAAgB;EAChB,SAAS;EACT,gBAAgB;A7Bs6GjB;;A6B76GA;EAUK,kBAAkB;EAClB,oBAAoB;EACpB,oBAAoB;EACpB,cAAc;EACd,gBAAgB;EACb,gBAAgB;A7Bu6GxB;;A6Bt7GA;EAqBS,iBAAiB;EACjB,oBAAoB;EACpB,cAAc;EACd,mBAAmB;EACnB,SAAS;EACT,gBAAgB;A7Bq6GzB;;A6B/7GA;EA+BK,cAAc;EACd,gBAAgB;EAChB,SAAS;EACN,gBAAgB;A7Bo6GxB;;A6Bj6GA;;+E7Bq6G+E;A8BppH/E;;+E9BupH+E;A8BnpH/E;E7BCI,kBAAkB;EAClB,qBAAqB;EACrB,WAAW;EACX,sBAJmD;ADypHvD;;A8BppHE;EACC,eAAe;A9BupHlB;;A8B7pHA;EAYI,UAAU;A9BqpHd;;A8BjqHA;E7B4JI,8F6B5IuF;E7B8IvF,8F6B9IuF;E7B8IvF,sF6B9IuF;E7B8IvF,8E6B9IuF;E7B8IvF,4I6B9IuF;E7BkJvF,uC6BjJyC;E7BmJzC,+B6BnJyC;A9BypH7C;;A8B1qHA;EAsBG,eAAe;A9BwpHlB;;A8B9qHA;EA2BE,kBAAkB;EAClB,0CAAsC;E7B0HpC,qC6BzHsC;E7B2HtC,6B6B3HsC;A9BypH1C;;A8BtrHA;EAgCG,0CAAsC;A9B0pHzC;;A8B1rHA;E7BCI,kBAAkB;EAClB,qBAAqB;EACrB,WAAW;EACX,sBAJmD;E6BsCrD,WAAW;EACX,gBAAgB;A9B4pHlB;;A8BnsHA;EA0CY,cAAc;EACvB,YAAY;EACZ,aAAa;EACb,oBAAiB;EAAjB,iBAAiB;EACjB,kBAAkB;EAClB,sBAAsB;A9B6pHzB;;A8B5sHA;E7BCI,kBAAkB;EAClB,qBAAqB;EACrB,WAAW;EACX,sBAJmD;E6BqDrD,kBAAkB;EAClB,kBAAkB;A9B+pHpB;;A8BrtHA;EA0DQ,SAAS;A9B+pHjB;;A8BztHA;EA8DQ,gBAAgB;EACtB,eAAe;EACf,YAAY;EACZ,yBAAyB;EACzB,sBAAsB;EACtB,gBAAgB;EAChB,iBAAiB;A9B+pHnB;;A8BnuHA;EAwEE,gBAAgB;A9B+pHlB;;A8BvuHA;E7BQI,kBAAkB;EAClB,cAAc;EACd,WAAW;EACX,YAAY;EACZ,MAAM;EACN,OAAO;E6BgET,2CAA2B;EAC3B,UAAU;EACV,UAAU;E7BmFR,gC6BlFkC;E7BoFlC,wB6BpFkC;E7BsElC,gC6BrEiC;E7BuEjC,wB6BvEiC;A9BwqHrC;;A8BzvHA;E7BqHI,kBAAkB;EAClB,cAAc;EACd,mBAAmB;EACnB,YAAY;EACZ,WAAW;ADwoHf;;A8BjwHA;EAyFE,kBAAkB;EAClB,mBAAmB;EACnB,YAAY;EACZ,WAAW;EACX,uBAAuB;EACvB,sBAAsB;E7B8DpB,+C6B7DuC;E7B+DvC,uC6B/DuC;E7B+DvC,+B6B/DuC;E7B+DvC,4D6B/DuC;E7BmEvC,0C6BlE0C;E7BoE1C,kC6BpE0C;A9BgrH9C;;A8BhxHA;E7BCI,kBAAkB;EAClB,qBAAqB;EACrB,WAAW;EACX,sBAJmD;ADuxHvD;;A8BvxHA;EAuGG,eAAe;EACf,SAAS;A9BorHZ;;A8B5xHA;EA2GI,eAAe;EACf,kBAAkB;EAClB,UAAU;EACV,YAAY;A9BqrHhB;;A8BnyHA;EAkHI,eAAe;EACf,kBAAkB;EAClB,WAAW;EACX,YAAY;A9BqrHhB;;A8B1yHA;EAyHI,eAAe;EACf,kBAAkB;EAClB,YAAY;EACZ,aAAa;A9BqrHjB;;A8BjzHA;EAgII,eAAe;EACf,kBAAkB;EAClB,WAAW;EACX,aAAa;A9BqrHjB;;A8BxzHA;EAuII,eAAe;EACf,kBAAkB;EAClB,UAAU;EACV,aAAa;A9BqrHjB;;A8B/zHA;EA8II,kBAAkB;E7BQlB,wB6BP4B;E7BS5B,gB6BT4B;EAC5B,WAAW;A9BurHf;;A8BlrHA;;+E9BsrH+E;A+B/0H/E;;+E/Bk1H+E;A+B90H/E;EACC,kBAAkB;EAClB,mBAAmB;EACnB,c7BmC8B;E6BlC9B,eAAe;EACf,kBAAkB;EAClB,gBAAgB;EAChB,gBAAgB;A/Bg1HjB;;A+Bv1HA;EAUE,kBAAkB;EAClB,qBAAqB;EACrB,mBAAmB;EACnB,eAAe;EACf,8BAAsB;EAAtB,sBAAsB;A/Bi1HxB;;A+B/1HA;EAiBG,kBAAkB;EAClB,MAAM;EACN,OAAO;A/Bk1HV;;A+B90HA;;+E/Bk1H+E;AgC72H/E;;+EhCg3H+E;AgC52H/E;EACC,kBAAkB;EAClB,qBAAqB;EACrB,sBAAsB;AhC82HvB;;AgCj3HA;;E/BQI,kBAAkB;EAClB,cAAc;EACd,WAAW;EACX,YAAY;EACZ,MAAM;EACN,OAAO;E+BHR,UAAU;AhCk3Hb;;AgC53HA;;EAaI,kBAAkB;EAClB,QAAQ;EACR,OAAO;EACP,cAAc;EACd,kBAAkB;AhCo3HtB;;AgCr4HA;;EAqBK,kBAAkB;EAClB,QAAQ;EACR,QAAQ;E/B2IT,uC+B1I4C;E/B4I5C,mC+B5I4C;E/B4I5C,+B+B5I4C;AhCu3HhD;;AgC/4HA;E/BCI,kBAAkB;EAClB,qBAAqB;EACrB,WAAW;EACX,sBAJmD;ADs5HvD;;AgCt5HA;EAoCG,cAAc;AhCs3HjB;;AgC15HA;;EA0CE,kBAAkB;EAClB,qBAAqB;EACrB,mBAAmB;EACnB,UAAU;AhCq3HZ;;AgCl6HA;EAiDE,WAAW;EACX,eAAe;EACf,cAAc;AhCq3HhB;;AgCx6HA;EAsDG,cAAc;EACd,oBAAoB;AhCs3HvB;;AgC76HA;EA0DI,cAAc;EACd,oBAAoB;AhCu3HxB;;AgCl7HA;EA+DI,qBAAqB;EACrB,YAAY;EACT,aAAa;EACb,QAAQ;EACR,kBAAkB;E/B+FrB,mC+B9F0C;E/BgG1C,+B+BhG0C;E/BgG1C,2B+BhG0C;EAC1C,kBAAkB;E/BiFlB,qC+BhFwC;E/BkFxC,6B+BlFwC;AhC23H5C;;AgCj8HA;EAyEK,6CAAqC;EAArC,qCAAqC;AhC43H1C;;AgCr8HA;EA8EI,kBAAkB;AhC23HtB;;AgCz8HA;EAiFO,cAAc;EACd,kBAAkB;EAClB,QAAQ;EACR,OAAO;EACP,mBAAA;EACA,YAAY;EACZ,kBAAkB;EAClB,aAAa;EACb,oCAA4B;EAA5B,4BAA4B;AhC43HnC;;AgCr9HA;EAwGM,UAAU;AhCi3HhB;;AgCz9HA;EA4GM,UAAU;AhCi3HhB;;AgC79HA;EAmHG,cAAc;EACd,cAAc;E/BkCb,4C+BjC8C;E/BmC9C,oC+BnC8C;AhCg3HlD;;AgCr+HA;EAwHI,kBAAkB;EAClB,UAAU;AhCi3Hd;;AgC1+HA;EA6HI,kBAAkB;EAClB,MAAM;EACN,SAAS;EACT,UAAU;E/BkCV,iD+BjCqD;E/BmCrD,yC+BnCqD;AhCm3HzD;;AgCj3HI;E/B+BA,gC+B9BqC;E/BgCrC,wB+BhCqC;AhCs3HzC;;AgC1/HA;EA2IE,cAAa;EACb,kBAAkB;EAClB,WAAW;AhCm3Hb;;AgChgIA;EAgJG,UAAS;EACT,mBAAkB;EAClB,sBAAqB;EACrB,WAAW;AhCo3Hd;;AgCvgIA;EAuJG,UAAS;EACT,eAAe;EACf,mBAAkB;EAClB,sBAAqB;AhCo3HxB;;AgC9gIA;EA+JE,kBAAkB;EAClB,YAAY;EACZ,SAAS;E/BCP,mC+BAqC;E/BErC,+B+BFqC;E/BErC,2B+BFqC;EACvC,UAAU;EACV,WAAW;AhCq3Hb;;AgCl3HA;;+EhCs3H+E;AiCjiI/E;EACC,gBAAgB;EAChB,kBAAkB;AjCmiInB;;AiCriIA;EAKE,mBAAmB;EACnB,SAAS;EACT,iBAAiB;EACjB,kBAAkB;EAClB,UAAU;EACV,MAAM;EACN,YAAY;EACZ,UAAU;AjCoiIZ;;AiChjIA;EAgBE,iBAAiB;EACjB,kBAAkB;EAClB,cAAc;EACd,kBAAkB;EAClB,oBAAoB;EACpB,gBAAgB;AjCoiIlB;;AiCzjIA;EAwBG,gBAAgB;AjCqiInB;;AiC7jIA;EA2BI,oBAAa;EAAb,oBAAa;EAAb,aAAa;EACb,yBAAmB;EAAnB,sBAAmB;EAAnB,mBAAmB;AjCsiIvB;;AiClkIA;EA8BK,iBAAiB;AjCwiItB;;AiCtkIA;EAoCG,iBAAiB;AjCsiIpB;;AiC1kIA;EAuCI,oBAAoB;EAEpB,oBAAoB;EACpB,aAAa;EACb,yBAAmB;EAAnB,sBAAmB;EAAnB,mBAAmB;EACnB,8BAA8B;EAC9B,8BAA8B;EAE9B,+BAA+B;EAC/B,2BAA2B;EAE3B,mBAAmB;EACnB,eAAe;AjCuiInB;;AiC1lIA;EAsDK,gBAAgB;AjCwiIrB;;AiC9lIA;EA4DG,qBAAqB;EACrB,kBAAkB;EAClB,WAAW;EACX,sBAAsB;AjCsiIzB;;AiCrmIA;;EAmEI,WAAW;EACX,SAAS;EACT,UAAU;EACV,8BAAsB;EAAtB,sBAAsB;AjCuiI1B;;AiC7mIA;EA0EI,eAAe;AjCuiInB;;AiCjnIA;EA6EK,gBAAgB;AjCwiIrB;;AiCrnIA;EAgFK,iBAAiB;AjCyiItB;;AiCznIA;EAoFK,kBAAkB;EAClB,aAAa;EACb,qBAAqB;AjCyiI1B;;AiC/nIA;EA0FK,kBAAiB;EACjB,QAAQ;EACR,MAAM;EACN,UAAU;EACV,eAAe;AjCyiIpB;;AiCvoIA;EAmGI,eAAe;AjCwiInB;;AiC3oIA;EAsGK,gBAAgB;AjCyiIrB;;AiC/oIA;EAyGK,iBAAiB;AjC0iItB;;AiCnpIA;EA6GK,eAAe;AjC0iIpB;;AiCvpIA;EAiHK,gBAAgB;EAChB,kBAAkB;AjC0iIvB;;AiC5pIA;EAsHK,gBAAgB;AjC0iIrB;;AiChqIA;EA0HK,uBAAuB;EACvB,yBAAyB;EACzB,kBAAkB;EAClB,WAAW;EACX,YAAY;EACZ,SAAS;EACT,mBAAmB;EACnB,kBAAkB;EAClB,QAAQ;EACR,WAAW;AjC0iIhB;;AiC7qIA;EAyIG,sBAAsB;EACtB,SAAS;EACT,SAAS;EACT,iBAAiB;EACjB,kBAAkB;EAClB,UAAU;AjCwiIb;;AiCtrIA;EAiJI,MAAO;AjCyiIX;;AiC1rIA;EAqJI,qBAAqB;AjCyiIzB;;AiC9rIA;EA0JG,aAAa;AjCwiIhB;;AiClsIA;EA+JG,aAAa;AjCuiIhB;;AiCtsIA;EhCsKI,oCgCDsC;EhCGtC,gCgCHsC;EhCGtC,4BgCHsC;EACxC,UAAU;EACV,wHAAsH;EACtH,gHAAsG;EAAtG,wGAAsG;EAAtG,gKAAsG;AjCuiIxG;;AiC/sIA;EA0KG,UAAU;EACV,SAAS;EhCjBR,sFgCkBwF;EhChBxF,8EgCgBwF;AjC2iI5F;;AiCvtIA;EhCsKI,6BgCS+B;EhCP/B,yBgCO+B;EhCP/B,qBgCO+B;EAChC,oFAAkF;EAClF,4EAAkE;EAAlE,oEAAkE;EAAlE,sIAAkE;AjC8iIrE;;AiC/tIA;EAsLK,UAAU;EhChBX,6BgCiBiC;EhCfjC,yBgCeiC;EhCfjC,qBgCeiC;EAChC,mGAAiG;EACjG,mGAAiF;EAAjF,2FAAiF;EAAjF,mFAAiF;EAAjF,6IAAiF;AjC+iItF;;AiCxuIA;EA6LM,UAAU;EhCnCZ,uEgCoC2E;EhClC3E,+DgCkC2E;AjCijI/E;;AiC/uIA;EhCsKI,gCgC8BmC;EhC5BnC,4BgC4BmC;EhC5BnC,wBgC4BmC;EACpC,UAAU;AjCijIb;;AiCtvIA;EAuMI,UAAU;EACV,YAAY;AjCmjIhB;;AiC3vIA;EA4MK,UAAU;EhCtCX,2BgCuCgC;EhCrChC,uBgCqCgC;EhCrChC,mBgCqCgC;AjCqjIpC;;AiClwIA;EAiNM,UAAU;AjCqjIhB;;AiCtwIA;EhCsKI,2BgCiD+B;EhC/C/B,uBgC+C+B;EhC/C/B,mBgC+C+B;AjCqjInC;;AiC5wIA;EA6NG,WAAW;AjCmjId", "file": "../../../../boostup-core/assets/css/scss/shortcodes-map.css", "sourcesContent": ["/* ==========================================================================\n   Global partials\n   ========================================================================== */\n@import '../../../../../themes/boostup/assets/css/scss/variables';\n@import '../../../../../themes/boostup/assets/css/scss/mixins';\n\n/* ==========================================================================\n   Shortcodes styles\n   ========================================================================== */\n@import \"D:/Projects/boostup/wp-content/plugins/boostup-core/shortcodes/accordions/assets/css/scss/default/_accordions.scss\";\n@import \"D:/Projects/boostup/wp-content/plugins/boostup-core/shortcodes/banner/assets/css/scss/default/_banner.scss\";\n@import \"D:/Projects/boostup/wp-content/plugins/boostup-core/shortcodes/button/assets/css/scss/default/_button.scss\";\n@import \"D:/Projects/boostup/wp-content/plugins/boostup-core/shortcodes/call-to-action/assets/css/scss/default/_call-to-action.scss\";\n@import \"D:/Projects/boostup/wp-content/plugins/boostup-core/shortcodes/clients-carousel/assets/css/scss/default/_clients-carousel.scss\";\n@import \"D:/Projects/boostup/wp-content/plugins/boostup-core/shortcodes/clients-grid/assets/css/scss/default/_clients-grid.scss\";\n@import \"D:/Projects/boostup/wp-content/plugins/boostup-core/shortcodes/countdown/assets/css/scss/default/_countdown.scss\";\n@import \"D:/Projects/boostup/wp-content/plugins/boostup-core/shortcodes/counter/assets/css/scss/default/_counter.scss\";\n@import \"D:/Projects/boostup/wp-content/plugins/boostup-core/shortcodes/custom-font/assets/css/scss/default/_custom-font.scss\";\n@import \"D:/Projects/boostup/wp-content/plugins/boostup-core/shortcodes/dropcaps/assets/css/scss/default/_dropcaps.scss\";\n@import \"D:/Projects/boostup/wp-content/plugins/boostup-core/shortcodes/elements-holder/assets/css/scss/default/_elements-holder.scss\";\n@import \"D:/Projects/boostup/wp-content/plugins/boostup-core/shortcodes/google-map/assets/css/scss/default/_google-map.scss\";\n@import \"D:/Projects/boostup/wp-content/plugins/boostup-core/shortcodes/icon-list-item/assets/css/scss/default/_icon-list-item.scss\";\n@import \"D:/Projects/boostup/wp-content/plugins/boostup-core/shortcodes/icon-with-text/assets/css/scss/default/_icon-with-text.scss\";\n@import \"D:/Projects/boostup/wp-content/plugins/boostup-core/shortcodes/icon/assets/css/scss/default/_icon.scss\";\n@import \"D:/Projects/boostup/wp-content/plugins/boostup-core/shortcodes/image-gallery/assets/css/scss/default/_image-gallery.scss\";\n@import \"D:/Projects/boostup/wp-content/plugins/boostup-core/shortcodes/image-with-text/assets/css/scss/default/_image-with-text.scss\";\n@import \"D:/Projects/boostup/wp-content/plugins/boostup-core/shortcodes/pie-chart/assets/css/scss/default/_pie-chart.scss\";\n@import \"D:/Projects/boostup/wp-content/plugins/boostup-core/shortcodes/pricing-table/assets/css/scss/default/_pricing-tables.scss\";\n@import \"D:/Projects/boostup/wp-content/plugins/boostup-core/shortcodes/process/assets/css/scss/default/_process.scss\";\n@import \"D:/Projects/boostup/wp-content/plugins/boostup-core/shortcodes/progress-bar/assets/css/scss/default/_progress-bar.scss\";\n@import \"D:/Projects/boostup/wp-content/plugins/boostup-core/shortcodes/roadmap/assets/css/scss/default/_roadmap.scss\";\n@import \"D:/Projects/boostup/wp-content/plugins/boostup-core/shortcodes/section-title/assets/css/scss/default/_section-title.scss\";\n@import \"D:/Projects/boostup/wp-content/plugins/boostup-core/shortcodes/separator/assets/css/scss/default/_separator.scss\";\n@import \"D:/Projects/boostup/wp-content/plugins/boostup-core/shortcodes/single-image/assets/css/scss/default/_single-image.scss\";\n@import \"D:/Projects/boostup/wp-content/plugins/boostup-core/shortcodes/social-share/assets/css/scss/default/_social-share.scss\";\n@import \"D:/Projects/boostup/wp-content/plugins/boostup-core/shortcodes/tabs/assets/css/scss/default/_tabs.scss\";\n@import \"D:/Projects/boostup/wp-content/plugins/boostup-core/shortcodes/team/assets/css/scss/default/_team.scss\";\n@import \"D:/Projects/boostup/wp-content/plugins/boostup-core/shortcodes/text-marquee/assets/css/scss/default/text-marquee.scss\";\n@import \"D:/Projects/boostup/wp-content/plugins/boostup-core/shortcodes/video-button/assets/css/scss/default/_video-button.scss\";\n@import \"D:/Projects/boostup/wp-content/plugins/boostup-core/shortcodes/workflow/assets/css/scss/default/_workflow.scss\";", "/* ==========================================================================\n   Global partials\n   ========================================================================== */\n@keyframes animate-btn-line {\n  0% {\n    -webkit-transform-origin: 0 50%;\n    -moz-transform-origin: 0 50%;\n    transform-origin: 0 50%;\n    -webkit-transform: scaleX(0);\n    -moz-transform: scaleX(0);\n    transform: scaleX(0);\n  }\n  100% {\n    -webkit-transform-origin: 0 50%;\n    -moz-transform-origin: 0 50%;\n    transform-origin: 0 50%;\n    -webkit-transform: scaleX(1);\n    -moz-transform: scaleX(1);\n    transform: scaleX(1);\n  }\n}\n\n@-webkit-keyframes mkdfPulsebig {\n  0% {\n    -moz-box-shadow: 0 0 0 0 currentColor;\n    box-shadow: 0 0 0 0 currentColor;\n  }\n  70% {\n    -moz-box-shadow: 0 0 0 20px rgba(234, 61, 86, 0.3);\n    box-shadow: 0 0 0 20px rgba(234, 61, 86, 0);\n  }\n  100% {\n    -moz-box-shadow: 0 0 0 0 rgba(234, 61, 86, 0);\n    box-shadow: 0 0 0 0 rgba(234, 61, 86, 0);\n  }\n}\n\n@keyframes mkdfPulsebig {\n  0% {\n    -moz-box-shadow: 0 0 0 0 currentColor;\n    box-shadow: 0 0 0 0 currentColor;\n  }\n  70% {\n    -moz-box-shadow: 0 0 0 20px rgba(234, 61, 86, 0.3);\n    box-shadow: 0 0 0 20px rgba(234, 61, 86, 0);\n  }\n  100% {\n    -moz-box-shadow: 0 0 0 0 rgba(234, 61, 86, 0);\n    box-shadow: 0 0 0 0 rgba(234, 61, 86, 0);\n  }\n}\n\n@-webkit-keyframes mkdfPulsesmall {\n  0% {\n    -webkit-box-shadow: 0 0 0 0 currentColor;\n  }\n  70% {\n    -webkit-box-shadow: 0 0 0 10px rgba(234, 61, 86, 0.15);\n  }\n  100% {\n    -webkit-box-shadow: 0 0 0 0 rgba(234, 61, 86, 0);\n  }\n}\n\n@keyframes mkdfPulsesmall {\n  0% {\n    -moz-box-shadow: 0 0 0 0 currentColor;\n    box-shadow: 0 0 0 0 currentColor;\n  }\n  70% {\n    -moz-box-shadow: 0 0 0 10px rgba(234, 61, 86, 0.15);\n    box-shadow: 0 0 0 10px rgba(234, 61, 86, 0);\n  }\n  100% {\n    -moz-box-shadow: 0 0 0 0 rgba(234, 61, 86, 0);\n    box-shadow: 0 0 0 0 rgba(234, 61, 86, 0);\n  }\n}\n\n@-webkit-keyframes mkdfPulsesmallfirst {\n  0% {\n    -webkit-box-shadow: 0 0 0 0 #ea3d56;\n  }\n  70% {\n    -webkit-box-shadow: 0 0 0 10px rgba(234, 61, 86, 0.15);\n  }\n  100% {\n    -webkit-box-shadow: 0 0 0 0 rgba(234, 61, 86, 0);\n  }\n}\n\n@keyframes mkdfPulsesmallfirst {\n  0% {\n    -moz-box-shadow: 0 0 0 0 #ea3d56;\n    box-shadow: 0 0 0 0 #ea3d56;\n  }\n  70% {\n    -moz-box-shadow: 0 0 0 10px rgba(234, 61, 86, 0.15);\n    box-shadow: 0 0 0 10px rgba(234, 61, 86, 0);\n  }\n  100% {\n    -moz-box-shadow: 0 0 0 0 rgba(234, 61, 86, 0);\n    box-shadow: 0 0 0 0 rgba(234, 61, 86, 0);\n  }\n}\n\n/* ==========================================================================\n   Shortcodes styles\n   ========================================================================== */\n/* ==========================================================================\n   Accordions shortcode style - begin\n   ========================================================================== */\n.mkdf-accordion-holder {\n  position: relative;\n  display: inline-block;\n  width: 100%;\n  vertical-align: middle;\n}\n\n.mkdf-accordion-holder .mkdf-accordion-title {\n  position: relative;\n  cursor: pointer;\n  margin: 0;\n  box-sizing: border-box;\n  -webkit-transform: translateZ(0px);\n  -moz-transform: translateZ(0px);\n  transform: translateZ(0px);\n  border: 2px rgba(225, 225, 225, 0.3) solid;\n}\n\n.mkdf-accordion-holder .mkdf-accordion-title .mkdf-tab-title {\n  display: block;\n  line-height: inherit;\n}\n\n.mkdf-accordion-holder .mkdf-accordion-title .mkdf-accordion-mark {\n  position: absolute;\n  top: 50%;\n  right: 0;\n  width: 40px;\n  height: 40px;\n  margin: -1px 0 0;\n  font-size: 40px;\n  line-height: 40px;\n  text-align: center;\n  -webkit-transform: translateY(-50%);\n  -moz-transform: translateY(-50%);\n  transform: translateY(-50%);\n  -webkit-transition: width 0.2s ease-in-out;\n  -moz-transition: width 0.2s ease-in-out;\n  transition: width 0.2s ease-in-out;\n}\n\n.mkdf-accordion-holder .mkdf-accordion-title .mkdf-accordion-mark span {\n  position: absolute;\n  display: block;\n  width: 100%;\n  height: 100%;\n  font-size: inherit;\n  line-height: inherit;\n  -webkit-transition: opacity 0.2s ease-out;\n  -moz-transition: opacity 0.2s ease-out;\n  transition: opacity 0.2s ease-out;\n}\n\n.mkdf-accordion-holder .mkdf-accordion-title .mkdf-accordion-mark span:before {\n  display: block;\n  line-height: inherit;\n}\n\n.mkdf-accordion-holder .mkdf-accordion-title .mkdf-accordion-mark .mkdf-eye-line {\n  display: block;\n  -webkit-transform: rotate(-45deg);\n  -moz-transform: rotate(-45deg);\n  transform: rotate(-45deg);\n}\n\n.mkdf-accordion-holder .mkdf-accordion-title .mkdf-accordion-mark .mkdf-eye-line:after {\n  content: '';\n  display: block;\n  height: 2px;\n  width: 100%;\n  position: absolute;\n  left: 0;\n  bottom: 19px;\n  background-color: currentColor;\n  -webkit-transition: width 0.2s ease-in-out;\n  -moz-transition: width 0.2s ease-in-out;\n  transition: width 0.2s ease-in-out;\n}\n\n.mkdf-accordion-holder .mkdf-accordion-title .mkdf-accordion-mark:hover .mkdf-eye-line:after {\n  width: 100%;\n}\n\n.mkdf-accordion-holder .mkdf-accordion-title.ui-state-active .mkdf-accordion-mark .mkdf-eye-line:after, .mkdf-accordion-holder .mkdf-accordion-title.ui-state-hover .mkdf-accordion-mark .mkdf-eye-line:after {\n  width: 0%;\n}\n\n.mkdf-accordion-holder .mkdf-accordion-content {\n  margin: 0;\n  border: 2px solid rgba(225, 225, 225, 0.3);\n  border-top: none;\n}\n\n.mkdf-accordion-holder .mkdf-accordion-content p {\n  margin: 0;\n}\n\n.mkdf-accordion-holder.mkdf-ac-boxed.mkdf-white-skin {\n  color: #fff;\n}\n\n.mkdf-accordion-holder.mkdf-ac-boxed.mkdf-white-skin .mkdf-accordion-title {\n  color: #fff;\n}\n\n.mkdf-accordion-holder.mkdf-ac-boxed .mkdf-accordion-title {\n  margin: 23px 0 0;\n  padding: 24px 70px 24px 33px;\n}\n\n.mkdf-accordion-holder.mkdf-ac-boxed .mkdf-accordion-title:first-child {\n  margin: 0;\n}\n\n.mkdf-accordion-holder.mkdf-ac-boxed .mkdf-accordion-title .mkdf-accordion-mark {\n  right: 28px;\n}\n\n.mkdf-accordion-holder.mkdf-ac-boxed .mkdf-accordion-title.ui-state-active {\n  border-bottom: none;\n}\n\n.mkdf-accordion-holder.mkdf-ac-boxed .mkdf-accordion-content {\n  padding: 11px 36px 35px 36px;\n}\n\n.mkdf-accordion-holder.mkdf-ac-simple {\n  border-bottom: 1px solid rgba(225, 225, 225, 0.3);\n}\n\n.mkdf-accordion-holder.mkdf-ac-simple .mkdf-accordion-title {\n  padding: 17px 0 17px 30px;\n  border-top: 1px solid rgba(225, 225, 225, 0.3);\n}\n\n.mkdf-accordion-holder.mkdf-ac-simple .mkdf-accordion-content {\n  border-top: 1px solid transparent;\n}\n\n.mkdf-accordion-holder.mkdf-ac-simple .mkdf-accordion-content.ui-accordion-content-active {\n  border-color: rgba(225, 225, 225, 0.3);\n}\n\n.mkdf-accordion-holder.mkdf-ac-simple .mkdf-accordion-content {\n  padding: 21px 0 16px;\n}\n\n/* ==========================================================================\n   Accordions shortcode style - end\n   ========================================================================== */\n/* ==========================================================================\n   Banner shortcode style - begin\n   ========================================================================== */\n.mkdf-banner-holder {\n  position: relative;\n  display: inline-block;\n  width: 100%;\n  vertical-align: middle;\n}\n\n.touch .mkdf-banner-holder {\n  cursor: pointer;\n}\n\n.mkdf-banner-holder.mkdf-visible-on-hover:hover .mkdf-banner-text-holder {\n  opacity: 1;\n}\n\n.mkdf-banner-holder.mkdf-visible-on-hover .mkdf-banner-text-holder {\n  opacity: 0;\n  -webkit-transition: opacity 0.2s ease-out;\n  -moz-transition: opacity 0.2s ease-out;\n  transition: opacity 0.2s ease-out;\n}\n\n.mkdf-banner-holder.mkdf-disabled .mkdf-banner-text-holder {\n  display: none;\n}\n\n.mkdf-banner-holder.mkdf-banner-info-centered .mkdf-banner-text-holder {\n  padding: 70px 20px;\n  text-align: center;\n}\n\n.mkdf-banner-holder .mkdf-banner-image {\n  position: relative;\n  display: inline-block;\n  width: 100%;\n  vertical-align: middle;\n}\n\n.mkdf-banner-holder .mkdf-banner-image img {\n  display: block;\n}\n\n.mkdf-banner-holder .mkdf-banner-text-holder {\n  position: absolute;\n  display: block;\n  width: 100%;\n  height: 100%;\n  top: 0;\n  left: 0;\n  padding: 35px;\n  background-color: rgba(27, 44, 88, 0.4);\n  box-sizing: border-box;\n}\n\n@media only screen and (max-width: 768px) {\n  .mkdf-banner-holder .mkdf-banner-text-holder {\n    padding: 25px;\n  }\n}\n\n.mkdf-banner-holder .mkdf-banner-text-outer {\n  position: relative;\n  display: table;\n  table-layout: fixed;\n  height: 100%;\n  width: 100%;\n}\n\n.mkdf-banner-holder .mkdf-banner-text-inner {\n  position: relative;\n  display: table-cell;\n  height: 100%;\n  width: 100%;\n  vertical-align: bottom;\n}\n\n.mkdf-banner-holder .mkdf-banner-subtitle {\n  margin: 0 0 4px;\n  color: #fff;\n}\n\n.mkdf-banner-holder .mkdf-banner-title {\n  margin: 0;\n  color: #fff;\n}\n\n.mkdf-banner-holder .mkdf-banner-title .mkdf-banner-title-light {\n  font-weight: 300;\n}\n\n.mkdf-banner-holder .mkdf-banner-link-text {\n  position: relative;\n  display: inline-block;\n  vertical-align: top;\n  margin: 11px 0 0;\n  color: #fff;\n  line-height: 1em;\n  z-index: 2;\n  -webkit-transform: translateZ(0);\n  -moz-transform: translateZ(0);\n  transform: translateZ(0);\n}\n\n.mkdf-banner-holder .mkdf-banner-link-text:hover .mkdf-banner-link-hover {\n  width: 100%;\n}\n\n.mkdf-banner-holder .mkdf-banner-link-text .mkdf-banner-link-original {\n  position: relative;\n  display: inline-block;\n  vertical-align: top;\n  width: 100%;\n}\n\n.mkdf-banner-holder .mkdf-banner-link-text .mkdf-banner-link-original span {\n  color: inherit;\n}\n\n.mkdf-banner-holder .mkdf-banner-link-text .mkdf-banner-link-hover {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 0.1%;\n  height: 100%;\n  display: inline-block;\n  vertical-align: top;\n  white-space: nowrap;\n  overflow: hidden;\n  -webkit-transition: width 0.4s ease-in-out;\n  -moz-transition: width 0.4s ease-in-out;\n  transition: width 0.4s ease-in-out;\n}\n\n.mkdf-banner-holder .mkdf-banner-link-text .mkdf-banner-link-hover span {\n  color: #ea3d56;\n}\n\n.mkdf-banner-holder .mkdf-banner-link-text .mkdf-banner-link-icon,\n.mkdf-banner-holder .mkdf-banner-link-text .mkdf-banner-link-label {\n  position: relative;\n  display: inline-block;\n  vertical-align: top;\n}\n\n.mkdf-banner-holder .mkdf-banner-link-text .mkdf-banner-link-icon {\n  margin: 0 2px 0 0;\n  font-size: 15px;\n}\n\n.mkdf-banner-holder .mkdf-banner-link-text .mkdf-banner-link-label {\n  font-size: 14px;\n  line-height: inherit;\n}\n\n.mkdf-banner-holder .mkdf-banner-link {\n  position: absolute;\n  display: block;\n  width: 100%;\n  height: 100%;\n  top: 0;\n  left: 0;\n  z-index: 1;\n}\n\n/* ==========================================================================\n   Banner shortcode style - end\n   ========================================================================== */\n/* ==========================================================================\n   Button shortcode style - begin\n   ========================================================================== */\n.mkdf-btn {\n  position: relative;\n  display: inline-block;\n  vertical-align: middle;\n  width: auto;\n  margin: 0;\n  font-family: \"Josefin Sans\", sans-serif;\n  font-size: 12px;\n  line-height: 2em;\n  letter-spacing: 0.16em;\n  font-weight: 700;\n  text-transform: uppercase;\n  outline: none;\n  box-sizing: border-box;\n  -webkit-transition: color 0.2s ease-in-out, background-color 0.2s ease-in-out, border-color 0.2s ease-in-out;\n  -moz-transition: color 0.2s ease-in-out, background-color 0.2s ease-in-out, border-color 0.2s ease-in-out;\n  transition: color 0.2s ease-in-out, background-color 0.2s ease-in-out, border-color 0.2s ease-in-out;\n  padding: 13px 34px 11px;\n  cursor: pointer;\n}\n\n.mkdf-btn.mkdf-btn-simple {\n  padding: 0 !important;\n  color: #868890;\n  background-color: transparent;\n  border: 0;\n  vertical-align: middle;\n}\n\n.mkdf-btn.mkdf-btn-simple .mkdf-btn-text {\n  display: inline-block;\n  vertical-align: middle;\n  line-height: 50px;\n}\n\n.mkdf-btn.mkdf-btn-simple.mkdf-btn-icon > i {\n  padding: 11px 9px;\n}\n\n.mkdf-btn.mkdf-btn-simple.mkdf-btn-icon .mkdf-btn-text {\n  position: relative;\n}\n\n.mkdf-btn.mkdf-btn-simple.mkdf-btn-icon .mkdf-btn-text::after {\n  content: '';\n  position: relative;\n  display: block;\n  top: -18px;\n  width: 100%;\n  height: 1px;\n  background-color: currentColor;\n  -webkit-transform: scaleX(1);\n  -moz-transform: scaleX(1);\n  transform: scaleX(1);\n  -webkit-transform-origin: 0 50%;\n  -moz-transform-origin: 0 50%;\n  transform-origin: 0 50%;\n  -webkit-transition: -webkit-transform .4s ease-out;\n  transition: transform .4s ease-out;\n}\n\n.mkdf-btn.mkdf-btn-simple:hover .mkdf-btn-text::after {\n  -webkit-transform-origin: 100% 50%;\n  -moz-transform-origin: 100% 50%;\n  transform-origin: 100% 50%;\n  -webkit-transform: scaleX(0);\n  -moz-transform: scaleX(0);\n  transform: scaleX(0);\n  -webkit-animation: animate-btn-line 0.5s 0.2s forwards;\n  -moz-animation: animate-btn-line 0.5s 0.2s forwards;\n  animation: animate-btn-line 0.5s 0.2s forwards;\n}\n\n.mkdf-btn.mkdf-btn-solid {\n  color: #fff;\n  background-color: #ea3d56;\n  border: 1px solid transparent;\n  -webkit-transition: all 0.2s ease-out;\n  -moz-transition: all 0.2s ease-out;\n  transition: all 0.2s ease-out;\n}\n\n.mkdf-btn.mkdf-btn-solid:not(.mkdf-btn-custom-hover-color):hover {\n  color: #fff !important;\n}\n\n.mkdf-btn.mkdf-btn-solid:not(.mkdf-btn-custom-hover-bg):hover {\n  background-color: #ff4661 !important;\n}\n\n.mkdf-btn.mkdf-btn-solid:not(.mkdf-btn-custom-border-hover):hover {\n  border-color: #ff4661 !important;\n}\n\n.mkdf-btn.mkdf-btn-solid:hover {\n  box-shadow: none !important;\n}\n\n.mkdf-btn.mkdf-btn-outline {\n  color: #ea3d56;\n  background-color: transparent;\n  border: 1px solid #ea3d56;\n}\n\n.mkdf-btn.mkdf-btn-outline:not(.mkdf-btn-custom-hover-color):hover {\n  color: #fff !important;\n}\n\n.mkdf-btn.mkdf-btn-outline:not(.mkdf-btn-custom-hover-bg):hover {\n  background-color: #ea3d56 !important;\n}\n\n.mkdf-btn.mkdf-btn-outline:not(.mkdf-btn-custom-border-hover):hover {\n  border-color: #ea3d56 !important;\n}\n\n.mkdf-btn.mkdf-btn-small {\n  padding: 11px 24px;\n}\n\n.mkdf-btn.mkdf-btn-large {\n  padding: 13px 43px 11px;\n}\n\n.mkdf-btn.mkdf-btn-huge {\n  padding: 21px 60px 16px;\n  font-size: 14px;\n}\n\n.mkdf-btn.mkdf-btn-icon > i,\n.mkdf-btn.mkdf-btn-icon > span:not(.mkdf-btn-text) {\n  position: relative;\n  display: inline-block;\n  vertical-align: middle;\n  margin: 0 10px 0 0;\n  font-size: 33px;\n  line-height: inherit;\n}\n\n.mkdf-btn.mkdf-btn-icon > i:before,\n.mkdf-btn.mkdf-btn-icon > span:not(.mkdf-btn-text):before {\n  display: block;\n  line-height: inherit;\n}\n\n/* ==========================================================================\n   Button shortcode style - end\n   ========================================================================== */\n/* ==========================================================================\n   Call To Action shortcode style - begin\n   ========================================================================== */\n.mkdf-call-to-action-holder {\n  position: relative;\n  display: inline-block;\n  width: 100%;\n  vertical-align: middle;\n  /***** Layout Style - begin *****/\n  /***** Layout Style - end *****/\n  /***** Columns Space - begin *****/\n  /***** Columns Space - end *****/\n}\n\n.mkdf-call-to-action-holder .mkdf-cta-text-holder,\n.mkdf-call-to-action-holder .mkdf-cta-button-holder {\n  position: relative;\n  display: inline-block;\n  vertical-align: middle;\n}\n\n.mkdf-call-to-action-holder .mkdf-cta-text-holder h1, .mkdf-call-to-action-holder .mkdf-cta-text-holder h2, .mkdf-call-to-action-holder .mkdf-cta-text-holder h3, .mkdf-call-to-action-holder .mkdf-cta-text-holder h4, .mkdf-call-to-action-holder .mkdf-cta-text-holder h5, .mkdf-call-to-action-holder .mkdf-cta-text-holder h6 {\n  margin: 0;\n}\n\n.mkdf-call-to-action-holder .mkdf-cta-button-holder .mkdf-btn {\n  white-space: nowrap;\n}\n\n.mkdf-call-to-action-holder.mkdf-normal-layout .mkdf-cta-inner {\n  display: table;\n}\n\n.mkdf-call-to-action-holder.mkdf-normal-layout:not(.mkdf-content-in-grid) .mkdf-cta-inner {\n  width: 100%;\n}\n\n.mkdf-call-to-action-holder.mkdf-normal-layout .mkdf-cta-text-holder,\n.mkdf-call-to-action-holder.mkdf-normal-layout .mkdf-cta-button-holder {\n  display: table-cell;\n  box-sizing: border-box;\n}\n\n.mkdf-call-to-action-holder.mkdf-normal-layout .mkdf-cta-button-holder {\n  text-align: right;\n}\n\n.mkdf-call-to-action-holder.mkdf-simple-layout .mkdf-cta-inner {\n  text-align: center;\n}\n\n.mkdf-call-to-action-holder.mkdf-simple-layout .mkdf-cta-text-holder,\n.mkdf-call-to-action-holder.mkdf-simple-layout .mkdf-cta-button-holder {\n  width: 100%;\n}\n\n.mkdf-call-to-action-holder.mkdf-simple-layout .mkdf-cta-button-holder {\n  margin: 28px 0 0;\n}\n\n.mkdf-call-to-action-holder.mkdf-two-halves-columns .mkdf-cta-text-holder,\n.mkdf-call-to-action-holder.mkdf-two-halves-columns .mkdf-cta-button-holder {\n  width: 50%;\n}\n\n.mkdf-call-to-action-holder.mkdf-two-thirds-columns .mkdf-cta-text-holder {\n  width: 66.66666666666667%;\n}\n\n.mkdf-call-to-action-holder.mkdf-two-thirds-columns .mkdf-cta-button-holder {\n  width: 33.33333333333333%;\n}\n\n.mkdf-call-to-action-holder.mkdf-three-quarters-columns .mkdf-cta-text-holder {\n  width: 75%;\n}\n\n.mkdf-call-to-action-holder.mkdf-three-quarters-columns .mkdf-cta-button-holder {\n  width: 25%;\n}\n\n.mkdf-call-to-action-holder.mkdf-four-fifths-columns .mkdf-cta-text-holder {\n  width: 80%;\n}\n\n.mkdf-call-to-action-holder.mkdf-four-fifths-columns .mkdf-cta-button-holder {\n  width: 20%;\n}\n\n/* ==========================================================================\n   Call To Action shortcode style - end\n   ========================================================================== */\n/* ==========================================================================\n   Clients Carousel shortcode style - begin\n   ========================================================================== */\n.mkdf-clients-carousel-holder {\n  position: relative;\n  display: inline-block;\n  width: 100%;\n  vertical-align: middle;\n  /***** Hover Types - begin *****/\n  /***** Hover Types - end *****/\n}\n\n.mkdf-clients-carousel-holder .mkdf-cc-inner {\n  position: relative;\n  display: inline-block;\n  width: 100%;\n  vertical-align: middle;\n}\n\n.mkdf-clients-carousel-holder .mkdf-cc-item {\n  position: relative;\n  display: inline-block;\n  width: 100%;\n  vertical-align: middle;\n}\n\n.touch .mkdf-clients-carousel-holder .mkdf-cc-item {\n  cursor: pointer;\n}\n\n.mkdf-clients-carousel-holder .mkdf-cc-item .mkdf-cc-item {\n  position: relative;\n  display: block;\n}\n\n.mkdf-clients-carousel-holder .mkdf-cc-link {\n  position: relative;\n  display: inline-block;\n  vertical-align: top;\n}\n\n.mkdf-clients-carousel-holder.mkdf-cc-hover-switch-images .mkdf-cc-item {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.mkdf-clients-carousel-holder.mkdf-cc-hover-switch-images .mkdf-cc-item:hover .mkdf-cc-image {\n  opacity: 0;\n}\n\n.mkdf-clients-carousel-holder.mkdf-cc-hover-switch-images .mkdf-cc-item:hover .mkdf-cc-hover-image {\n  opacity: 1;\n}\n\n.mkdf-clients-carousel-holder.mkdf-cc-hover-switch-images .mkdf-cc-item .mkdf-cc-image {\n  position: relative;\n  display: block;\n  width: auto;\n  margin: 0 auto;\n  opacity: 1;\n  -webkit-transform: translateZ(0);\n  -webkit-transition: opacity 0.15s ease-out;\n  -moz-transition: opacity 0.15s ease-out;\n  transition: opacity 0.15s ease-out;\n}\n\n.mkdf-clients-carousel-holder.mkdf-cc-hover-switch-images .mkdf-cc-item .mkdf-cc-hover-image {\n  position: absolute;\n  top: 0;\n  left: auto;\n  width: auto;\n  opacity: 0;\n  -webkit-transform: translateZ(0);\n  -webkit-transition: opacity 0.15s ease-out;\n  -moz-transition: opacity 0.15s ease-out;\n  transition: opacity 0.15s ease-out;\n}\n\n.mkdf-clients-carousel-holder.mkdf-cc-hover-opacity-images .mkdf-cc-item {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.mkdf-clients-carousel-holder.mkdf-cc-hover-opacity-images .mkdf-cc-item:hover .mkdf-cc-image {\n  opacity: 0.6;\n}\n\n.mkdf-clients-carousel-holder.mkdf-cc-hover-opacity-images .mkdf-cc-item .mkdf-cc-image {\n  position: relative;\n  display: block;\n  width: auto;\n  margin: 0 auto;\n  opacity: 1;\n  -webkit-transform: translateZ(0);\n  -webkit-transition: opacity 0.15s ease-out;\n  -moz-transition: opacity 0.15s ease-out;\n  transition: opacity 0.15s ease-out;\n}\n\n.mkdf-clients-carousel-holder.mkdf-cc-hover-opacity-images .mkdf-cc-item .mkdf-cc-hover-image {\n  position: absolute;\n  top: 0;\n  left: auto;\n  width: auto;\n  opacity: 0;\n  -webkit-transform: translateZ(0);\n  -webkit-transition: opacity 0.15s ease-out;\n  -moz-transition: opacity 0.15s ease-out;\n  transition: opacity 0.15s ease-out;\n}\n\n.mkdf-clients-carousel-holder.mkdf-cc-hover-roll-over .mkdf-cc-item {\n  overflow: hidden;\n}\n\n.mkdf-clients-carousel-holder.mkdf-cc-hover-roll-over .mkdf-cc-item:hover .mkdf-cc-image {\n  -webkit-transform: translateY(-100%);\n  -moz-transform: translateY(-100%);\n  transform: translateY(-100%);\n}\n\n.mkdf-clients-carousel-holder.mkdf-cc-hover-roll-over .mkdf-cc-item:hover .mkdf-cc-hover-image {\n  -webkit-transform: translate(-50%, 0);\n  -moz-transform: translate(-50%, 0);\n  transform: translate(-50%, 0);\n}\n\n.mkdf-clients-carousel-holder.mkdf-cc-hover-roll-over .mkdf-cc-item .mkdf-cc-image {\n  position: relative;\n  display: block;\n  width: auto;\n  margin: 0 auto;\n  -webkit-transition: -webkit-transform 0.4s ease;\n  -moz-transition: -moz-transform 0.4s ease;\n  transition: transform 0.4s ease;\n}\n\n.mkdf-clients-carousel-holder.mkdf-cc-hover-roll-over .mkdf-cc-item .mkdf-cc-hover-image {\n  position: absolute;\n  top: 0;\n  left: 50%;\n  width: auto;\n  -webkit-transform: translate(-50%, 100%);\n  -moz-transform: translate(-50%, 100%);\n  transform: translate(-50%, 100%);\n  -webkit-transition: -webkit-transform 0.4s ease;\n  -moz-transition: -moz-transform 0.4s ease;\n  transition: transform 0.4s ease;\n}\n\n/* ==========================================================================\n   Clients Carousel shortcode style - end\n   ========================================================================== */\n/* ==========================================================================\n   clients Holder shortcode style - begin\n   ========================================================================== */\n.mkdf-clients-grid-holder {\n  position: relative;\n  display: inline-block;\n  width: 100%;\n  vertical-align: middle;\n  text-align: center;\n}\n\n.mkdf-clients-grid-holder.mkdf-cg-alignment-left {\n  text-align: left;\n}\n\n.mkdf-clients-grid-holder.mkdf-cg-alignment-right {\n  text-align: right;\n}\n\n.mkdf-clients-grid-holder .mkdf-cc-link {\n  position: relative;\n  display: inline-block;\n  vertical-align: top;\n}\n\n/* ==========================================================================\n   clients Holder shortcode style - end\n   ========================================================================== */\n/* ==========================================================================\n   Countdown shortcode style - begin\n   ========================================================================== */\n.mkdf-countdown {\n  position: relative;\n  display: inline-block;\n  width: 100%;\n  vertical-align: middle;\n}\n\n.mkdf-countdown.mkdf-light-skin .countdown-row .countdown-section .countdown-amount,\n.mkdf-countdown.mkdf-light-skin .countdown-row .countdown-section .countdown-period {\n  color: #fff;\n}\n\n.mkdf-countdown .countdown-rtl {\n  direction: rtl;\n}\n\n.mkdf-countdown .countdown-row {\n  position: relative;\n  display: inline-block;\n  width: 100%;\n  vertical-align: middle;\n  text-align: center;\n  clear: both;\n}\n\n.mkdf-countdown .countdown-row.countdown-show1 .countdown-section {\n  width: 100%;\n}\n\n.mkdf-countdown .countdown-row.countdown-show2 .countdown-section {\n  width: 50%;\n}\n\n.mkdf-countdown .countdown-row.countdown-show3 .countdown-section {\n  width: 33.33333%;\n}\n\n.mkdf-countdown .countdown-row.countdown-show4 .countdown-section {\n  width: 25%;\n}\n\n.mkdf-countdown .countdown-row.countdown-show5 .countdown-section {\n  width: 20%;\n}\n\n.mkdf-countdown .countdown-row.countdown-show6 .countdown-section {\n  width: 16.66667%;\n}\n\n.mkdf-countdown .countdown-row .countdown-section {\n  position: relative;\n  display: inline-block;\n  width: 100%;\n  vertical-align: middle;\n  padding: 0 5px;\n  box-sizing: border-box;\n}\n\n.mkdf-countdown .countdown-row .countdown-section .countdown-amount {\n  position: relative;\n  display: block;\n  color: #1b2c58;\n  font-family: \"Josefin Sans\", sans-serif;\n  letter-spacing: -0.025em;\n  font-size: 72px;\n  line-height: 1em;\n  font-weight: 600;\n}\n\n.mkdf-countdown .countdown-row .countdown-section .countdown-period {\n  display: block;\n  font-family: \"Josefin Sans\", sans-serif;\n  font-size: 15px;\n  font-weight: 700;\n  font-size: 14px;\n  letter-spacing: .025em;\n  text-transform: uppercase;\n}\n\n/* ==========================================================================\n   Countdown shortcode style - end\n   ========================================================================== */\n/* ==========================================================================\n   Counter shortcode style - begin\n   ========================================================================== */\n.mkdf-counter-holder {\n  position: relative;\n  display: inline-block;\n  width: 100%;\n  vertical-align: middle;\n  opacity: 0;\n  -webkit-transition: opacity 0.01s ease-in;\n  -moz-transition: opacity 0.01s ease-in;\n  transition: opacity 0.01s ease-in;\n  text-align: center;\n}\n\n.mkdf-counter-holder .mkdf-counter-inner {\n  position: relative;\n  display: inline-block;\n  vertical-align: middle;\n}\n\n.mkdf-counter-holder .mkdf-counter {\n  height: 1em;\n  display: inline-block !important;\n  vertical-align: middle;\n  color: #1b2c58;\n  font-family: \"Josefin Sans\", sans-serif;\n  font-size: 60px;\n  letter-spacing: -0.025em;\n  line-height: 1em;\n  font-weight: 600;\n  overflow: hidden;\n}\n\n.mkdf-counter-holder .mkdf-counter-title {\n  margin: 13px 0 0;\n  font-size: 14px;\n  letter-spacing: 0.025em;\n}\n\n.mkdf-counter-holder .mkdf-counter-text {\n  margin: 14px 0 0;\n}\n\n/* ==========================================================================\n   Counter shortcode style - end\n   ========================================================================== */\n/* ==========================================================================\n   Custom Font shortcode style - begin\n   ========================================================================== */\n.mkdf-custom-font-holder .mkdf-cf-typed-wrap {\n  width: 0;\n  white-space: nowrap;\n}\n\n.mkdf-custom-font-holder .mkdf-cf-typed {\n  display: inline-block;\n}\n\n.mkdf-custom-font-holder .mkdf-cf-typed span {\n  display: none;\n}\n\n.mkdf-custom-font-holder .mkdf-cf-typed ~ .typed-cursor {\n  display: inline-block;\n  opacity: 1;\n  -webkit-animation: blink 0.7s infinite;\n  animation: blink 0.7s infinite;\n}\n\n@-webkit-keyframes blink {\n  0% {\n    opacity: 1;\n    filter: alpha(opacity=100);\n  }\n  50% {\n    opacity: 0;\n    filter: alpha(opacity=0);\n  }\n  100% {\n    opacity: 1;\n    filter: alpha(opacity=100);\n  }\n}\n\n@keyframes blink {\n  0% {\n    opacity: 1;\n    filter: alpha(opacity=100);\n  }\n  50% {\n    opacity: 0;\n    filter: alpha(opacity=0);\n  }\n  100% {\n    opacity: 1;\n    filter: alpha(opacity=100);\n  }\n}\n\n/* ==========================================================================\n   Custom Font shortcode style - end\n   ========================================================================== */\n/* ==========================================================================\n   Dropcaps shortcode style - begin\n   ========================================================================== */\n.mkdf-dropcaps {\n  position: relative;\n  display: inline-block;\n  vertical-align: top;\n  float: left;\n  font-family: \"Josefin Sans\", sans-serif;\n  line-height: 60px;\n  font-size: 55px;\n  color: #999;\n  font-weight: 600;\n  text-align: center;\n  margin: 0 20px 0 0;\n}\n\n.mkdf-dropcaps.mkdf-square, .mkdf-dropcaps.mkdf-circle {\n  height: 50px;\n  width: 50px;\n  font-family: \"Josefin Sans\", sans-serif;\n  font-size: 36px;\n  line-height: 58px;\n  font-weight: 600;\n  color: #fff;\n  background-color: #1b2c58;\n  margin: 2px 15px 0 0;\n  box-sizing: border-box;\n}\n\n.mkdf-dropcaps.mkdf-circle {\n  border-radius: 3em;\n}\n\n/* ==========================================================================\n   Dropcaps shortcode style - end\n   ========================================================================== */\n/* ==========================================================================\n   Elements Holder shortcode style - begin\n   ========================================================================== */\n.mkdf-elements-holder {\n  width: 100%;\n  display: table;\n  table-layout: fixed;\n}\n\n.mkdf-elements-holder.mkdf-eh-full-height {\n  height: 100%;\n}\n\n.mkdf-elements-holder.mkdf-ehi-float .mkdf-eh-item {\n  float: left;\n}\n\n.mkdf-elements-holder.mkdf-two-columns .mkdf-eh-item {\n  width: 50%;\n}\n\n.mkdf-elements-holder.mkdf-three-columns .mkdf-eh-item {\n  width: 33.33333%;\n}\n\n.mkdf-elements-holder.mkdf-four-columns .mkdf-eh-item {\n  width: 25%;\n}\n\n.mkdf-elements-holder.mkdf-five-columns .mkdf-eh-item {\n  width: 20%;\n}\n\n.mkdf-elements-holder.mkdf-six-columns .mkdf-eh-item {\n  width: 16.66667%;\n}\n\n.mkdf-elements-holder .mkdf-eh-item {\n  display: table-cell;\n  vertical-align: middle;\n  height: 100%;\n  background-position: center;\n  background-size: cover;\n}\n\n.mkdf-elements-holder .mkdf-eh-item.mkdf-vertical-alignment-top {\n  vertical-align: top;\n}\n\n.mkdf-elements-holder .mkdf-eh-item.mkdf-vertical-alignment-bottom {\n  vertical-align: bottom;\n}\n\n.mkdf-elements-holder .mkdf-eh-item.mkdf-horizontal-alignment-center {\n  text-align: center;\n}\n\n.mkdf-elements-holder .mkdf-eh-item.mkdf-horizontal-alignment-right {\n  text-align: right;\n}\n\n.mkdf-elements-holder .mkdf-eh-item .mkdf-elements-holder-item-inner {\n  width: 100%;\n}\n\n.mkdf-elements-holder .mkdf-ehi-content {\n  padding: 0 20px;\n}\n\n/* ==========================================================================\n   Elements Holder shortcode style - end\n   ========================================================================== */\n/* ==========================================================================\n   Google Map shortcode style - begin\n   ========================================================================== */\n.mkdf-google-map-holder {\n  position: relative;\n  display: inline-block;\n  width: 100%;\n  vertical-align: middle;\n}\n\n.mkdf-google-map-holder .mkdf-google-map-direction {\n  position: absolute;\n  top: 10px;\n  left: 10px;\n  padding: 0 8px;\n  font-size: 13px;\n  line-height: 24px;\n  color: #868890;\n  background-color: #fff;\n  z-index: 999;\n  box-sizing: border-box;\n}\n\n.mkdf-google-map-holder .mkdf-google-map-direction:hover {\n  color: #1b2c58;\n}\n\n.mkdf-google-map-holder .mkdf-google-map {\n  display: block;\n  width: 100%;\n  height: 300px;\n}\n\n.mkdf-google-map-holder .mkdf-google-map iframe,\n.mkdf-google-map-holder .mkdf-google-map object,\n.mkdf-google-map-holder .mkdf-google-map embed {\n  width: 100%;\n  display: block;\n}\n\n.mkdf-google-map-holder .mkdf-google-map img {\n  max-width: none;\n}\n\n.mkdf-google-map-holder .mkdf-snazzy-map {\n  display: none;\n}\n\n.mkdf-google-map-holder .mkdf-google-map-overlay {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  display: none;\n  z-index: 1000;\n}\n\n/* ==========================================================================\n   Google Map shortcode style - end\n   ========================================================================== */\n/* ==========================================================================\n   Icon List Item shortcode style - begin\n   ========================================================================== */\n.mkdf-icon-list-holder {\n  position: relative;\n  display: table;\n  table-layout: fixed;\n  height: auto;\n  width: 100%;\n  margin-bottom: 8px;\n}\n\n.mkdf-icon-list-holder .mkdf-il-icon-holder,\n.mkdf-icon-list-holder .mkdf-il-text {\n  position: relative;\n  display: table-cell;\n  vertical-align: top;\n}\n\n.mkdf-icon-list-holder .mkdf-il-icon-holder {\n  width: 1%;\n}\n\n.mkdf-icon-list-holder .mkdf-il-icon-holder > * {\n  position: relative;\n  display: inline-block;\n  vertical-align: top;\n  color: #1b2c58;\n  font-size: 17px;\n  line-height: inherit;\n}\n\n.mkdf-icon-list-holder .mkdf-il-icon-holder > *:before {\n  display: block;\n  line-height: inherit;\n}\n\n.mkdf-icon-list-holder .mkdf-il-text {\n  width: 99%;\n  padding: 0 0 0 13px;\n  box-sizing: border-box;\n}\n\n/* ==========================================================================\n   Icon List Item shortcode style - end\n   ========================================================================== */\n/* ==========================================================================\n   Icon With Text shortcode style - begin\n   ========================================================================== */\n.mkdf-iwt {\n  position: relative;\n  display: inline-block;\n  width: 100%;\n  vertical-align: middle;\n  -webkit-transition: all 0.2s ease-out;\n  -moz-transition: all 0.2s ease-out;\n  transition: all 0.2s ease-out;\n}\n\n.mkdf-iwt:hover {\n  cursor: pointer;\n}\n\n.mkdf-iwt .mkdf-iwt-icon {\n  -webkit-transition: all 0.3s ease-in-out;\n  -moz-transition: all 0.3s ease-in-out;\n  transition: all 0.3s ease-in-out;\n}\n\n.mkdf-iwt .mkdf-iwt-icon a {\n  position: relative;\n  display: inline-block;\n  vertical-align: middle;\n}\n\n.mkdf-iwt .mkdf-iwt-icon .mkdf-icon-shortcode {\n  line-height: 1;\n}\n\n.mkdf-iwt .mkdf-iwt-icon .mkdf-icon-shortcode.mkdf-circle, .mkdf-iwt .mkdf-iwt-icon .mkdf-icon-shortcode.mkdf-square, .mkdf-iwt .mkdf-iwt-icon .mkdf-icon-shortcode.mkdf-dropcaps.mkdf-circle {\n  line-height: 2;\n}\n\n.mkdf-iwt .mkdf-iwt-title {\n  margin: 0;\n  line-height: 1.2em;\n}\n\n.mkdf-iwt .mkdf-iwt-title a {\n  position: relative;\n  display: inline-block;\n  vertical-align: top;\n}\n\n.mkdf-iwt .mkdf-iwt-title-text {\n  display: block;\n}\n\n.mkdf-iwt .mkdf-iwt-text {\n  margin: 6px 0;\n  font-weight: 500;\n}\n\n.mkdf-iwt .mkdf-iwt-text a:hover {\n  color: #3745a5 !important;\n}\n\n.mkdf-iwt:hover .mkdf-iwt-icon {\n  -webkit-transform: translate(0, -3px);\n  -webkit-transform: translate(0, -3px);\n  -moz-transform: translate(0, -3px);\n  transform: translate(0, -3px);\n}\n\n.mkdf-iwt.mkdf-iwt-icon-left {\n  width: auto;\n}\n\n.mkdf-iwt.mkdf-iwt-icon-left .mkdf-iwt-icon,\n.mkdf-iwt.mkdf-iwt-icon-left .mkdf-iwt-content {\n  display: table-cell;\n  vertical-align: top;\n}\n\n.mkdf-iwt.mkdf-iwt-icon-left .mkdf-iwt-icon {\n  position: relative;\n  top: 1px;\n  padding: 5px 0 0 8px;\n}\n\n.mkdf-iwt.mkdf-iwt-icon-left .mkdf-iwt-icon img {\n  max-width: none;\n}\n\n.mkdf-iwt.mkdf-iwt-icon-left .mkdf-iwt-content {\n  padding: 0 0 0 22px;\n  vertical-align: middle;\n}\n\n.mkdf-iwt.mkdf-iwt-icon-left-from-title .mkdf-iwt-icon,\n.mkdf-iwt.mkdf-iwt-icon-left-from-title .mkdf-iwt-title-text {\n  position: relative;\n  display: table-cell;\n  vertical-align: middle;\n}\n\n.mkdf-iwt.mkdf-iwt-icon-left-from-title .mkdf-iwt-icon .mkdf-icon-element {\n  -webkit-transition: none;\n  -moz-transition: none;\n  transition: none;\n}\n\n.mkdf-iwt.mkdf-iwt-icon-left-from-title .mkdf-iwt-icon img {\n  max-width: none;\n}\n\n.mkdf-iwt.mkdf-iwt-icon-left-from-title .mkdf-iwt-title-text {\n  padding: 0 0 0 17px;\n}\n\n.mkdf-iwt.mkdf-iwt-icon-top {\n  text-align: center;\n}\n\n.mkdf-iwt.mkdf-iwt-icon-top .mkdf-iwt-content {\n  padding: 23px 0 0;\n}\n\n.mkdf-iwt.boxed-shadow {\n  width: 100%;\n  padding: 30px;\n  border-radius: 8px;\n  box-sizing: border-box;\n  background: #fff;\n  -webkit-transition: all 0.2s ease-out;\n  -moz-transition: all 0.2s ease-out;\n  transition: all 0.2s ease-out;\n}\n\n.mkdf-iwt.boxed-shadow .mkdf-iwt-text {\n  font-size: 11px;\n  line-height: 1em;\n  letter-spacing: 0.075em;\n}\n\n.mkdf-iwt.boxed-hover-shadow {\n  width: 100%;\n  padding: 30px;\n  border-radius: 8px;\n  box-sizing: border-box;\n  background: #fff;\n}\n\n@media only screen and (max-width: 1366px) {\n  .mkdf-iwt.boxed-hover-shadow.mkdf-iwt-icon-medium {\n    padding: 30px 30px 30px 20px !important;\n  }\n}\n\n.mkdf-iwt.boxed-hover-shadow .mkdf-iwt-text {\n  font-size: 11px;\n  line-height: 1em;\n  letter-spacing: 0.075em;\n}\n\n/* ==========================================================================\n   Icon With Text shortcode style - end\n   ========================================================================== */\n/* ==========================================================================\n   Icon shortcode style - begin\n   ========================================================================== */\n.mkdf-icon-shortcode {\n  position: relative;\n  display: inline-block;\n  vertical-align: middle;\n  line-height: 1.1em;\n}\n\n.mkdf-icon-shortcode.mkdf-circle, .mkdf-icon-shortcode.mkdf-square, .mkdf-icon-shortcode.mkdf-dropcaps.mkdf-circle {\n  width: 2em;\n  height: 2em;\n  line-height: 2em;\n  text-align: center;\n  background-color: #ea3d56;\n  -webkit-transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out;\n  -moz-transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out;\n  transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out;\n}\n\n.mkdf-icon-shortcode.mkdf-circle a, .mkdf-icon-shortcode.mkdf-square a, .mkdf-icon-shortcode.mkdf-dropcaps.mkdf-circle a {\n  position: relative;\n  display: inline-block;\n  vertical-align: top;\n  width: 100%;\n  height: 100%;\n}\n\n.mkdf-icon-shortcode.mkdf-circle .mkdf-icon-element, .mkdf-icon-shortcode.mkdf-square .mkdf-icon-element, .mkdf-icon-shortcode.mkdf-dropcaps.mkdf-circle .mkdf-icon-element {\n  color: #fff;\n  line-height: inherit;\n}\n\n.mkdf-icon-shortcode.mkdf-circle {\n  border-radius: 50%;\n}\n\n.mkdf-icon-shortcode .mkdf-icon-element {\n  display: block;\n  line-height: inherit;\n  -webkit-transition: color 0.15s ease-in-out;\n  -moz-transition: color 0.15s ease-in-out;\n  transition: color 0.15s ease-in-out;\n}\n\n.mkdf-icon-shortcode .mkdf-icon-element:before {\n  display: block;\n  line-height: inherit;\n}\n\n.mkdf-icon-shortcode.mkdf-icon-switch {\n  overflow: hidden;\n}\n\n.mkdf-icon-shortcode.mkdf-icon-switch .mkdf-icon-original {\n  -webkit-transform: translateY(0);\n  -moz-transform: translateY(0);\n  transform: translateY(0);\n  -webkit-transition: -webkit-transform 0.5s ease;\n  -moz-transition: -moz-transform 0.5s ease;\n  transition: transform 0.5s ease;\n}\n\n.mkdf-icon-shortcode.mkdf-icon-switch .mkdf-icon-duplicate {\n  position: absolute;\n  top: 100%;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  -webkit-transform: translateY(0);\n  -moz-transform: translateY(0);\n  transform: translateY(0);\n  -webkit-transition: -webkit-transform 0.5s ease;\n  -moz-transition: -moz-transform 0.5s ease;\n  transition: transform 0.5s ease;\n}\n\n.mkdf-icon-shortcode.mkdf-icon-switch:hover .mkdf-icon-original {\n  -webkit-transform: translateY(-100%);\n  -moz-transform: translateY(-100%);\n  transform: translateY(-100%);\n}\n\n.mkdf-icon-shortcode.mkdf-icon-switch:hover .mkdf-icon-duplicate {\n  -webkit-transform: translateY(-100%);\n  -moz-transform: translateY(-100%);\n  transform: translateY(-100%);\n}\n\n.mkdf-icon-animation-holder {\n  position: relative;\n  display: inline-block;\n  vertical-align: middle;\n  -webkit-transform: scale(0);\n  -moz-transform: scale(0);\n  transform: scale(0);\n  -webkit-transition: transform 0.15s ease-in-out;\n  -moz-transition: transform 0.15s ease-in-out;\n  transition: transform 0.15s ease-in-out;\n}\n\n.mkdf-icon-animation-holder.mkdf-icon-animation-show {\n  -webkit-transform: scale(1);\n  -moz-transform: scale(1);\n  transform: scale(1);\n}\n\n.mkdf-icon-tiny {\n  font-size: 1.33333333em;\n  line-height: .75em;\n  vertical-align: -15%;\n}\n\n.mkdf-icon-small {\n  font-size: 2em;\n}\n\n.mkdf-icon-medium {\n  font-size: 3em;\n}\n\n.mkdf-icon-large {\n  font-size: 4em;\n}\n\n.mkdf-icon-huge {\n  font-size: 5em;\n}\n\n/* ==========================================================================\n   Icon shortcode style - end\n   ========================================================================== */\n/* ==========================================================================\n   Image Gallery shortcode style - begin\n   ========================================================================== */\n.mkdf-image-gallery {\n  position: relative;\n  display: inline-block;\n  width: 100%;\n  vertical-align: middle;\n  /***** Image Gallery Masonry Style - begin *****/\n  /***** Image Gallery Masonry Style - end *****/\n  /***** Custom Link Behavior Style - begin *****/\n  /***** Custom Link Behavior Style - end *****/\n  /***** Lightbox Behavior Style - begin *****/\n  /***** Lightbox Behavior Style - end *****/\n  /***** Zoom Behavior Style - begin *****/\n  /***** Zoom Behavior Style - end *****/\n  /***** Grayscale Behavior Style - begin *****/\n  /***** Grayscale Behavior Style - end *****/\n}\n\n@media only screen and (max-width: 680px) {\n  .mkdf-image-gallery .owl-nav {\n    display: none;\n  }\n}\n\n.mkdf-image-gallery .owl-nav .owl-prev, .mkdf-image-gallery .owl-nav .owl-next {\n  transition: opacity .2s ease;\n  -webkit-transform: translateY(-50%) !important;\n  -moz-transform: translateY(-50%) !important;\n  transform: translateY(-50%) !important;\n}\n\n.mkdf-image-gallery .owl-nav .owl-prev.disabled, .mkdf-image-gallery .owl-nav .owl-next.disabled {\n  opacity: 0;\n}\n\n.mkdf-image-gallery .owl-nav .owl-prev span, .mkdf-image-gallery .owl-nav .owl-next span {\n  font-size: 48px;\n  color: #fff;\n  width: 80px;\n  height: 80px;\n  line-height: 80px;\n  background-color: #ea3d56;\n  border-radius: 50%;\n  -webkit-box-shadow: 0 -5px 10px rgba(234, 61, 86, 0.3);\n  -moz-box-shadow: 0 -5px 10px rgba(234, 61, 86, 0.3);\n  box-shadow: 0 -5px 10px rgba(234, 61, 86, 0.3);\n}\n\n.mkdf-image-gallery .owl-nav .owl-prev {\n  left: 13%;\n}\n\n.mkdf-image-gallery .owl-nav .owl-next {\n  right: 13%;\n}\n\n.mkdf-image-gallery .owl-dots {\n  display: none;\n}\n\n@media only screen and (max-width: 680px) {\n  .mkdf-image-gallery .owl-dots {\n    display: block;\n  }\n}\n\n.mkdf-image-gallery.mkdf-ig-carousel-type .owl-item:first-child {\n  margin-left: -346px;\n}\n\n@media screen and (max-width: 1366px) {\n  .mkdf-image-gallery.mkdf-ig-carousel-type .owl-item:first-child {\n    margin-left: -247px;\n  }\n}\n\n@media screen and (max-width: 1024px) {\n  .mkdf-image-gallery.mkdf-ig-carousel-type .owl-item:first-child {\n    margin-left: -82px;\n  }\n}\n\n@media screen and (max-width: 768px) {\n  .mkdf-image-gallery.mkdf-ig-carousel-type .owl-item:first-child {\n    margin-left: 0;\n  }\n}\n\n.mkdf-image-gallery.mkdf-has-shadow .mkdf-ig-image-inner {\n  box-shadow: 0 0 4.85px 0.15px rgba(0, 0, 0, 0.09);\n}\n\n.mkdf-image-gallery.mkdf-has-shadow.mkdf-ig-slider-type .owl-stage-outer, .mkdf-image-gallery.mkdf-has-shadow.mkdf-ig-carousel-type .owl-stage-outer {\n  padding: 0 0 20px;\n}\n\n.mkdf-image-gallery.mkdf-has-shadow.mkdf-ig-slider-type .mkdf-ig-image, .mkdf-image-gallery.mkdf-has-shadow.mkdf-ig-carousel-type .mkdf-ig-image {\n  box-shadow: 0 0 4.85px 0.15px rgba(0, 0, 0, 0.09);\n}\n\n.mkdf-image-gallery .mkdf-ig-image a, .mkdf-image-gallery .mkdf-ig-image img {\n  position: relative;\n  display: block;\n}\n\n.mkdf-image-gallery .mkdf-ig-image-inner {\n  position: relative;\n  display: inline-block;\n  width: 100%;\n  vertical-align: middle;\n}\n\n.mkdf-image-gallery .mkdf-ig-slider {\n  position: relative;\n  display: inline-block;\n  width: 100%;\n  vertical-align: middle;\n}\n\n.mkdf-image-gallery.mkdf-ig-masonry-type .mkdf-ig-image.mkdf-fixed-masonry-item .mkdf-ig-image-inner,\n.mkdf-image-gallery.mkdf-ig-masonry-type .mkdf-ig-image.mkdf-fixed-masonry-item a {\n  height: 100%;\n}\n\n.mkdf-image-gallery.mkdf-image-behavior-custom-link .mkdf-ig-image a:hover:after {\n  opacity: 1;\n}\n\n.mkdf-image-gallery.mkdf-image-behavior-custom-link .mkdf-ig-image a:after {\n  position: absolute;\n  display: block;\n  width: 100%;\n  height: 100%;\n  top: 0;\n  left: 0;\n  content: '';\n  background-color: rgba(234, 61, 86, 0.4);\n  opacity: 0;\n  -webkit-transition: opacity 0.2s ease-in-out;\n  -moz-transition: opacity 0.2s ease-in-out;\n  transition: opacity 0.2s ease-in-out;\n}\n\n.mkdf-image-gallery.mkdf-image-behavior-custom-link .mkdf-ig-image a img {\n  border-radius: 10px;\n}\n\n.mkdf-image-gallery.mkdf-image-behavior-custom-link .mkdf-ig-image a:after {\n  border-radius: 10px;\n}\n\n.mkdf-image-gallery.mkdf-image-behavior-custom-link.mkdf-ig-carousel-type .mkdf-ig-image {\n  -webkit-transition: all 0.3s ease-in-out;\n  -moz-transition: all 0.3s ease-in-out;\n  transition: all 0.3s ease-in-out;\n}\n\n.mkdf-image-gallery.mkdf-image-behavior-custom-link.mkdf-ig-carousel-type .mkdf-ig-image a:after {\n  display: none;\n}\n\n.mkdf-image-gallery.mkdf-image-behavior-custom-link.mkdf-ig-carousel-type .mkdf-ig-image:hover {\n  box-shadow: 6px 36px 67px -27px rgba(0, 0, 0, 0.1);\n}\n\n.mkdf-image-gallery.mkdf-image-behavior-custom-link .owl-dot span {\n  background: rgba(0, 0, 0, 0.25);\n  border: 0;\n}\n\n.mkdf-image-gallery.mkdf-image-behavior-custom-link .owl-dot.active span {\n  background: rgba(0, 0, 0, 0.5);\n}\n\n.mkdf-image-gallery.mkdf-image-behavior-lightbox .mkdf-ig-image {\n  position: relative;\n}\n\n.mkdf-image-gallery.mkdf-image-behavior-lightbox .mkdf-ig-image a:after {\n  position: absolute;\n  display: block;\n  width: 100%;\n  height: 100%;\n  top: 0;\n  left: 0;\n  content: '';\n  background-color: #74cccd;\n  opacity: 0;\n  -webkit-transition: opacity 0.2s ease-in-out;\n  -moz-transition: opacity 0.2s ease-in-out;\n  transition: opacity 0.2s ease-in-out;\n}\n\n.mkdf-image-gallery.mkdf-image-behavior-lightbox .mkdf-ig-image a:before {\n  content: \"\\4c\";\n  font-family: ElegantIcons;\n  position: absolute;\n  font-size: 72px;\n  color: #fff;\n  opacity: 0;\n  text-align: center;\n  box-sizing: border-box;\n  top: calc(50% - 36px);\n  left: calc(50% - 36px);\n  right: 0;\n  z-index: 9;\n  -webkit-transition: all 0.4s ease-in-out;\n  -moz-transition: all 0.4s ease-in-out;\n  transition: all 0.4s ease-in-out;\n  transform-origin: 50% 50%;\n  width: 72px;\n  height: 72px;\n  line-height: 72px;\n  display: block;\n}\n\n.mkdf-image-gallery.mkdf-image-behavior-lightbox .mkdf-ig-image:hover a:after {\n  opacity: 1;\n}\n\n.mkdf-image-gallery.mkdf-image-behavior-lightbox .mkdf-ig-image:hover a:before {\n  opacity: 1;\n  -webkit-transform: rotate(90deg);\n  -moz-transform: rotate(90deg);\n  transform: rotate(90deg);\n}\n\n.touch .mkdf-image-gallery.mkdf-image-behavior-zoom .mkdf-ig-image {\n  cursor: pointer;\n}\n\n.mkdf-image-gallery.mkdf-image-behavior-zoom .mkdf-ig-image:hover img {\n  -webkit-transform: scale(1.04);\n  -moz-transform: scale(1.04);\n  transform: scale(1.04);\n}\n\n.mkdf-image-gallery.mkdf-image-behavior-zoom .mkdf-ig-image .mkdf-ig-image-inner {\n  overflow: hidden;\n}\n\n.mkdf-image-gallery.mkdf-image-behavior-zoom .mkdf-ig-image img {\n  -webkit-transform: scale(1);\n  -moz-transform: scale(1);\n  transform: scale(1);\n  -webkit-transition: -webkit-transform 0.3s ease-in-out;\n  -moz-transition: -moz-transform 0.3s ease-in-out;\n  transition: transform 0.3s ease-in-out;\n}\n\n.mkdf-image-gallery.mkdf-image-behavior-grayscale .mkdf-ig-image {\n  overflow: hidden;\n}\n\n.touch .mkdf-image-gallery.mkdf-image-behavior-grayscale .mkdf-ig-image {\n  cursor: pointer;\n}\n\n.mkdf-image-gallery.mkdf-image-behavior-grayscale .mkdf-ig-image:hover img {\n  -webkit-filter: grayscale(0);\n  filter: none;\n}\n\n.mkdf-image-gallery.mkdf-image-behavior-grayscale .mkdf-ig-image img {\n  filter: url(\"img/desaturate.svg#grayscale\");\n  -webkit-filter: grayscale(100%);\n  -moz-filter: grayscale(100%);\n  filter: gray;\n  filter: grayscale(100%);\n  -webkit-transition: all 0.3s ease-in-out;\n  -moz-transition: all 0.3s ease-in-out;\n  transition: all 0.3s ease-in-out;\n}\n\n/* ==========================================================================\n   Image Gallery shortcode style - end\n   ========================================================================== */\n/* ==========================================================================\n   Image With Text shortcode style - begin\n   ========================================================================== */\n.mkdf-image-with-text-holder {\n  position: relative;\n  display: inline-block;\n  width: 100%;\n  vertical-align: middle;\n  -webkit-transition: all 0.3s ease-in-out;\n  -moz-transition: all 0.3s ease-in-out;\n  transition: all 0.3s ease-in-out;\n  /***** Custom Link Behavior Style - begin *****/\n  /***** Custom Link Behavior Style - end *****/\n  /***** Lightbox Behavior Style - begin *****/\n  /***** Lightbox Behavior Style - end *****/\n  /***** Zoom Behavior Style - begin *****/\n  /***** Zoom Behavior Style - end *****/\n  /***** Grayscale Behavior Style - begin *****/\n  /***** Grayscale Behavior Style - end *****/\n}\n\n.mkdf-image-with-text-holder.mkdf-has-shadow .mkdf-iwt-image {\n  box-shadow: 0 0 42.85px 7.15px rgba(0, 0, 0, 0.09);\n}\n\n.mkdf-image-with-text-holder .mkdf-iwt-image {\n  position: relative;\n  display: inline-block;\n  width: 100%;\n  vertical-align: middle;\n  width: auto;\n}\n\n.mkdf-image-with-text-holder .mkdf-iwt-image a, .mkdf-image-with-text-holder .mkdf-iwt-image img {\n  position: relative;\n  display: block;\n  -webkit-transition: all 0.3s ease;\n  -moz-transition: all 0.3s ease;\n  transition: all 0.3s ease;\n}\n\n.mkdf-image-with-text-holder .mkdf-iwt-image .mkdf-icon-shortcode {\n  position: absolute;\n  right: -20px;\n  top: -15px;\n  z-index: 5;\n  font-size: 20px;\n  -webkit-transition: all 0.3s ease-out;\n  -moz-transition: all 0.3s ease-out;\n  transition: all 0.3s ease-out;\n}\n\n.mkdf-image-with-text-holder .mkdf-iwt-image:hover .mkdf-icon-shortcode {\n  animation: mkdfPulsesmallfirst 1.8s infinite;\n}\n\n.mkdf-image-with-text-holder .mkdf-iwt-text-holder {\n  position: relative;\n  display: inline-block;\n  width: 100%;\n  vertical-align: middle;\n}\n\n.mkdf-image-with-text-holder .mkdf-iwt-title {\n  margin: 40px 0 0;\n}\n\n.mkdf-image-with-text-holder .mkdf-iwt-text {\n  margin: 18px 0 0;\n  line-height: 1.8em;\n}\n\n.mkdf-image-with-text-holder.mkdf-image-behavior-custom-link:hover {\n  -webkit-transform: translate(0, -8px);\n  -webkit-transform: translate(0, -8px);\n  -moz-transform: translate(0, -8px);\n  transform: translate(0, -8px);\n}\n\n.mkdf-image-with-text-holder.mkdf-image-behavior-lightbox .mkdf-iwt-image a:hover:after {\n  opacity: 1;\n}\n\n.mkdf-image-with-text-holder.mkdf-image-behavior-lightbox .mkdf-iwt-image a:after {\n  position: absolute;\n  display: block;\n  width: 100%;\n  height: 100%;\n  top: 0;\n  left: 0;\n  content: '';\n  background-color: rgba(234, 61, 86, 0.4);\n  opacity: 0;\n  -webkit-transition: opacity 0.2s ease-in-out;\n  -moz-transition: opacity 0.2s ease-in-out;\n  transition: opacity 0.2s ease-in-out;\n}\n\n.mkdf-image-with-text-holder.mkdf-image-behavior-zoom .mkdf-iwt-image {\n  overflow: hidden;\n}\n\n.touch .mkdf-image-with-text-holder.mkdf-image-behavior-zoom .mkdf-iwt-image {\n  cursor: pointer;\n}\n\n.mkdf-image-with-text-holder.mkdf-image-behavior-zoom .mkdf-iwt-image:hover img {\n  -webkit-transform: scale(1.04);\n  -moz-transform: scale(1.04);\n  transform: scale(1.04);\n}\n\n.mkdf-image-with-text-holder.mkdf-image-behavior-zoom .mkdf-iwt-image img {\n  -webkit-transform: scale(1);\n  -moz-transform: scale(1);\n  transform: scale(1);\n  -webkit-transition: -webkit-transform 0.3s ease-in-out;\n  -moz-transition: -moz-transform 0.3s ease-in-out;\n  transition: transform 0.3s ease-in-out;\n}\n\n.mkdf-image-with-text-holder.mkdf-image-behavior-grayscale .mkdf-iwt-image {\n  overflow: hidden;\n}\n\n.touch .mkdf-image-with-text-holder.mkdf-image-behavior-grayscale .mkdf-iwt-image {\n  cursor: pointer;\n}\n\n.mkdf-image-with-text-holder.mkdf-image-behavior-grayscale .mkdf-iwt-image:hover img {\n  -webkit-filter: grayscale(0);\n  filter: none;\n}\n\n.mkdf-image-with-text-holder.mkdf-image-behavior-grayscale .mkdf-iwt-image img {\n  filter: url(\"img/desaturate.svg#grayscale\");\n  -webkit-filter: grayscale(100%);\n  -moz-filter: grayscale(100%);\n  filter: gray;\n  filter: grayscale(100%);\n  -webkit-transition: all 0.3s ease-in-out;\n  -moz-transition: all 0.3s ease-in-out;\n  transition: all 0.3s ease-in-out;\n}\n\n/***** Landing Appeared Item start *****/\n.custom-image-padding-row {\n  -webkit-transition: opacity 0.22s cubic-bezier(0.23, 1, 0.32, 1), -webkit-transform 0s cubic-bezier(0.23, 1, 0.32, 1);\n  transition: opacity 0.22s cubic-bezier(0.23, 1, 0.32, 1), transform 0s cubic-bezier(0.23, 1, 0.32, 1);\n}\n\n.custom-image-padding-row .mkdf-image-with-text-holder .mkdf-iwt-image {\n  opacity: 0;\n  -webkit-transform: scale(0.6);\n  -moz-transform: scale(0.6);\n  transform: scale(0.6);\n  -webkit-transition: -webkit-transform 0.6s cubic-bezier(0.18, 0.89, 0.32, 1), opacity 0.2s ease-out;\n  transition: transform 0.4s cubic-bezier(0.18, 0.89, 0.32, 1), opacity 0.2s ease-out;\n}\n\n.custom-image-padding-row .mkdf-image-with-text-holder .mkdf-iwt-image.mkdf-landing-appeared {\n  opacity: 1;\n  -webkit-transform: scale(1);\n  -moz-transform: scale(1);\n  transform: scale(1);\n}\n\n/***** Landing Appeared Item end *****/\n/* ==========================================================================\n   Image With Text shortcode style - end\n   ========================================================================== */\n/* ==========================================================================\n   Pie Chart shortcode style - begin\n   ========================================================================== */\n.mkdf-pie-chart-holder {\n  position: relative;\n  display: inline-block;\n  width: 100%;\n  vertical-align: middle;\n  opacity: 0;\n  -webkit-transition: opacity 0.2s ease-in;\n  -moz-transition: opacity 0.2s ease-in;\n  transition: opacity 0.2s ease-in;\n}\n\n.mkdf-pie-chart-holder .mkdf-pc-percentage {\n  position: relative;\n  display: block;\n  height: 176px;\n  width: 176px;\n  line-height: 176px;\n  text-align: center;\n  margin: 0 auto;\n}\n\n.mkdf-pie-chart-holder .mkdf-pc-percentage canvas {\n  position: absolute;\n  top: 0;\n  left: 0;\n}\n\n.mkdf-pie-chart-holder .mkdf-pc-percentage .mkdf-pc-percent {\n  position: relative;\n  display: inline-block;\n  vertical-align: middle;\n  color: #1b2c58;\n  font-size: 30px;\n  line-height: inherit;\n  font-weight: 600;\n  font-family: \"Josefin Sans\", sans-serif;\n}\n\n.mkdf-pie-chart-holder .mkdf-pc-percentage .mkdf-pc-percent:after {\n  position: relative;\n  content: '%';\n  font-size: 30px;\n}\n\n.mkdf-pie-chart-holder .mkdf-pc-text-holder {\n  position: relative;\n  display: inline-block;\n  width: 100%;\n  vertical-align: middle;\n  text-align: center;\n  margin: 38px 0 0;\n}\n\n.mkdf-pie-chart-holder .mkdf-pc-text-holder .mkdf-pc-title {\n  margin: 0;\n}\n\n.mkdf-pie-chart-holder .mkdf-pc-text-holder .mkdf-pc-text {\n  margin: 7px 0 0;\n  font-family: \"Josefin Sans\", sans-serif;\n  font-size: 12px;\n  font-weight: 700;\n  letter-spacing: 0.09em;\n}\n\n/* ==========================================================================\n   Pie Chart shortcode style - end\n   ========================================================================== */\n/* ==========================================================================\n   Pricing Tables shortcode style - begin\n   ========================================================================== */\n.mkdf-pricing-tables {\n  position: relative;\n  display: inline-block;\n  width: 100%;\n  vertical-align: middle;\n}\n\n@media screen and (max-width: 680px) {\n  .mkdf-price-table {\n    padding: 0 0 37px !important;\n  }\n  .mkdf-price-table:last-child {\n    padding-bottom: 0 !important;\n  }\n}\n\n.mkdf-price-table .mkdf-pt-inner {\n  position: relative;\n  display: inline-block;\n  width: 100%;\n  vertical-align: middle;\n  border: 2px solid rgba(237, 238, 239, 0.6);\n  box-sizing: border-box;\n}\n\n.mkdf-price-table .mkdf-pt-inner > ul {\n  list-style: none;\n  margin: 0;\n  padding: 0;\n}\n\n.mkdf-price-table .mkdf-pt-inner > ul > li {\n  margin: 0;\n  text-align: center;\n  box-sizing: border-box;\n}\n\n.mkdf-price-table .mkdf-pt-inner > ul > li.mkdf-pt-title-holder {\n  padding: 10px 20px;\n  position: relative;\n  height: 203px;\n  color: #1b2c58;\n  font-size: 18px;\n  line-height: 26px;\n  font-weight: 600;\n}\n\n.mkdf-price-table .mkdf-pt-inner > ul > li.mkdf-pt-title-holder .mkdf-pt-active-title {\n  display: block;\n  font-family: \"Josefin Sans\", sans-serif;\n  color: #ea3d56;\n  font-size: 14px;\n  font-weight: 700;\n  letter-spacing: .1em;\n  text-transform: uppercase;\n  padding: 15px 0 17px;\n}\n\n.mkdf-price-table .mkdf-pt-inner > ul > li.mkdf-pt-title-holder .mkdf-pt-title {\n  display: block;\n  font-size: 36px;\n  font-family: \"Josefin Sans\", sans-serif;\n  letter-spacing: -.025em;\n  font-weight: 600;\n  padding: 28px 0 22px;\n  box-sizing: border-box;\n}\n\n.mkdf-price-table .mkdf-pt-inner > ul > li.mkdf-pt-content > ul {\n  list-style: none;\n  margin: 0;\n  padding: 0;\n  display: flex;\n  flex-direction: column;\n}\n\n.mkdf-price-table .mkdf-pt-inner > ul > li.mkdf-pt-content > ul > li {\n  padding: 20px;\n  line-height: 33px;\n  border-top: 2px solid rgba(225, 225, 225, 0.3);\n  font-size: 20px;\n  font-weight: 600;\n  color: #1b2c58;\n  letter-spacing: -.025em;\n}\n\n.mkdf-price-table .mkdf-pt-inner > ul > li.mkdf-pt-content > ul > li:last-child {\n  border-bottom: 2px solid rgba(225, 225, 225, 0.3);\n}\n\n.mkdf-price-table .mkdf-pt-inner > ul > li.mkdf-pt-prices {\n  position: relative;\n  padding: 33px 15px 28px;\n  border-bottom: 2px solid rgba(225, 225, 225, 0.3);\n}\n\n.mkdf-price-table .mkdf-pt-inner > ul > li.mkdf-pt-prices .mkdf-pt-value {\n  position: relative;\n  display: inline-block;\n  vertical-align: middle;\n  font-size: 60px;\n  font-weight: 600;\n  line-height: 1em;\n  color: #1b2c58;\n  font-family: \"Josefin Sans\", sans-serif;\n}\n\n.mkdf-price-table .mkdf-pt-inner > ul > li.mkdf-pt-prices .mkdf-pt-price {\n  position: relative;\n  display: inline-block;\n  vertical-align: middle;\n  font-size: 60px;\n  font-weight: 600;\n  line-height: 1em;\n  color: #1b2c58;\n  font-family: \"Josefin Sans\", sans-serif;\n}\n\n.mkdf-price-table .mkdf-pt-inner > ul > li.mkdf-pt-prices .mkdf-pt-mark {\n  position: relative;\n  display: block;\n  font-size: 14px;\n  font-weight: 600;\n  line-height: 1em;\n  color: rgba(204, 204, 204, 0.8);\n  font-family: \"Josefin Sans\", sans-serif;\n  letter-spacing: .1em;\n  margin: 12px 0 0;\n}\n\n.mkdf-price-table .mkdf-pt-inner > ul > li.mkdf-pt-button {\n  padding: 0;\n}\n\n.mkdf-price-table .mkdf-pt-inner > ul > li.mkdf-pt-button a {\n  padding: 24px 0;\n  width: 100%;\n  border: none !important;\n  color: #1b2c58;\n}\n\n.mkdf-price-table .mkdf-pt-inner > ul > li.mkdf-pt-button a.mkdf-btn-solid {\n  color: #fff !important;\n}\n\n.mkdf-pt-active-item.mkdf-price-table {\n  -webkit-transform: translateY(-85px);\n  -moz-transform: translateY(-85px);\n  transform: translateY(-85px);\n  z-index: 10;\n}\n\n@media screen and (max-width: 680px) {\n  .mkdf-pt-active-item.mkdf-price-table {\n    -webkit-transform: none;\n    -moz-transform: none;\n    transform: none;\n  }\n}\n\n.mkdf-pt-active-item.mkdf-price-table .mkdf-pt-inner {\n  border-color: transparent;\n  border-left: 0;\n  border-right: 0;\n  border-bottom: 0;\n  width: calc(100% + 6px);\n  margin: 0 -3px;\n}\n\n@media screen and (max-width: 680px) {\n  .mkdf-pt-active-item.mkdf-price-table .mkdf-pt-inner {\n    width: 100%;\n    margin: 0;\n  }\n}\n\n.mkdf-pt-active-item.mkdf-price-table .mkdf-pt-inner > ul > li.mkdf-pt-title-holder {\n  height: 288px;\n}\n\n.mkdf-pt-active-item.mkdf-price-table .mkdf-pt-inner > ul > li.mkdf-pt-title-holder .mkdf-pt-title {\n  padding: 0 0 22px;\n}\n\n.mkdf-pt-active-item.mkdf-price-table .mkdf-pt-inner > ul > li.mkdf-pt-button {\n  padding: 0;\n}\n\n.mkdf-pt-active-item.mkdf-price-table .mkdf-pt-inner > ul > li.mkdf-pt-button a {\n  padding: 26px 0;\n  margin-top: -2px;\n}\n\n/* ==========================================================================\n   Custom left menu style - start\n   ========================================================================== */\n.mkdf-custom-opacity {\n  -webkit-transition: opacity 0.22s cubic-bezier(0.23, 1, 0.32, 1), -webkit-transform 0s cubic-bezier(0.23, 1, 0.32, 1);\n  transition: opacity 0.22s cubic-bezier(0.23, 1, 0.32, 1), transform 0s cubic-bezier(0.23, 1, 0.32, 1);\n}\n\n.mkdf-custom-opacity h5 {\n  -webkit-transition: all 0.3s ease-in-out;\n  -moz-transition: all 0.3s ease-in-out;\n  transition: all 0.3s ease-in-out;\n  opacity: 0;\n  -webkit-transition: -webkit-transform 0.6s cubic-bezier(0.18, 0.89, 0.32, 1), opacity 0.2s ease-out;\n  transition: transform 0.4s cubic-bezier(0.18, 0.89, 0.32, 1), opacity 0.2s ease-out;\n}\n\n.mkdf-custom-opacity h5.mkdf-appeared {\n  opacity: 1;\n}\n\n.mkdf-custom-opacity h5:hover {\n  opacity: 0.6;\n}\n\n/* ==========================================================================\n   Custom left menu style - start\n   ========================================================================== */\n/* ==========================================================================\n   Pricing Tables shortcode style - end\n   ========================================================================== */\n/* ==========================================================================\n   Process shortcode style - begin\n   ========================================================================== */\n.mkdf-process-holder {\n  position: relative;\n  display: inline-block;\n  width: 100%;\n  vertical-align: middle;\n}\n\n.mkdf-process-holder.mkdf-two-columns .mkdf-mark-horizontal-holder .mkdf-process-mark {\n  width: 50%;\n}\n\n.mkdf-process-holder.mkdf-two-columns .mkdf-mark-vertical-holder .mkdf-process-mark {\n  height: 50%;\n}\n\n.mkdf-process-holder.mkdf-two-columns .mkdf-process-item {\n  width: 50%;\n}\n\n.mkdf-process-holder.mkdf-three-columns .mkdf-mark-horizontal-holder .mkdf-process-mark {\n  width: 33.33333%;\n}\n\n.mkdf-process-holder.mkdf-three-columns .mkdf-mark-vertical-holder .mkdf-process-mark {\n  height: 33.33333%;\n}\n\n.mkdf-process-holder.mkdf-three-columns .mkdf-process-item {\n  width: 33.33333%;\n}\n\n.mkdf-process-holder.mkdf-four-columns .mkdf-mark-horizontal-holder .mkdf-process-mark {\n  width: 25%;\n}\n\n.mkdf-process-holder.mkdf-four-columns .mkdf-mark-vertical-holder .mkdf-process-mark {\n  height: 25%;\n}\n\n.mkdf-process-holder.mkdf-four-columns .mkdf-process-item {\n  width: 25%;\n}\n\n.mkdf-process-holder.mkdf-process-appeared .mkdf-process-circle {\n  opacity: 1;\n  -webkit-transform: scale(1);\n  -moz-transform: scale(1);\n  transform: scale(1);\n}\n\n.mkdf-process-holder.mkdf-process-appeared .mkdf-mark-horizontal-holder .mkdf-process-line {\n  width: 100%;\n}\n\n.mkdf-process-holder.mkdf-process-appeared .mkdf-mark-vertical-holder .mkdf-process-line {\n  height: 100%;\n}\n\n.mkdf-process-holder.mkdf-process-appeared .mkdf-process-item {\n  opacity: 1;\n}\n\n.mkdf-process-holder .mkdf-mark-horizontal-holder {\n  position: relative;\n  display: inline-block;\n  width: 100%;\n  vertical-align: middle;\n  clear: both;\n}\n\n.mkdf-process-holder .mkdf-mark-horizontal-holder .mkdf-process-mark {\n  float: left;\n}\n\n.mkdf-process-holder .mkdf-mark-horizontal-holder .mkdf-process-line {\n  top: 50%;\n  left: 50%;\n  width: 0;\n  height: 1px;\n  -webkit-transition: width 0.4s ease 0.1s;\n  -moz-transition: width 0.4s ease 0.1s;\n  transition: width 0.4s ease 0.1s;\n}\n\n.mkdf-process-holder .mkdf-mark-vertical-holder {\n  position: absolute;\n  top: 26px;\n  left: 0;\n  display: none;\n  width: 46px;\n  height: 100%;\n}\n\n.mkdf-process-holder .mkdf-mark-vertical-holder .mkdf-process-line {\n  top: 23px;\n  left: 50%;\n  width: 1px;\n  height: 0;\n  -webkit-transition: height 0.4s ease 0.1s;\n  -moz-transition: height 0.4s ease 0.1s;\n  transition: height 0.4s ease 0.1s;\n}\n\n.mkdf-process-holder .mkdf-process-mark {\n  position: relative;\n  display: inline-block;\n  vertical-align: top;\n  text-align: center;\n}\n\n.mkdf-process-holder .mkdf-process-mark:last-child .mkdf-process-line {\n  display: none;\n}\n\n.mkdf-process-holder .mkdf-process-mark:nth-child(2) .mkdf-process-circle {\n  -webkit-transition-delay: 0.5s;\n  -moz-transition-delay: 0.5s;\n  transition-delay: 0.5s;\n}\n\n.mkdf-process-holder .mkdf-process-mark:nth-child(2) .mkdf-process-line {\n  -webkit-transition-delay: 0.6s;\n  -moz-transition-delay: 0.6s;\n  transition-delay: 0.6s;\n}\n\n.mkdf-process-holder .mkdf-process-mark:nth-child(3) .mkdf-process-circle {\n  -webkit-transition-delay: 1s;\n  -moz-transition-delay: 1s;\n  transition-delay: 1s;\n}\n\n.mkdf-process-holder .mkdf-process-mark:nth-child(3) .mkdf-process-line {\n  -webkit-transition-delay: 1.2s;\n  -moz-transition-delay: 1.2s;\n  transition-delay: 1.2s;\n}\n\n.mkdf-process-holder .mkdf-process-mark:nth-child(4) .mkdf-process-circle {\n  -webkit-transition-delay: 1.5s;\n  -moz-transition-delay: 1.5s;\n  transition-delay: 1.5s;\n}\n\n.mkdf-process-holder .mkdf-process-mark:nth-child(4) .mkdf-process-line {\n  -webkit-transition-delay: 1.8s;\n  -moz-transition-delay: 1.8s;\n  transition-delay: 1.8s;\n}\n\n.mkdf-process-holder .mkdf-process-circle {\n  position: relative;\n  display: inline-block;\n  vertical-align: top;\n  width: 46px;\n  height: 46px;\n  font-size: 18px;\n  line-height: 46px;\n  font-weight: 700;\n  color: #fff;\n  background-color: #ea3d56;\n  border-radius: 100%;\n  opacity: 0;\n  -webkit-transition: opacity .2s ease, -webkit-transform .3s ease;\n  -moz-transition: opacity .2s ease, -moz-transform .3s ease;\n  transition: opacity .2s ease, transform .3s ease;\n  -webkit-transform: scale(0.6);\n  -moz-transform: scale(0.6);\n  transform: scale(0.6);\n}\n\n.mkdf-process-holder .mkdf-process-line {\n  position: absolute;\n  background-color: #ea3d56;\n}\n\n.mkdf-process-holder .mkdf-process-inner {\n  margin: 0 -15px;\n}\n\n.mkdf-process-holder .mkdf-process-item {\n  position: relative;\n  display: inline-block;\n  vertical-align: top;\n  float: left;\n  padding: 0 15px;\n  opacity: 0;\n  text-align: center;\n  box-sizing: border-box;\n  -webkit-transition: opacity 0.2s ease;\n  -moz-transition: opacity 0.2s ease;\n  transition: opacity 0.2s ease;\n}\n\n.mkdf-process-holder .mkdf-process-item:nth-child(2) {\n  -webkit-transition-delay: 0.5s;\n  -moz-transition-delay: 0.5s;\n  transition-delay: 0.5s;\n}\n\n.mkdf-process-holder .mkdf-process-item:nth-child(3) {\n  -webkit-transition-delay: 1s;\n  -moz-transition-delay: 1s;\n  transition-delay: 1s;\n}\n\n.mkdf-process-holder .mkdf-process-item:nth-child(4) {\n  -webkit-transition-delay: 1.5s;\n  -moz-transition-delay: 1.5s;\n  transition-delay: 1.5s;\n}\n\n.mkdf-process-holder .mkdf-pi-content {\n  position: relative;\n  display: inline-block;\n  width: 100%;\n  vertical-align: middle;\n  margin: 26px 0 10px;\n}\n\n.mkdf-process-holder .mkdf-pi-title {\n  margin: 0;\n}\n\n.mkdf-process-holder .mkdf-pi-text {\n  margin: 11px 0 0;\n}\n\n/* ==========================================================================\n   Process shortcode style - end\n   ========================================================================== */\n/* ==========================================================================\n   Progress Bar shortcode style - begin\n   ========================================================================== */\n.mkdf-progress-bar {\n  position: relative;\n  display: inline-block;\n  width: 100%;\n  vertical-align: middle;\n}\n\n.mkdf-progress-bar.mkdf-pb-percent-floating {\n  width: 100%;\n  height: 100%;\n}\n\n.mkdf-progress-bar.mkdf-pb-percent-floating .mkdf-pb-percent {\n  position: absolute;\n  left: 0;\n  right: auto;\n  bottom: 0;\n  -webkit-transform: translateX(-50%);\n  -moz-transform: translateX(-50%);\n  transform: translateX(-50%);\n}\n\n.mkdf-progress-bar .mkdf-pb-title-holder {\n  position: relative;\n  margin: 10px 0 6px;\n}\n\n.mkdf-progress-bar .mkdf-pb-title-holder .mkdf-pb-title {\n  position: relative;\n  display: inline-block;\n  vertical-align: middle;\n  z-index: 100;\n}\n\n.mkdf-progress-bar .mkdf-pb-percent {\n  position: absolute;\n  right: 0;\n  bottom: -2px;\n  width: auto;\n  display: inline-block;\n  vertical-align: middle;\n  opacity: 0;\n  z-index: 10;\n}\n\n.mkdf-progress-bar .mkdf-pb-percent:after {\n  content: '%';\n}\n\n.mkdf-progress-bar .mkdf-pb-content-holder {\n  position: relative;\n  height: 5px;\n  overflow: hidden;\n  background-color: #ebebeb;\n}\n\n.mkdf-progress-bar .mkdf-pb-content-holder .mkdf-pb-content {\n  height: 5px;\n  max-width: 100%;\n  overflow: hidden;\n  background-color: #ea3d56;\n}\n\n/* ==========================================================================\n   Progress Bar shortcode style - end\n   ========================================================================== */\n/* ==========================================================================\n   Roadmap shortcode style - begin\n   ========================================================================== */\n.mkdf-roadmap {\n  position: relative;\n  display: inline-block;\n  width: 100%;\n  vertical-align: middle;\n  padding: 200px 0;\n  overflow: hidden;\n}\n\n.mkdf-roadmap .mkdf-roadmap-holder {\n  overflow: hidden;\n}\n\n.mkdf-roadmap .mkdf-roadmap-line {\n  position: relative;\n  width: 0%;\n  height: 3px;\n  background-color: #f6f6f6;\n}\n\n.mkdf-roadmap .mkdf-roadmap-line .mkdf-rl-arrow-left,\n.mkdf-roadmap .mkdf-roadmap-line .mkdf-rl-arrow-right {\n  position: absolute;\n  top: 50%;\n  font-size: 30px;\n  color: #ea3d56;\n  cursor: pointer;\n  -webkit-transform: translateY(-50%);\n  -moz-transform: translateY(-50%);\n  transform: translateY(-50%);\n  z-index: 50;\n}\n\n.mkdf-roadmap .mkdf-roadmap-line .mkdf-rl-arrow-left {\n  left: -2px;\n  padding: 10px 10px 10px 0;\n}\n\n.mkdf-roadmap .mkdf-roadmap-line .mkdf-rl-arrow-left svg {\n  -webkit-transform: rotate(180deg);\n  -moz-transform: rotate(180deg);\n  transform: rotate(180deg);\n}\n\n.mkdf-roadmap .mkdf-roadmap-line .mkdf-rl-arrow-right {\n  right: -2px;\n  padding: 10px 0 10px 10px;\n}\n\n.mkdf-roadmap .mkdf-roadmap-inner-holder {\n  -webkit-transition: all 0.2s ease-in-out;\n  -moz-transition: all 0.2s ease-in-out;\n  transition: all 0.2s ease-in-out;\n}\n\n.mkdf-roadmap .mkdf-roadmap-item {\n  position: relative;\n  float: left;\n  text-align: center;\n  -webkit-transform: translateY(-17px);\n  -moz-transform: translateY(-17px);\n  transform: translateY(-17px);\n}\n\n.mkdf-roadmap .mkdf-roadmap-item .mkdf-roadmap-item-circle-holder {\n  font-size: 0;\n}\n\n.mkdf-roadmap .mkdf-roadmap-item .mkdf-roadmap-item-before-circle,\n.mkdf-roadmap .mkdf-roadmap-item .mkdf-roadmap-item-after-circle {\n  display: inline-block;\n  vertical-align: middle;\n  width: 0;\n  height: 3px;\n  background-color: #dfdfdf;\n  position: absolute;\n  top: 14px;\n}\n\n.mkdf-roadmap .mkdf-roadmap-item .mkdf-roadmap-item-before-circle {\n  left: 0;\n}\n\n.mkdf-roadmap .mkdf-roadmap-item .mkdf-roadmap-item-after-circle {\n  left: calc(50% - -11px);\n}\n\n.mkdf-roadmap .mkdf-roadmap-item .mkdf-roadmap-item-circle {\n  display: inline-block;\n  vertical-align: middle;\n  width: 20px;\n  height: 20px;\n  border-radius: 50%;\n  background-color: #f6f6f6;\n  box-shadow: inset 0px 0px 0px 6px #ea3d56;\n}\n\n.mkdf-roadmap .mkdf-roadmap-item .mkdf-roadmap-item-stage-title-holder {\n  position: absolute;\n  left: 0;\n  width: 100%;\n}\n\n.mkdf-roadmap .mkdf-roadmap-item .mkdf-roadmap-item-stage-title-holder .mkdf-ris-title {\n  color: #1b2c58;\n  font-size: 20px;\n  font-family: \"Josefin Sans\", sans-serif;\n  font-weight: 600;\n}\n\n.mkdf-roadmap .mkdf-roadmap-item .mkdf-roadmap-item-content-holder {\n  position: absolute;\n  left: 4%;\n  width: 92%;\n  text-align: left;\n  padding: 30px 36px;\n  box-sizing: border-box;\n  box-shadow: 0px 5px 31px 0px rgba(0, 0, 0, 0.2);\n  background-color: #fff;\n  border-radius: 5px;\n  z-index: -1;\n}\n\n@media screen and (max-width: 1440px) and (min-width: 1280px) {\n  .mkdf-roadmap .mkdf-roadmap-item .mkdf-roadmap-item-content-holder {\n    padding: 20px;\n  }\n}\n\n.mkdf-roadmap .mkdf-roadmap-item .mkdf-roadmap-item-content-holder .mkdf-ric-title {\n  margin: 0 0 14px;\n}\n\n@media only screen and (max-width: 480px) {\n  .mkdf-roadmap .mkdf-roadmap-item .mkdf-roadmap-item-content-holder .mkdf-ric-title {\n    text-align: center;\n  }\n}\n\n@media only screen and (max-width: 480px) {\n  .mkdf-roadmap .mkdf-roadmap-item .mkdf-roadmap-item-content-holder .mkdf-ric-content {\n    text-align: center;\n  }\n}\n\n.mkdf-roadmap .mkdf-roadmap-item .mkdf-roadmap-item-content-holder:after {\n  content: '';\n  position: absolute;\n  left: 50%;\n  width: 3px;\n  height: 70px;\n  background-color: #dfdfdf;\n  -webkit-transform: translateX(-50%);\n  -moz-transform: translateX(-50%);\n  transform: translateX(-50%);\n  z-index: -1;\n}\n\n.mkdf-roadmap .mkdf-roadmap-item.mkdf-roadmap-item-above .mkdf-roadmap-item-stage-title-holder {\n  top: 35px;\n}\n\n.mkdf-roadmap .mkdf-roadmap-item.mkdf-roadmap-item-above .mkdf-roadmap-item-content-holder {\n  bottom: 75px;\n}\n\n.mkdf-roadmap .mkdf-roadmap-item.mkdf-roadmap-item-above .mkdf-roadmap-item-content-holder:after {\n  top: 100%;\n}\n\n.mkdf-roadmap .mkdf-roadmap-item.mkdf-roadmap-item-below .mkdf-roadmap-item-stage-title-holder {\n  bottom: 32px;\n}\n\n.mkdf-roadmap .mkdf-roadmap-item.mkdf-roadmap-item-below .mkdf-roadmap-item-content-holder {\n  top: 75px;\n}\n\n.mkdf-roadmap .mkdf-roadmap-item.mkdf-roadmap-item-below .mkdf-roadmap-item-content-holder:after {\n  bottom: 100%;\n}\n\n.mkdf-roadmap .mkdf-roadmap-item.mkdf-roadmap-reached-item .mkdf-roadmap-item-before-circle {\n  background-color: #ea3d56;\n  left: 0;\n}\n\n.mkdf-roadmap .mkdf-roadmap-item.mkdf-roadmap-passed-item .mkdf-roadmap-item-before-circle,\n.mkdf-roadmap .mkdf-roadmap-item.mkdf-roadmap-passed-item .mkdf-roadmap-item-after-circle {\n  background-color: #ea3d56;\n}\n\n.mkdf-roadmap.mkdf-roadmap-skin-dark .mkdf-roadmap-item-content-holder {\n  background-color: #fff;\n}\n\n.mkdf-roadmap.mkdf-roadmap-skin-firstmain .mkdf-roadmap-item .mkdf-roadmap-item-content-holder {\n  background-color: #ea3d56;\n}\n\n.mkdf-roadmap.mkdf-roadmap-skin-firstmain .mkdf-roadmap-item .mkdf-roadmap-item-content-holder:after {\n  background-color: #ea3d56;\n}\n\n.mkdf-roadmap.mkdf-roadmap-skin-firstmain .mkdf-roadmap-item .mkdf-roadmap-item-content-holder .mkdf-ric-title {\n  color: #fff;\n}\n\n.mkdf-roadmap.mkdf-roadmap-skin-firstmain .mkdf-roadmap-item .mkdf-roadmap-item-content-holder .mkdf-ric-content {\n  color: #fff;\n}\n\n.mkdf-roadmap.mkdf-roadmap-skin-firstmain .mkdf-roadmap-item .mkdf-roadmap-item-before-circle,\n.mkdf-roadmap.mkdf-roadmap-skin-firstmain .mkdf-roadmap-item .mkdf-roadmap-item-after-circle {\n  background-color: rgba(234, 61, 86, 0.3);\n}\n\n.mkdf-roadmap.mkdf-roadmap-skin-firstmain .mkdf-roadmap-item .mkdf-roadmap-item-circle {\n  background-color: #fff;\n  box-shadow: inset 0px 0px 0px 6px #ea3d56;\n}\n\n.mkdf-roadmap.mkdf-roadmap-skin-firstmain .mkdf-roadmap-item.mkdf-roadmap-reached-item .mkdf-roadmap-item-before-circle {\n  background-color: #ea3d56;\n}\n\n.mkdf-roadmap.mkdf-roadmap-skin-firstmain .mkdf-roadmap-item.mkdf-roadmap-passed-item .mkdf-roadmap-item-before-circle,\n.mkdf-roadmap.mkdf-roadmap-skin-firstmain .mkdf-roadmap-item.mkdf-roadmap-passed-item .mkdf-roadmap-item-after-circle {\n  background-color: #ea3d56;\n}\n\n.mkdf-roadmap.mkdf-roadmap-skin-firstmain .mkdf-roadmap-item-stage-title-holder .mkdf-ris-title {\n  color: #ea3d56;\n}\n\n.mkdf-roadmap.mkdf-roadmap-skin-firstmain .mkdf-roadmap-line {\n  background-color: rgba(234, 61, 86, 0.3);\n}\n\n.mkdf-roadmap.mkdf-roadmap-skin-firstmain .mkdf-roadmap-line .mkdf-rl-arrow-left,\n.mkdf-roadmap.mkdf-roadmap-skin-firstmain .mkdf-roadmap-line .mkdf-rl-arrow-right {\n  color: #ea3d56;\n}\n\n.mkdf-roadmap.mkdf-roadmap-skin-light .mkdf-roadmap-item .mkdf-roadmap-item-content-holder {\n  background-color: #fff;\n}\n\n.mkdf-roadmap.mkdf-roadmap-skin-light .mkdf-roadmap-item .mkdf-roadmap-item-content-holder:after {\n  background-color: #fff;\n}\n\n.mkdf-roadmap.mkdf-roadmap-skin-light .mkdf-roadmap-item .mkdf-roadmap-item-before-circle,\n.mkdf-roadmap.mkdf-roadmap-skin-light .mkdf-roadmap-item .mkdf-roadmap-item-after-circle {\n  display: inline-block;\n  vertical-align: middle;\n  width: 0;\n  height: 3px;\n  background-color: rgba(255, 255, 255, 0.3);\n}\n\n.mkdf-roadmap.mkdf-roadmap-skin-light .mkdf-roadmap-item .mkdf-roadmap-item-circle {\n  background-color: #1f75ff;\n  box-shadow: inset 0px 0px 0px 6px #fff;\n}\n\n.mkdf-roadmap.mkdf-roadmap-skin-light .mkdf-roadmap-item.mkdf-roadmap-reached-item .mkdf-roadmap-item-before-circle {\n  background-color: #fff;\n}\n\n.mkdf-roadmap.mkdf-roadmap-skin-light .mkdf-roadmap-item.mkdf-roadmap-passed-item .mkdf-roadmap-item-before-circle,\n.mkdf-roadmap.mkdf-roadmap-skin-light .mkdf-roadmap-item.mkdf-roadmap-passed-item .mkdf-roadmap-item-after-circle {\n  background-color: #fff;\n}\n\n.mkdf-roadmap.mkdf-roadmap-skin-light .mkdf-roadmap-item-stage-title-holder .mkdf-ris-title {\n  color: #fff;\n}\n\n.mkdf-roadmap.mkdf-roadmap-skin-light .mkdf-roadmap-line {\n  background-color: rgba(255, 255, 255, 0.3);\n}\n\n.mkdf-roadmap.mkdf-roadmap-skin-light .mkdf-roadmap-line .mkdf-rl-arrow-left,\n.mkdf-roadmap.mkdf-roadmap-skin-light .mkdf-roadmap-line .mkdf-rl-arrow-right {\n  color: #fff;\n}\n\n/* ==========================================================================\n   Roadmap shortcode style - end\n   ========================================================================== */\n/* ==========================================================================\n   Section Title shortcode styles - begin\n   ========================================================================== */\n.mkdf-section-title-holder {\n  position: relative;\n  display: inline-block;\n  width: 100%;\n  vertical-align: middle;\n  box-sizing: border-box;\n}\n\n.mkdf-section-title-holder.mkdf-st-two-columns.mkdf-st-tiny-space .mkdf-st-inner {\n  margin: 0 -5px;\n}\n\n.mkdf-section-title-holder.mkdf-st-two-columns.mkdf-st-tiny-space .mkdf-st-title,\n.mkdf-section-title-holder.mkdf-st-two-columns.mkdf-st-tiny-space .mkdf-st-text {\n  padding: 0 5px;\n}\n\n.mkdf-section-title-holder.mkdf-st-two-columns.mkdf-st-small-space .mkdf-st-inner {\n  margin: 0 -10px;\n}\n\n.mkdf-section-title-holder.mkdf-st-two-columns.mkdf-st-small-space .mkdf-st-title,\n.mkdf-section-title-holder.mkdf-st-two-columns.mkdf-st-small-space .mkdf-st-text {\n  padding: 0 10px;\n}\n\n.mkdf-section-title-holder.mkdf-st-two-columns.mkdf-st-normal-space .mkdf-st-inner {\n  margin: 0 -15px;\n}\n\n.mkdf-section-title-holder.mkdf-st-two-columns.mkdf-st-normal-space .mkdf-st-title,\n.mkdf-section-title-holder.mkdf-st-two-columns.mkdf-st-normal-space .mkdf-st-text {\n  padding: 0 15px;\n}\n\n.mkdf-section-title-holder.mkdf-st-two-columns .mkdf-st-title,\n.mkdf-section-title-holder.mkdf-st-two-columns .mkdf-st-text {\n  position: relative;\n  display: inline-block;\n  vertical-align: middle;\n  width: 50%;\n  float: left;\n  margin: 0;\n  box-sizing: border-box;\n}\n\n.mkdf-section-title-holder.mkdf-st-two-columns.mkdf-st-title-left .mkdf-st-title {\n  text-align: right;\n}\n\n.mkdf-section-title-holder.mkdf-st-two-columns.mkdf-st-title-left .mkdf-st-text {\n  text-align: left;\n}\n\n.mkdf-section-title-holder.mkdf-st-two-columns.mkdf-st-title-right .mkdf-st-title {\n  float: right;\n  text-align: left;\n}\n\n.mkdf-section-title-holder.mkdf-st-two-columns.mkdf-st-title-right .mkdf-st-text {\n  text-align: right;\n}\n\n.mkdf-section-title-holder .mkdf-st-title {\n  display: block;\n  margin: 0;\n}\n\n.mkdf-section-title-holder .mkdf-st-title .mkdf-st-title-bold {\n  font-weight: 700;\n}\n\n.mkdf-section-title-holder .mkdf-st-title .mkdf-st-title-light {\n  font-weight: 300;\n}\n\n.mkdf-section-title-holder .mkdf-st-text {\n  display: block;\n  margin: 7px 0 0;\n  font-size: 18px;\n  line-height: 1.88em;\n}\n\n/* ==========================================================================\n   Section Title shortcode styles - end\n   ========================================================================== */\n/* ==========================================================================\n   Separator shortcode style - begin\n   ========================================================================== */\n.mkdf-separator-holder {\n  position: relative;\n  height: auto;\n  font-size: 0;\n  line-height: 1em;\n}\n\n.mkdf-separator-holder.mkdf-separator-center {\n  text-align: center;\n}\n\n.mkdf-separator-holder.mkdf-separator-left {\n  text-align: left;\n}\n\n.mkdf-separator-holder.mkdf-separator-right {\n  text-align: right;\n}\n\n.mkdf-separator-holder.mkdf-separator-full-width .mkdf-separator {\n  width: 100% !important;\n}\n\n.mkdf-separator {\n  position: relative;\n  display: inline-block;\n  vertical-align: middle;\n  border-bottom: 1px solid #ea3d56;\n  margin: 10px 0;\n}\n\n/* ==========================================================================\n   Separator shortcode style - end\n   ========================================================================== */\n/* ==========================================================================\n   Single Image shortcode style - begin\n   ========================================================================== */\n.mkdf-single-image-holder {\n  position: relative;\n  display: inline-block;\n  width: 100%;\n  vertical-align: middle;\n  /***** Custom Link Behavior Style - end *****/\n  /***** Lightbox Behavior Style - begin *****/\n  /***** Lightbox Behavior Style - end *****/\n  /***** Zoom Behavior Style - begin *****/\n  /***** Zoom Behavior Style - end *****/\n  /***** Grayscale Behavior Style - begin *****/\n  /***** Grayscale Behavior Style - end *****/\n  /***** Moving Behavior Style - begin *****/\n  /***** Moving Behavior Style - end *****/\n}\n\n.mkdf-single-image-holder.mkdf-has-shadow .mkdf-si-inner {\n  box-shadow: 0 0 4.85px 0.15px rgba(0, 0, 0, 0.09);\n}\n\n.mkdf-single-image-holder.mkdf-has-border .mkdf-si-inner {\n  border: 2px solid rgba(225, 225, 225, 0.3);\n  padding: 34px;\n}\n\n.mkdf-single-image-holder .mkdf-si-inner {\n  position: relative;\n  display: inline-block;\n  width: 100%;\n  vertical-align: middle;\n}\n\n.mkdf-single-image-holder .mkdf-si-inner a, .mkdf-single-image-holder .mkdf-si-inner img {\n  position: relative;\n  display: block;\n}\n\n.mkdf-tabs .mkdf-single-image-holder.mkdf-image-behavior-custom-link .mkdf-si-inner a:hover:after {\n  opacity: 1;\n}\n\n.mkdf-tabs .mkdf-single-image-holder.mkdf-image-behavior-custom-link .mkdf-si-inner a:after {\n  position: absolute;\n  display: block;\n  width: 100%;\n  height: 100%;\n  top: 0;\n  left: 0;\n  content: '';\n  background-color: rgba(234, 61, 86, 0.4);\n  opacity: 0;\n  -webkit-transition: opacity 0.2s ease-in-out;\n  -moz-transition: opacity 0.2s ease-in-out;\n  transition: opacity 0.2s ease-in-out;\n}\n\n.mkdf-single-image-holder.mkdf-image-behavior-lightbox .mkdf-si-inner a:hover:after {\n  opacity: 1;\n}\n\n.mkdf-single-image-holder.mkdf-image-behavior-lightbox .mkdf-si-inner a:after {\n  position: absolute;\n  display: block;\n  width: 100%;\n  height: 100%;\n  top: 0;\n  left: 0;\n  content: '';\n  background-color: rgba(234, 61, 86, 0.4);\n  opacity: 0;\n  -webkit-transition: opacity 0.2s ease-in-out;\n  -moz-transition: opacity 0.2s ease-in-out;\n  transition: opacity 0.2s ease-in-out;\n}\n\n.mkdf-single-image-holder.mkdf-image-behavior-zoom .mkdf-si-inner {\n  overflow: hidden;\n}\n\n.touch .mkdf-single-image-holder.mkdf-image-behavior-zoom .mkdf-si-inner {\n  cursor: pointer;\n}\n\n.mkdf-single-image-holder.mkdf-image-behavior-zoom .mkdf-si-inner:hover img {\n  -webkit-transform: scale(1.04);\n  -moz-transform: scale(1.04);\n  transform: scale(1.04);\n}\n\n.mkdf-single-image-holder.mkdf-image-behavior-zoom .mkdf-si-inner img {\n  -webkit-transform: scale(1);\n  -moz-transform: scale(1);\n  transform: scale(1);\n  -webkit-transition: -webkit-transform 0.3s ease-in-out;\n  -moz-transition: -moz-transform 0.3s ease-in-out;\n  transition: transform 0.3s ease-in-out;\n}\n\n.mkdf-single-image-holder.mkdf-image-behavior-grayscale .mkdf-si-inner {\n  overflow: hidden;\n}\n\n.touch .mkdf-single-image-holder.mkdf-image-behavior-grayscale .mkdf-si-inner {\n  cursor: pointer;\n}\n\n.mkdf-single-image-holder.mkdf-image-behavior-grayscale .mkdf-si-inner:hover img {\n  -webkit-filter: grayscale(0);\n  filter: none;\n}\n\n.mkdf-single-image-holder.mkdf-image-behavior-grayscale .mkdf-si-inner img {\n  filter: url(\"img/desaturate.svg#grayscale\");\n  -webkit-filter: grayscale(100%);\n  -moz-filter: grayscale(100%);\n  filter: gray;\n  filter: grayscale(100%);\n  -webkit-transition: all 0.3s ease-in-out;\n  -moz-transition: all 0.3s ease-in-out;\n  transition: all 0.3s ease-in-out;\n}\n\n.mkdf-single-image-holder.mkdf-image-behavior-moving .mkdf-si-inner {\n  overflow: hidden;\n  padding: 10% 0;\n  background-repeat: no-repeat;\n  background-position: 0 center;\n  background-size: 120%;\n  -webkit-transition: background 0.7s ease-out;\n  -moz-transition: background 0.7s ease-out;\n  transition: background 0.7s ease-out;\n}\n\n.mkdf-single-image-holder.mkdf-image-behavior-moving .mkdf-si-inner:hover {\n  background-position: 90% center;\n}\n\n.touch .mkdf-single-image-holder.mkdf-image-behavior-moving .mkdf-si-inner {\n  cursor: pointer;\n}\n\n.mkdf-single-image-holder.mkdf-image-behavior-moving .mkdf-si-inner img {\n  z-index: -1;\n  max-width: 80%;\n}\n\n@media only screen and (max-width: 1024px) {\n  .mkdf-single-image-holder.mkdf-image-behavior-moving .mkdf-si-inner {\n    padding: 0;\n    background: none;\n  }\n  .mkdf-single-image-holder.mkdf-image-behavior-moving .mkdf-si-inner img {\n    z-index: inherit;\n    max-width: 100%;\n  }\n}\n\n/* ==========================================================================\n   Single Image shortcode style - end\n   ========================================================================== */\n/* ==========================================================================\n   Social Share shortcode style - begin\n   ========================================================================== */\n.mkdf-social-share-holder {\n  position: relative;\n  display: inline-block;\n  vertical-align: top;\n}\n\n.mkdf-social-share-holder .mkdf-social-title {\n  position: relative;\n  display: inline-block;\n  vertical-align: top;\n  margin-right: 13px;\n}\n\n.mkdf-social-share-holder ul {\n  position: relative;\n  display: inline-block;\n  vertical-align: top;\n  list-style: none;\n  padding: 0;\n  margin: 0;\n}\n\n.mkdf-social-share-holder li {\n  position: relative;\n  display: inline-block;\n  vertical-align: top;\n  padding: 0;\n  margin: 0;\n}\n\n.mkdf-social-share-holder li a {\n  font-size: 14px;\n}\n\n.mkdf-social-share-holder.mkdf-list li {\n  margin-right: 13px;\n}\n\n.mkdf-social-share-holder.mkdf-list li:last-child {\n  margin-right: 0;\n}\n\n.mkdf-social-share-holder.mkdf-text li {\n  margin-right: 13px;\n}\n\n.mkdf-social-share-holder.mkdf-text li:last-child {\n  margin-right: 0;\n}\n\n.mkdf-social-share-holder.mkdf-dropdown {\n  position: relative;\n  display: inline-block;\n  vertical-align: bottom;\n}\n\n.mkdf-social-share-holder.mkdf-dropdown:hover .mkdf-social-share-dropdown ul li {\n  opacity: 1;\n  visibility: visible;\n  cursor: pointer;\n  /* opacity and visibility need to be different, but not background-color */\n}\n\n.mkdf-social-share-holder.mkdf-dropdown:hover .mkdf-social-share-dropdown ul li:nth-child(2) {\n  -webkit-transition-delay: 0.2s;\n  -moz-transition-delay: 0.2s;\n  transition-delay: 0.2s;\n}\n\n.mkdf-social-share-holder.mkdf-dropdown:hover .mkdf-social-share-dropdown ul li:nth-child(3) {\n  -webkit-transition-delay: 0.3s;\n  -moz-transition-delay: 0.3s;\n  transition-delay: 0.3s;\n}\n\n.mkdf-social-share-holder.mkdf-dropdown:hover .mkdf-social-share-dropdown ul li:nth-child(4) {\n  -webkit-transition-delay: 0.4s;\n  -moz-transition-delay: 0.4s;\n  transition-delay: 0.4s;\n}\n\n.mkdf-social-share-holder.mkdf-dropdown:hover .mkdf-social-share-dropdown ul li:nth-child(5) {\n  -webkit-transition-delay: 0.5s;\n  -moz-transition-delay: 0.5s;\n  transition-delay: 0.5s;\n}\n\n.mkdf-social-share-holder.mkdf-dropdown:hover .mkdf-social-share-dropdown ul li:nth-child(6) {\n  -webkit-transition-delay: 0.6s;\n  -moz-transition-delay: 0.6s;\n  transition-delay: 0.6s;\n}\n\n.mkdf-social-share-holder.mkdf-dropdown:hover .mkdf-social-share-dropdown ul li:nth-child(7) {\n  -webkit-transition-delay: 0.7s;\n  -moz-transition-delay: 0.7s;\n  transition-delay: 0.7s;\n}\n\n.mkdf-social-share-holder.mkdf-dropdown .mkdf-social-share-dropdown-opener {\n  display: block;\n}\n\n.mkdf-social-share-holder.mkdf-dropdown .mkdf-social-share-dropdown-opener .mkdf-social-share-title {\n  display: inline-block;\n  vertical-align: top;\n  margin-right: 5px;\n}\n\n.mkdf-social-share-holder.mkdf-dropdown .mkdf-social-share-dropdown {\n  position: absolute;\n  visibility: hidden;\n  z-index: 950;\n}\n\n.mkdf-social-share-holder.mkdf-dropdown .mkdf-social-share-dropdown ul {\n  position: relative;\n  display: block;\n  z-index: 990;\n  margin: 0;\n  padding: 0 !important;\n}\n\n.mkdf-social-share-holder.mkdf-dropdown .mkdf-social-share-dropdown li {\n  position: absolute;\n  display: block;\n  text-align: center;\n  visibility: hidden;\n  overflow: hidden;\n  opacity: 0;\n  box-sizing: border-box;\n  -webkit-transition: opacity 0.2s ease-out, visibility 0.2s ease-out;\n  -moz-transition: opacity 0.2s ease-out, visibility 0.2s ease-out;\n  transition: opacity 0.2s ease-out, visibility 0.2s ease-out;\n}\n\n.mkdf-social-share-holder.mkdf-dropdown .mkdf-social-share-dropdown li a {\n  -webkit-transition: color 0.2s ease-out, background-color 0.2s ease-out;\n  -moz-transition: color 0.2s ease-out, background-color 0.2s ease-out;\n  transition: color 0.2s ease-out, background-color 0.2s ease-out;\n}\n\n.mkdf-social-share-holder.mkdf-dropdown .mkdf-social-share-dropdown li * {\n  display: block;\n  line-height: inherit;\n}\n\n.mkdf-social-share-holder.mkdf-dropdown.mkdf-bottom .mkdf-social-share-dropdown {\n  bottom: 0;\n  left: 0;\n}\n\n.mkdf-social-share-holder.mkdf-dropdown.mkdf-bottom .mkdf-social-share-dropdown li {\n  width: 90px;\n  height: 30px;\n  line-height: 30px;\n  border: 1px solid rgba(225, 225, 225, 0.3);\n}\n\n.mkdf-social-share-holder.mkdf-dropdown.mkdf-bottom .mkdf-social-share-dropdown li:not(:first-child) {\n  border-top: none;\n}\n\n.mkdf-social-share-holder.mkdf-dropdown.mkdf-bottom .mkdf-social-share-dropdown li.mkdf-facebook-share a:hover {\n  background-color: #3b5998;\n  color: #fff;\n}\n\n.mkdf-social-share-holder.mkdf-dropdown.mkdf-bottom .mkdf-social-share-dropdown li.mkdf-twitter-share a:hover {\n  background-color: #00aced;\n  color: #fff;\n}\n\n.mkdf-social-share-holder.mkdf-dropdown.mkdf-bottom .mkdf-social-share-dropdown li.mkdf-google_plus-share a:hover {\n  background-color: #dd4b39;\n  color: #fff;\n}\n\n.mkdf-social-share-holder.mkdf-dropdown.mkdf-bottom .mkdf-social-share-dropdown li.mkdf-linkedin-share a:hover {\n  background-color: #007bb5;\n  color: #fff;\n}\n\n.mkdf-social-share-holder.mkdf-dropdown.mkdf-bottom .mkdf-social-share-dropdown li.mkdf-tumblr-share a:hover {\n  background-color: #32506d;\n  color: #fff;\n}\n\n.mkdf-social-share-holder.mkdf-dropdown.mkdf-bottom .mkdf-social-share-dropdown li.mkdf-pinterest-share a:hover {\n  background-color: #cb2027;\n  color: #fff;\n}\n\n.mkdf-social-share-holder.mkdf-dropdown.mkdf-bottom .mkdf-social-share-dropdown li.mkdf-vk-share a:hover {\n  background-color: #45668e;\n  color: #fff;\n}\n\n.mkdf-social-share-holder.mkdf-dropdown.mkdf-bottom .mkdf-social-share-dropdown li a {\n  font-size: 12px;\n  color: #868890;\n  background-color: #fff;\n}\n\n.mkdf-social-share-holder.mkdf-dropdown.mkdf-bottom .mkdf-social-share-dropdown li:nth-child(1) {\n  bottom: -30px;\n}\n\n.mkdf-social-share-holder.mkdf-dropdown.mkdf-bottom .mkdf-social-share-dropdown li:nth-child(2) {\n  bottom: -60px;\n}\n\n.mkdf-social-share-holder.mkdf-dropdown.mkdf-bottom .mkdf-social-share-dropdown li:nth-child(3) {\n  bottom: -90px;\n}\n\n.mkdf-social-share-holder.mkdf-dropdown.mkdf-bottom .mkdf-social-share-dropdown li:nth-child(4) {\n  bottom: -120px;\n}\n\n.mkdf-social-share-holder.mkdf-dropdown.mkdf-bottom .mkdf-social-share-dropdown li:nth-child(5) {\n  bottom: -150px;\n}\n\n.mkdf-social-share-holder.mkdf-dropdown.mkdf-bottom .mkdf-social-share-dropdown li:nth-child(6) {\n  bottom: -180px;\n}\n\n.mkdf-social-share-holder.mkdf-dropdown.mkdf-bottom .mkdf-social-share-dropdown li:nth-child(7) {\n  bottom: -210px;\n}\n\n.mkdf-social-share-holder.mkdf-dropdown.mkdf-right .mkdf-social-share-dropdown {\n  top: 0;\n  right: 0;\n}\n\n.mkdf-social-share-holder.mkdf-dropdown.mkdf-right .mkdf-social-share-dropdown li {\n  width: calc(90px / 3);\n}\n\n.mkdf-social-share-holder.mkdf-dropdown.mkdf-right .mkdf-social-share-dropdown li:nth-child(1) {\n  left: 5px;\n}\n\n.mkdf-social-share-holder.mkdf-dropdown.mkdf-right .mkdf-social-share-dropdown li:nth-child(2) {\n  left: 35px;\n}\n\n.mkdf-social-share-holder.mkdf-dropdown.mkdf-right .mkdf-social-share-dropdown li:nth-child(3) {\n  left: 65px;\n}\n\n.mkdf-social-share-holder.mkdf-dropdown.mkdf-right .mkdf-social-share-dropdown li:nth-child(4) {\n  left: 95px;\n}\n\n.mkdf-social-share-holder.mkdf-dropdown.mkdf-right .mkdf-social-share-dropdown li:nth-child(5) {\n  left: 125px;\n}\n\n.mkdf-social-share-holder.mkdf-dropdown.mkdf-right .mkdf-social-share-dropdown li:nth-child(6) {\n  left: 155px;\n}\n\n.mkdf-social-share-holder.mkdf-dropdown.mkdf-right .mkdf-social-share-dropdown li:nth-child(7) {\n  left: 185px;\n}\n\n.mkdf-social-share-holder.mkdf-dropdown.mkdf-left .mkdf-social-share-dropdown {\n  top: 0;\n  left: 0;\n}\n\n.mkdf-social-share-holder.mkdf-dropdown.mkdf-left .mkdf-social-share-dropdown li {\n  width: calc(90px / 3);\n}\n\n.mkdf-social-share-holder.mkdf-dropdown.mkdf-left .mkdf-social-share-dropdown li:nth-child(1) {\n  right: 5px;\n}\n\n.mkdf-social-share-holder.mkdf-dropdown.mkdf-left .mkdf-social-share-dropdown li:nth-child(2) {\n  right: 35px;\n}\n\n.mkdf-social-share-holder.mkdf-dropdown.mkdf-left .mkdf-social-share-dropdown li:nth-child(3) {\n  right: 65px;\n}\n\n.mkdf-social-share-holder.mkdf-dropdown.mkdf-left .mkdf-social-share-dropdown li:nth-child(4) {\n  right: 95px;\n}\n\n.mkdf-social-share-holder.mkdf-dropdown.mkdf-left .mkdf-social-share-dropdown li:nth-child(5) {\n  right: 125px;\n}\n\n.mkdf-social-share-holder.mkdf-dropdown.mkdf-left .mkdf-social-share-dropdown li:nth-child(6) {\n  right: 155px;\n}\n\n.mkdf-social-share-holder.mkdf-dropdown.mkdf-left .mkdf-social-share-dropdown li:nth-child(7) {\n  right: 185px;\n}\n\n/* ==========================================================================\n   Social Share shortcode style - end\n   ========================================================================== */\n/* ==========================================================================\n   Tabs shortcode style - begin\n   ========================================================================== */\n.mkdf-tabs {\n  position: relative;\n  display: inline-block;\n  width: 100%;\n  vertical-align: middle;\n}\n\n.mkdf-tabs .mkdf-tabs-nav {\n  position: relative;\n  display: inline-block;\n  width: 100%;\n  vertical-align: middle;\n  margin: 0;\n  padding: 0;\n  list-style: none;\n  border-bottom: 2px solid rgba(225, 225, 225, 0.3);\n}\n\n.mkdf-tabs .mkdf-tabs-nav li {\n  float: left;\n  margin: 0;\n  padding: 0;\n  position: relative !important;\n}\n\n.mkdf-tabs .mkdf-tabs-nav li a {\n  position: relative;\n  display: inline-block;\n  vertical-align: middle;\n  box-sizing: border-box;\n  -webkit-transition: all 0.2s ease-out, background-color 0.2s ease-out, border-color 0.2s ease-out;\n  -moz-transition: all 0.2s ease-out, background-color 0.2s ease-out, border-color 0.2s ease-out;\n  transition: all 0.2s ease-out, background-color 0.2s ease-out, border-color 0.2s ease-out;\n  font-size: 16px;\n  font-family: \"Josefin Sans\", sans-serif;\n  opacity: 0.6;\n}\n\n.mkdf-tabs .mkdf-tabs-nav li .mkdf-tabs-underline {\n  width: 0%;\n  height: 8px;\n  position: absolute;\n  bottom: -2px;\n  left: 0;\n  opacity: 1 !important;\n  -webkit-transition: all 0.4s ease-in;\n  -moz-transition: all 0.4s ease-in;\n  transition: all 0.4s ease-in;\n}\n\n.mkdf-tabs .mkdf-tabs-nav li.ui-state-active a {\n  opacity: 1;\n}\n\n.mkdf-tabs .mkdf-tab-container {\n  box-sizing: border-box;\n  position: relative;\n  display: inline-block;\n  width: 100%;\n  vertical-align: middle;\n}\n\n.mkdf-tabs .mkdf-tab-container p {\n  margin: 0;\n}\n\n.mkdf-tabs.mkdf-tabs-standard .mkdf-tabs-nav li a {\n  padding: 21px 42px;\n  line-height: 25px;\n  font-weight: 600;\n  letter-spacing: 1px;\n  color: #1b2c58;\n  margin-bottom: -2px;\n  border-bottom: 8px solid transparent;\n}\n\n.mkdf-tabs.mkdf-tabs-standard .mkdf-tabs-nav li.ui-state-active a {\n  border-bottom: 8px solid transparent;\n  opacity: 1;\n}\n\n.mkdf-tabs.mkdf-tabs-standard .mkdf-tab-container {\n  margin: 25px 0 0;\n}\n\n.mkdf-tabs.mkdf-tabs-boxed .mkdf-tabs-nav li {\n  margin: 0 12px 0 0;\n}\n\n.mkdf-tabs.mkdf-tabs-boxed .mkdf-tabs-nav li a {\n  padding: 21px 43px;\n  line-height: 25px;\n  font-weight: 400;\n  letter-spacing: 1px;\n  color: #fff;\n  background-color: #1b2c58;\n  margin-bottom: -2px;\n}\n\n.mkdf-tabs.mkdf-tabs-boxed .mkdf-tabs-nav li.ui-state-active a,\n.mkdf-tabs.mkdf-tabs-boxed .mkdf-tabs-nav li.ui-state-hover a {\n  color: #fff;\n  background-color: #ea3d56;\n}\n\n.mkdf-tabs.mkdf-tabs-boxed .mkdf-tabs-nav li:last-child {\n  margin: 0;\n}\n\n.mkdf-tabs.mkdf-tabs-boxed .mkdf-tab-container {\n  margin: 25px 0 0;\n}\n\n.mkdf-tabs.mkdf-tabs-simple .mkdf-tabs-nav {\n  border-bottom: 1px solid rgba(225, 225, 225, 0.3);\n}\n\n.mkdf-tabs.mkdf-tabs-simple .mkdf-tabs-nav li {\n  margin: 0 31px 0 0;\n}\n\n.mkdf-tabs.mkdf-tabs-simple .mkdf-tabs-nav li a {\n  padding: 13px 0;\n  font-size: 18px;\n  line-height: 26px;\n  font-weight: 400;\n  color: #1b2c58;\n}\n\n.mkdf-tabs.mkdf-tabs-simple .mkdf-tabs-nav li.ui-state-active a,\n.mkdf-tabs.mkdf-tabs-simple .mkdf-tabs-nav li.ui-state-hover a {\n  color: #ea3d56;\n}\n\n.mkdf-tabs.mkdf-tabs-simple .mkdf-tabs-nav li:last-child {\n  margin: 0;\n}\n\n.mkdf-tabs.mkdf-tabs-simple .mkdf-tab-container {\n  padding: 31px 0;\n}\n\n.mkdf-tabs.mkdf-tabs-vertical {\n  display: table;\n}\n\n.mkdf-tabs.mkdf-tabs-vertical .mkdf-tabs-nav {\n  display: table-cell;\n  vertical-align: top;\n  width: 140px;\n  height: 100%;\n  border-right: 1px solid rgba(225, 225, 225, 0.3);\n  border-bottom: 0;\n  box-sizing: border-box;\n}\n\n.mkdf-tabs.mkdf-tabs-vertical .mkdf-tabs-nav li {\n  display: block;\n  float: none;\n}\n\n.mkdf-tabs.mkdf-tabs-vertical .mkdf-tabs-nav li a {\n  font-size: 16px;\n  line-height: 26px;\n  font-weight: 400;\n  display: block;\n  color: #1b2c58;\n  padding: 12px 0 12px;\n}\n\n.mkdf-tabs.mkdf-tabs-vertical .mkdf-tabs-nav li.ui-state-active a,\n.mkdf-tabs.mkdf-tabs-vertical .mkdf-tabs-nav li.ui-state-hover a {\n  color: #ea3d56;\n  border-right: 8px solid transparent;\n}\n\n.mkdf-tabs.mkdf-tabs-vertical .mkdf-tabs-nav li:last-child {\n  margin: 0;\n}\n\n.mkdf-tabs.mkdf-tabs-vertical .mkdf-tab-container {\n  display: table-cell;\n  vertical-align: top;\n  width: calc(100% - 140px);\n  height: 100%;\n  padding: 0 0 0 45px;\n  box-sizing: border-box;\n}\n\n.ui-widget-content {\n  padding: 0;\n  font-family: inherit;\n  font-size: inherit;\n  color: inherit;\n  background: none;\n  border: 0;\n  border-radius: 0;\n}\n\n.ui-widget-content .ui-widget-header {\n  font-size: inherit;\n  line-height: inherit;\n  font-weight: inherit;\n  color: initial;\n  background: none;\n  border-radius: 0;\n}\n\n.ui-widget-content .ui-tabs-nav li {\n  position: initial;\n  font-weight: inherit;\n  color: inherit;\n  background: initial;\n  border: 0;\n  border-radius: 0;\n}\n\n.ui-widget-content .ui-widget-content {\n  color: inherit;\n  background: none;\n  border: 0;\n  border-radius: 0;\n}\n\n/* ==========================================================================\n   Tabs shortcode style - end\n   ========================================================================== */\n/* ==========================================================================\n   Team shortcode style - begin\n   ========================================================================== */\n.mkdf-team-holder {\n  position: relative;\n  display: inline-block;\n  width: 100%;\n  vertical-align: middle;\n}\n\n.touch .mkdf-team-holder.mkdf-team-info-on-image {\n  cursor: pointer;\n}\n\n.mkdf-team-holder.mkdf-team-info-on-image:hover .mkdf-team-social-wrapper {\n  opacity: 1;\n}\n\n.mkdf-team-holder.mkdf-team-info-on-image:hover .mkdf-team-social-inner {\n  -webkit-transition: -webkit-transform 0.45s cubic-bezier(0.64, 0.01, 0.15, 1.16), opacity 0.5s;\n  -moz-transition: -moz-transform 0.45s cubic-bezier(0.64, 0.01, 0.15, 1.16), opacity 0.5s;\n  transition: transform 0.45s cubic-bezier(0.64, 0.01, 0.15, 1.16), opacity 0.5s;\n  -webkit-transform: translate3d(0, 0, 0);\n  -moz-transform: translate3d(0, 0, 0);\n  transform: translate3d(0, 0, 0);\n}\n\n.mkdf-team-holder.mkdf-team-info-on-image .mkdf-team-social-holder {\n  margin: 7px 0 0;\n}\n\n.mkdf-team-holder .mkdf-team-inner {\n  text-align: center;\n  border: 1px solid rgba(225, 225, 225, 0.2);\n  -webkit-transition: all 0.3s ease-out;\n  -moz-transition: all 0.3s ease-out;\n  transition: all 0.3s ease-out;\n}\n\n.mkdf-team-holder .mkdf-team-inner:hover {\n  border: 1px solid rgba(225, 225, 225, 0.4);\n}\n\n.mkdf-team-holder .mkdf-team-image {\n  position: relative;\n  display: inline-block;\n  width: 100%;\n  vertical-align: middle;\n  width: auto;\n  margin-top: 38px;\n}\n\n.mkdf-team-holder .mkdf-team-image img {\n  display: block;\n  width: 200px;\n  height: 200px;\n  object-fit: cover;\n  border-radius: 50%;\n  border: 3px solid #fff;\n}\n\n.mkdf-team-holder .mkdf-team-info {\n  position: relative;\n  display: inline-block;\n  width: 100%;\n  vertical-align: middle;\n  margin: 7px 0 57px;\n  text-align: center;\n}\n\n.mkdf-team-holder .mkdf-team-name {\n  margin: 0;\n}\n\n.mkdf-team-holder .mkdf-team-position {\n  margin: 12px 0 0;\n  font-size: 14px;\n  opacity: 0.4;\n  text-transform: uppercase;\n  letter-spacing: 0.01em;\n  font-weight: 700;\n  line-height: 26px;\n}\n\n.mkdf-team-holder .mkdf-team-text {\n  margin: 10px 0 0;\n}\n\n.mkdf-team-holder .mkdf-team-social-wrapper {\n  position: absolute;\n  display: block;\n  width: 100%;\n  height: 100%;\n  top: 0;\n  left: 0;\n  background-color: rgba(255, 255, 255, 0.85);\n  z-index: 1;\n  opacity: 0;\n  -webkit-transform: translateZ(0);\n  -moz-transform: translateZ(0);\n  transform: translateZ(0);\n  -webkit-transition: opacity 0.3s;\n  -moz-transition: opacity 0.3s;\n  transition: opacity 0.3s;\n}\n\n.mkdf-team-holder .mkdf-team-social-outer {\n  position: relative;\n  display: table;\n  table-layout: fixed;\n  height: 100%;\n  width: 100%;\n}\n\n.mkdf-team-holder .mkdf-team-social-inner {\n  position: relative;\n  display: table-cell;\n  height: 100%;\n  width: 100%;\n  padding: 20px 40px 33px;\n  vertical-align: bottom;\n  -webkit-transition: -webkit-transform 0.2s ease;\n  -moz-transition: -moz-transform 0.2s ease;\n  transition: transform 0.2s ease;\n  -webkit-transform: translate3d(0, 40px, 0);\n  -moz-transform: translate3d(0, 40px, 0);\n  transform: translate3d(0, 40px, 0);\n}\n\n.mkdf-team-holder .mkdf-team-social-holder {\n  position: relative;\n  display: inline-block;\n  width: 100%;\n  vertical-align: middle;\n}\n\n.mkdf-team-holder .mkdf-team-social-holder .mkdf-team-icon {\n  font-size: 14px;\n  margin: 0;\n}\n\n.mkdf-team-holder .mkdf-team-social-holder .mkdf-team-icon:first-child {\n  font-size: 24px;\n  position: absolute;\n  right: 5px;\n  bottom: 24px;\n}\n\n.mkdf-team-holder .mkdf-team-social-holder .mkdf-team-icon:nth-child(2) {\n  font-size: 16px;\n  position: absolute;\n  right: -9px;\n  bottom: 69px;\n}\n\n.mkdf-team-holder .mkdf-team-social-holder .mkdf-team-icon:nth-child(3) {\n  font-size: 14px;\n  position: absolute;\n  right: -13px;\n  bottom: 103px;\n}\n\n.mkdf-team-holder .mkdf-team-social-holder .mkdf-team-icon:nth-child(4) {\n  font-size: 12px;\n  position: absolute;\n  right: -8px;\n  bottom: 132px;\n}\n\n.mkdf-team-holder .mkdf-team-social-holder .mkdf-team-icon:nth-child(5) {\n  font-size: 10px;\n  position: absolute;\n  right: 4px;\n  bottom: 155px;\n}\n\n.mkdf-team-holder .mkdf-team-social-holder .mkdf-team-icon .mkdf-icon-element {\n  font-size: inherit;\n  -webkit-transition: none;\n  -moz-transition: none;\n  transition: none;\n  color: #fff;\n}\n\n/* ==========================================================================\n   Team shortcode style - end\n   ========================================================================== */\n/* ==========================================================================\n   Text Marquee shortcode style - begin\n   ========================================================================== */\n.mkdf-text-marquee {\n  position: relative;\n  white-space: nowrap;\n  color: #1b2c58;\n  font-size: 60px;\n  line-height: 1.2em;\n  font-weight: 600;\n  overflow: hidden;\n}\n\n.mkdf-text-marquee .mkdf-marquee-element {\n  position: relative;\n  display: inline-block;\n  vertical-align: top;\n  padding: 0 25px;\n  box-sizing: border-box;\n}\n\n.mkdf-text-marquee .mkdf-marquee-element.mkdf-aux-text {\n  position: absolute;\n  top: 0;\n  left: 0;\n}\n\n/* ==========================================================================\n   Text Marquee shortcode style - end\n   ========================================================================== */\n/* ==========================================================================\n   Video Button shortcode start styles\n   ========================================================================== */\n.mkdf-video-button-holder {\n  position: relative;\n  display: inline-block;\n  vertical-align: middle;\n}\n\n.mkdf-video-button-holder.mkdf-vb-has-img .mkdf-video-button-play,\n.mkdf-video-button-holder.mkdf-vb-has-img .mkdf-video-button-play-image {\n  position: absolute;\n  display: block;\n  width: 100%;\n  height: 100%;\n  top: 0;\n  left: 0;\n  z-index: 1;\n}\n\n.mkdf-video-button-holder.mkdf-vb-has-img .mkdf-video-button-play .mkdf-video-button-play-inner,\n.mkdf-video-button-holder.mkdf-vb-has-img .mkdf-video-button-play-image .mkdf-video-button-play-inner {\n  position: relative;\n  top: 50%;\n  left: 0;\n  display: block;\n  text-align: center;\n}\n\n.mkdf-video-button-holder.mkdf-vb-has-img .mkdf-video-button-play .mkdf-video-button-play-inner.top-right,\n.mkdf-video-button-holder.mkdf-vb-has-img .mkdf-video-button-play-image .mkdf-video-button-play-inner.top-right {\n  position: absolute;\n  top: 14%;\n  right: 0;\n  -webkit-transform: translate(50%, -50%);\n  -moz-transform: translate(50%, -50%);\n  transform: translate(50%, -50%);\n}\n\n.mkdf-video-button-holder .mkdf-video-button-image {\n  position: relative;\n  display: inline-block;\n  width: 100%;\n  vertical-align: middle;\n}\n\n.mkdf-video-button-holder .mkdf-video-button-image img {\n  display: block;\n}\n\n.mkdf-video-button-holder .mkdf-video-button-play,\n.mkdf-video-button-holder .mkdf-video-button-play-image {\n  position: relative;\n  display: inline-block;\n  vertical-align: top;\n  z-index: 1;\n}\n\n.mkdf-video-button-holder .mkdf-video-button-play {\n  color: #fff;\n  font-size: 40px;\n  line-height: 1;\n}\n\n.mkdf-video-button-holder .mkdf-video-button-play span {\n  display: block;\n  line-height: inherit;\n}\n\n.mkdf-video-button-holder .mkdf-video-button-play span:before {\n  display: block;\n  line-height: inherit;\n}\n\n.mkdf-video-button-holder .mkdf-video-button-play span span {\n  display: inline-block;\n  width: 105px;\n  height: 105px;\n  top: 50%;\n  line-height: 101px;\n  -webkit-transform: translateY(-50%);\n  -moz-transform: translateY(-50%);\n  transform: translateY(-50%);\n  border-radius: 50%;\n  -webkit-transition: all 0.2s ease-out;\n  -moz-transition: all 0.2s ease-out;\n  transition: all 0.2s ease-out;\n}\n\n.mkdf-video-button-holder .mkdf-video-button-play span span:hover .icon-basic-animation {\n  animation: mkdfPulsebig 1.8s infinite;\n}\n\n.mkdf-video-button-holder .mkdf-video-button-play span .icon-basic-video {\n  position: relative;\n}\n\n.mkdf-video-button-holder .mkdf-video-button-play span .icon-basic-animation {\n  display: block;\n  position: absolute;\n  top: 50%;\n  left: 0;\n  /* padding: 28px; */\n  width: 105px;\n  border-radius: 50%;\n  height: 105px;\n  transition: all .2s ease-out;\n}\n\n.mkdf-video-button-holder .mkdf-video-button-play-image.mkdf-vb-has-hover-image:hover img:first-child {\n  opacity: 0;\n}\n\n.mkdf-video-button-holder .mkdf-video-button-play-image.mkdf-vb-has-hover-image:hover img:nth-child(2) {\n  opacity: 1;\n}\n\n.mkdf-video-button-holder .mkdf-video-button-play-image img {\n  display: block;\n  margin: 0 auto;\n  -webkit-transition: opacity 0.3s ease-in-out;\n  -moz-transition: opacity 0.3s ease-in-out;\n  transition: opacity 0.3s ease-in-out;\n}\n\n.mkdf-video-button-holder .mkdf-video-button-play-image img:first-child {\n  position: relative;\n  opacity: 1;\n}\n\n.mkdf-video-button-holder .mkdf-video-button-play-image img:nth-child(2) {\n  position: absolute;\n  top: 0;\n  left: 50%;\n  opacity: 0;\n  -webkit-transform: translateX(-50%) translateZ(0);\n  -moz-transform: translateX(-50%) translateZ(0);\n  transform: translateX(-50%) translateZ(0);\n}\n\n.rev_slider_wrapper .mkdf-video-button-holder .mkdf-video-button-play-image img:nth-child(2) {\n  -webkit-transform: translateZ(0);\n  -moz-transform: translateZ(0);\n  transform: translateZ(0);\n}\n\n.mkdf-video-button-holder .mkdf-video-button-text {\n  display: table;\n  padding: 60px 50px;\n  color: #fff;\n}\n\n.mkdf-video-button-holder .mkdf-video-button-text .mkdf-video-button-title {\n  width: 30%;\n  display: table-cell;\n  vertical-align: middle;\n  color: #fff;\n}\n\n.mkdf-video-button-holder .mkdf-video-button-text p {\n  width: 70%;\n  font-size: 18px;\n  display: table-cell;\n  vertical-align: middle;\n}\n\n.mkdf-video-button-holder .mkdf-video-button-text-shadow-holder {\n  position: absolute;\n  bottom: 70px;\n  left: 50%;\n  -webkit-transform: translateX(-50%);\n  -moz-transform: translateX(-50%);\n  transform: translateX(-50%);\n  width: 76%;\n  z-index: -5;\n}\n\n/* ==========================================================================\n   Video Button shortcode end styles\n   ========================================================================== */\n.mkdf-workflow {\n  margin-top: 50px;\n  position: relative;\n}\n\n.mkdf-workflow .main-line {\n  background: #dee0e0;\n  left: 50%;\n  margin-left: -1px;\n  position: absolute;\n  right: 50%;\n  top: 0;\n  height: 100%;\n  width: 2px;\n}\n\n.mkdf-workflow .mkdf-workflow-item {\n  margin-left: auto;\n  margin-right: auto;\n  max-width: 80%;\n  position: relative;\n  padding-bottom: 21px;\n  overflow: hidden;\n}\n\n.mkdf-workflow .mkdf-workflow-item:nth-of-type(2n) {\n  text-align: left;\n}\n\n.mkdf-workflow .mkdf-workflow-item:nth-of-type(2n) .mkdf-workflow-item-inner {\n  display: flex;\n  align-items: center;\n}\n\n.mkdf-workflow .mkdf-workflow-item:nth-of-type(2n) .mkdf-workflow-item-inner .mkdf-workflow-image {\n  text-align: right;\n}\n\n.mkdf-workflow .mkdf-workflow-item:nth-of-type(2n+1) {\n  text-align: right;\n}\n\n.mkdf-workflow .mkdf-workflow-item:nth-of-type(2n+1) .mkdf-workflow-item-inner {\n  display: -webkit-box;\n  display: -webkit-flex;\n  display: -ms-flexbox;\n  display: flex;\n  align-items: center;\n  -webkit-box-orient: horizontal;\n  -webkit-box-direction: reverse;\n  -webkit-flex-direction: row-reverse;\n  -ms-flex-direction: row-reverse;\n  flex-direction: row-reverse;\n  -webkit-flex-wrap: wrap;\n  -ms-flex-wrap: wrap;\n  flex-wrap: wrap;\n}\n\n.mkdf-workflow .mkdf-workflow-item:nth-of-type(2n+1) .mkdf-workflow-item-inner .mkdf-workflow-image {\n  text-align: left;\n}\n\n.mkdf-workflow .mkdf-workflow-item .mkdf-workflow-item-inner {\n  display: inline-block;\n  position: relative;\n  width: 100%;\n  vertical-align: middle;\n}\n\n.mkdf-workflow .mkdf-workflow-item .mkdf-workflow-item-inner .mkdf-workflow-image,\n.mkdf-workflow .mkdf-workflow-item .mkdf-workflow-item-inner .mkdf-workflow-text {\n  float: left;\n  margin: 0;\n  width: 50%;\n  box-sizing: border-box;\n}\n\n.mkdf-workflow .mkdf-workflow-item .mkdf-workflow-item-inner .mkdf-workflow-image {\n  padding: 0 90px;\n}\n\n.mkdf-workflow .mkdf-workflow-item .mkdf-workflow-item-inner .mkdf-workflow-image.left {\n  text-align: left;\n}\n\n.mkdf-workflow .mkdf-workflow-item .mkdf-workflow-item-inner .mkdf-workflow-image.right {\n  text-align: right;\n}\n\n.mkdf-workflow .mkdf-workflow-item .mkdf-workflow-item-inner .mkdf-workflow-image .mkdf-workflow-image-inner {\n  position: relative;\n  padding: 20px;\n  display: inline-block;\n}\n\n.mkdf-workflow .mkdf-workflow-item .mkdf-workflow-item-inner .mkdf-workflow-image .mkdf-icon-shortcode {\n  position: absolute;\n  right: 0;\n  top: 0;\n  z-index: 5;\n  font-size: 20px;\n}\n\n.mkdf-workflow .mkdf-workflow-item .mkdf-workflow-item-inner .mkdf-workflow-text {\n  padding: 0 90px;\n}\n\n.mkdf-workflow .mkdf-workflow-item .mkdf-workflow-item-inner .mkdf-workflow-text.left {\n  text-align: left;\n}\n\n.mkdf-workflow .mkdf-workflow-item .mkdf-workflow-item-inner .mkdf-workflow-text.right {\n  text-align: right;\n}\n\n.mkdf-workflow .mkdf-workflow-item .mkdf-workflow-item-inner .mkdf-workflow-text .mkdf-workflow-text-inner {\n  padding: 0 20px;\n}\n\n.mkdf-workflow .mkdf-workflow-item .mkdf-workflow-item-inner .mkdf-workflow-text h4 {\n  margin-top: 12px;\n  margin-bottom: 0px;\n}\n\n.mkdf-workflow .mkdf-workflow-item .mkdf-workflow-item-inner .mkdf-workflow-text p.text {\n  margin-top: 14px;\n}\n\n.mkdf-workflow .mkdf-workflow-item .mkdf-workflow-item-inner .mkdf-workflow-text .circle {\n  background: transparent;\n  border: 3px solid #dee0e0;\n  border-radius: 50%;\n  content: \"\";\n  height: 14px;\n  left: 50%;\n  margin: 0 0 0 -10px;\n  position: absolute;\n  top: 50%;\n  width: 14px;\n}\n\n.mkdf-workflow .mkdf-workflow-item .line {\n  background-color: #fff;\n  height: 0;\n  left: 50%;\n  margin-left: -1px;\n  position: absolute;\n  width: 2px;\n}\n\n.mkdf-workflow .mkdf-workflow-item .line.line-one {\n  top: 0;\n}\n\n.mkdf-workflow .mkdf-workflow-item .line.line-two {\n  top: calc(50% + 10px);\n}\n\n.mkdf-workflow .mkdf-workflow-item:first-of-type .line-one {\n  display: none;\n}\n\n.mkdf-workflow .mkdf-workflow-item:last-of-type .line-two {\n  display: none;\n}\n\n.mkdf-workflow.mkdf-workflow-animate {\n  -webkit-transform: translateY(100px);\n  -moz-transform: translateY(100px);\n  transform: translateY(100px);\n  opacity: 0;\n  -webkit-transition: opacity 0.55s cubic-bezier(0.23, 1, 0.32, 1), -webkit-transform 0.55s cubic-bezier(0.23, 1, 0.32, 1);\n  transition: opacity 0.55s cubic-bezier(0.23, 1, 0.32, 1), transform 0.55s cubic-bezier(0.23, 1, 0.32, 1);\n}\n\n.mkdf-workflow.mkdf-workflow-animate .main-line {\n  opacity: 0;\n  height: 0;\n  -webkit-transition: opacity 0.55s cubic-bezier(0.23, 1, 0.32, 1), height 1.8s ease-out;\n  -moz-transition: opacity 0.55s cubic-bezier(0.23, 1, 0.32, 1), height 1.8s ease-out;\n  transition: opacity 0.55s cubic-bezier(0.23, 1, 0.32, 1), height 1.8s ease-out;\n}\n\n.mkdf-workflow.mkdf-workflow-animate .circle {\n  -webkit-transform: scale(0.2);\n  -moz-transform: scale(0.2);\n  transform: scale(0.2);\n  -webkit-transition: -webkit-transform 0.6s cubic-bezier(0.18, 0.89, 0.32, 1.68) 0.5s;\n  transition: transform 0.6s cubic-bezier(0.18, 0.89, 0.32, 1.68) 0.5s;\n}\n\n.mkdf-workflow.mkdf-workflow-animate .mkdf-workflow-item .mkdf-workflow-item-inner .mkdf-workflow-image {\n  opacity: 0;\n  -webkit-transform: scale(0.6);\n  -moz-transform: scale(0.6);\n  transform: scale(0.6);\n  -webkit-transition: -webkit-transform 0.6s cubic-bezier(0.18, 0.89, 0.32, 1), opacity 0.3s ease-out;\n  transition: transform 0.6s cubic-bezier(0.18, 0.89, 0.32, 1), opacity 0.3s ease-out;\n}\n\n.mkdf-workflow.mkdf-workflow-animate .mkdf-workflow-item .mkdf-workflow-item-inner .mkdf-workflow-text h4, .mkdf-workflow.mkdf-workflow-animate .mkdf-workflow-item .mkdf-workflow-item-inner .mkdf-workflow-text p {\n  opacity: 0;\n  -webkit-transition: opacity 0.5s cubic-bezier(0.22, 0.61, 0.36, 1) 0.2s;\n  -moz-transition: opacity 0.5s cubic-bezier(0.22, 0.61, 0.36, 1) 0.2s;\n  transition: opacity 0.5s cubic-bezier(0.22, 0.61, 0.36, 1) 0.2s;\n}\n\n.mkdf-workflow.mkdf-workflow-animate.mkdf-appeared {\n  -webkit-transform: translateY(0);\n  -moz-transform: translateY(0);\n  transform: translateY(0);\n  opacity: 1;\n}\n\n.mkdf-workflow.mkdf-workflow-animate.mkdf-appeared .main-line {\n  opacity: 1;\n  height: 100%;\n}\n\n.mkdf-workflow.mkdf-workflow-animate.mkdf-appeared .mkdf-workflow-item.mkdf-appeared .mkdf-workflow-image {\n  opacity: 1;\n  -webkit-transform: scale(1);\n  -moz-transform: scale(1);\n  transform: scale(1);\n}\n\n.mkdf-workflow.mkdf-workflow-animate.mkdf-appeared .mkdf-workflow-item.mkdf-appeared .mkdf-workflow-text h4, .mkdf-workflow.mkdf-workflow-animate.mkdf-appeared .mkdf-workflow-item.mkdf-appeared .mkdf-workflow-text p {\n  opacity: 1;\n}\n\n.mkdf-workflow.mkdf-workflow-animate.mkdf-appeared .circle {\n  -webkit-transform: scale(1);\n  -moz-transform: scale(1);\n  transform: scale(1);\n}\n\n.mkdf-workflow.mkdf-workflow-light h4, .mkdf-workflow.mkdf-workflow-light p.text {\n  color: #fff;\n}\n", "////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////\n\n// layout mixins - start\n\n@mixin mkdfRelativeHolderLayout($vertical-align: middle) {\n    position: relative;\n    display: inline-block;\n    width: 100%;\n    vertical-align: $vertical-align;\n}\n\n@mixin mkdfAbsoluteHolderLayout() {\n    position: absolute;\n    display: block;\n    width: 100%;\n    height: 100%;\n    top: 0;\n    left: 0;\n}\n\n@mixin mkdfFlexBoxLayout($position: null, $align-items: null, $justify-content: null) {\n    @if ($position) {\n        position: $position;\n    }\n    \n    @include mkdfFlexLayout();\n    \n    @if ($align-items) {\n        @include mkdfFlexAlignItems($align-items);\n    }\n    \n    @if ($justify-content) {\n        @include mkdfFlexJustifyContent($justify-content);\n    }\n}\n\n@mixin mkdfFlexContainer($align-items: null, $justify-content: null, $flex-direction: null, $flex-wrap: null, $align-content: null) {\n\t@include mkdfFlexBoxLayout(null, $align-items, $justify-content);\n\t\n\t@if ($flex-direction) {\n\t\tflex-direction: $flex-direction;\n\t}\n\t\n\t@if ($flex-wrap) {\n\t\tflex-wrap: $flex-wrap;\n\t}\n\t\n\t@if ($align-content) {\n\t\talign-content: $align-content;\n\t}\n}\n\n@mixin mkdfFlexLayout() {\n    display: -webkit-box;\n    display: -webkit-flex;\n    display: -ms-flexbox;\n    display: flex;\n}\n\n@mixin mkdfInlineFlexLayout() {\n    display: -webkit-inline-flex;\n    display: -ms-inline-flexbox;\n    display: inline-flex;\n}\n\n@mixin mkdfFlexItem($order: 0, $flex-grow: 0, $flex-shrink: 1, $flex-basis: auto) {\n    order: $order;\n    flex-grow: $flex-grow;\n    flex-shrink: $flex-shrink;\n    flex-basis: $flex-basis;\n}\n\n@mixin mkdfFlexAlignItems($align-items) {\n    $older-align-items: $align-items;\n    \n    @if ($align-items == 'flex-start') {\n        $older-align-items: start;\n    } @else if ($align-items == 'flex-end') {\n        $older-align-items: end;\n    }\n    \n    -webkit-box-align: $older-align-items;\n    -webkit-align-items: $align-items;\n    -ms-flex-align: $older-align-items;\n    align-items: $align-items;\n}\n@mixin mkdfDefaultTransition($transition-param...) {\n    $transitions_each: ('-webkit-transition', '-moz-transition', 'transition');\n    $string: '';\n\n    @each $var in $transition-param{\n        @if $string == '' {\n            $string : $var $default-transition-duration $default-easing-function\n        } @else {\n            $string : $string, $var $default-transition-duration $default-easing-function\n        }\n    }\n\n\n    @each $transition in $transitions_each{\n        #{$transition}: $string;\n    }\n}\n@mixin mkdfFlexJustifyContent($justify-content) {\n    $older-justify-content: $justify-content;\n    \n    @if ($justify-content == 'flex-start') {\n        $older-justify-content: start;\n    } @else if ($justify-content == 'flex-end') {\n        $older-justify-content: end;\n    } @else if ($justify-content == 'space-between') {\n        $older-justify-content: justify;\n    }\n    \n    -webkit-box-pack: $older-justify-content;\n    -webkit-justify-content: $justify-content;\n    -ms-flex-pack: $older-justify-content;\n    justify-content: $justify-content;\n}\n\n@mixin mkdfTableLayout() {\n    position: relative;\n    display: table;\n    table-layout: fixed;\n    height: 100%;\n    width: 100%;\n}\n\n@mixin mkdfTableCellLayout($vertical-align: middle) {\n    position: relative;\n    display: table-cell;\n    height: 100%;\n    width: 100%;\n    vertical-align: $vertical-align;\n}\n\n@mixin mkdfTypographyLayout($important : null) {\n    color: inherit $important;\n    font-family: inherit $important;\n    font-size: inherit $important;\n    font-weight: inherit $important;\n    font-style: inherit $important;\n    line-height: inherit $important;\n    letter-spacing: inherit $important;\n    text-transform: inherit $important;\n}\n\n// layout mixins - end\n\n////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////\n\n// transition mixins - start\n\n@mixin mkdfTransition($transition-param...) {\n    -webkit-transition: $transition-param;\n    -moz-transition: $transition-param;\n    transition: $transition-param;\n}\n\n@mixin mkdfTransitionTransform($transition-param...) {\n    -webkit-transition: -webkit-transform $transition-param;\n    -moz-transition: -moz-transform $transition-param;\n    transition: transform $transition-param;\n}\n\n@mixin mkdfTransform($transform-param...) {\n    -webkit-transform: $transform-param;\n    -moz-transform: $transform-param;\n    transform: $transform-param;\n}\n\n@mixin mkdfAnimation($animation-param...) {\n    -webkit-animation: $animation-param;\n    -moz-animation: $animation-param;\n    animation: $animation-param;\n}\n\n@mixin mkdfTransformOrigin($animation-param...) {\n    -webkit-transform-origin: $animation-param;\n    -moz-transform-origin: $animation-param;\n    transform-origin: $animation-param;\n}\n@mixin mkdfPulse {\n\n}\n\n// transition mixins - end\n\n////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////\n\n// checkbox mixins - start\n\n$checkbox-size: 15px;\n$checkbox-border-width: 1px;\n\n%checkbox-style {\n    position: relative;\n    margin: 8px 0;\n    line-height: 1;\n\n    input[type=checkbox] {\n        width: $checkbox-size;\n        height: $checkbox-size;\n        max-height: $checkbox-size;\n        position: relative;\n        display: inline-block;\n        vertical-align: top;\n        top: 0;\n        left: 0;\n        margin: 0;\n    }\n\n    input[type=checkbox] + label {\n        position: absolute;\n        top: 0;\n        left: 0;\n        display: inline-block;\n        line-height: 0;\n        pointer-events: none;\n        cursor: pointer;\n    }\n\n    input[type=checkbox] + label span.mkdf-label-text {\n        display: inline-block;\n        padding-left: 10px;\n        line-height: $checkbox-size;\n        color: $default-heading-color;\n    }\n\n    input[type=checkbox] + label .mkdf-label-view {\n        display: inline-block;\n        vertical-align: top;\n        width: $checkbox-size;\n        height: $checkbox-size;\n        background-color: $default-background-color;\n        border: $checkbox-border-width solid $default-border-color;\n        border-radius: 2px;\n        cursor: pointer;\n        box-sizing: border-box;\n\n        &:hover {\n            cursor: pointer;\n        }\n    }\n\n    input[type=checkbox] + label .mkdf-label-view:after {\n        content: '';\n        position: absolute;\n        top: ($checkbox-size - ($checkbox-size / 2 - $checkbox-border-width)) / 2;\n        left: ($checkbox-size - ($checkbox-size / 2 - $checkbox-border-width)) / 2;\n        width: $checkbox-size / 2 - $checkbox-border-width;\n        height: $checkbox-size / 2 - $checkbox-border-width;\n        background-color: $first-main-color;\n        opacity: 0;\n        @include mkdfTransition(opacity 0.3s ease-in-out);\n    }\n\n    input[type=checkbox]:checked + label .mkdf-label-view:after {\n        opacity: 1;\n    }\n}\n\n// checkbox mixins - end\n\n////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////\n\n// common mixins - start\n\n@mixin mkdfBckImageStyle() {\n    background-size: cover;\n    background-repeat: no-repeat;\n    background-position: center center;\n}\n\n@mixin mkdfImageOverlayHoverStyle($with-hover: true) {\n    \n    @if ($with-hover) {\n        \n        &:hover {\n            \n            &:after {\n                opacity: 1;\n            }\n        }\n\n        &:after {\n            @include mkdfAbsoluteHolderLayout();\n            content: '';\n            background-color: rgba($first-main-color, .4);\n            opacity: 0;\n            @include mkdfTransition(opacity .2s ease-in-out);\n        }\n\n    } @else {\n        @include mkdfAbsoluteHolderLayout();\n        content: '';\n        background-color: rgba($first-main-color, .4);\n        opacity: 0;\n        @include mkdfTransition(opacity .2s ease-in-out);\n    }\n}\n\n@mixin mkdfStandardPaginationStyle($list_type: null) {\n    @include mkdfRelativeHolderLayout(top);\n    margin: 40px 0 0;\n    clear: both;\n\n    ul {\n        @include mkdfRelativeHolderLayout(top);\n        padding: 0;\n        margin: 0;\n        list-style: none;\n        text-align: center;\n\n        li {\n            position: relative;\n            display: inline-block;\n            vertical-align: top;\n            margin: 0 12px;\n\n            a {\n                position: relative;\n                display: inline-block;\n                vertical-align: top;\n                margin: 0;\n                padding: 0;\n            }\n\n            &.mkdf-pag-active {\n                \n                a {\n                    color: $first-main-color;\n                }\n            }\n\n            &.mkdf-pag-prev,\n            &.mkdf-pag-next,\n            &.mkdf-pag-first,\n            &.mkdf-pag-last {\n                margin: 0 2px;\n\n                a {\n                    font-size: 24px;\n\n                    span {\n                        display: block;\n                        line-height: inherit;\n\n                        &:before {\n                            display: block;\n                            line-height: inherit;\n                        }\n                    }\n                }\n            }\n\n            @if ($list_type == 'shortcode') {\n                \n                &.mkdf-pag-prev {\n                    \n                    a {\n                        opacity: 0;\n                    }\n                }\n\n                &.mkdf-pag-next {\n                    \n                    a {\n                        opacity: 1;\n                    }\n                }\n\n            } @else if ($list_type == 'shop') {\n                span {\n                    position: relative;\n                    display: inline-block;\n                    vertical-align: top;\n                    margin: 0;\n                    padding: 0;\n                    color: $first-main-color;\n                }\n\n                a {\n                    \n                    &.next,\n                    &.prev {\n                        font-size: 0;\n                        line-height: 0;\n\n                        &:before {\n                            display: block;\n                            font-family: 'ElegantIcons'; // same icon pack as in our templates for pagination\n                            font-size: 24px;\n                            line-height: 26px;\n                            -webkit-font-smoothing: antialiased;\n                            -moz-osx-font-smoothing: grayscale;\n                        }\n                    }\n\n                    &.prev {\n                        margin-right: -10px;\n\n                        &:before {\n                            content: \"\\34\";\n                        }\n                    }\n\n                    &.next {\n                        margin-left: -10px;\n\n                        &:before {\n                            content: \"\\35\";\n                        }\n                    }\n                }\n            }\n        }\n    }\n}\n\n@mixin mkdfButtonDefaultStyle() {\n    position: relative;\n    display: inline-block;\n    vertical-align: middle;\n    width: auto;\n    margin: 0;\n    font-family: $default-text-font;\n    font-size: 12px;\n    line-height: 2em;\n    letter-spacing: 0.16em;\n    font-weight: 700;\n    text-transform: uppercase;\n    outline: none;\n    box-sizing: border-box;\n    @include mkdfTransition(color .2s ease-in-out, background-color .2s ease-in-out, border-color .2s ease-in-out);\n}\n\n@mixin mkdfButtonSize($size: medium) {\n    \n    @if ($size == 'small') {\n        padding: 11px 24px;\n\n    } @else if ($size == 'medium') {\n        padding: 13px 34px 11px;\n\n\n    } @else if ($size == 'large') {\n        padding: 13px 43px 11px;\n\n    } @else if ($size == 'huge') {\n        padding: 21px 60px 16px;\n        font-size:14px;\n    }\n}\n\n@mixin mkdfButtonTransparentColor() {\n    color: $default-text-color;\n    background-color: transparent;\n}\n\n@mixin mkdfButtonSolidColor($important: null) {\n    color: #fff $important;\n    background-color: $first-main-color $important;\n    border: 1px solid transparent $important;\n}\n\n@mixin mkdfButtonSolidHoverColor($important: null) {\n    color: #fff $important;\n    background-color: $first-main-color $important;\n    box-shadow: none !important;\n}\n\n@mixin mkdfButtonOutlineColor($important: null) {\n    color: $first-main-color $important;\n    background-color: transparent $important;\n    border: 1px solid $first-main-color $important;\n}\n\n@mixin mkdfButtonOutlineHoverColor($important: null) {\n    color: #fff $important;\n    background-color: $first-main-color $important;\n    border-color: $first-main-color $important;\n}\n\n@mixin mkdfPlaceholder {\n    &::-webkit-input-placeholder {\n        @content\n    }\n\n    &:-moz-placeholder {\n        @content;\n\n        opacity: 1;\n    }\n    &::-moz-placeholder {\n        @content;\n\n        opacity: 1;\n    }\n    &:-ms-input-placeholder {\n        @content\n    }\n}\n\n\n @keyframes animate-btn-line {\n    0% {\n        @include mkdfTransformOrigin(0 50%);\n        @include mkdfTransform(scaleX(0));\n    }\n    100% {\n        @include mkdfTransformOrigin(0 50%);\n        @include mkdfTransform(scaleX(1));\n    }\n}\n@-webkit-keyframes mkdfPulsebig {\n   0% {\n    -moz-box-shadow: 0 0 0 0 currentColor;\n    box-shadow: 0 0 0 0 currentColor;\n  }\n  70% {\n      -moz-box-shadow: 0 0 0 20px rgba($first-main-color, 0.3);\n      box-shadow: 0 0 0 20px rgba($first-main-color, 0);\n  }\n  100% {\n      -moz-box-shadow: 0 0 0 0 rgba($first-main-color, 0);\n      box-shadow: 0 0 0 0 rgba($first-main-color, 0);\n  }\n}\n@keyframes mkdfPulsebig {\n  0% {\n    -moz-box-shadow: 0 0 0 0 currentColor;\n    box-shadow: 0 0 0 0 currentColor;\n  }\n  70% {\n      -moz-box-shadow: 0 0 0 20px rgba($first-main-color, 0.3);\n      box-shadow: 0 0 0 20px rgba($first-main-color, 0);\n  }\n  100% {\n      -moz-box-shadow: 0 0 0 0 rgba($first-main-color, 0);\n      box-shadow: 0 0 0 0 rgba($first-main-color, 0);\n  }\n}\n@-webkit-keyframes mkdfPulsesmall {\n  0% {\n    -webkit-box-shadow: 0 0 0 0 currentColor;\n  }\n  70% {\n      -webkit-box-shadow: 0 0 0 10px rgba($first-main-color, 0.15);\n  }\n  100% {\n      -webkit-box-shadow: 0 0 0 0 rgba($first-main-color, 0);\n  }\n}\n@keyframes mkdfPulsesmall {\n  0% {\n    -moz-box-shadow: 0 0 0 0 currentColor;\n    box-shadow: 0 0 0 0 currentColor;\n  }\n  70% {\n      -moz-box-shadow: 0 0 0 10px rgba($first-main-color, 0.15);\n      box-shadow: 0 0 0 10px rgba($first-main-color, 0);\n  }\n  100% {\n      -moz-box-shadow: 0 0 0 0 rgba($first-main-color, 0);\n      box-shadow: 0 0 0 0 rgba($first-main-color, 0);\n  }\n}\n@-webkit-keyframes mkdfPulsesmallfirst {\n  0% {\n    -webkit-box-shadow: 0 0 0 0 $first-main-color;\n  }\n  70% {\n      -webkit-box-shadow: 0 0 0 10px rgba($first-main-color, 0.15);\n  }\n  100% {\n      -webkit-box-shadow: 0 0 0 0 rgba($first-main-color, 0);\n  }\n}\n@keyframes mkdfPulsesmallfirst {\n  0% {\n    -moz-box-shadow: 0 0 0 0 $first-main-color;\n    box-shadow: 0 0 0 0 $first-main-color;\n  }\n  70% {\n      -moz-box-shadow: 0 0 0 10px rgba($first-main-color, 0.15);\n      box-shadow: 0 0 0 10px rgba($first-main-color, 0);\n  }\n  100% {\n      -moz-box-shadow: 0 0 0 0 rgba($first-main-color, 0);\n      box-shadow: 0 0 0 0 rgba($first-main-color, 0);\n  }\n}\n\n// common mixins - end\n\n////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////\n\n// mixins styles - start\n\n%input-style {\n    position: relative;\n    width: 100%;\n    margin: 0 0 $input-margin;\n    padding: $input-vertical-padding $input-horizontal-padding;\n    font-family: $default-text-font;\n    font-size: 16px;\n    font-weight: inherit;\n    color: #a2a3a3;\n    background-color: transparent;\n    border: 2px solid $default-border-color;\n    border-radius: 0;\n    outline: 0;\n    cursor: pointer;\n    -webkit-appearance: none;\n    box-sizing: border-box;\n    @include mkdfTransition(border-color 0.2s ease-in-out);\n\n    &:focus {\n        color: $default-heading-color;\n        \n    }\n\n    @include mkdfPlaceholder {\n        color: inherit;\n    }\n}\n\n// mixins styles - end\n\n////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////\n\n//media query mixins - start\n\n@mixin laptop-landscape-large {\n    @media only screen and (max-width: map-get($breakpoints, laptop-landscape-large)) {\n        @content;\n    }\n}\n\n@mixin laptop-landscape-mac {\n    @media only screen and (max-width: map-get($breakpoints, laptop-landscape-mac)) {\n        @content;\n    }\n}\n\n@mixin laptop-landscape-medium {\n    @media only screen and (max-width: map-get($breakpoints, laptop-landscape-medium)) {\n        @content;\n    }\n}\n\n@mixin laptop-landscape {\n    @media only screen and (max-width: map-get($breakpoints, laptop-landscape)) {\n        @content;\n    }\n}\n\n@mixin ipad-landscape {\n    @media only screen and (max-width: map-get($breakpoints, ipad-landscape)) {\n        @content;\n    }\n}\n\n@mixin ipad-portrait {\n    @media only screen and (max-width: map-get($breakpoints, ipad-portrait)) {\n        @content;\n    }\n}\n\n@mixin phone-landscape {\n    @media only screen and (max-width: map-get($breakpoints, phone-landscape)) {\n        @content;\n    }\n}\n\n@mixin phone-portrait {\n    @media only screen and (max-width: map-get($breakpoints, phone-portrait)) {\n        @content;\n    }\n}\n\n@mixin smaller-phone-portrait {\n    @media only screen and (max-width: map-get($breakpoints, smaller-phone-portrait)) {\n        @content;\n    }\n}\n\n// media query mixins - end\n\n// animation mixin - start\n\n@mixin keyframes($name) {\n    @-webkit-keyframes #{$name} {\n        @content;\n    }\n\n    @keyframes #{$name} {\n        @content;\n    }\n}\n\n@mixin animation($name, $duration, $repeat, $timing, $delay) {\n    -webkit-animation-name: $name;\n    -webkit-animation-duration: $duration;\n    -webkit-animation-iteration-count: $repeat;\n    -webkit-animation-timing-function: $timing;\n    -webkit-animation-delay: $delay;\n    -webkit-animation-fill-mode: forwards; // this prevents the animation from restarting!\n\n    animation-name: $name;\n    animation-duration: $duration;\n    animation-iteration-count: $repeat;\n    animation-timing-function: $timing;\n    animation-delay: $delay;\n    animation-fill-mode: forwards; // this prevents the animation from restarting!\n}\n\n// animation mixin - end\n\n////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////\n\n// heading mixins - start\n\n@mixin mkdfDefaultHeadingStyle() {\n    @include mkdfHeadingStyle();\n    font-weight: 600;\n    margin: 25px 0;\n    letter-spacing: -0.025em;\n    font-family: $default-text-font;\n    -ms-word-wrap: break-word;\n    word-wrap: break-word;\n    \n    a {\n        @include mkdfTypographyLayout();\n        \n        &:hover {\n            color: $first-main-color;\n        }\n    }\n}\n\n@mixin mkdfHeadingStyle($with-heading: null, $with-color: true) {\n    \n    @if ($with-color) {\n        color: $default-heading-color;\n    }\n    \n    @if ($with-heading == 'h1') {\n        @include mkdfH1();\n    } @else if ($with-heading == 'h2') {\n        @include mkdfH2();\n    } @else if ($with-heading == 'h3') {\n        @include mkdfH3();\n    } @else if ($with-heading == 'h4') {\n        @include mkdfH4();\n    } @else if ($with-heading == 'h5') {\n        @include mkdfH5();\n    } @else if ($with-heading == 'h6') {\n        @include mkdfH6();\n    }\n}\n\n@mixin mkdfBody() {\n    font-family: $additional-text-font;\n    font-size: 16px;\n    font-weight: 400;\n    line-height: 30px;\n    color: $default-text-color;\n    background-color: $default-background-color;\n    -webkit-font-smoothing: antialiased;\n}\n\n@mixin mkdfH1() {\n    font-size: 55px;\n    line-height: 1.23em;\n}\n\n@mixin mkdfH2() {\n    font-size: 40px;\n    line-height: 1.25em;\n}\n\n@mixin mkdfH3() {\n    font-size: 36px;\n    line-height: 1.16em;\n}\n\n@mixin mkdfH4() {\n    font-size: 30px;\n    line-height: 1.2em;\n}\n\n@mixin mkdfH5() {\n    font-size: 26px;\n    line-height: 1.23em;\n}\n\n@mixin mkdfH6() {\n    font-size: 20px;\n    line-height: 1.3em;\n    letter-spacing: 0;\n}\n\n// heading mixins - end\n\n////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////\n\n@mixin mkdfBlockquote($important : null) {\n    @include mkdfRelativeHolderLayout();\n    margin: 10px 0 $important;\n    padding: 20px 40px $important;\n    font-size: 18px $important;\n    line-height: 30px $important;\n    quotes: none;\n    box-sizing: border-box;\n    border: none $important;\n    color: $default-text-color $important;\n\n    > * {\n        @include mkdfTypographyLayout();\n        margin: 0;\n    }\n\n    &:after,\n    &:before{\n        content: '';\n    }\n\n    //&:before {\n    //    content: \"\\7b\";\n    //    font-family: \"ElegantIcons\";\n    //    font-size: 60px;\n    //    color: $first-main-color;\n    //    position: absolute;\n    //    top:50%;\n    //    @include mkdfTransform(translateY(-50%));\n    //}\n\n    cite,\n    .wp-block-quote__citation,\n    .wp-block-pullquote__citation,\n    footer {\n        display: block $important;\n        margin-top: 10px $important;\n        text-align: inherit $important;\n        font-size: 14px $important;\n        line-height: 1.3em $important;\n        letter-spacing: 0 $important;\n        font-style: normal  $important;\n        font-weight: 400 $important;\n        text-transform: none $important;\n    }\n\n    cite{\n        padding-left: 10%;\n        display: inline-block $important;\n    }\n\n}\n\n", "$breakpoints: (\n        laptop-landscape-large: 1440px,\n        laptop-landscape-mac: 1366px,\n        laptop-landscape-medium: 1280px,\n        laptop-landscape: 1200px,\n        ipad-landscape: 1024px,\n        ipad-portrait: 768px,\n        phone-landscape: 680px,\n        phone-portrait: 480px,\n        smaller-phone-portrait: 320px\n);\n\n$grid-width: 1100px;\n$grid-width-laptop-landscape: 950px;\n$grid-width-ipad-landscape: 768px;\n$grid-width-ipad-portrait: 600px;\n$grid-width-phone-landscape: 420px;\n$grid-width-phone-portrait: 300px;\n$grid-width-smaller-phone-portrait: 90%;\n\n$grid-width-boxed: 1150px;\n$grid-width-laptop-landscape-boxed: 1000px;\n$grid-width-ipad-landscape-boxed: 818px;\n$grid-width-ipad-portrait-boxed: 650px;\n$grid-width-phone-landscape-boxed: 470px;\n$grid-width-phone-portrait-boxed: 350px;\n$grid-width-smaller-phone-portrait-boxed: 92%;\n\n$grid-width-1300: 1300px;\n$grid-width-1200: 1200px;\n$grid-width-1000: 1000px;\n$grid-width-800: 800px;\n\n$default-text-font: '<PERSON><PERSON>', sans-serif;\n$additional-text-font: '<PERSON><PERSON><PERSON>', sans-serif;\n\n$first-main-color: #ea3d56;\n$first-main-color-dark-blue: #1b2c58;\n$first-main-color-medium-blue: #3745a5;\n$first-main-color-ligh-blue: #1f75ff;\n$first-main-color-yellow: #ffc40e;\n$first-main-color-green: #56c4c5;\n$default-heading-color: #1b2c58;\n$default-text-color: #868890;\n$shadow-color: inherit;\n\n\n$default-background-color: #fff;\n$additional-background-color: #f6f6f6;\n$default-border-color: rgba(#e1e1e1, 0.3);\n$default-border-radius: 4px;\n$default-box-shadow: 0 0 4.85px 0.15px rgba(#000, 0.09);\n\n$header-light-color: #fff;\n$header-light-hover-color: $first-main-color;\n$header-dark-color: $default-heading-color;\n$header-dark-hover-color: $first-main-color;\n\n// input elements\n$input-height: 50px;\n$sselect-input-height: $input-height;\n$input-vertical-padding: 22px;\n$input-horizontal-padding: 16px;\n$input-margin: 18px;\n\n// responsive breakpoints\n$laptop-landscape-large-plus-pixel: 1441px;\n$laptop-landscape-large: 1440px;\n$laptop-landscape-mac-plus-pixel: 1367px;\n$laptop-landscape-mac: 1366px;\n$laptop-landscape-medium-plus-pixel: 1281px;\n$laptop-landscape-medium: 1280px;\n$laptop-landscape-plus-pixel: 1201px;\n$laptop-landscape: 1200px;\n$ipad-landscape-plus-pixel: 1025px;\n$ipad-landscape: 1024px;\n$ipad-portrait-plus-pixel: 769px;\n$ipad-portrait: 768px;\n$phone-landscape-plus-pixel: 681px;\n$phone-landscape: 680px;\n$phone-portrait-plus-pixel: 481px;\n$phone-portrait: 480px;\n$smaller-phone-portrait-plus-pixel: 321px;\n$smaller-phone-portrait: 320px;\n\n$default-easing-function: ease;\n$default-transition-duration: .5s;", "/* ==========================================================================\n   Accordions shortcode style - begin\n   ========================================================================== */\n\n.mkdf-accordion-holder {\n    @include mkdfRelativeHolderLayout();\n\n    .mkdf-accordion-title {\n        position: relative;\n        cursor: pointer;\n        margin: 0;\n        box-sizing: border-box;\n        @include mkdfTransform(translateZ(0px));\n\t\tborder: 2px $default-border-color solid;\n\t\n\t    .mkdf-tab-title {\n\t\t    display: block;\n\t\t    line-height: inherit;\n\t    }\n\n        .mkdf-accordion-mark {\n            position: absolute;\n            top: 50%;\n\t        right: 0;\n            width: 40px;\n\t        height: 40px;\n\t        margin: -1px 0 0;\n\t        font-size: 40px;\n\t        line-height: 40px;\n            text-align: center;\n            @include mkdfTransform(translateY(-50%));\n            @include mkdfTransition(width 0.2s ease-in-out);\n\t\n\t        span {\n\t\t        position: absolute;\n\t\t        display: block;\n\t\t        width: 100%;\n\t\t        height: 100%;\n\t\t        font-size: inherit;\n\t\t        line-height: inherit;\n\t\t        @include mkdfTransition(opacity .2s ease-out);\n\t\t\n\t\t        &:before {\n\t\t\t        display: block;\n\t\t\t        line-height: inherit;\n\t\t        }\n\n\t\t        \n\t            \n\t\t        \n\t\t        \n\t        }\n\t        .mkdf-eye-line {\n\t        \tdisplay: block;\n\t        \t@include mkdfTransform (rotate(-45deg));\n\n\t        \t&:after {\n\t                content: '';\n\t                display: block;\n\t                height: 2px;\n\t                width: 100%;\n\t                position: absolute;\n\t                left: 0;\n\t                bottom: 19px;\n\t                background-color: currentColor;\n\t                @include mkdfTransition(width 0.2s ease-in-out);\n\t                \n\t            }\n\n\t        }\n\t        &:hover {\n\t        \t.mkdf-eye-line {\n\t        \t\t&:after {\n\t        \t\t\twidth: 100%;\n\t        \t\t}\n\t        \t}\n\t        }\n        }\n\t    \n\t    &.ui-state-active,\n\t    &.ui-state-hover {\n\t\t\n\t\t    .mkdf-accordion-mark {\n\t\t\t    \n\t\t\t    .mkdf-eye-line {\n\t        \t\t&:after {\n\t        \t\t\twidth: 0%;\n\t        \t\t\t\n\t        \t\t}\n\t        \t}\n\t\t    }\n\t    }\n\t    \n    }\n\n    .mkdf-accordion-content {\n        margin: 0;\n\t\tborder: 2px solid $default-border-color;\n\t\tborder-top: none;\n\t    \n\t    p {\n\t\t    margin: 0;\n\t    }\n    }\n\t\n\t&.mkdf-ac-boxed {\n\t\t\n\t\t&.mkdf-white-skin {\n\n\t\t\tcolor: #fff;\n\t\t\t\n\t\t\t.mkdf-accordion-title {\n\t\t\t\tcolor: #fff;\n\t\t\t}\n\t\t}\n\t\t\n\t\t.mkdf-accordion-title {\n\t\t\tmargin: 23px 0 0;\n\t\t\tpadding: 24px 70px 24px 33px;\n\t\t\t\n\t\t\t&:first-child {\n\t\t\t\tmargin: 0;\n\t\t\t}\n\t\t\t\n\t\t\t.mkdf-accordion-mark {\n\t\t\t\tright: 28px;\n\t\t\t}\n\t\t\t\n\t\t\t&.ui-state-active {\n\t\t\t\tborder-bottom: none;\n\t\t\t}\n\t\t}\n\t\t\n\t\t.mkdf-accordion-content {\n\t\t\tpadding: 11px 36px 35px 36px;\n\t\t}\n\t}\n\t\n\t&.mkdf-ac-simple {\n\t\tborder-bottom: 1px solid $default-border-color;\n\t\t\n\t\t.mkdf-accordion-title {\n\t\t\tpadding: 17px 0 17px 30px;\n\t\t\tborder-top: 1px solid $default-border-color;\n\t\t}\n\t\t\n\t\t.mkdf-accordion-content {\n\t\t\tborder-top: 1px solid transparent;\n\t\t\t\n\t\t\t&.ui-accordion-content-active {\n\t\t\t\tborder-color: $default-border-color;\n\t\t\t}\n\t\t}\n\t\t\n\t\t.mkdf-accordion-content {\n\t\t\tpadding: 21px 0 16px;\n\t\t}\n\t}\n}\n/* ==========================================================================\n   Accordions shortcode style - end\n   ========================================================================== */", "/* ==========================================================================\n   Banner shortcode style - begin\n   ========================================================================== */\n\n.mkdf-banner-holder {\n    @include mkdfRelativeHolderLayout();\n\t\n\t.touch & {\n\t\tcursor: pointer;\n\t}\n\t\n\t&.mkdf-visible-on-hover {\n\t\t\n\t\t&:hover {\n\t\t\t\n\t\t\t.mkdf-banner-text-holder {\n\t\t\t\topacity: 1;\n\t\t\t}\n\t\t}\n\t\t\n\t\t.mkdf-banner-text-holder {\n\t\t\topacity: 0;\n\t\t\t@include mkdfTransition(opacity .2s ease-out);\n\t\t}\n\t}\n\t\n\t&.mkdf-disabled {\n\t\t\n\t\t.mkdf-banner-text-holder {\n\t\t\tdisplay: none;\n\t\t}\n\t}\n\t\n\t&.mkdf-banner-info-centered {\n\t\t\n\t\t.mkdf-banner-text-holder {\n\t\t\tpadding: 70px 20px;\n\t\t\ttext-align: center;\n\t\t}\n\t}\n\t\n\t.mkdf-banner-image {\n\t\t@include mkdfRelativeHolderLayout();\n\t\t\n\t\timg {\n\t\t\tdisplay: block;\n\t\t}\n\t}\n\t\n\t.mkdf-banner-text-holder {\n\t\t@include mkdfAbsoluteHolderLayout();\n\t\tpadding: 35px;\n\t\tbackground-color: rgba($default-heading-color, .4);\n\t\tbox-sizing: border-box;\n\t\t\n\t\t@include ipad-portrait {\n\t\t\tpadding: 25px;\n\t\t}\n\t}\n\t\n\t.mkdf-banner-text-outer {\n\t\t@include mkdfTableLayout();\n\t}\n\t\n\t.mkdf-banner-text-inner {\n\t\tposition: relative;\n\t\tdisplay: table-cell;\n\t\theight: 100%;\n\t\twidth: 100%;\n\t\tvertical-align: bottom;\n\t}\n\t\n\t.mkdf-banner-subtitle {\n\t\tmargin: 0 0 4px;\n\t\tcolor: #fff;\n\t}\n\t\n\t.mkdf-banner-title {\n\t\tmargin: 0;\n\t\tcolor: #fff;\n\t\t\n\t\t.mkdf-banner-title-light {\n\t\t\tfont-weight: 300;\n\t\t}\n\t}\n\t\n\t.mkdf-banner-link-text {\n\t\tposition: relative;\n\t\tdisplay: inline-block;\n\t\tvertical-align: top;\n\t\tmargin: 11px 0 0;\n\t\tcolor: #fff;\n\t\tline-height: 1em;\n\t\tz-index: 2;\n\t\t@include mkdfTransform(translateZ(0));\n\t\t\n\t\t&:hover {\n\t\t\t\n\t\t\t.mkdf-banner-link-hover {\n\t\t\t\twidth: 100%;\n\t\t\t}\n\t\t}\n\t\t\n\t\t.mkdf-banner-link-original {\n\t\t\tposition: relative;\n\t\t\tdisplay: inline-block;\n\t\t\tvertical-align: top;\n\t\t\twidth: 100%;\n\t\t\t\n\t\t\tspan {\n\t\t\t\tcolor: inherit;\n\t\t\t}\n\t\t}\n\t\t\n\t\t.mkdf-banner-link-hover {\n\t\t\tposition: absolute;\n\t\t\ttop: 0;\n\t\t\tleft: 0;\n\t\t\twidth: 0.1%;\n\t\t\theight: 100%;\n\t\t\tdisplay: inline-block;\n\t\t\tvertical-align: top;\n\t\t\twhite-space: nowrap;\n\t\t\toverflow: hidden;\n\t\t\t@include mkdfTransition(width .4s ease-in-out);\n\t\t\t\n\t\t\tspan {\n\t\t\t\tcolor: $first-main-color;\n\t\t\t}\n\t\t}\n\t\t\n\t\t.mkdf-banner-link-icon,\n\t\t.mkdf-banner-link-label {\n\t\t\tposition: relative;\n\t\t\tdisplay: inline-block;\n\t\t\tvertical-align: top;\n\t\t}\n\t\t\n\t\t.mkdf-banner-link-icon {\n\t\t\tmargin: 0 2px 0 0;\n\t\t\tfont-size: 15px;\n\t\t}\n\t\t\n\t\t.mkdf-banner-link-label {\n\t\t\tfont-size: 14px;\n\t\t\tline-height: inherit;\n\t\t}\n\t}\n\n\t.mkdf-banner-link {\n\t\t@include mkdfAbsoluteHolderLayout();\n\t\tz-index: 1;\n\t}\n}\n/* ==========================================================================\n   Banner shortcode style - end\n   ========================================================================== */", "/* ==========================================================================\n   Button shortcode style - begin\n   ========================================================================== */\n\n.mkdf-btn {\n    @include mkdfButtonDefaultStyle();\n    @include mkdfButtonSize();\n    cursor: pointer;\n    \n    &.mkdf-btn-simple {\n        padding: 0 !important;\n        color: $default-text-color;\n        background-color: transparent;\n        border: 0;\n        vertical-align: middle;\n\n        .mkdf-btn-text {\n            display: inline-block;\n            vertical-align: middle;\n            line-height: 50px;\n\n        }\n\n\n        &.mkdf-btn-icon {\n            > i {\n                padding: 11px 9px;\n            }\n            \n    \n            .mkdf-btn-text {\n                position: relative;\n                \n            \n               \n                  \n                  &::after {\n                    content: '';\n                    position: relative;\n                    display: block;\n                    top: -18px;\n                    width: 100%;\n                    height: 1px;\n                    background-color: currentColor;\n                    @include mkdfTransform(scaleX(1));\n                    @include mkdfTransformOrigin(0 50%);\n                    -webkit-transition: -webkit-transform .4s ease-out;\n                    transition: transform .4s ease-out;\n                    \n                  }\n\n                \n            }\n\n            \n        }\n\n        &:hover {\n                .mkdf-btn-text {\n                    &::after {\n                        @include mkdfTransformOrigin(100% 50%);\n                        @include mkdfTransform(scaleX(0));\n                        @include mkdfAnimation(animate-btn-line .5s .2s forwards);\n                    }\n                    \n                    \n                }\n            }\n\n\n\n    }\n\n    &.mkdf-btn-solid {\n        @include mkdfButtonSolidColor();\n        @include mkdfTransition(all .2s ease-out);\n       \n\n        &:not(.mkdf-btn-custom-hover-color):hover {\n            //important because of inline color attribute. :not is used so we don't have to use important in JS\n            color: #fff !important;\n            \n\n        }\n\n        &:not(.mkdf-btn-custom-hover-bg):hover {\n            //important because of inline color attribute. :not is used so we don't have to use important in JS\n            background-color: #ff4661 !important;\n          \n        }\n\n        &:not(.mkdf-btn-custom-border-hover):hover {\n            //important because of inline color attribute. :not is used so we don't have to use important in JS\n            border-color: #ff4661 !important;\n\n        }\n\n        &:hover {\n            box-shadow: none !important;\n        }\n    }\n\n    &.mkdf-btn-outline {\n\t    @include mkdfButtonOutlineColor();\n\n        &:not(.mkdf-btn-custom-hover-color):hover {\n            //important because of inline color attribute. :not is used so we don't have to use important in JS\n            color: #fff !important;\n        }\n\n        &:not(.mkdf-btn-custom-hover-bg):hover {\n            //important because of inline color attribute. :not is used so we don't have to use important in JS\n            background-color: $first-main-color !important;\n        }\n\n        &:not(.mkdf-btn-custom-border-hover):hover {\n            //important because of inline color attribute. :not is used so we don't have to use important in JS\n            border-color: $first-main-color !important;\n        }\n    }\n    \n    &.mkdf-btn-small {\n        @include mkdfButtonSize(small);\n    }\n    \n    &.mkdf-btn-large {\n        @include mkdfButtonSize(large);\n    }\n    \n    &.mkdf-btn-huge {\n        @include mkdfButtonSize(huge);\n    }\n\n    &.mkdf-btn-icon {\n\n        > i,\n        > span:not(.mkdf-btn-text) {\n            position: relative;\n            display: inline-block;\n            vertical-align: middle;\n            margin: 0 10px 0 0;\n            font-size: 33px;\n            line-height: inherit;\n\n            &:before {\n                display: block;\n                line-height: inherit;\n            }\n        }\n    }\n}\n/* ==========================================================================\n   Button shortcode style - end\n   ========================================================================== */", "/* ==========================================================================\n   Call To Action shortcode style - begin\n   ========================================================================== */\n\n.mkdf-call-to-action-holder {\n    @include mkdfRelativeHolderLayout();\n\t\n\t.mkdf-cta-text-holder,\n\t.mkdf-cta-button-holder {\n\t\tposition: relative;\n\t\tdisplay: inline-block;\n\t\tvertical-align: middle;\n\t}\n\t\n\t.mkdf-cta-text-holder {\n\t\t\n\t\th1, h2, h3, h4, h5, h6 {\n\t\t\tmargin: 0;\n\t\t}\n\t}\n\t\n\t.mkdf-cta-button-holder {\n\t\t\n\t\t.mkdf-btn {\n\t\t\twhite-space: nowrap;\n\t\t}\n\t}\n\t\n\t/***** Layout Style - begin *****/\n\t\n\t&.mkdf-normal-layout {\n\t\t\n\t\t.mkdf-cta-inner {\n\t\t\tdisplay: table;\n\t\t}\n\t\t\n\t\t&:not(.mkdf-content-in-grid) {\n\t\t\t\n\t\t\t.mkdf-cta-inner {\n\t\t\t\twidth: 100%;\n\t\t\t}\n\t\t}\n\t\t\n\t\t.mkdf-cta-text-holder,\n\t\t.mkdf-cta-button-holder {\n\t\t\tdisplay: table-cell;\n\t\t\tbox-sizing: border-box;\n\t\t}\n\t\t\n\t\t.mkdf-cta-button-holder {\n\t\t\ttext-align: right;\n\t\t}\n\t}\n\t\n\t&.mkdf-simple-layout {\n\t\t\n\t\t.mkdf-cta-inner {\n\t\t\ttext-align: center;\n\t\t}\n\t\t\n\t\t.mkdf-cta-text-holder,\n\t\t.mkdf-cta-button-holder {\n\t\t\twidth: 100%;\n\t\t}\n\t\t\n\t\t.mkdf-cta-button-holder {\n\t\t\tmargin: 28px 0 0;\n\t\t}\n\t}\n\t\n\t/***** Layout Style - end *****/\n\t\n\t/***** Columns Space - begin *****/\n\t\n\t&.mkdf-two-halves-columns {\n\t\t\n\t\t.mkdf-cta-text-holder,\n\t\t.mkdf-cta-button-holder {\n\t\t\twidth: 50%;\n\t\t}\n\t}\n\t\n\t&.mkdf-two-thirds-columns {\n\t\t\n\t\t.mkdf-cta-text-holder {\n\t\t\twidth: 66.66666666666667%;\n\t\t}\n\t\t\n\t\t.mkdf-cta-button-holder {\n\t\t\twidth: 33.33333333333333%;\n\t\t}\n\t}\n\t\n\t&.mkdf-three-quarters-columns {\n\t\t\n\t\t.mkdf-cta-text-holder {\n\t\t\twidth: 75%;\n\t\t}\n\t\t\n\t\t.mkdf-cta-button-holder {\n\t\t\twidth: 25%;\n\t\t}\n\t}\n\t\n\t&.mkdf-four-fifths-columns {\n\t\t\n\t\t.mkdf-cta-text-holder {\n\t\t\twidth: 80%;\n\t\t}\n\t\t\n\t\t.mkdf-cta-button-holder {\n\t\t\twidth: 20%;\n\t\t}\n\t}\n\t\n\t/***** Columns Space - end *****/\n}\n/* ==========================================================================\n   Call To Action shortcode style - end\n   ========================================================================== */", "/* ==========================================================================\n   Clients Carousel shortcode style - begin\n   ========================================================================== */\n\n.mkdf-clients-carousel-holder {\n    @include mkdfRelativeHolderLayout();\n    \n\t.mkdf-cc-inner {\n\t\t@include mkdfRelativeHolderLayout();\n\t}\n\t\n\t.mkdf-cc-item {\n\t\t@include mkdfRelativeHolderLayout();\n\t\t\n\t\t.touch & {\n\t\t\tcursor: pointer;\n\t\t}\n\t\t\n\t\t.mkdf-cc-item {\n\t\t\tposition: relative;\n\t\t\tdisplay: block;\n\t\t}\n\t}\n\t.mkdf-cc-link {\n\t\tposition: relative;\n\t\tdisplay: inline-block;\n\t\tvertical-align: top;\n\t}\n\t/***** Hover Types - begin *****/\n\n\n\n\t\n\t&.mkdf-cc-hover-switch-images {\n\t\t\n\t\t.mkdf-cc-item {\n\t\t\tdisplay: flex;\n\t\t    align-items: center;\n\t\t    justify-content: center;\n\t\t\t\n\t\t\t&:hover {\n\t\t\t\t\n\t\t\t\t.mkdf-cc-image {\n\t\t\t\t\topacity: 0;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t.mkdf-cc-hover-image {\n\t\t\t\t\topacity: 1;\n\t\t\t\t}\n\t\t\t}\n\t\t\t\n\t\t\t.mkdf-cc-image {\n\t\t\t\tposition: relative;\n\t\t\t\tdisplay: block;\n\t\t\t\twidth: auto;\n\t\t\t\tmargin: 0 auto;\n\t\t\t\topacity: 1;\n\t\t\t\t-webkit-transform: translateZ(0);\n\t\t\t\t@include mkdfTransition(opacity .15s ease-out);\n\t\t\t}\n\t\t\t\n\t\t\t.mkdf-cc-hover-image {\n\t\t\t\tposition: absolute;\n\t\t\t\ttop: 0;\n\t\t\t\tleft: auto;\n\t\t\t\twidth: auto;\n\t\t\t\topacity: 0;\n\t\t\t    -webkit-transform: translateZ(0);\n\t\t\t\t@include mkdfTransition(opacity .15s ease-out);\n\t\t\t}\n\t\t}\n\t}\n\t&.mkdf-cc-hover-opacity-images {\n\t\t\n\t\t.mkdf-cc-item {\n\t\t\tdisplay: flex;\n\t\t    align-items: center;\n\t\t    justify-content: center;\n\t\t\t\n\t\t\t&:hover {\n\t\t\t\t\n\t\t\t\t.mkdf-cc-image {\n\t\t\t\t\topacity: 0.6;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t\n\t\t\t}\n\t\t\t\n\t\t\t.mkdf-cc-image {\n\t\t\t\tposition: relative;\n\t\t\t\tdisplay: block;\n\t\t\t\twidth: auto;\n\t\t\t\tmargin: 0 auto;\n\t\t\t\topacity: 1;\n\t\t\t\t-webkit-transform: translateZ(0);\n\t\t\t\t@include mkdfTransition(opacity .15s ease-out);\n\t\t\t}\n\t\t\t\n\t\t\t.mkdf-cc-hover-image {\n\t\t\t\tposition: absolute;\n\t\t\t\ttop: 0;\n\t\t\t\tleft: auto;\n\t\t\t\twidth: auto;\n\t\t\t\topacity: 0;\n\t\t\t    -webkit-transform: translateZ(0);\n\t\t\t\t@include mkdfTransition(opacity .15s ease-out);\n\t\t\t}\n\t\t}\n\t}\n\n\t&.mkdf-cc-hover-roll-over {\n\t\t\n\t\t.mkdf-cc-item {\n\t\t\toverflow: hidden;\n\t\t\t\n\t\t\t&:hover {\n\t\t\t\t\n\t\t\t\t.mkdf-cc-image {\n\t\t\t\t\t@include mkdfTransform(translateY(-100%));\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t.mkdf-cc-hover-image {\n\t\t\t\t\t@include mkdfTransform(translate(-50%, 0));\n\t\t\t\t}\n\t\t\t}\n\t\t\t\n\t\t\t.mkdf-cc-image {\n\t\t\t\tposition: relative;\n\t\t\t\tdisplay: block;\n\t\t\t\twidth: auto;\n\t\t\t\tmargin: 0 auto;\n\t\t\t\t@include mkdfTransitionTransform(.4s ease);\n\t\t\t}\n\t\t\t\n\t\t\t.mkdf-cc-hover-image {\n\t\t\t\tposition: absolute;\n\t\t\t\ttop: 0;\n\t\t\t\tleft: 50%;\n\t\t\t\twidth: auto;\n\t\t\t\t@include mkdfTransform(translate(-50%, 100%));\n\t\t\t\t@include mkdfTransitionTransform(.4s ease);\n\t\t\t}\n\t\t}\n\t}\n\t\n\t/***** Hover Types - end *****/\n}\n/* ==========================================================================\n   Clients Carousel shortcode style - end\n   ========================================================================== */", "/* ==========================================================================\n   clients Holder shortcode style - begin\n   ========================================================================== */\n\n.mkdf-clients-grid-holder {\n\t@include mkdfRelativeHolderLayout();\n\ttext-align: center;\n\t\n\t&.mkdf-cg-alignment-left {\n\t\ttext-align: left;\n\t}\n\t\n\t&.mkdf-cg-alignment-right {\n\t\ttext-align: right;\n\t}\n\t\n\t.mkdf-cc-link {\n\t\tposition: relative;\n\t\tdisplay: inline-block;\n\t\tvertical-align: top;\n\t}\n}\n\n/* ==========================================================================\n   clients Holder shortcode style - end\n   ========================================================================== */", "/* ==========================================================================\n   Countdown shortcode style - begin\n   ========================================================================== */\n\n.mkdf-countdown {\n\t@include mkdfRelativeHolderLayout();\n\t\n\t&.mkdf-light-skin {\n\t\t\n\t\t.countdown-row {\n\t\t\t\n\t\t\t.countdown-section {\n\t\t\t\t\n\t\t\t\t.countdown-amount,\n\t\t\t\t.countdown-period {\n\t\t\t\t\tcolor: #fff;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\t\n\t.countdown-rtl {\n\t\tdirection: rtl;\n\t}\n\t\n\t.countdown-row {\n\t\t@include mkdfRelativeHolderLayout();\n\t\ttext-align: center;\n\t\tclear: both;\n\t\t\n\t\t$columns_label: ('countdown-show1', 'countdown-show2', 'countdown-show3', 'countdown-show4', 'countdown-show5', 'countdown-show6');\n\t\t@for $i from 0 to length($columns_label) {\n\t\t\t&.#{nth($columns_label, $i+1)} {\n\t\t\t\t$column_width: 100% / ($i+1);\n\t\t\t\t\n\t\t\t\t.countdown-section {\n\t\t\t\t\twidth: $column_width;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\t\n\t\t.countdown-section {\n\t\t\t@include mkdfRelativeHolderLayout();\n\t\t\tpadding: 0 5px;\n\t\t\tbox-sizing: border-box;\n\t\t\t\n\t\t\t.countdown-amount {\n\t\t\t\tposition: relative;\n\t\t\t\tdisplay: block;\n\t\t\t\tcolor: $default-heading-color;\n\t\t\t\tfont-family: $default-text-font;\n\t\t\t\tletter-spacing: -0.025em;\n\t\t\t\tfont-size: 72px;\n\t\t\t\tline-height: 1em;\n\t\t\t\tfont-weight: 600;\n\t\t\t}\n\t\t\t\n\t\t\t.countdown-period {\n\t\t\t\tdisplay: block;\n\t\t\t\tfont-family: $default-text-font;\n\t\t\t\tfont-size: 15px;\n\t\t\t\tfont-weight: 700;\n\t\t\t\tfont-size: 14px;\n    \t\t\tletter-spacing: .025em;\n\t\t\t\ttext-transform: uppercase;\n\t\t\t}\n\t\t}\n\t}\n}\n/* ==========================================================================\n   Countdown shortcode style - end\n   ========================================================================== */", "/* ==========================================================================\n   Counter shortcode style - begin\n   ========================================================================== */\n\n.mkdf-counter-holder {\n    @include mkdfRelativeHolderLayout();\n    opacity: 0;\n    @include mkdfTransition(opacity 0.01s ease-in);\n\ttext-align: center;\n\t\n\t.mkdf-counter-inner {\n\t\tposition: relative;\n\t\tdisplay: inline-block;\n\t\tvertical-align: middle;\n\t}\n\n    .mkdf-counter {\n\t    height: 1em;\n\t    display: inline-block !important;\n\t    vertical-align: middle;\n\t    color: $default-heading-color;\n\t\tfont-family: $default-text-font;\n\t    font-size: 60px;\n\t\tletter-spacing: -0.025em;\n\t    line-height: 1em;\n\t    font-weight: 600;\n\t    overflow: hidden;\n    }\n\n    .mkdf-counter-title {\n\t    margin: 13px 0 0;\n\t\tfont-size:14px;\n\t\tletter-spacing: 0.025em;\n    }\n\n    .mkdf-counter-text {\n\t    margin: 14px 0 0;\n    }\n}\n/* ==========================================================================\n   Counter shortcode style - end\n   ========================================================================== */", "/* ==========================================================================\n   Custom Font shortcode style - begin\n   ========================================================================== */\n\n.mkdf-custom-font-holder {\n\t\n\t.mkdf-cf-typed-wrap {\n\t\twidth: 0;\n\t\twhite-space: nowrap;\n\t}\n\t\n\t.mkdf-cf-typed {\n\t\tdisplay: inline-block;\n\t\t\n\t\tspan {\n\t\t\tdisplay: none; //remove initial strings\n\t\t}\n\t\t\n\t\t~ .typed-cursor {\n\t\t\tdisplay: inline-block;\n\t\t\topacity: 1;\n\t\t\t-webkit-animation: blink 0.7s infinite;\n\t\t\tanimation: blink 0.7s infinite;\n\t\t}\n\t\t\n\t\t@-webkit-keyframes blink {\n\t\t\t0% {\n\t\t\t\topacity: 1;\n\t\t\t\tfilter: alpha(opacity=100);\n\t\t\t}\n\t\t\t50% {\n\t\t\t\topacity: 0;\n\t\t\t\tfilter: alpha(opacity=0);\n\t\t\t}\n\t\t\t100% {\n\t\t\t\topacity: 1;\n\t\t\t\tfilter: alpha(opacity=100);\n\t\t\t}\n\t\t}\n\t\t\n\t\t@keyframes blink {\n\t\t\t0% {\n\t\t\t\topacity: 1;\n\t\t\t\tfilter: alpha(opacity=100);\n\t\t\t}\n\t\t\t50% {\n\t\t\t\topacity: 0;\n\t\t\t\tfilter: alpha(opacity=0);\n\t\t\t}\n\t\t\t100% {\n\t\t\t\topacity: 1;\n\t\t\t\tfilter: alpha(opacity=100);\n\t\t\t}\n\t\t}\n\t}\n}\n/* ==========================================================================\n   Custom Font shortcode style - end\n   ========================================================================== */", "/* ==========================================================================\n   Dropcaps shortcode style - begin\n   ========================================================================== */\n\n.mkdf-dropcaps {\n    position: relative;\n    display: inline-block;\n    vertical-align: top;\n    float: left;\n    font-family: $default-text-font;\n    line-height: 60px;\n    font-size: 55px;\n    color: #999;\n    font-weight: 600;\n    text-align: center;\n    margin: 0 20px 0 0;\n\n    &.mkdf-square {\n        height: 50px;\n        width: 50px;\n        font-family: $default-text-font;\n        font-size: 36px;\n        line-height: 58px;\n        font-weight: 600;\n        color: #fff;\n        background-color: $default-heading-color;\n        margin: 2px 15px 0 0;\n        box-sizing: border-box;\n    }\n\n    &.mkdf-circle {\n        @extend .mkdf-square;\n        border-radius: 3em;\n    }\n}\n/* ==========================================================================\n   Dropcaps shortcode style - end\n   ========================================================================== */", "/* ==========================================================================\n   Elements Holder shortcode style - begin\n   ========================================================================== */\n\n.mkdf-elements-holder {\n\twidth: 100%;\n\tdisplay: table;\n\ttable-layout: fixed;\n\t\n\t&.mkdf-eh-full-height {\n\t\theight: 100%;\n\t}\n\t\n\t&.mkdf-ehi-float {\n\t\t\n\t\t.mkdf-eh-item {\n\t\t\tfloat: left;\n\t\t}\n\t}\n\t\n\t$columns_label: ('two', 'three', 'four', 'five', 'six');\n\t\n\t@for $i from 0 to length($columns_label) {\n\t\t&.mkdf-#{nth($columns_label,$i+1)}-columns {\n\t\t\t$column_width: 100%/($i+2);\n\t\t\t\n\t\t\t.mkdf-eh-item {\n\t\t\t\twidth: $column_width;\n\t\t\t}\n\t\t}\n\t}\n\n\t.mkdf-eh-item {\n\t\tdisplay: table-cell;\n\t\tvertical-align: middle;\n\t\theight: 100%;\n\t\tbackground-position: center;\n\t\tbackground-size: cover;\n\n\t\t&.mkdf-vertical-alignment-top {\n\t\t\tvertical-align: top;\n\t\t}\n\n\t\t&.mkdf-vertical-alignment-bottom {\n\t\t\tvertical-align: bottom;\n\t\t}\n\n\t\t&.mkdf-horizontal-alignment-center {\n\t\t\ttext-align: center;\n\t\t}\n\n\t\t&.mkdf-horizontal-alignment-right {\n\t\t\ttext-align: right;\n\t\t}\n\n\t\t.mkdf-elements-holder-item-inner {\n\t\t\twidth: 100%;\n\t\t}\n\t}\n\n\t.mkdf-ehi-content {\n\t\tpadding: 0 20px;\n\t}\n}\n/* ==========================================================================\n   Elements Holder shortcode style - end\n   ========================================================================== */", "/* ==========================================================================\n   Google Map shortcode style - begin\n   ========================================================================== */\n\n.mkdf-google-map-holder {\n\t@include mkdfRelativeHolderLayout();\n\t\n\t.mkdf-google-map-direction {\n\t\tposition: absolute;\n\t\ttop: 10px;\n\t\tleft: 10px;\n\t\tpadding: 0 8px;\n\t\tfont-size: 13px;\n\t\tline-height: 24px;\n\t\tcolor: $default-text-color;\n\t\tbackground-color: #fff;\n\t\tz-index: 999;\n\t\tbox-sizing: border-box;\n\t\t\n\t\t&:hover {\n\t\t\tcolor: $default-heading-color;\n\t\t}\n\t}\n\t\n\t.mkdf-google-map {\n\t\tdisplay: block;\n\t\twidth: 100%;\n\t\theight: 300px;\n\t\t\n\t\tiframe,\n\t\tobject,\n\t\tembed {\n\t\t\twidth: 100%;\n\t\t\tdisplay: block;\n\t\t}\n\t\t\n\t\timg {\n\t\t\tmax-width: none;\n\t\t}\n\t}\n\t\n\t.mkdf-snazzy-map {\n\t\tdisplay: none;\n\t}\n\t\n\t.mkdf-google-map-overlay {\n\t\tposition: absolute;\n\t\ttop: 0;\n\t\tleft: 0;\n\t\twidth: 100%;\n\t\theight: 100%;\n\t\tdisplay: none;\n\t\tz-index: 1000;\n\t}\n}\n\n/* ==========================================================================\n   Google Map shortcode style - end\n   ========================================================================== */", "/* ==========================================================================\n   Icon List Item shortcode style - begin\n   ========================================================================== */\n\n.mkdf-icon-list-holder {\n    position: relative;\n    display: table;\n    table-layout: fixed;\n    height: auto;\n    width: 100%;\n    margin-bottom: 8px;\n\t\n\t.mkdf-il-icon-holder,\n\t.mkdf-il-text {\n\t\tposition: relative;\n\t\tdisplay: table-cell;\n\t\tvertical-align: top;\n\t}\n\n    .mkdf-il-icon-holder {\n\t    width: 1%;\n\t    \n\t    > * {\n\t\t    position: relative;\n\t\t    display: inline-block;\n\t\t    vertical-align: top;\n\t\t    color: $default-heading-color;\n\t\t    font-size: 17px;\n\t\t    line-height: inherit;\n\t\t\n\t\t    &:before {\n\t\t\t    display: block;\n\t\t\t    line-height: inherit;\n\t\t    }\n\t    }\n    }\n\n    .mkdf-il-text {\n\t    width: 99%;\n\t    padding: 0 0 0 13px;\n\t    box-sizing: border-box;\n    }\n}\n/* ==========================================================================\n   Icon List Item shortcode style - end\n   ========================================================================== */", "/* ==========================================================================\n   Icon With Text shortcode style - begin\n   ========================================================================== */\n\n.mkdf-iwt {\n\t@include mkdfRelativeHolderLayout();\n\t@include mkdfTransition(all .2s ease-out);\n\n\t&:hover {\n\t\tcursor: pointer;\n\t}\n\t\t\n\t.mkdf-iwt-icon {\n\n\t\t@include mkdfTransition(all 0.3s ease-in-out);\n\t\t\n\t\t\n\t\ta {\n\t\t\tposition: relative;\n\t\t\tdisplay: inline-block;\n\t\t\tvertical-align: middle;\n\t\t}\n\t\t\n\t\t.mkdf-icon-shortcode {\n\t\t\tline-height: 1;\n\t\t\t\n\t\t\t&.mkdf-circle,\n\t\t\t&.mkdf-square {\n\t\t\t\tline-height: 2;\n\t\t\t}\n\t\t}\n\n\n\t}\n\t\n\t.mkdf-iwt-title {\n\t\tmargin: 0;\n\t\tline-height: 1.2em;\n\t\t\n\t\ta {\n\t\t\tposition: relative;\n\t\t\tdisplay: inline-block;\n\t\t\tvertical-align: top;\n\t\t}\n\t}\n\t\n\t.mkdf-iwt-title-text {\n\t\tdisplay: block;\n\t}\n\t\n\t.mkdf-iwt-text {\n\t\tmargin: 6px 0;\n\t\tfont-weight: 500;\n\n\t\ta {\n\n\t\t\t&:hover {\n\t\t\t\tcolor: $first-main-color-medium-blue !important;\n\t\t\t}\n\t\t}\n\t}\n\t&:hover {\n\t\t.mkdf-iwt-icon {\n\t\t\t-webkit-transform: translate(0, -3px);\n\t        @include mkdfTransform(translate(0, -3px));\n\t\t}\n\t}\n\t\n\t&.mkdf-iwt-icon-left {\n\t\twidth: auto;\n\t\t\n\t\t.mkdf-iwt-icon,\n\t\t.mkdf-iwt-content {\n\t\t\tdisplay: table-cell;\n\t\t\tvertical-align: top;\n\t\t}\n\t\t\n\t\t.mkdf-iwt-icon {\n\t\t\tposition: relative;\n\t\t\ttop: 1px;\n\t\t\tpadding: 5px 0 0 8px;\n\t\t\t\n\t\t\timg {\n\t\t\t\tmax-width: none;\n\t\t\t}\n\t\t}\n\t\t\n\t\t.mkdf-iwt-content {\n\t\t\tpadding: 0 0 0 22px;\n\t\t\tvertical-align: middle;\n\t\t}\n\n\t}\n\t\n\t&.mkdf-iwt-icon-left-from-title {\n\t\t\n\t\t.mkdf-iwt-icon,\n\t\t.mkdf-iwt-title-text {\n\t\t\tposition: relative;\n\t\t\tdisplay: table-cell;\n\t\t\tvertical-align: middle;\n\t\t}\n\n\t\t.mkdf-iwt-icon {\n\t\t\t\n\t\t\t.mkdf-icon-element {\n\t\t\t\t@include mkdfTransition(none);\n\t\t\t}\n\t\t\t\n\t\t\timg {\n\t\t\t\tmax-width: none;\n\t\t\t}\n\t\t}\n\t\t\n\t\t.mkdf-iwt-title-text {\n\t\t\tpadding: 0 0 0 17px;\n\t\t}\n\t}\n\t\n\t&.mkdf-iwt-icon-top {\n\t\ttext-align: center;\n\t\t\n\t\t.mkdf-iwt-content {\n\t\t\tpadding: 23px 0 0;\n\t\t}\n\t}\n\n\t&.boxed-shadow {\n\t\twidth:100%;\n\t\tpadding:30px;\n\t\tborder-radius: 8px;\n\t\tbox-sizing:border-box;\n\t\tbackground: #fff;\n\t\t@include mkdfTransition(all .2s ease-out);\n\n\t\n\n\t\t.mkdf-iwt-text {\n\t\t\tfont-size: 11px;\n\t\t\tline-height: 1em;\n\t\t\tletter-spacing: 0.075em;\n\t\t}\n\t}\n\n\t&.boxed-hover-shadow {\n\t\twidth:100%;\n\t\tpadding:30px;\n\t\tborder-radius: 8px;\n\t\tbox-sizing:border-box;\n\t\tbackground: #fff;\n\n\t\t@include laptop-landscape-mac {\n\t\t\t&.mkdf-iwt-icon-medium {\n\t\t\t\tpadding: 30px 30px 30px 20px !important;\n\t\t\t}\n\t\t}\n\n\t\t.mkdf-iwt-text {\n\t\t\tfont-size: 11px;\n\t\t\tline-height: 1em;\n\t\t\tletter-spacing: 0.075em;\n\t\t}\n\t}\n}\n/* ==========================================================================\n   Icon With Text shortcode style - end\n   ========================================================================== */", "/* ==========================================================================\n   Icon shortcode style - begin\n   ========================================================================== */\n\n.mkdf-icon-shortcode {\n    position: relative;\n    display: inline-block;\n    vertical-align: middle;\n\tline-height: 1.1em;\n\t\n    &.mkdf-circle,\n    &.mkdf-square {\n        width: 2em;\n        height: 2em;\n        line-height: 2em;\n        text-align: center;\n        background-color: $first-main-color;\n        @include mkdfTransition(background-color .15s ease-in-out, border-color .15s ease-in-out);\n\n        a {\n\t        position: relative;\n\t        display: inline-block;\n\t        vertical-align: top;\n\t        width: 100%;\n\t        height: 100%;\n        }\n\n        .mkdf-icon-element {\n            color: #fff;\n            line-height: inherit;\n        }\n    }\n\n    &.mkdf-circle {\n        border-radius: 50%;\n\n   \n        &:hover {\n            \n        }\n       \n    }\n\n\n    .mkdf-icon-element {\n\t    display: block;\n\t    line-height: inherit;\n        @include mkdfTransition(color .15s ease-in-out);\n\t    \n\t    &:before {\n\t\t    display: block;\n\t\t    line-height: inherit;\n\t    }\n    }\n    &.mkdf-icon-switch{\n        overflow: hidden;\n\n        .mkdf-icon-original{\n            @include mkdfTransform(translateY(0));\n            @include mkdfTransitionTransform($default-transition-duration $default-easing-function);\n        }\n        .mkdf-icon-duplicate{\n            position: absolute;\n            top: 100%;\n            left: 0;\n            width: 100%;\n            height: 100%;\n            @include mkdfTransform(translateY(0));\n            @include mkdfTransitionTransform($default-transition-duration $default-easing-function);\n        }\n\n        &:hover{\n\n            .mkdf-icon-original{\n                @include mkdfTransform(translateY(-100%));\n            }\n\n            .mkdf-icon-duplicate{\n                @include mkdfTransform(translateY(-100%));\n            }\n        }\n    }\n}\n\n.mkdf-icon-animation-holder {\n\tposition: relative;\n\tdisplay: inline-block;\n\tvertical-align: middle;\n    @include mkdfTransform(scale(0));\n    @include mkdfTransition(transform 0.15s ease-in-out);\n\n    &.mkdf-icon-animation-show {\n        @include mkdfTransform(scale(1));\n    }\n}\n\n.mkdf-icon-tiny {\n    font-size: 1.33333333em;\n    line-height: .75em;\n    vertical-align: -15%;\n}\n\n.mkdf-icon-small {\n    font-size: 2em;\n}\n\n.mkdf-icon-medium {\n    font-size: 3em;\n}\n\n.mkdf-icon-large {\n    font-size: 4em;;\n}\n\n.mkdf-icon-huge {\n    font-size: 5em;\n}\n/* ==========================================================================\n   Icon shortcode style - end\n   ========================================================================== */", "/* ==========================================================================\n   Image Gallery shortcode style - begin\n   ========================================================================== */\n\n.mkdf-image-gallery {\n\t@include mkdfRelativeHolderLayout();\n\n\t.owl-nav{\n\n\t\t@include phone-landscape {\n\t\t\tdisplay: none;\n\t\t}\n\n\t\t.owl-prev, .owl-next{\n\t\t\ttransition: opacity .2s ease;\n\t\t\t@include mkdfTransform(translateY(-50%) !important);\n\n\t\t\t&.disabled{\n\t\t\t\topacity: 0;\n\t\t\t}\n\n\t\t\tspan{\n\t\t\t\tfont-size: 48px;\n\t\t\t\tcolor: #fff;\n\t\t\t\twidth: 80px;\n\t\t\t\theight: 80px;\n\t\t\t\tline-height: 80px;\n\t\t\t\tbackground-color: $first-main-color;\n\t\t\t\tborder-radius: 50%;\n\t\t\t\t-webkit-box-shadow: 0 -5px 10px rgba(234,61,86,.3);\n\t\t\t\t-moz-box-shadow: 0 -5px 10px rgba(234,61,86,.3);\n\t\t\t\tbox-shadow: 0 -5px 10px rgba(234,61,86,.3);\n\t\t\t}\n\t\t}\n\n\t\t.owl-prev{\n\t\t\tleft: 13%;\n\n\t\t}\n\n\t\t.owl-next{\n\t\t\tright: 13%;\n\t\t}\n\t}\n\n\t.owl-dots {\n\n\t\tdisplay: none;\n\n\t\t@include phone-landscape {\n\t\t\tdisplay: block;\n\t\t}\n\t}\n\n\t&.mkdf-ig-carousel-type {\n\n\t\t.owl-item:first-child {\n\t\t\tmargin-left: -346px;\n\n\t\t\t@media screen and (max-width: 1366px) {\n\t\t\t\tmargin-left: -247px;\n\t\t\t}\n\n\t\t\t@media screen and (max-width: 1024px) {\n\t\t\t\tmargin-left: -82px;\n\t\t\t}\n\n\t\t\t@media screen and (max-width: 768px) {\n\t\t\t\tmargin-left: 0;\n\t\t\t}\n\t\t}\n\t}\n\n\t&.mkdf-has-shadow {\n\n\t\t.mkdf-ig-image-inner {\n\t\t\tbox-shadow: $default-box-shadow;\n\t\t}\n\n\t\t&.mkdf-ig-slider-type,\n\t\t&.mkdf-ig-carousel-type {\n\n\t\t\t.owl-stage-outer {\n\t\t\t\tpadding: 0 0 20px;\n\t\t\t}\n\n\t\t\t.mkdf-ig-image {\n\t\t\t\tbox-shadow: $default-box-shadow;\n\t\t\t}\n\t\t}\n\t}\n\n\t.mkdf-ig-image {\n\n\t\ta, img {\n\t\t\tposition: relative;\n\t\t\tdisplay: block;\n\t\t}\n\t}\n\n\t.mkdf-ig-image-inner {\n\t\t@include mkdfRelativeHolderLayout();\n\t}\n\n\t.mkdf-ig-slider {\n\t\t@include mkdfRelativeHolderLayout();\n\t}\n\n\t/***** Image Gallery Masonry Style - begin *****/\n\n\t&.mkdf-ig-masonry-type {\n\n\t\t.mkdf-ig-image {\n\n\t\t\t&.mkdf-fixed-masonry-item {\n\n\t\t\t\t.mkdf-ig-image-inner,\n\t\t\t\ta {\n\t\t\t\t\theight: 100%;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\t/***** Image Gallery Masonry Style - end *****/\n\n\t/***** Custom Link Behavior Style - begin *****/\n\n\t&.mkdf-image-behavior-custom-link {\n\n\t\t.mkdf-ig-image {\n\n\t\t\ta {\n\t\t\t\t@include mkdfImageOverlayHoverStyle();\n\n\t\t\t\timg {\n\t\t\t\t\tborder-radius: 10px;\n\t\t\t\t}\n\n\t\t\t\t&:after {\n\t\t\t\t\tborder-radius: 10px;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\t&.mkdf-ig-carousel-type {\n\t\t\t.mkdf-ig-image {\n\t\t\t\t@include mkdfTransition(all 0.3s ease-in-out);\n\t\t\t\ta {\n\t\t\t\t\t&:after {\n\t\t\t\t\t\tdisplay: none;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t&:hover {\n\t\t\t\t\tbox-shadow: 6px 36px 67px -27px rgba(0,0,0,.1);\n\t\t\t\t}\n\t\t\t}\n\n\t\t}\n\n\t\t.owl-dot span {\n\t\t\tbackground:rgba(0,0,0,0.25);\n\t\t\tborder: 0;\n\n\t\t}\n\n\t\t.owl-dot.active span {\n\t\t\tbackground:rgba(0, 0, 0, 0.5);\n\t\t}\n\n\t}\n\n\t/***** Custom Link Behavior Style - end *****/\n\n\t/***** Lightbox Behavior Style - begin *****/\n\n\t&.mkdf-image-behavior-lightbox {\n\n\t\t.mkdf-ig-image {\n\n\t\t\tposition: relative;\n\n\t\t\ta {\n\t\t\t\t&:after {\n\t\t            @include mkdfAbsoluteHolderLayout();\n\t\t            content: '';\n\t\t            background-color: #74cccd;\n\t\t            opacity: 0;\n\t\t            @include mkdfTransition(opacity .2s ease-in-out);\n\t\t        }\n\n\t\t        &:before {\n\t\t        \t\n\t        \t    content: \"\\4c\";\n\t\t\t\t    font-family: ElegantIcons;\n\t\t\t\t    position: absolute;\n\t\t\t\t    font-size: 72px;\n\t\t\t\t    color: #fff;\n\t\t\t\t    opacity: 0;\n\t\t\t\t    text-align: center;\n\t\t\t\t    box-sizing: border-box;\n\t\t\t\t    top: calc(50% - 36px);\n\t\t\t\t    left: calc(50% - 36px);\n\t\t\t\t    right: 0;\n\t\t\t\t    z-index: 9;\n\t\t\t\t   @include mkdfTransition(all .4s ease-in-out);\n\t\t\t\t    transform-origin: 50% 50%;\n\t\t\t\t    width: 72px;\n\t\t\t\t    height: 72px;\n\t\t\t\t    line-height: 72px;\n\t\t\t\t    display: block;\n\n\n\t\t        }\n\t\t\t}\n\n\t\t\t&:hover {\n\n\t\t\t\ta {\n\t\t\t\t\t&:after {\n\t\t\t\t\t\topacity: 1;\n\t\t\t\t\t}\n\t\t\t\t\t&:before {\n\t\t\t\t\t\topacity: 1;\n\t\t\t\t\t\t@include mkdfTransform (rotate(90deg));\n\t\t\t\t\t\t\n\t\t\t\t\t\t   \n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\t/***** Lightbox Behavior Style - end *****/\n\n\t/***** Zoom Behavior Style - begin *****/\n\n\t&.mkdf-image-behavior-zoom {\n\n\t\t.mkdf-ig-image {\n\n\t\t\t.touch & {\n\t\t\t\tcursor: pointer;\n\t\t\t}\n\n\t\t\t&:hover {\n\n\t\t\t\timg {\n\t\t\t\t\t@include mkdfTransform(scale(1.04));\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t.mkdf-ig-image-inner {\n\t\t\t\toverflow: hidden;\n\t\t\t}\n\n\t\t\timg {\n\t\t\t\t@include mkdfTransform(scale(1));\n\t\t\t\t@include mkdfTransitionTransform(.3s ease-in-out);\n\t\t\t}\n\t\t}\n\t}\n\n\t/***** Zoom Behavior Style - end *****/\n\n\t/***** Grayscale Behavior Style - begin *****/\n\n\t&.mkdf-image-behavior-grayscale {\n\n\t\t.mkdf-ig-image {\n\t\t\toverflow: hidden;\n\n\t\t\t.touch & {\n\t\t\t\tcursor: pointer;\n\t\t\t}\n\n\t\t\t&:hover {\n\n\t\t\t\timg {\n\t\t\t\t\t-webkit-filter: grayscale(0);\n\t\t\t\t\tfilter: none;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\timg {\n\t\t\t\tfilter: url('img/desaturate.svg#grayscale');\n\t\t\t\t-webkit-filter: grayscale(100%);\n\t\t\t\t-moz-filter: grayscale(100%);\n\t\t\t\tfilter: gray;\n\t\t\t\tfilter: grayscale(100%);\n\t\t\t\t@include mkdfTransition(all .3s ease-in-out);\n\t\t\t}\n\t\t}\n\t}\n\n\t/***** Grayscale Behavior Style - end *****/\n}\n\n/* ==========================================================================\n   Image Gallery shortcode style - end\n   ========================================================================== */", "/* ==========================================================================\n   Image With Text shortcode style - begin\n   ========================================================================== */\n\n.mkdf-image-with-text-holder {\n    @include mkdfRelativeHolderLayout();\n    @include mkdfTransition(all 0.3s ease-in-out);\n\n    \n\t\n\t&.mkdf-has-shadow {\n\t\t\n\t\t.mkdf-iwt-image {\n\t\t\tbox-shadow: 0 0 42.85px 7.15px rgba(0,0,0,.09);\n\t\t}\n\t}\n\t\n\t.mkdf-iwt-image {\n\t\t@include mkdfRelativeHolderLayout();\n\t\t\n\t\twidth: auto;\n\t\t\n\t\ta, img {\n\t\t\tposition: relative;\n\t\t\tdisplay: block;\n\t\t\t@include mkdfTransition(all .3s ease);\n\t\t}\n\n\t\t.mkdf-icon-shortcode {\n\t\t\tposition:absolute;\n\t\t\tright: -20px;\n\t\t\ttop: -15px;\n\t\t\tz-index: 5;\n\t\t\tfont-size: 20px;\n\t\t\t@include mkdfTransition(all .3s ease-out);\n\n\n\t\t}\n\n\t\t&:hover {\n\n\t\t\t.mkdf-icon-shortcode {\n\n\t\t\t\tanimation: mkdfPulsesmallfirst 1.8s infinite;\n\t\t\t}\n\n\t\t\t\t\n\t\t}\n\t}\n\t\n\t.mkdf-iwt-text-holder {\n\t\t@include mkdfRelativeHolderLayout();\n\t}\n\t\n\t.mkdf-iwt-title {\n\t\tmargin: 40px 0 0;\n\t}\n\t\n\t.mkdf-iwt-text {\n\t\tmargin: 18px 0 0;\n\t\tline-height: 1.8em;\n\t}\n\t\n\t/***** Custom Link Behavior Style - begin *****/\n\t\n\t&.mkdf-image-behavior-custom-link {\n\t\t\n\t\t&:hover {\n            -webkit-transform: translate(0, -8px);\n            @include mkdfTransform(translate(0, -8px));\n\t\t\t\n\n\t\t}\n\t}\n\t\n\t/***** Custom Link Behavior Style - end *****/\n\t\n\t/***** Lightbox Behavior Style - begin *****/\n\t\n\t&.mkdf-image-behavior-lightbox {\n\t\t\n\t\t.mkdf-iwt-image {\n\t\t\t\n\t\t\ta {\n\t\t\t\t@include mkdfImageOverlayHoverStyle();\n\t\t\t}\n\t\t}\n\t}\n\t\n\t/***** Lightbox Behavior Style - end *****/\n\t\n\t/***** Zoom Behavior Style - begin *****/\n\t\n\t&.mkdf-image-behavior-zoom {\n\t\t\n\t\t.mkdf-iwt-image {\n\t\t\toverflow: hidden;\n\t\t\t\n\t\t\t.touch & {\n\t\t\t\tcursor: pointer;\n\t\t\t}\n\t\t\t\n\t\t\t&:hover {\n\t\t\t\t\n\t\t\t\timg {\n\t\t\t\t\t@include mkdfTransform(scale(1.04));\n\t\t\t\t}\n\t\t\t}\n\t\t\t\n\t\t\timg {\n\t\t\t\t@include mkdfTransform(scale(1));\n\t\t\t\t@include mkdfTransitionTransform(.3s ease-in-out);\n\t\t\t}\n\t\t}\n\t}\n\t\n\t/***** Zoom Behavior Style - end *****/\n\t\n\t/***** Grayscale Behavior Style - begin *****/\n\t\n\t&.mkdf-image-behavior-grayscale {\n\t\t\n\t\t.mkdf-iwt-image {\n\t\t\toverflow: hidden;\n\t\t\t\n\t\t\t.touch & {\n\t\t\t\tcursor: pointer;\n\t\t\t}\n\t\t\t\n\t\t\t&:hover {\n\t\t\t\t\n\t\t\t\timg {\n\t\t\t\t\t-webkit-filter: grayscale(0);\n\t\t\t\t\tfilter: none;\n\t\t\t\t}\n\t\t\t}\n\t\t\t\n\t\t\timg {\n\t\t\t\tfilter: url('img/desaturate.svg#grayscale');\n\t\t\t\t-webkit-filter: grayscale(100%);\n\t\t\t\t-moz-filter: grayscale(100%);\n\t\t\t\tfilter: gray;\n\t\t\t\tfilter: grayscale(100%);\n\t\t\t\t@include mkdfTransition(all .3s ease-in-out);\n\t\t\t}\n\t\t}\n\t}\n\t\n\t/***** Grayscale Behavior Style - end *****/\n\n\n}\n/***** Landing Appeared Item start *****/\n\n\n\t.custom-image-padding-row {\n\n\t\t\n\t\t\n\t\t-webkit-transition: opacity .22s cubic-bezier(0.23, 1, 0.32, 1), -webkit-transform 0s cubic-bezier(0.23, 1, 0.32, 1);\n\t\ttransition: opacity .22s cubic-bezier(0.23, 1, 0.32, 1), transform 0s cubic-bezier(0.23, 1, 0.32, 1);\n\n\t\t.mkdf-image-with-text-holder {\n\n\t\t\t.mkdf-iwt-image {\n\n\t\t\t\topacity: 0;\n\t\t\t\t@include mkdfTransform(scale(.6));\n\t\t\t\t-webkit-transition: -webkit-transform .6s cubic-bezier(0.18, 0.89, 0.32, 1), opacity .2s ease-out;\n\t\t\t\ttransition: transform .4s cubic-bezier(0.18, 0.89, 0.32, 1), opacity .2s ease-out;\n\n\t\t\t\t&.mkdf-landing-appeared {\n\n\t\t\t\t\topacity: 1;\n\t\t\t\t\t@include mkdfTransform(scale(1));\n\n\t\t\t\t}\n\t\t\t}\n\n\t\t}\n\n\t}\n\n\t\n\t\n\n\n    /***** Landing Appeared Item end *****/\n/* ==========================================================================\n   Image With Text shortcode style - end\n   ========================================================================== */\n", "/* ==========================================================================\n   Pie Chart shortcode style - begin\n   ========================================================================== */\n\n.mkdf-pie-chart-holder {\n    @include mkdfRelativeHolderLayout();\n\topacity: 0;\n\t@include mkdfTransition(opacity .2s ease-in);\n\t\n    .mkdf-pc-percentage {\n        position: relative;\n        display: block;\n        height: 176px;\n        width: 176px;\n        line-height: 176px;\n\t    text-align: center;\n\t    margin: 0 auto;\n    \n        canvas {\n            position: absolute;\n            top: 0;\n            left: 0;\n        }\n        \n        .mkdf-pc-percent {\n            position: relative;\n            display: inline-block;\n            vertical-align: middle;\n            color: $default-heading-color;\n\t        font-size: 30px;\n\t        line-height: inherit;\n\t        font-weight: 600;\n            font-family: $default-text-font;\n    \n            &:after {\n                position: relative;\n                content: '%';\n\t            font-size: 30px;\n            }\n        }\n    }\n\n    .mkdf-pc-text-holder {\n        @include mkdfRelativeHolderLayout();\n        text-align: center;\n        margin: 38px 0 0;\n     \n\t    \n\t    .mkdf-pc-title {\n\t\t    margin: 0;\n\t    }\n\t\n\t    .mkdf-pc-text {\n\t\t    margin: 7px 0 0;\n            font-family: $default-text-font;\n            font-size: 12px;\n            font-weight: 700;\n            letter-spacing: 0.09em;\n\t    }\n    }\n}\n/* ==========================================================================\n   Pie Chart shortcode style - end\n   ========================================================================== */", "/* ==========================================================================\n   Pricing Tables shortcode style - begin\n   ========================================================================== */\n\n.mkdf-pricing-tables {\n    @include mkdfRelativeHolderLayout();\n}\n\n.mkdf-price-table {\n    @media screen and (max-width: 680px){\n        padding: 0 0 37px !important;\n\n        &:last-child{\n            padding-bottom: 0 !important;\n        }\n    }\n\n    .mkdf-pt-inner {\n        @include mkdfRelativeHolderLayout();\n        border: 2px solid rgba(237,238,239,0.6);\n        box-sizing: border-box;\n\n        > ul {\n            list-style: none;\n            margin: 0;\n            padding: 0;\n\n            > li {\n                margin: 0;\n                text-align: center;\n                box-sizing: border-box;\n\n                &.mkdf-pt-title-holder {\n                    padding: 10px 20px;\n                    position: relative;\n                    height: 203px;\n                    color: $default-heading-color;\n                    font-size: 18px;\n                    line-height: 26px;\n                    font-weight: 600;\n\n                    .mkdf-pt-active-title{\n                        display: block;\n                        font-family: $default-text-font;\n                        color: $first-main-color;\n                        font-size: 14px;\n                        font-weight: 700;\n                        letter-spacing: .1em;\n                        text-transform: uppercase;\n                        padding: 15px 0 17px;\n                    }\n\t                \n                    .mkdf-pt-title {\n                        display: block;\n                        font-size: 36px;\n                        font-family: $default-text-font;\n                        letter-spacing: -.025em;\n                        font-weight: 600;\n\t                    padding: 28px 0 22px;\n\t                    box-sizing: border-box;\n                    }\n                }\n\n                &.mkdf-pt-content {\n\n                    > ul {\n                        list-style: none;\n                        margin: 0;\n                        padding: 0;\n                        display: flex;\n                        flex-direction: column;\n\n                        > li{\n                            padding: 20px;\n                            line-height: 33px;\n                            border-top: 2px solid $default-border-color;\n                            font-size: 20px;\n                            font-weight: 600;\n                            color: $first-main-color-dark-blue;\n                            letter-spacing: -.025em;\n\n                            &:last-child{\n                                border-bottom: 2px solid $default-border-color;\n                            }\n                        }\n                    }\n                }\n\n                &.mkdf-pt-prices {\n                    position: relative;\n\t\t\t\t\tpadding: 33px 15px 28px;\n                    border-bottom: 2px solid $default-border-color;\n\t                \n                    .mkdf-pt-value {\n                        position: relative;\n                        display: inline-block;\n                        vertical-align: middle;\n                        font-size: 60px;\n                        font-weight: 600;\n                        line-height: 1em;\n                        color: $default-heading-color;\n                        font-family: $default-text-font;\n                    }\n\n                    .mkdf-pt-price {\n                        position: relative;\n                        display: inline-block;\n                        vertical-align: middle;\n                        font-size: 60px;\n                        font-weight: 600;\n                        line-height: 1em;\n                        color: $default-heading-color;\n                        font-family: $default-text-font;\n                    }\n\n                    .mkdf-pt-mark {\n                        position: relative;\n                        display: block;\n                        font-size: 14px;\n                        font-weight: 600;\n                        line-height: 1em;\n                        color: rgba(#ccc, 0.8);\n                        font-family: $default-text-font;\n                        letter-spacing: .1em;\n                        margin: 12px 0 0;\n                    }\n                }\n\n                &.mkdf-pt-button {\n                    padding: 0;\n\n                    a{\n                        padding: 24px 0;\n                        width: 100%;\n                        border: none !important;\n                        color: $first-main-color-dark-blue;\n\n                        &.mkdf-btn-solid{\n                            color: #fff !important;\n                        }\n                    }\n                }\n            }\n        }\n    }\n}\n\n.mkdf-pt-active-item.mkdf-price-table{\n    @include mkdfTransform(translateY(-85px));\n    z-index: 10;\n\n    @media screen and (max-width: 680px){\n        @include mkdfTransform(none);\n    }\n\n    .mkdf-pt-inner {\n        border-color: transparent;\n        border-left: 0;\n        border-right: 0;\n        border-bottom: 0;\n        width: calc(100% + 6px);\n        margin: 0 -3px;\n\n        @media screen and (max-width: 680px){\n            width: 100%;\n            margin: 0;\n        }\n\n        > ul {\n\n            > li {\n\n                &.mkdf-pt-title-holder {\n                    height: 203px + 85px;\n\n                    .mkdf-pt-title{\n                        padding: 0 0 22px;\n                    }\n                }\n\n                &.mkdf-pt-button {\n                    padding: 0;\n\n                    a{\n                        padding: 26px 0;\n                        margin-top: -2px;\n                    }\n                }\n            }\n        }\n    }\n}\n\n/* ==========================================================================\n   Custom left menu style - start\n   ========================================================================== */\n\n   .mkdf-custom-opacity {\n       \n        -webkit-transition: opacity .22s cubic-bezier(0.23, 1, 0.32, 1), -webkit-transform 0s cubic-bezier(0.23, 1, 0.32, 1);\n        transition: opacity .22s cubic-bezier(0.23, 1, 0.32, 1), transform 0s cubic-bezier(0.23, 1, 0.32, 1);\n\n    h5 {\n        @include mkdfTransition(all 0.3s ease-in-out);\n        opacity: 0;\n        -webkit-transition: -webkit-transform .6s cubic-bezier(0.18, 0.89, 0.32, 1), opacity .2s ease-out;\n        transition: transform .4s cubic-bezier(0.18, 0.89, 0.32, 1), opacity .2s ease-out;\n\n        \n\n        &.mkdf-appeared {\n            opacity: 1;\n        }\n\n        &:hover {\n            opacity: 0.6;\n        }\n    }\n   }\n\n   /* ==========================================================================\n   Custom left menu style - start\n   ========================================================================== */\n/* ==========================================================================\n   Pricing Tables shortcode style - end\n   ========================================================================== */", "/* ==========================================================================\n   Process shortcode style - begin\n   ========================================================================== */\n\n.mkdf-process-holder {\n\t@include mkdfRelativeHolderLayout();\n\t\n\t$columns: ('two', 'three', 'four');\n\t@for $i from 0 to length($columns) {\n\t\t&.mkdf-#{nth($columns, $i+1)}-columns {\n\t\t\t$column_width: 100%/($i+2);\n\t\t\t\n\t\t\t.mkdf-mark-horizontal-holder {\n\t\t\t\t\n\t\t\t\t.mkdf-process-mark {\n\t\t\t\t\twidth: $column_width;\n\t\t\t\t}\n\t\t\t}\n\t\t\t\n\t\t\t.mkdf-mark-vertical-holder {\n\t\t\t\t\n\t\t\t\t.mkdf-process-mark {\n\t\t\t\t\theight: $column_width;\n\t\t\t\t}\n\t\t\t}\n\t\t\t\n\t\t\t.mkdf-process-item {\n\t\t\t\twidth: $column_width;\n\t\t\t}\n\t\t}\n\t}\n\t\n\t&.mkdf-process-appeared {\n\t\t\n\t\t.mkdf-process-circle {\n\t\t\topacity: 1;\n\t\t\t@include mkdfTransform(scale(1));\n\t\t}\n\t\t\n\t\t.mkdf-mark-horizontal-holder {\n\t\t\t\n\t\t\t.mkdf-process-line {\n\t\t\t\twidth: 100%;\n\t\t\t}\n\t\t}\n\t\t\n\t\t.mkdf-mark-vertical-holder {\n\t\t\t\n\t\t\t.mkdf-process-line {\n\t\t\t\theight: 100%;\n\t\t\t}\n\t\t}\n\t\t\n\t\t.mkdf-process-item {\n\t\t\topacity: 1;\n\t\t}\n\t}\n\t\n\t.mkdf-mark-horizontal-holder {\n\t\t@include mkdfRelativeHolderLayout();\n\t\tclear: both;\n\t\t\n\t\t.mkdf-process-mark {\n\t\t\tfloat: left;\n\t\t}\n\t\t\n\t\t.mkdf-process-line {\n\t\t\ttop: 50%;\n\t\t\tleft: 50%;\n\t\t\twidth: 0;\n\t\t\theight: 1px;\n\t\t\t@include mkdfTransition(width .4s ease .1s);\n\t\t}\n\t}\n\t\n\t.mkdf-mark-vertical-holder {\n\t\tposition: absolute;\n\t\ttop: 26px;\n\t\tleft: 0;\n\t\tdisplay: none;\n\t\twidth: 46px;\n\t\theight: 100%;\n\t\t\n\t\t.mkdf-process-line {\n\t\t\ttop: 23px;\n\t\t\tleft: 50%;\n\t\t\twidth: 1px;\n\t\t\theight: 0;\n\t\t\t@include mkdfTransition(height .4s ease .1s);\n\t\t}\n\t}\n\t\n\t.mkdf-process-mark {\n\t\tposition: relative;\n\t\tdisplay: inline-block;\n\t\tvertical-align: top;\n\t\ttext-align: center;\n\t\t\n\t\t&:last-child {\n\t\t\t\n\t\t\t.mkdf-process-line {\n\t\t\t\tdisplay: none;\n\t\t\t}\n\t\t}\n\t\t\n\t\t@for $i from 2 to 5 {\n\t\t\t\n\t\t\t&:nth-child(#{$i}) {\n\t\t\t\t\n\t\t\t\t.mkdf-process-circle {\n\t\t\t\t\t-webkit-transition-delay: #{.5 * ($i - 1)}s;\n\t\t\t\t\t-moz-transition-delay: #{.5 * ($i - 1)}s;\n\t\t\t\t\ttransition-delay: #{.5 * ($i - 1)}s;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t.mkdf-process-line {\n\t\t\t\t\t-webkit-transition-delay: #{.6 * ($i - 1)}s;\n\t\t\t\t\t-moz-transition-delay: #{.6 * ($i - 1)}s;\n\t\t\t\t\ttransition-delay: #{.6 * ($i - 1)}s;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\t\n\t.mkdf-process-circle {\n\t\tposition: relative;\n\t\tdisplay: inline-block;\n\t\tvertical-align: top;\n\t\twidth: 46px;\n\t\theight: 46px;\n\t\tfont-size: 18px;\n\t\tline-height: 46px;\n\t\tfont-weight: 700;\n\t\tcolor: #fff;\n\t\tbackground-color: $first-main-color;\n\t\tborder-radius: 100%;\n\t\topacity: 0;\n\t\t-webkit-transition: opacity .2s ease, -webkit-transform .3s ease;\n\t\t-moz-transition: opacity .2s ease, -moz-transform .3s ease;\n\t\ttransition: opacity .2s ease, transform .3s ease;\n\t\t@include mkdfTransform(scale(.6));\n\t}\n\t\n\t.mkdf-process-line {\n\t\tposition: absolute;\n\t\tbackground-color: $first-main-color;\n\t}\n\t\n\t.mkdf-process-inner {\n\t\tmargin: 0 -15px;\n\t}\n\t\n\t.mkdf-process-item {\n\t\tposition: relative;\n\t\tdisplay: inline-block;\n\t\tvertical-align: top;\n\t\tfloat: left;\n\t\tpadding: 0 15px;\n\t\topacity: 0;\n\t\ttext-align: center;\n\t\tbox-sizing: border-box;\n\t\t@include mkdfTransition(opacity .2s ease);\n\t\t\n\t\t@for $i from 2 to 5 {\n\t\t\t\n\t\t\t&:nth-child(#{$i}) {\n\t\t\t\t-webkit-transition-delay: #{.5 * ($i - 1)}s;\n\t\t\t\t-moz-transition-delay: #{.5 * ($i - 1)}s;\n\t\t\t\ttransition-delay: #{.5 * ($i - 1)}s;\n\t\t\t}\n\t\t}\n\t}\n\t\n\t.mkdf-pi-content {\n\t\t@include mkdfRelativeHolderLayout();\n\t\tmargin: 26px 0 10px;\n\t}\n\t\n\t.mkdf-pi-title {\n\t\tmargin: 0;\n\t}\n\t\n\t.mkdf-pi-text {\n\t\tmargin: 11px 0 0;\n\t}\n}\n/* ==========================================================================\n   Process shortcode style - end\n   ========================================================================== */", "/* ==========================================================================\n   Progress Bar shortcode style - begin\n   ========================================================================== */\n\n.mkdf-progress-bar {\n    @include mkdfRelativeHolderLayout();\n    \n    &.mkdf-pb-percent-floating {\n        width: 100%;\n        height: 100%;\n\n        .mkdf-pb-percent {\n            position: absolute;\n            left: 0;\n            right: auto;\n            bottom: 0;\n            @include mkdfTransform(translateX(-50%));\n        }\n    }\n    \n    .mkdf-pb-title-holder {\n        position: relative;\n        margin: 10px 0 6px;\n        \n        .mkdf-pb-title {\n            position: relative;\n            display: inline-block;\n            vertical-align: middle;\n            z-index: 100;\n        }\n    }\n\t\n    .mkdf-pb-percent {\n        position: absolute;\n        right: 0;\n        bottom: -2px;\n        width: auto;\n\t    display: inline-block;\n\t    vertical-align: middle;\n\t    opacity: 0;\n\t    z-index: 10;\n        \n        &:after {\n            content: '%';\n        }\n    }\n    \n    .mkdf-pb-content-holder {\n        position: relative;\n        height: 5px;\n        overflow: hidden;\n        background-color: #ebebeb;\n        \n        .mkdf-pb-content {\n            height: 5px;\n            max-width: 100%;\n            overflow: hidden;\n            background-color: $first-main-color;\n        }\n    }\n}\n/* ==========================================================================\n   Progress Bar shortcode style - end\n   ========================================================================== */", "/* ==========================================================================\n   Roadmap shortcode style - begin\n   ========================================================================== */\n\n.mkdf-roadmap {\n    @include mkdfRelativeHolderLayout();\n\tpadding: 200px 0; //3px due to arrow point\n\toverflow: hidden;\n\n\t.mkdf-roadmap-holder{\n\t\toverflow: hidden;\n\t}\n\n\t.mkdf-roadmap-line{\n\t\tposition: relative;\n\t\twidth: 0%;\n\t\theight: 3px;\n\t\tbackground-color: $additional-background-color;\n\n\t\t.mkdf-rl-arrow-left,\n\t\t.mkdf-rl-arrow-right{\n\t\t\tposition: absolute;\n\t\t\ttop: 50%;\n\t\t\tfont-size: 30px;\n\t\t\tcolor: $first-main-color;\n\t\t\tcursor: pointer;\n\t\t\t@include mkdfTransform(translateY(-50%));\n\t\t\tz-index: 50;\n\t\t}\n\n\t\t.mkdf-rl-arrow-left{\n\t\t\tleft: -2px;\n\t\t\tpadding: 10px 10px 10px 0; //to enlarge click area\n\n\t\t\tsvg{\n\t\t\t\t@include mkdfTransform(rotate(180deg));\n\t\t\t}\n\t\t}\n\n\t\t.mkdf-rl-arrow-right{\n\t\t\tright: -2px;\n\t\t\tpadding: 10px 0 10px 10px; //to enlarge click area\n\t\t}\n\t}\n\n\t.mkdf-roadmap-inner-holder{\n\t\t@include mkdfTransition(all .2s ease-in-out);\n\t}\n\n\t.mkdf-roadmap-item{\n\t\tposition: relative;\n\t\tfloat: left;\n\t\ttext-align: center;\n\t\t@include mkdfTransform(translateY(-17px)); //2px due to line height/2\n\n\t\t.mkdf-roadmap-item-circle-holder{\n\t\t\tfont-size: 0;\n\t\t}\n\n\t\t.mkdf-roadmap-item-before-circle,\n\t\t.mkdf-roadmap-item-after-circle{\n\t\t\tdisplay: inline-block;\n\t\t\tvertical-align: middle;\n\t\t\twidth: 0;\n\t\t\theight: 3px;\n\t\t\tbackground-color: #dfdfdf;\n\t\t\tposition: absolute;\n\t\t\ttop: 14px;\n\t\t}\n\t\t.mkdf-roadmap-item-before-circle {\n\t\t    left: 0;\n\t\t}\n\t\t.mkdf-roadmap-item-after-circle {\n\t\t    left: calc(50% - -11px);\n\t\t}\n\n\t\t.mkdf-roadmap-item-circle {\n\t\t\tdisplay: inline-block;\n\t\t\tvertical-align: middle;\n\t\t\twidth: 20px;\n\t\t\theight: 20px;\n\t\t\tborder-radius: 50%;\n\t\t\tbackground-color: $additional-background-color;\n\t\t\tbox-shadow: inset 0px 0px 0px 6px $first-main-color;\n\t\t}\n\n\t\t.mkdf-roadmap-item-stage-title-holder {\n\t\t\tposition: absolute;\n\t\t\tleft: 0;\n\t\t\twidth: 100%;\n\n\t\t\t.mkdf-ris-title{\n\t\t\t\tcolor: $default-heading-color;\n\t\t\t\tfont-size: 20px;\n\t\t\t\tfont-family: $default-text-font;\n\t\t\t\tfont-weight: 600;\n\t\t\t}\n\t\t}\n\n\t\t.mkdf-roadmap-item-content-holder {\n\t\t\tposition: absolute;\n\t\t\tleft: 4%;\n\t\t\twidth: 92%;\n\t\t\ttext-align: left;\n\t\t\tpadding: 30px 36px;\n\t\t\tbox-sizing: border-box;\n\t\t\tbox-shadow: 0px 5px 31px 0px rgba(0, 0, 0, 0.2);\n\t\t\tbackground-color: #fff;\n\t\t\tborder-radius: 5px;\n\t\t\tz-index: -1;\n\n\t\t\t@media screen and (max-width: 1440px) and (min-width: 1280px) {\n\t\t\t\tpadding: 20px;\n\t\t\t}\n\n\t\t\t.mkdf-ric-title{\n\t\t\t\tmargin: 0 0 14px;\n\t\t\t\t@include phone-portrait {\n\t\t\t\t\ttext-align: center;\n\t\t\t\t}\n\t\t\t}\n\n\n\t\t\t.mkdf-ric-content {\n\t\t\t\t@include phone-portrait {\n\t\t\t\t\ttext-align: center;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t&:after{\n\t\t\t\tcontent: '';\n\t\t\t\tposition: absolute;\n\t\t\t\tleft: 50%;\n\t\t\t\twidth: 3px;\n\t\t\t\theight: 70px;\n\t\t\t\tbackground-color: #dfdfdf;\n\t\t\t\t@include mkdfTransform(translateX(-50%));\n\t\t\t\tz-index: -1;\n\t\t\t}\n\t\t}\n\n\t\t&.mkdf-roadmap-item-above{\n\t\t\t.mkdf-roadmap-item-stage-title-holder{\n\t\t\t\ttop: 35px;\n\t\t\t}\n\n\t\t\t.mkdf-roadmap-item-content-holder{\n\t\t\t\tbottom: 75px;\n\n\t\t\t\t&:after{\n\t\t\t\t\ttop: 100%;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t&.mkdf-roadmap-item-below{\n\t\t\t.mkdf-roadmap-item-stage-title-holder{\n\t\t\t\tbottom: 32px;\n\t\t\t}\n\n\t\t\t.mkdf-roadmap-item-content-holder{\n\t\t\t\ttop: 75px;\n\n\t\t\t\t&:after{\n\t\t\t\t\tbottom: 100%;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t&.mkdf-roadmap-reached-item{\n\t\t\t.mkdf-roadmap-item-before-circle{\n\t\t\t\tbackground-color: $first-main-color;\n\t\t\t\tleft: 0;\n\t\t\t}\n\t\t}\n\n\t\t&.mkdf-roadmap-passed-item{\n\t\t\t.mkdf-roadmap-item-before-circle,\n\t\t\t.mkdf-roadmap-item-after-circle{\n\t\t\t\tbackground-color: $first-main-color;\n\t\t\t}\n\t\t}\n\t}\n\n\t&.mkdf-roadmap-skin-dark{\n\t\t.mkdf-roadmap-line,\n\t\t.mkdf-roadmap-item-before-circle,\n\t\t.mkdf-roadmap-item-after-circle,\n\t\t.mkdf-roadmap-item-circle,\n\t\t.mkdf-roadmap-item-content-holder:after{\n\t\t}\n\n\t\t.mkdf-roadmap-item-stage-title-holder .mkdf-ris-title{\n\t\t}\n\n\t\t.mkdf-ric-title{\n\t\t}\n\n\t\t.mkdf-roadmap-item-content-holder {\n\t\t\tbackground-color: #fff;\n\t\t}\n\t}\n\n\t&.mkdf-roadmap-skin-firstmain {\n\n\t\t.mkdf-roadmap-item {\n\n\t\t\t.mkdf-roadmap-item-content-holder {\n\t\t\t\tbackground-color: $first-main-color;\n\n\t\t\t\t&:after{\n\t\t\t\t\tbackground-color: $first-main-color;\n\t\t\t\t}\n\n\t\t\t\t.mkdf-ric-title {\n\t\t\t\t\tcolor: #fff;\n\t\t\t\t}\n\n\t\t\t\t.mkdf-ric-content {\n\t\t\t\t\tcolor: #fff;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t.mkdf-roadmap-item-before-circle,\n\t\t\t.mkdf-roadmap-item-after-circle{\n\t\t\t\tbackground-color: rgba($first-main-color,0.3);\n\t\t\t}\n\n\t\t\t.mkdf-roadmap-item-circle {\n\t\t\t\tbackground-color: #fff;\n\t\t\t\tbox-shadow: inset 0px 0px 0px 6px $first-main-color;\n\t\t\t}\n\n\n\t\t\t&.mkdf-roadmap-reached-item{\n\t\t\t\t.mkdf-roadmap-item-before-circle{\n\t\t\t\t\tbackground-color: $first-main-color;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t&.mkdf-roadmap-passed-item{\n\t\t\t\t.mkdf-roadmap-item-before-circle,\n\t\t\t\t.mkdf-roadmap-item-after-circle{\n\t\t\t\t\tbackground-color: $first-main-color;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t.mkdf-roadmap-item-stage-title-holder .mkdf-ris-title{\n\t\t\tcolor: $first-main-color;\n\t\t}\n\n\t\t.mkdf-roadmap-line {\n\t\t\tbackground-color: rgba($first-main-color,0.3);\n\n\t\t\t.mkdf-rl-arrow-left,\n\t\t\t.mkdf-rl-arrow-right{\n\t\t\t\tcolor: $first-main-color;\n\t\t\t}\n\t\t}\n\n\t}\n\n\t&.mkdf-roadmap-skin-light {\n\t\t\n\t\t.mkdf-roadmap-item {\n\n\t\t\t.mkdf-roadmap-item-content-holder {\n\t\t\t\tbackground-color: #fff;\n\n\t\t\t\t&:after{\n\t\t\t\t\tbackground-color: #fff;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t.mkdf-roadmap-item-before-circle,\n\t\t\t.mkdf-roadmap-item-after-circle{\n\t\t\t\tdisplay: inline-block;\n\t\t\t\tvertical-align: middle;\n\t\t\t\twidth: 0;\n\t\t\t\theight: 3px;\n\t\t\t\tbackground-color: rgba(255,255,255,0.3);\n\t\t\t}\n\n\t\t\t.mkdf-roadmap-item-circle {\n\t\t\t\tbackground-color: $first-main-color-ligh-blue;\n\t\t\t\tbox-shadow: inset 0px 0px 0px 6px #fff;\n\t\t\t}\n\n\n\t\t\t&.mkdf-roadmap-reached-item{\n\t\t\t\t.mkdf-roadmap-item-before-circle{\n\t\t\t\t\tbackground-color: #fff;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t&.mkdf-roadmap-passed-item{\n\t\t\t\t.mkdf-roadmap-item-before-circle,\n\t\t\t\t.mkdf-roadmap-item-after-circle{\n\t\t\t\t\tbackground-color: #fff;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t.mkdf-roadmap-item-stage-title-holder .mkdf-ris-title{\n\t\t\tcolor: #fff;\n\t\t}\n\n\t\t.mkdf-roadmap-line {\n\t\t\tbackground-color: rgba(255,255,255,0.3);\n\n\t\t\t.mkdf-rl-arrow-left,\n\t\t\t.mkdf-rl-arrow-right{\n\t\t\t\tcolor: #fff;\n\t\t\t}\n\t\t}\n\n\t}\n\n}\n/* ==========================================================================\n   Roadmap shortcode style - end\n   ========================================================================== */", "/* ==========================================================================\n   Section Title shortcode styles - begin\n   ========================================================================== */\n\n.mkdf-section-title-holder {\n    @include mkdfRelativeHolderLayout();\n\tbox-sizing: border-box;\n\t\n\t&.mkdf-st-two-columns {\n\t\t\n\t\t$space_label: ('tiny', 'small', 'normal');\n\t\t$space_width: (5, 10, 15);\n\t\t\n\t\t@for $i from 0 to length($space_label) {\n\t\t\t&.mkdf-st-#{nth($space_label,$i+1)}-space {\n\t\t\t\t$column_width: nth($space_width,$i+1);\n\t\t\t\t\n\t\t\t\t.mkdf-st-inner {\n\t\t\t\t\tmargin: 0 -#{$column_width}px;\n\t\t\t\t}\n\t\t\t\n\t\t\t\t.mkdf-st-title,\n\t\t\t\t.mkdf-st-text {\n\t\t\t\t\tpadding: 0 #{$column_width}px;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\t\n\t\t.mkdf-st-title,\n\t\t.mkdf-st-text {\n\t\t\tposition: relative;\n\t\t\tdisplay: inline-block;\n\t\t\tvertical-align: middle;\n\t\t\twidth: 50%;\n\t\t\tfloat: left;\n\t\t\tmargin: 0;\n\t\t\tbox-sizing: border-box;\n\t\t}\n\t\t\n\t\t&.mkdf-st-title-left {\n\t\t\t\n\t\t\t.mkdf-st-title {\n\t\t\t\ttext-align: right;\n\t\t\t}\n\t\t\t\n\t\t\t.mkdf-st-text {\n\t\t\t\ttext-align: left;\n\t\t\t}\n\t\t}\n\t\t\n\t\t&.mkdf-st-title-right {\n\t\t\t\n\t\t\t.mkdf-st-title {\n\t\t\t\tfloat: right;\n\t\t\t\ttext-align: left;\n\t\t\t}\n\t\t\t\n\t\t\t.mkdf-st-text {\n\t\t\t\ttext-align: right;\n\t\t\t}\n\t\t}\n\t}\n\t\n    .mkdf-st-title {\n\t    display: block;\n\t\tmargin: 0;\n\t    \n\t    .mkdf-st-title-bold {\n\t\t    font-weight: 700;\n\t    }\n\t    \n\t    .mkdf-st-title-light {\n\t\t    font-weight: 300;\n\t    }\n    }\n    \n    .mkdf-st-text {\n\t    display: block;\n        margin: 7px 0 0;\n\t\tfont-size: 18px;\n\t\tline-height: 1.88em;\n    }\n}\n/* ==========================================================================\n   Section Title shortcode styles - end\n   ========================================================================== */\n\n\n", "/* ==========================================================================\n   Separator shortcode style - begin\n   ========================================================================== */\n\n.mkdf-separator-holder {\n    position: relative;\n    height: auto;\n    font-size: 0;\n    line-height: 1em;\n\n    &.mkdf-separator-center {\n        text-align: center;\n    }\n\t\n    &.mkdf-separator-left {\n        text-align: left;\n    }\n\t\n    &.mkdf-separator-right {\n        text-align: right;\n    }\n\n    &.mkdf-separator-full-width {\n\t    \n        .mkdf-separator {\n            width: 100% !important;\n        }\n    }\n}\n\n.mkdf-separator {\n    position: relative;\n    display: inline-block;\n    vertical-align: middle;\n    border-bottom: 1px solid $first-main-color;\n    margin: 10px 0;\n}\n/* ==========================================================================\n   Separator shortcode style - end\n   ========================================================================== */", "/* ==========================================================================\n   Single Image shortcode style - begin\n   ========================================================================== */\n\n.mkdf-single-image-holder {\n    @include mkdfRelativeHolderLayout();\n\n\t&.mkdf-has-shadow {\n\n\t\t.mkdf-si-inner {\n\t\t\tbox-shadow: $default-box-shadow;\n\t\t}\n\t}\n\n\t&.mkdf-has-border {\n\n\t\t.mkdf-si-inner {\n\t\t\tborder: 2px solid $default-border-color;\n            padding: 34px;\n\t\t}\n\t}\n\t\n\t.mkdf-si-inner {\n\t\t@include mkdfRelativeHolderLayout();\n\t\t\n\t\ta, img {\n\t\t\tposition: relative;\n\t\t\tdisplay: block;\n\t\t}\n\t}\n\t.mkdf-tabs\n\t/***** Custom Link Behavior Style - begin *****/\n\t\n\t&.mkdf-image-behavior-custom-link {\n\t\t\n\t\t.mkdf-si-inner {\n\t\t\t\n\t\t\ta {\n\t\t\t\t@include mkdfImageOverlayHoverStyle();\n\t\t\t}\n\t\t}\n\t}\n\t\n\t/***** Custom Link Behavior Style - end *****/\n\t\n\t/***** Lightbox Behavior Style - begin *****/\n\t\n\t&.mkdf-image-behavior-lightbox {\n\t\t\n\t\t.mkdf-si-inner {\n\t\t\t\n\t\t\ta {\n\t\t\t\t@include mkdfImageOverlayHoverStyle();\n\t\t\t}\n\t\t}\n\t}\n\t\n\t/***** Lightbox Behavior Style - end *****/\n\t\n\t/***** Zoom Behavior Style - begin *****/\n\t\n\t&.mkdf-image-behavior-zoom {\n\t\t\n\t\t.mkdf-si-inner {\n\t\t\toverflow: hidden;\n\t\t\t\n\t\t\t.touch & {\n\t\t\t\tcursor: pointer;\n\t\t\t}\n\t\t\t\n\t\t\t&:hover {\n\t\t\t\t\n\t\t\t\timg {\n\t\t\t\t\t@include mkdfTransform(scale(1.04));\n\t\t\t\t}\n\t\t\t}\n\t\t\t\n\t\t\timg {\n\t\t\t\t@include mkdfTransform(scale(1));\n\t\t\t\t@include mkdfTransitionTransform(.3s ease-in-out);\n\t\t\t}\n\t\t}\n\t}\n\t\n\t/***** Zoom Behavior Style - end *****/\n\t\n\t/***** Grayscale Behavior Style - begin *****/\n\t\n\t&.mkdf-image-behavior-grayscale {\n\t\t\n\t\t.mkdf-si-inner {\n\t\t\toverflow: hidden;\n\t\t\t\n\t\t\t.touch & {\n\t\t\t\tcursor: pointer;\n\t\t\t}\n\t\t\t\n\t\t\t&:hover {\n\t\t\t\t\n\t\t\t\timg {\n\t\t\t\t\t-webkit-filter: grayscale(0);\n\t\t\t\t\tfilter: none;\n\t\t\t\t}\n\t\t\t}\n\t\t\t\n\t\t\timg {\n\t\t\t\tfilter: url('img/desaturate.svg#grayscale');\n\t\t\t\t-webkit-filter: grayscale(100%);\n\t\t\t\t-moz-filter: grayscale(100%);\n\t\t\t\tfilter: gray;\n\t\t\t\tfilter: grayscale(100%);\n\t\t\t\t@include mkdfTransition(all .3s ease-in-out);\n\t\t\t}\n\t\t}\n\t}\n\t\n\t/***** Grayscale Behavior Style - end *****/\n\t\n\t/***** Moving Behavior Style - begin *****/\n\t\n\t&.mkdf-image-behavior-moving {\n\t\t\n\t\t.mkdf-si-inner {\n\t\t\toverflow: hidden;\n\t\t\tpadding: 10% 0;\n\t\t\tbackground-repeat: no-repeat;\n\t\t\tbackground-position: 0 center;\n\t\t\tbackground-size: 120%;\n\t\t\t@include mkdfTransition(background .7s ease-out);\n\t\t\t\n\t\t\t&:hover {\n\t\t\t\tbackground-position: 90% center;\n\t\t\t}\n\t\t\t\n\t\t\t.touch & {\n\t\t\t\tcursor: pointer;\n\t\t\t}\n\t\t\t\n\t\t\timg {\n\t\t\t\tz-index: -1;\n\t\t\t\tmax-width: 80%;\n\t\t\t}\n\t\t\t\n\t\t\t@include ipad-landscape {\n\t\t\t\tpadding: 0;\n\t\t\t\tbackground: none;\n\t\t\t\t\n\t\t\t\timg {\n\t\t\t\t\tz-index: inherit;\n\t\t\t\t\tmax-width: 100%;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\t\n\t/***** Moving Behavior Style - end *****/\n}\n/* ==========================================================================\n   Single Image shortcode style - end\n   ========================================================================== */", "/* ==========================================================================\n   Social Share shortcode style - begin\n   ========================================================================== */\n\n.mkdf-social-share-holder {\n\tposition: relative;\n\tdisplay: inline-block;\n\tvertical-align: top;\n\t\n\t.mkdf-social-title {\n\t\tposition: relative;\n\t\tdisplay: inline-block;\n\t\tvertical-align: top;\n\t\tmargin-right: 13px;\n\t}\n\t\n\tul {\n\t\tposition: relative;\n\t\tdisplay: inline-block;\n\t\tvertical-align: top;\n\t\tlist-style: none;\n\t\tpadding: 0;\n\t\tmargin: 0;\n\t}\n\t\n\tli {\n\t\tposition: relative;\n\t\tdisplay: inline-block;\n\t\tvertical-align: top;\n\t\tpadding: 0;\n\t\tmargin: 0;\n\t\t\n\t\ta {\n\t\t\tfont-size: 14px;\n\t\t}\n\t}\n\t\n\t&.mkdf-list {\n\t\t\n\t\tli {\n\t\t\tmargin-right: 13px;\n\t\t\t\n\t\t\t&:last-child {\n\t\t\t\tmargin-right: 0;\n\t\t\t}\n\t\t}\n\t}\n\t\n\t&.mkdf-text {\n\t\t\n\t\tli {\n\t\t\tmargin-right: 13px;\n\t\t\t\n\t\t\t&:last-child {\n\t\t\t\tmargin-right: 0;\n\t\t\t}\n\t\t}\n\t}\n\t\n\t$socialShareWidth: 90px;\n\t$socialShareHeight: 30px;\n\t\n\t&.mkdf-dropdown {\n\t\tposition: relative;\n\t\tdisplay: inline-block;\n\t\tvertical-align: bottom;\n\t\t\n\t\t&:hover {\n\t\t\t\n\t\t\t.mkdf-social-share-dropdown ul li {\n\t\t\t\topacity: 1;\n\t\t\t\tvisibility: visible;\n\t\t\t\tcursor: pointer;\n\t\t\t\t\n\t\t\t\t/* opacity and visibility need to be different, but not background-color */\n\t\t\t\t@for $i from 2 through 7 {\n\t\t\t\t\t&:nth-child(#{$i}) {\n\t\t\t\t\t\t$transition-delay: #{($i)/10+s};\n\t\t\t\t\t\t\n\t\t\t\t\t\t-webkit-transition-delay: $transition-delay;\n\t\t\t\t\t\t-moz-transition-delay: $transition-delay;\n\t\t\t\t\t\ttransition-delay: $transition-delay;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\t\n\t\t.mkdf-social-share-dropdown-opener {\n\t\t\tdisplay: block;\n\t\t\t\n\t\t\t.mkdf-social-share-title {\n\t\t\t\tdisplay: inline-block;\n\t\t\t\tvertical-align: top;\n\t\t\t\tmargin-right: 5px;\n\t\t\t}\n\t\t}\n\t\t\n\t\t.mkdf-social-share-dropdown {\n\t\t\tposition: absolute;\n\t\t\tvisibility: hidden;\n\t\t\tz-index: 950;\n\t\t\t\n\t\t\tul {\n\t\t\t\tposition: relative;\n\t\t\t\tdisplay: block;\n\t\t\t\tz-index: 990;\n\t\t\t\tmargin: 0;\n\t\t\t\tpadding: 0 !important;\n\t\t\t}\n\t\t\t\n\t\t\tli {\n\t\t\t\tposition: absolute;\n\t\t\t\tdisplay: block;\n\t\t\t\ttext-align: center;\n\t\t\t\tvisibility: hidden;\n\t\t\t\toverflow: hidden;\n\t\t\t\topacity: 0;\n\t\t\t\tbox-sizing: border-box;\n\t\t\t\t@include mkdfTransition(opacity .2s ease-out, visibility .2s ease-out);\n\t\t\t\t\n\t\t\t\ta {\n\t\t\t\t\t@include mkdfTransition(color .2s ease-out, background-color .2s ease-out);\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t* {\n\t\t\t\t\tdisplay: block;\n\t\t\t\t\tline-height: inherit;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\t\n\t\t&.mkdf-bottom {\n\t\t\t\n\t\t\t.mkdf-social-share-dropdown {\n\t\t\t\tbottom: 0;\n\t\t\t\tleft: 0;\n\t\t\t\t\n\t\t\t\tli {\n\t\t\t\t\twidth: $socialShareWidth;\n\t\t\t\t\theight: $socialShareHeight;\n\t\t\t\t\tline-height: $socialShareHeight;\n\t\t\t\t\tborder: 1px solid $default-border-color;\n\t\t\t\t\t\n\t\t\t\t\t&:not(:first-child) {\n\t\t\t\t\t\tborder-top: none;\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t&.mkdf-facebook-share a:hover {\n\t\t\t\t\t\tbackground-color: #3b5998;\n\t\t\t\t\t\tcolor: #fff;\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t&.mkdf-twitter-share a:hover {\n\t\t\t\t\t\tbackground-color: #00aced;\n\t\t\t\t\t\tcolor: #fff;\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t&.mkdf-google_plus-share a:hover {\n\t\t\t\t\t\tbackground-color: #dd4b39;\n\t\t\t\t\t\tcolor: #fff;\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t&.mkdf-linkedin-share a:hover {\n\t\t\t\t\t\tbackground-color: #007bb5;\n\t\t\t\t\t\tcolor: #fff;\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t&.mkdf-tumblr-share a:hover {\n\t\t\t\t\t\tbackground-color: #32506d;\n\t\t\t\t\t\tcolor: #fff;\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t&.mkdf-pinterest-share a:hover {\n\t\t\t\t\t\tbackground-color: #cb2027;\n\t\t\t\t\t\tcolor: #fff;\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t&.mkdf-vk-share a:hover {\n\t\t\t\t\t\tbackground-color: #45668e;\n\t\t\t\t\t\tcolor: #fff;\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\ta {\n\t\t\t\t\t\tfont-size: 12px;\n\t\t\t\t\t\tcolor: $default-text-color;\n\t\t\t\t\t\tbackground-color: #fff;\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t@for $i from 1 through 7 {\n\t\t\t\t\t\t&:nth-child(#{$i}) {\n\t\t\t\t\t\t\tbottom: #{-$i*(($socialShareHeight))};\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\t\n\t\t&.mkdf-right {\n\t\t\t\n\t\t\t.mkdf-social-share-dropdown {\n\t\t\t\ttop: 0;\n\t\t\t\tright: 0;\n\t\t\t\t\n\t\t\t\tli {\n\t\t\t\t\twidth: calc(#{$socialShareWidth} / 3);\n\t\t\t\t\t\n\t\t\t\t\t@for $i from 1 through 7 {\n\t\t\t\t\t\t&:nth-child(#{$i}) {\n\t\t\t\t\t\t\tleft: #{($i - 1)*(($socialShareWidth / 3)) + 5};\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\t\n\t\t&.mkdf-left {\n\t\t\t\n\t\t\t.mkdf-social-share-dropdown {\n\t\t\t\ttop: 0;\n\t\t\t\tleft: 0;\n\t\t\t\t\n\t\t\t\tli {\n\t\t\t\t\twidth: calc(#{$socialShareWidth} / 3);\n\t\t\t\t\t\n\t\t\t\t\t@for $i from 1 through 7 {\n\t\t\t\t\t\t&:nth-child(#{$i}) {\n\t\t\t\t\t\t\tright: #{($i - 1)*(($socialShareWidth / 3)) + 5};\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n}\n\n/* ==========================================================================\n   Social Share shortcode style - end\n   ========================================================================== */", "/* ==========================================================================\n   Tabs shortcode style - begin\n   ========================================================================== */\n\n.mkdf-tabs {\n\t@include mkdfRelativeHolderLayout();\n\t\n\t.mkdf-tabs-nav {\n\t\t@include mkdfRelativeHolderLayout();\n\t\tmargin: 0;\n\t\tpadding: 0;\n\t\tlist-style: none;\n\t\tborder-bottom: 2px solid $default-border-color;\n\t\t\n\t\tli {\n\t\t\tfloat: left;\n\t\t\tmargin: 0;\n\t\t\tpadding: 0;\n\t\t\tposition: relative !important;\n\t\t\t\n\t\t\ta {\n\t\t\t\tposition: relative;\n\t\t\t\tdisplay: inline-block;\n\t\t\t\tvertical-align: middle;\n\t\t\t\tbox-sizing: border-box;\n\t\t\t\t@include mkdfTransition(all .2s ease-out, background-color .2s ease-out, border-color .2s ease-out);\n\t\t\t\tfont-size: 16px;\n\t\t\t\tfont-family: $default-text-font;\n\t\t\t\topacity: 0.6;\n\n\t\t\t\t\n\t\t\t}\n\t\t\t.mkdf-tabs-underline {\n\t\t\t\t\twidth: 0%;\n\t\t\t\t\theight: 8px;\n\t\t\t\t\tposition: absolute;\n\t\t\t\t\tbottom: -2px;\n\t\t\t\t\tleft: 0;\n\t\t\t\t\topacity: 1 !important;\n\t\t\t\t\t@include mkdfTransition(all 0.4s ease-in);\n\t\t\t\t}\n\t\t\t&.ui-state-active a{\n\t\t\t\t\t\n\t\t\t\t\t\n\t\t\t\t\topacity: 1;\n\t\t\t\t}\n\t\t}\n\t}\n\t\n\t.mkdf-tab-container {\n        box-sizing: border-box;\n\t\t@include mkdfRelativeHolderLayout();\n\t\t\n\t\tp {\n\t\t\tmargin: 0;\n\t\t}\n\t}\n\t\n\t&.mkdf-tabs-standard {\n\t\t\n\t\t.mkdf-tabs-nav {\n\t\t\t\n\t\t\tli {\n\t\t\t\t\n\t\t\t\ta {\n\t\t\t\t\tpadding: 21px 42px;\n\t\t\t\t\tline-height: 25px;\n\t\t\t\t\tfont-weight: 600;\n\t\t\t\t\tletter-spacing: 1px;\n\t\t\t\t\tcolor: $default-heading-color;\n\t\t\t\t\tmargin-bottom: -2px;\n\t\t\t\t\tborder-bottom: 8px solid transparent;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t&.ui-state-active a{\n\t\t\t\t\t\n\t\t\t\t\tborder-bottom: 8px solid transparent;\n\t\t\t\t\topacity: 1;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\t\n\t\t.mkdf-tab-container {\n\t\t\tmargin: 25px 0 0;\n\t\t}\n\t}\n\t\n\t&.mkdf-tabs-boxed {\n\t\t\n\t\t.mkdf-tabs-nav {\n\t\t\t\n\t\t\tli {\n\t\t\t\tmargin: 0 12px 0 0;\n\t\t\t\t\n\t\t\t\ta {\n\t\t\t\t\tpadding: 21px 43px;\n\t\t\t\t\tline-height: 25px;\n\t\t\t\t\tfont-weight: 400;\n\t\t\t\t\tletter-spacing: 1px;\n\t\t\t\t\tcolor: #fff;\n\t\t\t\t\tbackground-color: $default-heading-color;\n\t\t\t\t\tmargin-bottom: -2px;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t&.ui-state-active a,\n\t\t\t\t&.ui-state-hover a {\n\t\t\t\t\tcolor: #fff;\n\t\t\t\t\tbackground-color: $first-main-color;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t&:last-child {\n\t\t\t\t\tmargin: 0;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\t\n\t\t.mkdf-tab-container {\n\t\t\tmargin: 25px 0 0;\n\t\t}\n\t}\n\t\n\t&.mkdf-tabs-simple {\n\t\t\n\t\t.mkdf-tabs-nav {\n\t\t\tborder-bottom: 1px solid $default-border-color;\n\t\t\t\n\t\t\tli {\n\t\t\t\tmargin: 0 31px 0 0;\n\t\t\t\t\n\t\t\t\ta {\n\t\t\t\t\tpadding: 13px 0;\n\t\t\t\t\tfont-size: 18px;\n\t\t\t\t\tline-height: 26px;\n\t\t\t\t\tfont-weight: 400;\n\t\t\t\t\tcolor: $default-heading-color;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t&.ui-state-active a,\n\t\t\t\t&.ui-state-hover a {\n\t\t\t\t\tcolor: $first-main-color;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t&:last-child {\n\t\t\t\t\tmargin: 0;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\t\n\t\t.mkdf-tab-container {\n\t\t\tpadding: 31px 0;\n\t\t}\n\t}\n\t\n\t&.mkdf-tabs-vertical {\n\t\tdisplay: table;\n\t\t\n\t\t.mkdf-tabs-nav {\n\t\t\tdisplay: table-cell;\n\t\t\tvertical-align: top;\n\t\t\twidth: 140px;\n\t\t\theight: 100%;\n\t\t\tborder-right: 1px solid $default-border-color;\n\t\t\tborder-bottom: 0;\n\t\t\tbox-sizing: border-box;\n\t\t\t\n\t\t\tli {\n\t\t\t\tdisplay: block;\n\t\t\t\tfloat: none;\n\t\t\t\t\n\t\t\t\ta {\n\t\t\t\t\tfont-size: 16px;\n\t\t\t\t\tline-height: 26px;\n\t\t\t\t\tfont-weight: 400;\n\t\t\t\t\tdisplay: block;\n\t\t\t\t\tcolor: $default-heading-color;\n\t\t\t\t\tpadding: 12px 0 12px;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t\n\t\t\t\t&.ui-state-active a,\n\t\t\t\t&.ui-state-hover a {\n\t\t\t\t\tcolor: $first-main-color;\n\t\t\t\t\tborder-right: 8px solid transparent;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t&:last-child {\n\t\t\t\t\tmargin: 0;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\t\n\t\t.mkdf-tab-container {\n\t\t\tdisplay: table-cell;\n\t\t\tvertical-align: top;\n\t\t\twidth: calc(100% - 140px);\n\t\t\theight: 100%;\n\t\t\tpadding: 0 0 0 45px;\n\t\t\tbox-sizing: border-box;\n\t\t}\n\t}\n}\n\n.ui-widget-content {\n\tpadding: 0;\n\tfont-family: inherit;\n\tfont-size: inherit;\n\tcolor: inherit;\n\tbackground: none;\n\tborder: 0;\n\tborder-radius: 0;\n\n    .ui-widget-header {\n\t    font-size: inherit;\n\t    line-height: inherit;\n\t    font-weight: inherit;\n\t    color: initial;\n\t    background: none;\n        border-radius: 0;\n    }\n\t\n    .ui-tabs-nav {\n\t    \n        li {\n\t        position: initial;\n\t        font-weight: inherit;\n\t        color: inherit;\n\t        background: initial;\n\t        border: 0;\n\t        border-radius: 0;\n        }\n    }\n\n    .ui-widget-content {\n\t    color: inherit;\n\t    background: none;\n\t    border: 0;\n        border-radius: 0;\n    }\n}\n/* ==========================================================================\n   Tabs shortcode style - end\n   ========================================================================== */", "/* ==========================================================================\n   Team shortcode style - begin\n   ========================================================================== */\n\n.mkdf-team-holder {\n    @include mkdfRelativeHolderLayout();\n\t\n\t&.mkdf-team-info-on-image {\n\t\t\n\t\t.touch & {\n\t\t\tcursor: pointer;\n\t\t}\n\n\t\t&:hover {\n\t\t\t\n\t\t\t.mkdf-team-social-wrapper {\n\t\t\t\topacity: 1;\n\t\t\t}\n\n\t\t\t.mkdf-team-social-inner {\n\t\t\t\t@include mkdfTransitionTransform(.45s cubic-bezier(0.64, 0.01, 0.15, 1.16), opacity .5s);\n\t\t\t\t@include mkdfTransform(translate3d(0,0,0));\n\t\t\t}\n\t\t}\n\t\t\n\t\t.mkdf-team-social-holder {\n\t\t\tmargin: 7px 0 0;\n\t\t}\n\t}\n\n\t.mkdf-team-inner {\n\t\ttext-align: center;\n\t\tborder: 1px solid rgba(225,225,225,.2);\n\t\t@include mkdfTransition(all .3s ease-out);\n\n\t\t&:hover {\n\t\t\tborder: 1px solid rgba(225,225,225,.4);\n\t\t}\n\t}\n\n    .mkdf-team-image {\n        @include mkdfRelativeHolderLayout();\n\t\twidth: auto;\n\t\tmargin-top: 38px;\n\n        img {\n            display: block;\n\t\t\twidth: 200px;\n\t\t\theight: 200px;\n\t\t\tobject-fit: cover;\n\t\t\tborder-radius: 50%;\n\t\t\tborder: 3px solid #fff;\n        }\n    }\n\t\n\t.mkdf-team-info {\n\t\t@include mkdfRelativeHolderLayout();\n\t\tmargin: 7px 0 57px;\n\t\ttext-align: center;\n\t}\n\n    .mkdf-team-name {\n        margin: 0;\n    }\n\n    .mkdf-team-position {\n        margin: 12px 0 0;\n\t\tfont-size: 14px;\n\t\topacity: 0.4;\n\t\ttext-transform: uppercase;\n\t\tletter-spacing: 0.01em;\n\t\tfont-weight: 700;\n\t\tline-height: 26px;\n    }\n\t\n\t.mkdf-team-text {\n\t\tmargin: 10px 0 0;\n\t}\n\t\n\t.mkdf-team-social-wrapper {\n\t\t@include mkdfAbsoluteHolderLayout();\n\t\tbackground-color: rgba(#fff, .85);\n\t\tz-index: 1;\n\t\topacity: 0;\n\t\t@include mkdfTransform(translateZ(0));\n\t\t@include mkdfTransition(opacity .3s);\n\t}\n\t\n\t.mkdf-team-social-outer {\n\t\t@include mkdfTableLayout();\n\t}\n\t\n\t.mkdf-team-social-inner {\n\t\tposition: relative;\n\t\tdisplay: table-cell;\n\t\theight: 100%;\n\t\twidth: 100%;\n\t\tpadding: 20px 40px 33px;\n\t\tvertical-align: bottom;\n\t\t@include mkdfTransitionTransform(.2s ease);\n\t\t@include mkdfTransform(translate3d(0,40px,0));\n\t}\n\t\n\t.mkdf-team-social-holder {\n\t\t@include mkdfRelativeHolderLayout();\n\t\t\n\t\t.mkdf-team-icon {\n\t\t\tfont-size: 14px;\n\t\t\tmargin: 0;\n\n\t\t\t&:first-child {\n\t\t\t\tfont-size: 24px;\n\t\t\t\tposition: absolute;\n\t\t\t\tright: 5px;\n\t\t\t\tbottom: 24px;\n\t\t\t}\n\n\t\t\t&:nth-child(2) {\n\t\t\t\tfont-size: 16px;\n\t\t\t\tposition: absolute;\n\t\t\t\tright: -9px;\n\t\t\t\tbottom: 69px;\n\t\t\t}\n\n\t\t\t&:nth-child(3) {\n\t\t\t\tfont-size: 14px;\n\t\t\t\tposition: absolute;\n\t\t\t\tright: -13px;\n\t\t\t\tbottom: 103px;\n\t\t\t}\n\n\t\t\t&:nth-child(4) {\n\t\t\t\tfont-size: 12px;\n\t\t\t\tposition: absolute;\n\t\t\t\tright: -8px;\n\t\t\t\tbottom: 132px;\n\t\t\t}\n\n\t\t\t&:nth-child(5) {\n\t\t\t\tfont-size: 10px;\n\t\t\t\tposition: absolute;\n\t\t\t\tright: 4px;\n\t\t\t\tbottom: 155px;\n\t\t\t}\n\t\t\t\n\t\t\t.mkdf-icon-element {\n\t\t\t\tfont-size: inherit;\n\t\t\t\t@include mkdfTransition(none);\n\t\t\t\tcolor: #fff;\n\t\t\t}\n\t\t}\n\t}\n}\n/* ==========================================================================\n   Team shortcode style - end\n   ========================================================================== */", "/* ==========================================================================\n   Text Marquee shortcode style - begin\n   ========================================================================== */\n\n.mkdf-text-marquee {\n\tposition: relative;\n\twhite-space: nowrap;\n\tcolor: $default-heading-color;\n\tfont-size: 60px;\n\tline-height: 1.2em;\n\tfont-weight: 600;\n\toverflow: hidden;\n\n\t.mkdf-marquee-element {\n\t\tposition: relative;\n\t\tdisplay: inline-block;\n\t\tvertical-align: top;\n\t\tpadding: 0 25px;\n\t\tbox-sizing: border-box;\n\n\t\t&.mkdf-aux-text {\n\t\t\tposition: absolute;\n\t\t\ttop: 0;\n\t\t\tleft: 0;\n\t\t}\n\t}\n}\n/* ==========================================================================\n   Text Marquee shortcode style - end\n   ========================================================================== */", "/* ==========================================================================\n   Video Button shortcode start styles\n   ========================================================================== */\n\n.mkdf-video-button-holder {\n\tposition: relative;\n\tdisplay: inline-block;\n\tvertical-align: middle;\n\t\n\t&.mkdf-vb-has-img {\n\t\t\n\t\t.mkdf-video-button-play,\n\t\t.mkdf-video-button-play-image {\n\t\t\t@include mkdfAbsoluteHolderLayout();\n\t\t\tz-index: 1;\n\t\t\t\n\t\t\t.mkdf-video-button-play-inner {\n\t\t\t\tposition: relative;\n\t\t\t\ttop: 50%;\n\t\t\t\tleft: 0;\n\t\t\t\tdisplay: block;\n\t\t\t\ttext-align: center;\n\t\t\t\t\n\n\t\t\t\t&.top-right {\n\t\t\t\t\tposition: absolute;\n\t\t\t\t\ttop: 14%;\n\t\t\t\t\tright: 0;\n\t\t\t\t\t@include mkdfTransform(translate(50%, -50%));\n\t\t\t\t}\n\n\n\t\t\t}\n\t\t}\n\t}\n\t\n\t.mkdf-video-button-image {\n\t\t@include mkdfRelativeHolderLayout();\n\t\t\n\t\timg {\n\t\t\tdisplay: block;\n\t\t}\n\t}\n\t\n\t.mkdf-video-button-play,\n\t.mkdf-video-button-play-image {\n\t\tposition: relative;\n\t\tdisplay: inline-block;\n\t\tvertical-align: top;\n\t\tz-index: 1;\n\t}\n\t\n\t.mkdf-video-button-play {\n\t\tcolor: #fff;\n\t\tfont-size: 40px;\n\t\tline-height: 1;\n\t\t\n\t\tspan {\n\t\t\tdisplay: block;\n\t\t\tline-height: inherit;\n\t\t\t\n\t\t\t&:before {\n\t\t\t\tdisplay: block;\n\t\t\t\tline-height: inherit;\n\t\t\t}\n\n\t\t\tspan {\n\t\t\t\tdisplay: inline-block;\n\t\t\t\twidth: 105px;\n   \t\t\t\theight: 105px;\n   \t\t\t\ttop: 50%;\n   \t\t\t\tline-height: 101px;\n   \t\t\t\t@include mkdfTransform(translateY(-50%));\n\t\t\t\tborder-radius: 50%;\n\t\t\t\t@include mkdfTransition(all .2s ease-out);\n\n\t\t\t\t&:hover .icon-basic-animation {\n\t\t\t\t\tanimation: mkdfPulsebig 1.8s infinite;\n\n\t\t\t\t}\n\t\t\t}\n\t\t\t.icon-basic-video {\n\t\t\t\tposition: relative;\n\t\t\t}\n\t\t\t.icon-basic-animation {\n\t\t\t    display: block;\n\t\t\t    position: absolute;\n\t\t\t    top: 50%;\n\t\t\t    left: 0;\n\t\t\t    /* padding: 28px; */\n\t\t\t    width: 105px;\n\t\t\t    border-radius: 50%;\n\t\t\t    height: 105px;\n\t\t\t    transition: all .2s ease-out;\n\t\t\t    \n\t\t\t}\n\t\t}\n\t}\n\t\n\t.mkdf-video-button-play-image {\n\t\t\n\t\t&.mkdf-vb-has-hover-image {\n\t\t\t\n\t\t\t&:hover {\n\t\t\t\t\n\t\t\t\timg {\n\t\t\t\t\t\n\t\t\t\t\t&:first-child {\n\t\t\t\t\t\topacity: 0;\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t&:nth-child(2) {\n\t\t\t\t\t\topacity: 1;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\t\n\t\timg {\n\t\t\tdisplay: block;\n\t\t\tmargin: 0 auto;\n\t\t\t@include mkdfTransition(opacity .3s ease-in-out);\n\t\t\t\n\t\t\t&:first-child {\n\t\t\t\tposition: relative;\n\t\t\t\topacity: 1;\n\t\t\t}\n\t\t\t\n\t\t\t&:nth-child(2) {\n\t\t\t\tposition: absolute;\n\t\t\t\ttop: 0;\n\t\t\t\tleft: 50%;\n\t\t\t\topacity: 0;\n\t\t\t\t@include mkdfTransform(translateX(-50%) translateZ(0));\n\t\t\t\t\n\t\t\t\t.rev_slider_wrapper & {\n\t\t\t\t\t@include mkdfTransform(translateZ(0));\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\t.mkdf-video-button-text {\n\t\tdisplay:table;\n\t\tpadding: 60px 50px;\n\t\tcolor: #fff;\n\n\t\t.mkdf-video-button-title {\n\t\t\twidth:30%;\n\t\t\tdisplay:table-cell;\n\t\t\tvertical-align:middle;\n\t\t\tcolor: #fff;\n\t\t}\n\n\t\tp {\n\t\t\twidth:70%;\n\t\t\tfont-size: 18px;\n\t\t\tdisplay:table-cell;\n\t\t\tvertical-align:middle;\n\t\t}\n\t}\n\n\t.mkdf-video-button-text-shadow-holder {\n\t\tposition: absolute;\n\t\tbottom: 70px;\n\t\tleft: 50%;\n\t\t@include mkdfTransform(translateX(-50%));\n\t\twidth: 76%;\n\t\tz-index: -5;\n\t}\n}\n/* ==========================================================================\n   Video Button shortcode end styles\n   ========================================================================== */", ".mkdf-workflow {\n\tmargin-top: 50px;\n\tposition: relative;\n\n\t.main-line {\n\t\tbackground: #dee0e0;\n\t\tleft: 50%;\n\t\tmargin-left: -1px;\n\t\tposition: absolute;\n\t\tright: 50%;\n\t\ttop: 0;\n\t\theight: 100%;\n\t\twidth: 2px;\n\t}\n\n\t.mkdf-workflow-item {\n\t\tmargin-left: auto;\n\t\tmargin-right: auto;\n\t\tmax-width: 80%;\n\t\tposition: relative;\n\t\tpadding-bottom: 21px;\n\t\toverflow: hidden;\n\n\t\t&:nth-of-type(2n) {\n\t\t\ttext-align: left;\n\n\t\t\t.mkdf-workflow-item-inner {\n\t\t\t\tdisplay: flex;\n\t\t\t\talign-items: center;\n\t\t\t\t.mkdf-workflow-image {\n\t\t\t\t\ttext-align: right;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t&:nth-of-type(2n+1) {\n\t\t\ttext-align: right;\n\n\t\t\t.mkdf-workflow-item-inner {\n\t\t\t\tdisplay: -webkit-box;\n\t\t\t\tdisplay: -webkit-flex;\n\t\t\t\tdisplay: -ms-flexbox;\n\t\t\t\tdisplay: flex;\n\t\t\t\talign-items: center;\n\t\t\t\t-webkit-box-orient: horizontal;\n\t\t\t\t-webkit-box-direction: reverse;\n\t\t\t\t-webkit-flex-direction: row-reverse;\n\t\t\t\t-ms-flex-direction: row-reverse;\n\t\t\t\tflex-direction: row-reverse;\n\t\t\t\t-webkit-flex-wrap: wrap;\n\t\t\t\t-ms-flex-wrap: wrap;\n\t\t\t\tflex-wrap: wrap;\n\n\t\t\t\t.mkdf-workflow-image {\n\t\t\t\t\ttext-align: left;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t.mkdf-workflow-item-inner {\n\t\t\tdisplay: inline-block;\n\t\t\tposition: relative;\n\t\t\twidth: 100%;\n\t\t\tvertical-align: middle;\n\n\t\t\t.mkdf-workflow-image,\n\t\t\t.mkdf-workflow-text {\n\t\t\t\tfloat: left;\n\t\t\t\tmargin: 0;\n\t\t\t\twidth: 50%;\n\t\t\t\tbox-sizing: border-box;\n\t\t\t}\n\n\t\t\t.mkdf-workflow-image {\n\t\t\t\tpadding: 0 90px;\n\n\t\t\t\t&.left{\n\t\t\t\t\ttext-align: left;\n\t\t\t\t}\n\t\t\t\t&.right{\n\t\t\t\t\ttext-align: right;\n\t\t\t\t}\n\n\t\t\t\t.mkdf-workflow-image-inner {\n\t\t\t\t\tposition: relative;\n\t\t\t\t\tpadding: 20px;\n\t\t\t\t\tdisplay: inline-block;\n\t\t\t\t}\n\n\t\t\t\t.mkdf-icon-shortcode {\n\t\t\t\t\tposition:absolute;\n\t\t\t\t\tright: 0;\n\t\t\t\t\ttop: 0;\n\t\t\t\t\tz-index: 5;\n\t\t\t\t\tfont-size: 20px;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t.mkdf-workflow-text {\n\t\t\t\tpadding: 0 90px;\n\n\t\t\t\t&.left{\n\t\t\t\t\ttext-align: left;\n\t\t\t\t}\n\t\t\t\t&.right{\n\t\t\t\t\ttext-align: right;\n\t\t\t\t}\n\n\t\t\t\t.mkdf-workflow-text-inner {\n\t\t\t\t\tpadding: 0 20px;\n\t\t\t\t}\n\n\t\t\t\th4 {\n\t\t\t\t\tmargin-top: 12px;\n\t\t\t\t\tmargin-bottom: 0px;\n\t\t\t\t}\n\n\t\t\t\tp.text {\n\t\t\t\t\tmargin-top: 14px;\n\t\t\t\t}\n\n\t\t\t\t.circle {\n\t\t\t\t\tbackground: transparent;\n\t\t\t\t\tborder: 3px solid #dee0e0;\n\t\t\t\t\tborder-radius: 50%;\n\t\t\t\t\tcontent: \"\";\n\t\t\t\t\theight: 14px;\n\t\t\t\t\tleft: 50%;\n\t\t\t\t\tmargin: 0 0 0 -10px;\n\t\t\t\t\tposition: absolute;\n\t\t\t\t\ttop: 50%;\n\t\t\t\t\twidth: 14px;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t.line {\n\t\t\tbackground-color: #fff;\n\t\t\theight: 0;\n\t\t\tleft: 50%;\n\t\t\tmargin-left: -1px;\n\t\t\tposition: absolute;\n\t\t\twidth: 2px;\n\n\t\t\t&.line-one {\n\t\t\t\ttop : 0;\n\t\t\t}\n\n\t\t\t&.line-two {\n\t\t\t\ttop: calc(50% + 10px);\n\t\t\t}\n\t\t}\n\n\t\t&:first-of-type .line-one {\n\t\t\tdisplay: none;\n\t\t}\n\n\t\t\n\t\t&:last-of-type .line-two {\n\t\t\tdisplay: none;\n\t\t}\n\n\t}\n\n\t&.mkdf-workflow-animate {\n\t\t@include mkdfTransform(translateY(100px));\n\t\topacity: 0;\n\t\t-webkit-transition: opacity .55s cubic-bezier(0.23, 1, 0.32, 1), -webkit-transform .55s cubic-bezier(0.23, 1, 0.32, 1);\n\t\ttransition: opacity .55s cubic-bezier(0.23, 1, 0.32, 1), transform .55s cubic-bezier(0.23, 1, 0.32, 1);\n\t\t.main-line {\n\t\t\topacity: 0;\n\t\t\theight: 0;\n\t\t\t@include mkdfTransition(opacity .55s cubic-bezier(0.23, 1, 0.32, 1), height 1.8s ease-out);\n\t\t}\n\t\t.circle {\n\t\t\t@include mkdfTransform(scale(.2));\n\t\t\t-webkit-transition: -webkit-transform .6s cubic-bezier(0.18, 0.89, 0.32, 1.68) .5s;\n\t\t\ttransition: transform .6s cubic-bezier(0.18, 0.89, 0.32, 1.68) .5s;\n\t\t}\n\t\t.mkdf-workflow-item {\n\t\t\t.mkdf-workflow-item-inner {\n\t\t\t\t.mkdf-workflow-image {\n\t\t\t\t\topacity: 0;\n\t\t\t\t\t@include mkdfTransform(scale(.6));\n\t\t\t\t\t-webkit-transition: -webkit-transform .6s cubic-bezier(0.18, 0.89, 0.32, 1), opacity .3s ease-out;\n\t\t\t\t\ttransition: transform .6s cubic-bezier(0.18, 0.89, 0.32, 1), opacity .3s ease-out;\n\t\t\t\t}\n\t\t\t\t.mkdf-workflow-text {\n\t\t\t\t\th4, p {\n\t\t\t\t\t\topacity: 0;\n\t\t\t\t\t\t@include mkdfTransition(opacity .5s cubic-bezier(0.22, 0.61, 0.36, 1) .2s);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\t&.mkdf-appeared {\n\t\t\t@include mkdfTransform(translateY(0));\n\t\t\topacity: 1;\n\t\t\t.main-line {\n\t\t\t\topacity: 1;\n\t\t\t\theight: 100%;\n\t\t\t}\n\t\t\t.mkdf-workflow-item.mkdf-appeared {\n\t\t\t\t.mkdf-workflow-image {\n\t\t\t\t\topacity: 1;\n\t\t\t\t\t@include mkdfTransform(scale(1));\n\t\t\t\t}\n\t\t\t\t.mkdf-workflow-text {\n\t\t\t\t\th4, p {\n\t\t\t\t\t\topacity: 1;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t}\n\t\t\t.circle {\n\t\t\t\t@include mkdfTransform(scale(1));\n\t\t\t}\n\t\t}\n\t}\n\t&.mkdf-workflow-light {\n\t\th4, p.text {\n\t\t\tcolor: #fff;\n\t\t}\n\t}\n}"]}