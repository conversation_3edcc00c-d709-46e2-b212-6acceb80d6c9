/* ==========================================================================
   Instagram widget responsive style - begin
   ========================================================================== */

@include ipad-landscape {
	
	.mkdf-instagram-feed:not(.mkdf-instagram-carousel) {
		
		li {
			
			img {
				width: 100%;
			}
		}
		
		$instagram_columns: ('mkdf-col-6', 'mkdf-col-9');
		
		@for $i from 0 to length($instagram_columns) {
			&.#{nth($instagram_columns,$i+1)} {
				$instagram_column_width: 100%/4;
				
				li {
					width: $instagram_column_width;
				}
			}
		}
	}
}

@include ipad-portrait {
	
	.mkdf-instagram-feed:not(.mkdf-instagram-carousel) {
		$instagram_columns: ('mkdf-col-6', 'mkdf-col-9');
		
		@for $i from 0 to length($instagram_columns) {
			&.#{nth($instagram_columns,$i+1)} {
				$instagram_column_width: 100%/3;
				
				li {
					width: $instagram_column_width;
				}
			}
		}
	}
}

@include phone-landscape {
	
	.mkdf-instagram-feed:not(.mkdf-instagram-carousel) li {
		width: 50% !important;
	}
}

/* ==========================================================================
   Instagram widget responsive style - end
   ========================================================================== */