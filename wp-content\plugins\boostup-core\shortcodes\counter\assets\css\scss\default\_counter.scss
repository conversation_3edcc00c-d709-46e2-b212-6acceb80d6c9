/* ==========================================================================
   Counter shortcode style - begin
   ========================================================================== */

.mkdf-counter-holder {
    @include mkdfRelativeHolderLayout();
    opacity: 0;
    @include mkdfTransition(opacity 0.01s ease-in);
	text-align: center;
	
	.mkdf-counter-inner {
		position: relative;
		display: inline-block;
		vertical-align: middle;
	}

    .mkdf-counter {
	    height: 1em;
	    display: inline-block !important;
	    vertical-align: middle;
	    color: $default-heading-color;
		font-family: $default-text-font;
	    font-size: 60px;
		letter-spacing: -0.025em;
	    line-height: 1em;
	    font-weight: 600;
	    overflow: hidden;
    }

    .mkdf-counter-title {
	    margin: 13px 0 0;
		font-size:14px;
		letter-spacing: 0.025em;
    }

    .mkdf-counter-text {
	    margin: 14px 0 0;
    }
}
/* ==========================================================================
   Counter shortcode style - end
   ========================================================================== */