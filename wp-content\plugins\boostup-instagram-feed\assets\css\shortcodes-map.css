/* ==========================================================================
   Global partials
   ========================================================================== */
@-webkit-keyframes animate-btn-line {
  0% {
    -webkit-transform-origin: 0 50%;
    transform-origin: 0 50%;
    -webkit-transform: scaleX(0);
    transform: scaleX(0);
  }
  100% {
    -webkit-transform-origin: 0 50%;
    transform-origin: 0 50%;
    -webkit-transform: scaleX(1);
    transform: scaleX(1);
  }
}
@keyframes animate-btn-line {
  0% {
    -webkit-transform-origin: 0 50%;
    transform-origin: 0 50%;
    -webkit-transform: scaleX(0);
    transform: scaleX(0);
  }
  100% {
    -webkit-transform-origin: 0 50%;
    transform-origin: 0 50%;
    -webkit-transform: scaleX(1);
    transform: scaleX(1);
  }
}

@-webkit-keyframes mkdfPulsebig {
  0% {
    -webkit-box-shadow: 0 0 0 0 currentColor;
    box-shadow: 0 0 0 0 currentColor;
  }
  70% {
    -webkit-box-shadow: 0 0 0 20px rgba(234, 61, 86, 0);
    box-shadow: 0 0 0 20px rgba(234, 61, 86, 0);
  }
  100% {
    -webkit-box-shadow: 0 0 0 0 rgba(234, 61, 86, 0);
    box-shadow: 0 0 0 0 rgba(234, 61, 86, 0);
  }
}

@keyframes mkdfPulsebig {
  0% {
    -webkit-box-shadow: 0 0 0 0 currentColor;
    box-shadow: 0 0 0 0 currentColor;
  }
  70% {
    -webkit-box-shadow: 0 0 0 20px rgba(234, 61, 86, 0);
    box-shadow: 0 0 0 20px rgba(234, 61, 86, 0);
  }
  100% {
    -webkit-box-shadow: 0 0 0 0 rgba(234, 61, 86, 0);
    box-shadow: 0 0 0 0 rgba(234, 61, 86, 0);
  }
}

@-webkit-keyframes mkdfPulsesmall {
  0% {
    -webkit-box-shadow: 0 0 0 0 currentColor;
  }
  70% {
    -webkit-box-shadow: 0 0 0 10px rgba(234, 61, 86, 0.15);
  }
  100% {
    -webkit-box-shadow: 0 0 0 0 rgba(234, 61, 86, 0);
  }
}

@keyframes mkdfPulsesmall {
  0% {
    -webkit-box-shadow: 0 0 0 0 currentColor;
    box-shadow: 0 0 0 0 currentColor;
  }
  70% {
    -webkit-box-shadow: 0 0 0 10px rgba(234, 61, 86, 0);
    box-shadow: 0 0 0 10px rgba(234, 61, 86, 0);
  }
  100% {
    -webkit-box-shadow: 0 0 0 0 rgba(234, 61, 86, 0);
    box-shadow: 0 0 0 0 rgba(234, 61, 86, 0);
  }
}

@-webkit-keyframes mkdfPulsesmallfirst {
  0% {
    -webkit-box-shadow: 0 0 0 0 #ea3d56;
  }
  70% {
    -webkit-box-shadow: 0 0 0 10px rgba(234, 61, 86, 0.15);
  }
  100% {
    -webkit-box-shadow: 0 0 0 0 rgba(234, 61, 86, 0);
  }
}

@keyframes mkdfPulsesmallfirst {
  0% {
    -webkit-box-shadow: 0 0 0 0 #ea3d56;
    box-shadow: 0 0 0 0 #ea3d56;
  }
  70% {
    -webkit-box-shadow: 0 0 0 10px rgba(234, 61, 86, 0);
    box-shadow: 0 0 0 10px rgba(234, 61, 86, 0);
  }
  100% {
    -webkit-box-shadow: 0 0 0 0 rgba(234, 61, 86, 0);
    box-shadow: 0 0 0 0 rgba(234, 61, 86, 0);
  }
}

/* ==========================================================================
   Shortcodes styles
   ========================================================================== */
.mkdf-instagram-list-holder {
  position: relative;
  display: inline-block;
  width: 100%;
  vertical-align: middle;
  clear: both;
}

.mkdf-instagram-list-holder:not(.mkdf-il-one-column) .mkdf-il-item {
  float: left;
}

.mkdf-instagram-list-holder .mkdf-instagram-carousel .mkdf-il-item {
  width: auto !important;
  padding: 0 !important;
  margin: 0 !important;
  float: none !important;
}

.mkdf-instagram-list-holder .mkdf-il-item {
  position: relative;
  display: inline-block;
  width: 100%;
  vertical-align: middle;
  padding: 0;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}

.mkdf-instagram-list-holder.mkdf-il-two-columns .mkdf-il-item {
  width: 50%;
}

@media only screen and (min-width: 1025px) {
  .mkdf-instagram-list-holder.mkdf-il-two-columns .mkdf-il-item:nth-child(2n+1) {
    clear: both;
  }
}

.mkdf-instagram-list-holder.mkdf-il-three-columns .mkdf-il-item {
  width: 33.33333%;
}

@media only screen and (min-width: 1201px) {
  .mkdf-instagram-list-holder.mkdf-il-three-columns .mkdf-il-item:nth-child(3n+1) {
    clear: both;
  }
}

.mkdf-instagram-list-holder.mkdf-il-four-columns .mkdf-il-item {
  width: 25%;
}

@media only screen and (min-width: 1281px) {
  .mkdf-instagram-list-holder.mkdf-il-four-columns .mkdf-il-item:nth-child(4n+1) {
    clear: both;
  }
}

.mkdf-instagram-list-holder.mkdf-il-five-columns .mkdf-il-item {
  width: 20%;
}

@media only screen and (min-width: 1281px) {
  .mkdf-instagram-list-holder.mkdf-il-five-columns .mkdf-il-item:nth-child(5n+1) {
    clear: both;
  }
}

/*# sourceMappingURL=../../../../plugins/boostup-instagram-feed/assets/css/shortcodes-map.css.map */
