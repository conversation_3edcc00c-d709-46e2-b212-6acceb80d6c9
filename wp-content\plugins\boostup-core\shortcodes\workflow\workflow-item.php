<?php

namespace BoostUpCore\CPT\Shortcodes\WorkflowItem;

use BoostUpCore\Lib;

class WorkflowItem implements  Lib\ShortcodeInterface {
    private $base;

    function __construct() {
        $this->base = 'mkdf_workflow_item';
        add_action('vc_before_init', array($this, 'vcMap'));
    }

    public function getBase() {
        return $this->base;
    }

    public function vcMap() {
        if ( function_exists( 'vc_map' ) ) {
            vc_map(
                array(
                    "name"                      => esc_html__('Workflow Item', 'boostup-core'),
                    "base"                      => $this->base,
                    "as_child"                  => array('only' => 'mkdf_workflow'),
                    "category"                  => esc_html__( 'by BOOSTUP', 'boostup-core' ),
                    "icon"                      => "icon-wpb-workflow-item extended-custom-icon",
                    "show_settings_on_create"   => true,
                    'params'                    => array_merge(
                        array(
                            array(
                                'type' => 'textfield',
                                'heading' => esc_html__('Title', 'boostup-core'),
                                'param_name' => 'title',
                                'admin_label' => true,
                                'description' => esc_html__('Enter workflow item title.', 'boostup-core')
                            ),
                            array(
                                'type' => 'textarea',
                                'heading' => esc_html__('Text', 'boostup-core'),
                                'param_name' => 'text',
                                'description' => esc_html__('Enter workflow item text.', 'boostup-core')
                            ),
                            array(
                                'type'        => 'dropdown',
                                'heading'     => esc_html__('Text alignment', 'boostup-core'),
                                'param_name'  => 'text_alignment',
                                'admin_label' => true,
                                'value'       => array(
                                    esc_html__('Left', 'hue')   => 'left',
                                    esc_html__('Right', 'hue')  => 'right'
                                )
                            ),
                            array(
                                'type' => 'attach_image',
                                'heading' => esc_html__('Image', 'boostup-core'),
                                'param_name' => 'image',
                                'description' => esc_html__('Insert workflow item image.', 'boostup-core')
                            ),
                            array(
                                'type'        => 'dropdown',
                                'heading'     => esc_html__('Image alignment', 'boostup-core'),
                                'param_name'  => 'image_alignment',
                                'admin_label' => true,
                                'value'       => array(
                                    esc_html__('Left', 'hue')   => 'left',
                                    esc_html__('Right', 'hue')  => 'right'
                                )
                            )
                        ),
                        boostup_mikado_icon_collections()->getVCParamsArray(array(),'', true),
                        array(
                            array(
                                'type'       => 'colorpicker',
                                'param_name' => 'icon_background_color',
                                'heading'    => esc_html__( 'Icon Background Color', 'boostup-core' ),
                            ),
                            array(
                                'type'       => 'colorpicker',
                                'param_name' => 'icon_shadow_color',
                                'heading'    => esc_html__( 'Icon Shadow Color', 'boostup-core' )
                            ),
                            array(
                                'type' => 'colorpicker',
                                'heading' => esc_html__('Workflow line color', 'boostup-core'),
                                'param_name' => 'line_color',
                                'description' => esc_html__('Pick a color for the workflow line.', 'boostup-core')
                            ),
                            array(
                                'type' => 'colorpicker',
                                'heading' => esc_html__('Circle border color', 'boostup-core'),
                                'param_name' => 'circle_border_color',
                                'description' => esc_html__('Pick a color for the circle border color.', 'boostup-core')
                            ),
                             array(
                                'type' => 'colorpicker',
                                'heading' => esc_html__('Circle background color', 'boostup-core'),
                                'param_name' => 'circle_background_color',
                                'description' => esc_html__('Pick a color for the circle background color.', 'boostup-core')
                            )
                        )
                    )
                )
            );
        }
    }

    public function render($atts, $content = null) {
        $default_atts = (array(
            'title'                   => '',
            'text'                    => '',
            'text_alignment'          => 'left',
            'image'                   => '',
            'image_alignment'         => 'left',
            'icon_type'               => 'mkdf-circle',
            'icon_background_color'   => '',
            'icon_shadow_color'       => '',
            'line_color'       => '',
            'circle_border_color'     => '',
            'circle_background_color' => '',
        ));

        $default_atts = array_merge( $default_atts, boostup_mikado_icon_collections()->getShortcodeParams() );

        $params       = shortcode_atts($default_atts, $atts);
        $style_params = $this->getStyleProperties($params);
        $params       = array_merge($params, $style_params);

        $params['icon_parameters'] = $this->getIconParameters( $params );

        extract($params);

        $output = '';
        $output .= boostup_core_get_shortcode_module_template_part('templates/workflow-item-template', 'workflow', '', $params);

        return $output;
    }

    private function getIconParameters( $params ) {
        $params_array = array();
        if($params['icon_pack'] != '') {
            $iconPackName = boostup_mikado_icon_collections()->getIconCollectionParamNameByKey($params['icon_pack']);

            $params_array['icon_pack'] = $params['icon_pack'];
            $params_array[$iconPackName] = $params[$iconPackName];

            if (!empty($params['icon_background_color'])) {
                $params_array['background_color'] = $params['icon_background_color'];
            }

            if (!empty($params['icon_type'])) {
                $params_array['type'] = $params['icon_type'];
            }

            if (!empty($params['icon_background_color'])) {
                $params_array['background_color'] = $params['icon_background_color'];
            }

            if (!empty($params['icon_shadow_color'])) {
                $params_array['shadow_color'] = $params['icon_shadow_color'];
            }
        }
        return $params_array;
    }


    private function getStyleProperties($params) {

        $style                            = array();
        $style['circle_border_color']     = '';
        $style['circle_background_color'] = '';
        $style['line_color']              = '';

        if($params['circle_border_color'] !== '') {
            $style['circle_border_color'] = 'border-color:'.$params['circle_border_color'].';';
        }
        if($params['circle_background_color'] !== '') {
            $style['circle_background_color'] = 'background-color:'.$params['circle_background_color'].';';
        }
        if($params['line_color'] !== '') {
            $style['line_color']              = 'background-color:'.$params['line_color'].';';
        }

        return $style;
    }
}
