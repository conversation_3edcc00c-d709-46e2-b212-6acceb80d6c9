<?php

if ( ! function_exists( 'boostup_mikado_portfolio_options_map' ) ) {
	function boostup_mikado_portfolio_options_map() {
		
		boostup_mikado_add_admin_page(
			array(
				'slug'  => '_portfolio',
				'title' => esc_html__( 'Portfolio', 'boostup-core' ),
				'icon'  => 'fa fa-camera-retro'
			)
		);
		
		$panel_archive = boostup_mikado_add_admin_panel(
			array(
				'title' => esc_html__( 'Portfolio Archive', 'boostup-core' ),
				'name'  => 'panel_portfolio_archive',
				'page'  => '_portfolio'
			)
		);
		
		boostup_mikado_add_admin_field(
			array(
				'name'        => 'portfolio_archive_number_of_items',
				'type'        => 'text',
				'label'       => esc_html__( 'Number of Items', 'boostup-core' ),
				'description' => esc_html__( 'Set number of items for your portfolio list on archive pages. Default value is 12', 'boostup-core' ),
				'parent'      => $panel_archive,
				'args'        => array(
					'col_width' => 3
				)
			)
		);
		
		boostup_mikado_add_admin_field(
			array(
				'name'          => 'portfolio_archive_number_of_columns',
				'type'          => 'select',
				'label'         => esc_html__( 'Number of Columns', 'boostup-core' ),
				'default_value' => 'four',
				'description'   => esc_html__( 'Set number of columns for your portfolio list on archive pages. Default value is Four columns', 'boostup-core' ),
				'parent'        => $panel_archive,
				'options'       => boostup_mikado_get_number_of_columns_array( false, array( 'one', 'six' ) )
			)
		);
		
		boostup_mikado_add_admin_field(
			array(
				'name'          => 'portfolio_archive_space_between_items',
				'type'          => 'select',
				'label'         => esc_html__( 'Space Between Items', 'boostup-core' ),
				'description'   => esc_html__( 'Set space size between portfolio items for your portfolio list on archive pages. Default value is normal', 'boostup-core' ),
				'default_value' => 'normal',
				'options'       => boostup_mikado_get_space_between_items_array(),
				'parent'        => $panel_archive
			)
		);
		
		boostup_mikado_add_admin_field(
			array(
				'name'          => 'portfolio_archive_image_size',
				'type'          => 'select',
				'label'         => esc_html__( 'Image Proportions', 'boostup-core' ),
				'default_value' => 'landscape',
				'description'   => esc_html__( 'Set image proportions for your portfolio list on archive pages. Default value is landscape', 'boostup-core' ),
				'parent'        => $panel_archive,
				'options'       => array(
					'full'      => esc_html__( 'Original', 'boostup-core' ),
					'landscape' => esc_html__( 'Landscape', 'boostup-core' ),
					'portrait'  => esc_html__( 'Portrait', 'boostup-core' ),
					'square'    => esc_html__( 'Square', 'boostup-core' )
				)
			)
		);
		
		boostup_mikado_add_admin_field(
			array(
				'name'          => 'portfolio_archive_item_layout',
				'type'          => 'select',
				'label'         => esc_html__( 'Item Style', 'boostup-core' ),
				'default_value' => 'standard-shader',
				'description'   => esc_html__( 'Set item style for your portfolio list on archive pages. Default value is Standard - Overlay', 'boostup-core' ),
				'parent'        => $panel_archive,
				'options'       => array(
					'standard-shader' => esc_html__( 'Standard - Overlay', 'boostup-core' ),
					'gallery-overlay' => esc_html__( 'Gallery - Overlay', 'boostup-core' )
				)
			)
		);
		
		$panel = boostup_mikado_add_admin_panel(
			array(
				'title' => esc_html__( 'Portfolio Single', 'boostup-core' ),
				'name'  => 'panel_portfolio_single',
				'page'  => '_portfolio'
			)
		);
		
		boostup_mikado_add_admin_field(
			array(
				'name'          => 'portfolio_single_template',
				'type'          => 'select',
				'label'         => esc_html__( 'Portfolio Type', 'boostup-core' ),
				'default_value' => 'small-images',
				'description'   => esc_html__( 'Choose a default type for Single Project pages', 'boostup-core' ),
				'parent'        => $panel,
				'options'       => array(
					'huge-images'       => esc_html__( 'Portfolio Full Width Images', 'boostup-core' ),
					'images'            => esc_html__( 'Portfolio Images', 'boostup-core' ),
					'small-images'      => esc_html__( 'Portfolio Small Images', 'boostup-core' ),
					'slider'            => esc_html__( 'Portfolio Slider', 'boostup-core' ),
					'small-slider'      => esc_html__( 'Portfolio Small Slider', 'boostup-core' ),
					'gallery'           => esc_html__( 'Portfolio Gallery', 'boostup-core' ),
					'small-gallery'     => esc_html__( 'Portfolio Small Gallery', 'boostup-core' ),
					'masonry'           => esc_html__( 'Portfolio Masonry', 'boostup-core' ),
					'small-masonry'     => esc_html__( 'Portfolio Small Masonry', 'boostup-core' ),
					'custom'            => esc_html__( 'Portfolio Custom', 'boostup-core' ),
					'full-width-custom' => esc_html__( 'Portfolio Full Width Custom', 'boostup-core' )
				)
			)
		);
		
		/***************** Gallery Layout *****************/
		
		$portfolio_gallery_container = boostup_mikado_add_admin_container(
			array(
				'parent'          => $panel,
				'name'            => 'portfolio_gallery_container',
				'dependency' => array(
					'show' => array(
						'portfolio_single_template'  => array(
							'gallery',
							'small-gallery'
						)
					)
				)
			)
		);
		
		boostup_mikado_add_admin_field(
			array(
				'name'          => 'portfolio_single_gallery_columns_number',
				'type'          => 'select',
				'label'         => esc_html__( 'Number of Columns', 'boostup-core' ),
				'default_value' => 'three',
				'description'   => esc_html__( 'Set number of columns for portfolio gallery type', 'boostup-core' ),
				'parent'        => $portfolio_gallery_container,
				'options'       => boostup_mikado_get_number_of_columns_array( false, array( 'one', 'five', 'six' ) )
			)
		);
		
		boostup_mikado_add_admin_field(
			array(
				'name'          => 'portfolio_single_gallery_space_between_items',
				'type'          => 'select',
				'label'         => esc_html__( 'Space Between Items', 'boostup-core' ),
				'description'   => esc_html__( 'Set space size between columns for portfolio gallery type', 'boostup-core' ),
				'default_value' => 'normal',
				'options'       => boostup_mikado_get_space_between_items_array(),
				'parent'        => $portfolio_gallery_container
			)
		);
		
		/***************** Gallery Layout *****************/
		
		/***************** Masonry Layout *****************/
		
		$portfolio_masonry_container = boostup_mikado_add_admin_container(
			array(
				'parent'          => $panel,
				'name'            => 'portfolio_masonry_container',
				'dependency' => array(
					'show' => array(
						'portfolio_single_template'  => array(
							'masonry',
							'small-masonry'
						)
					)
				)
			)
		);
		
		boostup_mikado_add_admin_field(
			array(
				'name'          => 'portfolio_single_masonry_columns_number',
				'type'          => 'select',
				'label'         => esc_html__( 'Number of Columns', 'boostup-core' ),
				'default_value' => 'three',
				'description'   => esc_html__( 'Set number of columns for portfolio masonry type', 'boostup-core' ),
				'parent'        => $portfolio_masonry_container,
				'options'       => boostup_mikado_get_number_of_columns_array( false, array( 'one', 'five', 'six' ) )
			)
		);
		
		boostup_mikado_add_admin_field(
			array(
				'name'          => 'portfolio_single_masonry_space_between_items',
				'type'          => 'select',
				'label'         => esc_html__( 'Space Between Items', 'boostup-core' ),
				'description'   => esc_html__( 'Set space size between columns for portfolio masonry type', 'boostup-core' ),
				'default_value' => 'normal',
				'options'       => boostup_mikado_get_space_between_items_array(),
				'parent'        => $portfolio_masonry_container
			)
		);
		
		/***************** Masonry Layout *****************/
		
		boostup_mikado_add_admin_field(
			array(
				'type'          => 'select',
				'name'          => 'show_title_area_portfolio_single',
				'default_value' => '',
				'label'         => esc_html__( 'Show Title Area', 'boostup-core' ),
				'description'   => esc_html__( 'Enabling this option will show title area on single projects', 'boostup-core' ),
				'parent'        => $panel,
				'options'       => array(
					''    => esc_html__( 'Default', 'boostup-core' ),
					'yes' => esc_html__( 'Yes', 'boostup-core' ),
					'no'  => esc_html__( 'No', 'boostup-core' )
				),
				'args'          => array(
					'col_width' => 3
				)
			)
		);
		
		boostup_mikado_add_admin_field(
			array(
				'name'          => 'portfolio_single_lightbox_images',
				'type'          => 'yesno',
				'label'         => esc_html__( 'Enable Lightbox for Images', 'boostup-core' ),
				'description'   => esc_html__( 'Enabling this option will turn on lightbox functionality for projects with images', 'boostup-core' ),
				'parent'        => $panel,
				'default_value' => 'yes'
			)
		);
		
		boostup_mikado_add_admin_field(
			array(
				'name'          => 'portfolio_single_lightbox_videos',
				'type'          => 'yesno',
				'label'         => esc_html__( 'Enable Lightbox for Videos', 'boostup-core' ),
				'description'   => esc_html__( 'Enabling this option will turn on lightbox functionality for YouTube/Vimeo projects', 'boostup-core' ),
				'parent'        => $panel,
				'default_value' => 'no'
			)
		);
		
		boostup_mikado_add_admin_field(
			array(
				'name'          => 'portfolio_single_enable_categories',
				'type'          => 'yesno',
				'label'         => esc_html__( 'Enable Categories', 'boostup-core' ),
				'description'   => esc_html__( 'Enabling this option will enable category meta description on single projects', 'boostup-core' ),
				'parent'        => $panel,
				'default_value' => 'yes'
			)
		);
		
		boostup_mikado_add_admin_field(
			array(
				'name'          => 'portfolio_single_hide_date',
				'type'          => 'yesno',
				'label'         => esc_html__( 'Enable Date', 'boostup-core' ),
				'description'   => esc_html__( 'Enabling this option will enable date meta on single projects', 'boostup-core' ),
				'parent'        => $panel,
				'default_value' => 'yes'
			)
		);
		
		boostup_mikado_add_admin_field(
			array(
				'name'          => 'portfolio_single_sticky_sidebar',
				'type'          => 'yesno',
				'label'         => esc_html__( 'Enable Sticky Side Text', 'boostup-core' ),
				'description'   => esc_html__( 'Enabling this option will make side text sticky on Single Project pages. This option works only for Full Width Images, Small Images, Small Gallery and Small Masonry portfolio types', 'boostup-core' ),
				'parent'        => $panel,
				'default_value' => 'yes'
			)
		);
		
		boostup_mikado_add_admin_field(
			array(
				'name'          => 'portfolio_single_comments',
				'type'          => 'yesno',
				'label'         => esc_html__( 'Show Comments', 'boostup-core' ),
				'description'   => esc_html__( 'Enabling this option will show comments on your page', 'boostup-core' ),
				'parent'        => $panel,
				'default_value' => 'no'
			)
		);
		
		boostup_mikado_add_admin_field(
			array(
				'name'          => 'portfolio_single_hide_pagination',
				'type'          => 'yesno',
				'label'         => esc_html__( 'Hide Pagination', 'boostup-core' ),
				'description'   => esc_html__( 'Enabling this option will turn off portfolio pagination functionality', 'boostup-core' ),
				'parent'        => $panel,
				'default_value' => 'no'
			)
		);
		
		$container_navigate_category = boostup_mikado_add_admin_container(
			array(
				'name'            => 'navigate_same_category_container',
				'parent'          => $panel,
				'dependency' => array(
					'hide' => array(
						'portfolio_single_hide_pagination'  => array(
							'yes'
						)
					)
				)
			)
		);
		
		boostup_mikado_add_admin_field(
			array(
				'name'          => 'portfolio_single_nav_same_category',
				'type'          => 'yesno',
				'label'         => esc_html__( 'Enable Pagination Through Same Category', 'boostup-core' ),
				'description'   => esc_html__( 'Enabling this option will make portfolio pagination sort through current category', 'boostup-core' ),
				'parent'        => $container_navigate_category,
				'default_value' => 'no'
			)
		);
		
		boostup_mikado_add_admin_field(
			array(
				'name'        => 'portfolio_single_slug',
				'type'        => 'text',
				'label'       => esc_html__( 'Portfolio Single Slug', 'boostup-core' ),
				'description' => esc_html__( 'Enter if you wish to use a different Single Project slug (Note: After entering slug, navigate to Settings -> Permalinks and click "Save" in order for changes to take effect)', 'boostup-core' ),
				'parent'      => $panel,
				'args'        => array(
					'col_width' => 3
				)
			)
		);

        boostup_mikado_add_admin_field(
            array(
                'name' => 'portfolio_single_related_posts',
                'type' => 'yesno',
                'label' => esc_html__('Show Related Projects', 'boostup-core'),
                'description' => esc_html__('Enabling this option will display related projects on Single Project', 'boostup-core'),
                'parent' => $panel,
                'default_value' => 'yes'
            )
        );
	}
	
	add_action( 'boostup_mikado_action_options_map', 'boostup_mikado_portfolio_options_map', boostup_mikado_set_options_map_position( 'portfolio' ) );
}