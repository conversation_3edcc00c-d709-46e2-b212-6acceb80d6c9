/* ==========================================================================
   Video Button shortcode start styles
   ========================================================================== */

.mkdf-video-button-holder {
	position: relative;
	display: inline-block;
	vertical-align: middle;
	
	&.mkdf-vb-has-img {
		
		.mkdf-video-button-play,
		.mkdf-video-button-play-image {
			@include mkdfAbsoluteHolderLayout();
			z-index: 1;
			
			.mkdf-video-button-play-inner {
				position: relative;
				top: 50%;
				left: 0;
				display: block;
				text-align: center;
				

				&.top-right {
					position: absolute;
					top: 14%;
					right: 0;
					@include mkdfTransform(translate(50%, -50%));
				}


			}
		}
	}
	
	.mkdf-video-button-image {
		@include mkdfRelativeHolderLayout();
		
		img {
			display: block;
		}
	}
	
	.mkdf-video-button-play,
	.mkdf-video-button-play-image {
		position: relative;
		display: inline-block;
		vertical-align: top;
		z-index: 1;
	}
	
	.mkdf-video-button-play {
		color: #fff;
		font-size: 40px;
		line-height: 1;
		
		span {
			display: block;
			line-height: inherit;
			
			&:before {
				display: block;
				line-height: inherit;
			}

			span {
				display: inline-block;
				width: 105px;
   				height: 105px;
   				top: 50%;
   				line-height: 101px;
   				@include mkdfTransform(translateY(-50%));
				border-radius: 50%;
				@include mkdfTransition(all .2s ease-out);

				&:hover .icon-basic-animation {
					animation: mkdfPulsebig 1.8s infinite;

				}
			}
			.icon-basic-video {
				position: relative;
			}
			.icon-basic-animation {
			    display: block;
			    position: absolute;
			    top: 50%;
			    left: 0;
			    /* padding: 28px; */
			    width: 105px;
			    border-radius: 50%;
			    height: 105px;
			    transition: all .2s ease-out;
			    
			}
		}
	}
	
	.mkdf-video-button-play-image {
		
		&.mkdf-vb-has-hover-image {
			
			&:hover {
				
				img {
					
					&:first-child {
						opacity: 0;
					}
					
					&:nth-child(2) {
						opacity: 1;
					}
				}
			}
		}
		
		img {
			display: block;
			margin: 0 auto;
			@include mkdfTransition(opacity .3s ease-in-out);
			
			&:first-child {
				position: relative;
				opacity: 1;
			}
			
			&:nth-child(2) {
				position: absolute;
				top: 0;
				left: 50%;
				opacity: 0;
				@include mkdfTransform(translateX(-50%) translateZ(0));
				
				.rev_slider_wrapper & {
					@include mkdfTransform(translateZ(0));
				}
			}
		}
	}

	.mkdf-video-button-text {
		display:table;
		padding: 60px 50px;
		color: #fff;

		.mkdf-video-button-title {
			width:30%;
			display:table-cell;
			vertical-align:middle;
			color: #fff;
		}

		p {
			width:70%;
			font-size: 18px;
			display:table-cell;
			vertical-align:middle;
		}
	}

	.mkdf-video-button-text-shadow-holder {
		position: absolute;
		bottom: 70px;
		left: 50%;
		@include mkdfTransform(translateX(-50%));
		width: 76%;
		z-index: -5;
	}
}
/* ==========================================================================
   Video Button shortcode end styles
   ========================================================================== */