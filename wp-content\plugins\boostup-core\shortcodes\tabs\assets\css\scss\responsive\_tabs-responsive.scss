/* ==========================================================================
   Tabs shortcode responsive style - begin
   ========================================================================== */

@include ipad-landscape {
	
	.mkdf-tabs {
		
		&.mkdf-tabs-standard {
			
			.mkdf-tabs-nav {
				
				li {
					
					a {
						padding: 7px 21px;
					}
				}
			}
		}
		
		&.mkdf-tabs-boxed {
			
			.mkdf-tabs-nav {
				
				li {
					margin: 0 8px 0 0;
					
					a {
						padding: 7px 18px;
					}
				}
			}
		}
		
		&.mkdf-tabs-simple {
			
			.mkdf-tabs-nav {
				
				li {
					margin: 0 26px 0 0;
				}
			}
		}
		
		&.mkdf-tabs-vertical {
			
			.mkdf-tabs-nav {
				width: 180px;
			}
			
			.mkdf-tab-container {
				width: calc(100% - 180px);
				padding: 0 0 0 30px;
			}
		}
	}
}

@include ipad-portrait {
	
	.mkdf-tabs {
		
		&.mkdf-tabs-standard {
			
			.mkdf-tabs-nav {
				
				li {
					display: block;
					float: none;
					
					a {
						width: 100%;
					}
				}
			}
		}
		
		&.mkdf-tabs-boxed {
			
			.mkdf-tabs-nav {
				
				li {
					display: block;
					float: none;
					margin: 0 0 8px;
					
					a {
						width: 100%;
					}
				}
			}
		}
		
		&.mkdf-tabs-simple {
			
			.mkdf-tabs-nav {
				
				li {
					margin: 0 20px 0 0;
				}
			}
		}
		
		&.mkdf-tabs-vertical {
			
			.mkdf-tabs-nav,
			.mkdf-tab-container {
				display: inline-block;
				width: 100%;
				height: auto;
			}
			
			.mkdf-tabs-nav {
				border-right: 0;
				
				li {
					float: left;
					margin: 0 20px 0 0;
				}
			}
			
			.mkdf-tab-container {
				padding: 31px 0 0;
			}
		}
		.mkdf-tab-container {

			img {
				display: none;
			}
		}
	}
}

@include phone-landscape {
	
	.mkdf-tabs {
		
		&.mkdf-tabs-simple {
			
			.mkdf-tabs-nav {
				padding: 0 0 20px;
				
				li {
					display: block;
					float: none;
					margin: 0 0 20px;
					
					a {
						padding: 0;
						width: 100%;
					}
				}
			}
		}
		
		&.mkdf-tabs-vertical {

			display: block;
			
			.mkdf-tabs-nav {
				
				li {
					display: block;
					float: none;
					margin: 0 0 20px;
					
					a {
						padding: 5px 10px 0 0;
						width: 100%;
						display: inline;
						
						
					}
				}
			}
		}
	}
}

@include phone-portrait {

	.mkdf-tabs {

		&.mkdf-tabs-standard {

			.mkdf-tabs-nav {

				li {

					a {
						padding: 13px 21px 7px;
					}
				}
			}
		}
	}

}
/* ==========================================================================
   Tabs shortcode responsive style - end
   ========================================================================== */