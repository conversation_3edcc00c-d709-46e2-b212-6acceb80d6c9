{"version": 3, "sources": ["shortcodes-map-responsive.scss", "shortcodes-map-responsive.css", "../../../../../themes/boostup/assets/css/scss/_mixins.scss", "../../../../../themes/boostup/assets/css/scss/_variables.scss", "../../../shortcodes/button/assets/css/scss/responsive/_button-responsive.scss", "../../../shortcodes/call-to-action/assets/css/scss/responsive/_call-to-action-responsive.scss", "../../../shortcodes/countdown/assets/css/scss/responsive/_countdown-responsive.scss", "../../../shortcodes/custom-font/assets/css/scss/responsive/_custom-font-responsive.scss", "../../../shortcodes/elements-holder/assets/css/scss/responsive/_elements-holder-responsive.scss", "../../../shortcodes/google-map/assets/css/scss/responsive/_google-map-responsive.scss", "../../../shortcodes/process/assets/css/scss/responsive/_process-responsive.scss", "../../../shortcodes/roadmap/assets/css/scss/responsive/_roadmap-responsive.scss", "../../../shortcodes/section-title/assets/css/scss/responsive/_section-title-responsive.scss", "../../../shortcodes/tabs/assets/css/scss/responsive/_tabs-responsive.scss", "../../../shortcodes/video-button/assets/css/scss/responsive/_video-button.scss", "../../../shortcodes/workflow/assets/css/scss/responsive/_workflow-responsive.scss"], "names": [], "mappings": "AAAA;;+ECE+E;ACuf9E;EACG;IAxUA,+BAyUsC;IAvUtC,uBAuUsC;IArVtC,4BAsVoC;IApVpC,oBAoVoC;EDjftC;ECmfE;IA5UA,+BA6UsC;IA3UtC,uBA2UsC;IAzVtC,4BA0VoC;IAxVpC,oBAwVoC;ED7etC;AACF;ACqeC;EACG;IAxUA,+BAyUsC;IAvUtC,uBAuUsC;IArVtC,4BAsVoC;IApVpC,oBAoVoC;EDjftC;ECmfE;IA5UA,+BA6UsC;IA3UtC,uBA2UsC;IAzVtC,4BA0VoC;IAxVpC,oBAwVoC;ED7etC;AACF;;AC+eA;EACG;IAEC,wCAAgC;IAAhC,gCAAgC;ED5elC;EC8eA;IAEI,mDCteoB;IDsepB,2CCteoB;EFNxB;EC8eA;IAEI,gDC1eoB;ID0epB,wCC1eoB;EFFxB;AACF;;AC8eA;EACE;IAEE,wCAAgC;IAAhC,gCAAgC;ED3elC;EC6eA;IAEI,mDCpfoB;IDofpB,2CCpfoB;EFSxB;EC6eA;IAEI,gDCxfoB;IDwfpB,wCCxfoB;EFaxB;AACF;;AC6eA;EACE;IACE,wCAAwC;ED1e1C;EC4eA;IACI,sDChgBoB;EFsBxB;EC4eA;IACI,gDCngBoB;EFyBxB;AACF;;AC4eA;EACE;IAEE,wCAAgC;IAAhC,gCAAgC;EDzelC;EC2eA;IAEI,mDC7gBoB;ID6gBpB,2CC7gBoB;EFoCxB;EC2eA;IAEI,gDCjhBoB;IDihBpB,wCCjhBoB;EFwCxB;AACF;;AC2eA;EACE;IACE,mCCthBsB;EF8CxB;EC0eA;IACI,sDCzhBoB;EFiDxB;EC0eA;IACI,gDC5hBoB;EFoDxB;AACF;;AC0eA;EACE;IAEE,mCCliBsB;IDkiBtB,2BCliBsB;EF2DxB;ECyeA;IAEI,mDCtiBoB;IDsiBpB,2CCtiBoB;EF+DxB;ECyeA;IAEI,gDC1iBoB;ID0iBpB,wCC1iBoB;EFmExB;AACF;;ADlGA;;+ECsG+E;AG5G/E;;+EH+G+E;ACoiB3E;EE7oBH;IFqbO,uBAAuB;EDxU7B;EGvGD;IFsbO,uBAAuB;IACvB,eAAc;ED5UpB;AACF;;AGpGA;;+EHwG+E;AI5H/E;;+EJ+H+E;AI1H/E;EAEC;IAGE,aAAa;EJyHd;AACF;;ACygBI;EG5nBH;IAMG,yBAAyB;EJkH3B;EIxHD;IAUG,yBAAyB;EJiH3B;AACF;;AC4gBI;EGtnBH;;;IAOG,cAAc;EJ0GhB;EIjHD;IAWG,gBAAgB;IAChB,mBAAmB;EJyGrB;EIrHD;;;;;IAuBG,WAAW;EJqGb;AACF;;AIjGA;;+EJqG+E;AKpK/E;;+ELuK+E;ACse3E;EIvoBH;IAOI,eAAe;EL8JlB;AACF;;AC6eI;EInoBA;IASU,yBAAyB;ELkJrC;EK3JE;IAgBO,eAAe;EL8IxB;AACF;;AC0eI;EIhnBH;IAOI,eAAe;ELoIlB;AACF;;AK/HA;;+ELmI+E;AM/L/E;;+ENkM+E;ACud3E;EKnpBH;IAKG,aAAa;EN2Lf;AACF;;AMvLA;;+EN2L+E;AO3M/E;;+EP8M+E;ACmb3E;EM7nBJ;IAYM,WAAW;IACX,YAAY;IACZ,qBAAqB;EPkMzB;EOhNF;IAYM,WAAW;IACX,YAAY;IACZ,qBAAqB;EPuMzB;EOrNF;IAYM,WAAW;IACX,YAAY;IACZ,qBAAqB;EP4MzB;EO1NF;IAYM,WAAW;IACX,YAAY;IACZ,qBAAqB;EPiNzB;EO/NF;IAYM,WAAW;IACX,YAAY;IACZ,qBAAqB;EPsNzB;EOpOF;IAwBM,gBAAgB;EP+MpB;EOvOF;IAkCM,iBAAiB;EPwMrB;EO1OF;IA4CM,kBAAkB;EPiMtB;AACF;;ACiaI;EM/oBJ;IA2DM,WAAW;IACX,YAAY;IACZ,qBAAqB;EPwLzB;EOrPF;IA2DM,WAAW;IACX,YAAY;IACZ,qBAAqB;EP6LzB;EO1PF;IA2DM,WAAW;IACX,YAAY;IACZ,qBAAqB;EPkMzB;EO/PF;IA2DM,WAAW;IACX,YAAY;IACZ,qBAAqB;EPuMzB;EOpQF;IA2DM,WAAW;IACX,YAAY;IACZ,qBAAqB;EP4MzB;EOzQF;IAuEM,gBAAgB;EPqMpB;EO5QF;IAiFM,iBAAiB;EP8LrB;EO/QF;IA2FM,kBAAkB;EPuLtB;AACF;;ACkYI;EMrpBJ;IA0GM,WAAW;IACX,YAAY;IACZ,qBAAqB;EP8KzB;EO1RF;IA0GM,WAAW;IACX,YAAY;IACZ,qBAAqB;EPmLzB;EO/RF;IA0GM,WAAW;IACX,YAAY;IACZ,qBAAqB;EPwLzB;EOpSF;IA0GM,WAAW;IACX,YAAY;IACZ,qBAAqB;EP6LzB;EOzSF;IA0GM,WAAW;IACX,YAAY;IACZ,qBAAqB;EPkMzB;EO9SF;IAsHM,gBAAgB;EP2LpB;EOjTF;IAgIM,iBAAiB;EPoLrB;EOpTF;IA0IM,kBAAkB;EP6KtB;AACF;;ACmWI;EM3pBJ;IAyJM,WAAW;IACX,YAAY;IACZ,qBAAqB;EPoKzB;EO/TF;IAyJM,WAAW;IACX,YAAY;IACZ,qBAAqB;EPyKzB;EOpUF;IAyJM,WAAW;IACX,YAAY;IACZ,qBAAqB;EP8KzB;EOzUF;IAyJM,WAAW;IACX,YAAY;IACZ,qBAAqB;EPmLzB;EO9UF;IAyJM,WAAW;IACX,YAAY;IACZ,qBAAqB;EPwLzB;EOnVF;IAqKM,gBAAgB;EPiLpB;EOtVF;IA+KM,iBAAiB;EP0KrB;EOzVF;IAyLM,kBAAkB;EPmKtB;AACF;;ACoUI;EMjqBJ;IAwMM,WAAW;IACX,YAAY;IACZ,qBAAqB;EP0JzB;EOpWF;IAwMM,WAAW;IACX,YAAY;IACZ,qBAAqB;EP+JzB;EOzWF;IAwMM,WAAW;IACX,YAAY;IACZ,qBAAqB;EPoKzB;EO9WF;IAwMM,WAAW;IACX,YAAY;IACZ,qBAAqB;EPyKzB;EOnXF;IAwMM,WAAW;IACX,YAAY;IACZ,qBAAqB;EP8KzB;EOxXF;IAoNM,gBAAgB;EPuKpB;EO3XF;IA8NM,iBAAiB;EPgKrB;EO9XF;IAwOM,kBAAkB;EPyJtB;EOjYF;IA+OG,eAAe;EPqJhB;AACF;;AOlJA;;+EPsJ+E;AQ7Y/E;;+ERgZ+E;ACmQ3E;EO7oBH;IACC,cAAc;ER6Yd;AACF;;AQ3YA;;+ER+Y+E;ASzZ/E;;+ET4Z+E;ACqO3E;EQ7nBJ;IAOI,aAAa;ETqZf;ES5ZF;IAWI,cAAc;EToZhB;ES/ZF;IRCI,kBAAkB;IAClB,qBAAqB;IACrB,WAAW;IACX,sBAJmD;IQgBnD,mBAAmB;IACnB,SAAS;IACT,8BAAsB;IAAtB,sBAAsB;ETsZxB;ESxaF;IAsBI,WAAW;IACX,WAAW;IACX,UAAU;IACV,mBAAmB;ETqZrB;AACF;;ACgOI;EQ/oBJ;IAmCI,aAAa;ETiZf;ESpbF;IAuCI,cAAc;ETgZhB;ESvbF;IRCI,kBAAkB;IAClB,qBAAqB;IACrB,WAAW;IACX,sBAJmD;IQ4CnD,mBAAmB;IACnB,SAAS;IACT,8BAAsB;IAAtB,sBAAsB;ETkZxB;EShcF;IAkDI,WAAW;IACX,WAAW;IACX,UAAU;IACV,mBAAmB;ETiZrB;AACF;;AC8MI;EQrpBJ;IA+DI,aAAa;ET6Yf;ES5cF;IAmEI,cAAc;ET4YhB;ES/cF;IRCI,kBAAkB;IAClB,qBAAqB;IACrB,WAAW;IACX,sBAJmD;IQwEnD,mBAAmB;IACnB,SAAS;IACT,8BAAsB;IAAtB,sBAAsB;ET8YxB;ESxdF;IA8EI,WAAW;IACX,WAAW;IACX,UAAU;IACV,mBAAmB;ET6YrB;AACF;;AC4LI;EQ3pBJ;IA2FI,aAAa;ETyYf;ESpeF;IA+FI,cAAc;ETwYhB;ESveF;IRCI,kBAAkB;IAClB,qBAAqB;IACrB,WAAW;IACX,sBAJmD;IQoGnD,mBAAmB;IACnB,SAAS;IACT,8BAAsB;IAAtB,sBAAsB;ET0YxB;EShfF;IA0GI,WAAW;IACX,WAAW;IACX,UAAU;IACV,mBAAmB;ETyYrB;AACF;;AC0KI;EQjqBJ;IAuHI,aAAa;ETqYf;ES5fF;IA2HI,cAAc;EToYhB;ES/fF;IRCI,kBAAkB;IAClB,qBAAqB;IACrB,WAAW;IACX,sBAJmD;IQgInD,mBAAmB;IACnB,SAAS;IACT,8BAAsB;IAAtB,sBAAsB;ETsYxB;ESxgBF;IAsII,WAAW;IACX,WAAW;IACX,UAAU;IACV,mBAAmB;ETqYrB;AACF;;ASjYA;;+ETqY+E;AUvhB/E;;+EV0hB+E;ACqI3E;ES3pBJ;IAEE,6BAA6B;EVwhB7B;EU1hBF;IAMI,SAAS;IACT,YAAY;EVuhBd;EU9hBF;IAWI,SAAS;IACT,YAAY;EVshBd;EUliBF;IAeK,SAAS;IACT,YAAY;EVshBf;AACF;;AUjhBA;;+EVqhB+E;AW/iB/E;;+EXkjB+E;ACiG3E;EU7oBH;IAGE,qBAAqB;EX6iBtB;AACF;;ACkGI;EUzoBH;IACC,qBAAqB;EX2iBrB;EW5iBD;;IAOG,WAAW;IACX,sBAAsB;IACtB,8BAA8B;EXyiBhC;EWljBD;IAaG,gBAAgB;EXwiBlB;EWrjBD;IAsBI,aAAa;EXkiBhB;AACF;;AW7hBA;;+EXiiB+E;AY7kB/E;;+EZglB+E;ACmE3E;EW7oBH;IASK,iBAAiB;EZqkBrB;EY9kBD;IAoBI,iBAAiB;EZ6jBpB;EYjlBD;IAuBK,iBAAiB;EZ6jBrB;EYplBD;IAkCI,kBAAkB;EZqjBrB;EYvlBD;IA0CG,YAAY;EZgjBd;EY1lBD;IA8CG,yBAAyB;IACzB,mBAAmB;EZ+iBrB;AACF;;ACoDI;EW5lBH;IAOI,cAAc;IACd,WAAW;EZsiBd;EY9iBD;IAWK,WAAW;EZsiBf;EYjjBD;IAsBI,cAAc;IACd,WAAW;IACX,eAAe;EZ8hBlB;EYtjBD;IA2BK,WAAW;EZ8hBf;EYzjBD;IAsCI,kBAAkB;EZshBrB;EY5jBD;;IA+CG,qBAAqB;IACrB,WAAW;IACX,YAAY;EZihBd;EYlkBD;IAqDG,eAAe;EZghBjB;EYrkBD;IAwDI,WAAW;IACX,kBAAkB;EZghBrB;EYzkBD;IA8DG,iBAAiB;EZ8gBnB;EY5kBD;IAoEG,aAAa;EZ2gBf;AACF;;ACkBI;EWthBH;IAKG,iBAAiB;EZogBnB;EYzgBD;IAQI,cAAc;IACd,WAAW;IACX,gBAAgB;EZogBnB;EY9gBD;IAaK,UAAU;IACV,WAAW;EZogBf;EYlhBD;IAsBE,cAAc;EZ+ff;EYrhBD;IA2BI,cAAc;IACd,WAAW;IACX,gBAAgB;EZ6fnB;EY1hBD;IAgCK,qBAAqB;IACrB,WAAW;IACX,eAAe;EZ6fnB;AACF;;ACJI;EW9eH;IASK,sBAAsB;EZ8e1B;AACF;;AYveA;;+EZ2e+E;AC1B3E;EYvpBH;IAGE,0BAA0B;EbkrB3B;EarrBD;IAOI,YAAY;IACZ,WAAW;IACR,YAAY;IACZ,iBAAiB;EbirBvB;Ea3rBD;IAaQ,WAAW;IACX,YAAY;IACZ,iBAAiB;EbirBxB;EahsBD;IAsBI,kBAAkB;Eb6qBrB;EansBD;IA8BE,uBAAuB;EbwqBxB;EatsBD;IAiCG,cAAa;IACb,WAAU;EbwqBZ;Ea1sBD;IAsCG,WAAU;IACV,cAAa;EbuqBf;Ea9sBD;IA4CE,aAAa;EbqqBd;AACF;;AC/CI;EYhnBH;IAGE,eAAe;EbiqBhB;EapqBD;IAQI,mBAAmB;Eb+pBtB;AACF;;AClDI;EYnmBH;IAGE,0BAA0B;EbupB3B;Ea1pBD;IAQI,WAAW;IACR,YAAY;IACZ,iBAAiB;EbqpBvB;Ea/pBD;IAaQ,WAAW;IACX,YAAY;IACZ,iBAAiB;EbqpBxB;EapqBD;IAsBI,mBAAmB;IACnB,qBAAqB;EbipBxB;AACF;;ACpGI;Ea5oBH;IAEE,eAAe;EdmvBhB;EcrvBD;IAKG,iBAAiB;EdmvBnB;EcxvBD;IASI,iBAAiB;EdkvBpB;AACF;;AC9FI;EazoBH;;;IAKE,wBAAwB;EdyuBzB;Ec9uBD;IASE,gBAAgB;EdwuBjB;EcjvBD;IAcI,WAAW;IACX,mBAAmB;IACnB,gBAAgB;IAChB,qBAAqB;EdsuBxB;EcvvBD;IAoBK,2BAA2B;EdsuB/B;Ec1vBD;IAyBI,sBAAsB;IACtB,uBAAuB;IACvB,2BAA2B;EdouB9B;Ec/vBD;IAsCG,yBAAyB;Ed4tB3B;AACF", "file": "../../../../boostup-core/assets/css/scss/shortcodes-map-responsive.css", "sourcesContent": ["/* ==========================================================================\n   Global partials\n   ========================================================================== */\n@import '../../../../../themes/boostup/assets/css/scss/variables';\n@import '../../../../../themes/boostup/assets/css/scss/mixins';\n\n/* ==========================================================================\n   Shortcodes responsive styles\n   ========================================================================== */\n@import \"D:/Projects/boostup/wp-content/plugins/boostup-core/shortcodes/button/assets/css/scss/responsive/_button-responsive.scss\";\n@import \"D:/Projects/boostup/wp-content/plugins/boostup-core/shortcodes/call-to-action/assets/css/scss/responsive/_call-to-action-responsive.scss\";\n@import \"D:/Projects/boostup/wp-content/plugins/boostup-core/shortcodes/countdown/assets/css/scss/responsive/_countdown-responsive.scss\";\n@import \"D:/Projects/boostup/wp-content/plugins/boostup-core/shortcodes/custom-font/assets/css/scss/responsive/_custom-font-responsive.scss\";\n@import \"D:/Projects/boostup/wp-content/plugins/boostup-core/shortcodes/elements-holder/assets/css/scss/responsive/_elements-holder-responsive.scss\";\n@import \"D:/Projects/boostup/wp-content/plugins/boostup-core/shortcodes/google-map/assets/css/scss/responsive/_google-map-responsive.scss\";\n@import \"D:/Projects/boostup/wp-content/plugins/boostup-core/shortcodes/process/assets/css/scss/responsive/_process-responsive.scss\";\n@import \"D:/Projects/boostup/wp-content/plugins/boostup-core/shortcodes/roadmap/assets/css/scss/responsive/_roadmap-responsive.scss\";\n@import \"D:/Projects/boostup/wp-content/plugins/boostup-core/shortcodes/section-title/assets/css/scss/responsive/_section-title-responsive.scss\";\n@import \"D:/Projects/boostup/wp-content/plugins/boostup-core/shortcodes/tabs/assets/css/scss/responsive/_tabs-responsive.scss\";\n@import \"D:/Projects/boostup/wp-content/plugins/boostup-core/shortcodes/video-button/assets/css/scss/responsive/_video-button.scss\";\n@import \"D:/Projects/boostup/wp-content/plugins/boostup-core/shortcodes/workflow/assets/css/scss/responsive/_workflow-responsive.scss\";", "/* ==========================================================================\n   Global partials\n   ========================================================================== */\n@keyframes animate-btn-line {\n  0% {\n    -webkit-transform-origin: 0 50%;\n    -moz-transform-origin: 0 50%;\n    transform-origin: 0 50%;\n    -webkit-transform: scaleX(0);\n    -moz-transform: scaleX(0);\n    transform: scaleX(0);\n  }\n  100% {\n    -webkit-transform-origin: 0 50%;\n    -moz-transform-origin: 0 50%;\n    transform-origin: 0 50%;\n    -webkit-transform: scaleX(1);\n    -moz-transform: scaleX(1);\n    transform: scaleX(1);\n  }\n}\n\n@-webkit-keyframes mkdfPulsebig {\n  0% {\n    -moz-box-shadow: 0 0 0 0 currentColor;\n    box-shadow: 0 0 0 0 currentColor;\n  }\n  70% {\n    -moz-box-shadow: 0 0 0 20px rgba(234, 61, 86, 0.3);\n    box-shadow: 0 0 0 20px rgba(234, 61, 86, 0);\n  }\n  100% {\n    -moz-box-shadow: 0 0 0 0 rgba(234, 61, 86, 0);\n    box-shadow: 0 0 0 0 rgba(234, 61, 86, 0);\n  }\n}\n\n@keyframes mkdfPulsebig {\n  0% {\n    -moz-box-shadow: 0 0 0 0 currentColor;\n    box-shadow: 0 0 0 0 currentColor;\n  }\n  70% {\n    -moz-box-shadow: 0 0 0 20px rgba(234, 61, 86, 0.3);\n    box-shadow: 0 0 0 20px rgba(234, 61, 86, 0);\n  }\n  100% {\n    -moz-box-shadow: 0 0 0 0 rgba(234, 61, 86, 0);\n    box-shadow: 0 0 0 0 rgba(234, 61, 86, 0);\n  }\n}\n\n@-webkit-keyframes mkdfPulsesmall {\n  0% {\n    -webkit-box-shadow: 0 0 0 0 currentColor;\n  }\n  70% {\n    -webkit-box-shadow: 0 0 0 10px rgba(234, 61, 86, 0.15);\n  }\n  100% {\n    -webkit-box-shadow: 0 0 0 0 rgba(234, 61, 86, 0);\n  }\n}\n\n@keyframes mkdfPulsesmall {\n  0% {\n    -moz-box-shadow: 0 0 0 0 currentColor;\n    box-shadow: 0 0 0 0 currentColor;\n  }\n  70% {\n    -moz-box-shadow: 0 0 0 10px rgba(234, 61, 86, 0.15);\n    box-shadow: 0 0 0 10px rgba(234, 61, 86, 0);\n  }\n  100% {\n    -moz-box-shadow: 0 0 0 0 rgba(234, 61, 86, 0);\n    box-shadow: 0 0 0 0 rgba(234, 61, 86, 0);\n  }\n}\n\n@-webkit-keyframes mkdfPulsesmallfirst {\n  0% {\n    -webkit-box-shadow: 0 0 0 0 #ea3d56;\n  }\n  70% {\n    -webkit-box-shadow: 0 0 0 10px rgba(234, 61, 86, 0.15);\n  }\n  100% {\n    -webkit-box-shadow: 0 0 0 0 rgba(234, 61, 86, 0);\n  }\n}\n\n@keyframes mkdfPulsesmallfirst {\n  0% {\n    -moz-box-shadow: 0 0 0 0 #ea3d56;\n    box-shadow: 0 0 0 0 #ea3d56;\n  }\n  70% {\n    -moz-box-shadow: 0 0 0 10px rgba(234, 61, 86, 0.15);\n    box-shadow: 0 0 0 10px rgba(234, 61, 86, 0);\n  }\n  100% {\n    -moz-box-shadow: 0 0 0 0 rgba(234, 61, 86, 0);\n    box-shadow: 0 0 0 0 rgba(234, 61, 86, 0);\n  }\n}\n\n/* ==========================================================================\n   Shortcodes responsive styles\n   ========================================================================== */\n/* ==========================================================================\n   Button shortcode responsive style - begin\n   ========================================================================== */\n@media only screen and (max-width: 1024px) {\n  .mkdf-btn.mkdf-btn-large {\n    padding: 13px 34px 11px;\n  }\n  .mkdf-btn.mkdf-btn-huge {\n    padding: 21px 60px 16px;\n    font-size: 14px;\n  }\n}\n\n/* ==========================================================================\n   Button shortcode responsive style - end\n   ========================================================================== */\n/* ==========================================================================\n   Call To Action shortcode responsive style - begin\n   ========================================================================== */\n@media only screen and (min-width: 1201px) and (max-width: 1300px) {\n  .mkdf-call-to-action-holder .mkdf-grid {\n    width: 1100px;\n  }\n}\n\n@media only screen and (max-width: 1200px) {\n  .mkdf-call-to-action-holder.mkdf-three-quarters-columns .mkdf-cta-text-holder, .mkdf-call-to-action-holder.mkdf-four-fifths-columns .mkdf-cta-text-holder {\n    width: 66.66666666666667%;\n  }\n  .mkdf-call-to-action-holder.mkdf-three-quarters-columns .mkdf-cta-button-holder, .mkdf-call-to-action-holder.mkdf-four-fifths-columns .mkdf-cta-button-holder {\n    width: 33.33333333333333%;\n  }\n}\n\n@media only screen and (max-width: 768px) {\n  .mkdf-call-to-action-holder.mkdf-normal-layout .mkdf-cta-inner,\n  .mkdf-call-to-action-holder.mkdf-normal-layout .mkdf-cta-text-holder,\n  .mkdf-call-to-action-holder.mkdf-normal-layout .mkdf-cta-button-holder {\n    display: block;\n  }\n  .mkdf-call-to-action-holder.mkdf-normal-layout .mkdf-cta-button-holder {\n    margin: 28px 0 0;\n    text-align: initial;\n  }\n  .mkdf-call-to-action-holder.mkdf-two-halves-columns .mkdf-cta-text-holder,\n  .mkdf-call-to-action-holder.mkdf-two-halves-columns .mkdf-cta-button-holder, .mkdf-call-to-action-holder.mkdf-two-thirds-columns .mkdf-cta-text-holder,\n  .mkdf-call-to-action-holder.mkdf-two-thirds-columns .mkdf-cta-button-holder, .mkdf-call-to-action-holder.mkdf-three-quarters-columns .mkdf-cta-text-holder,\n  .mkdf-call-to-action-holder.mkdf-three-quarters-columns .mkdf-cta-button-holder, .mkdf-call-to-action-holder.mkdf-four-fifths-columns .mkdf-cta-text-holder,\n  .mkdf-call-to-action-holder.mkdf-four-fifths-columns .mkdf-cta-button-holder {\n    width: 100%;\n  }\n}\n\n/* ==========================================================================\n   Call To Action shortcode responsive style - end\n   ========================================================================== */\n/* ==========================================================================\n   Countdown shortcode responsive style - begin\n   ========================================================================== */\n@media only screen and (max-width: 1200px) {\n  .mkdf-countdown .countdown-row .countdown-section .countdown-amount {\n    font-size: 60px;\n  }\n}\n\n@media only screen and (max-width: 768px) {\n  .mkdf-countdown .countdown-row.countdown-show4 .countdown-section, .mkdf-countdown .countdown-row.countdown-show5 .countdown-section, .mkdf-countdown .countdown-row.countdown-show6 .countdown-section {\n    width: 33.33333333333333%;\n  }\n  .mkdf-countdown .countdown-row .countdown-section .countdown-amount {\n    font-size: 50px;\n  }\n}\n\n@media only screen and (max-width: 680px) {\n  .mkdf-countdown .countdown-row .countdown-section .countdown-amount {\n    font-size: 40px;\n  }\n}\n\n/* ==========================================================================\n   Countdown shortcode responsive style - end\n   ========================================================================== */\n/* ==========================================================================\n   Custom Font shortcode responsive styles - begin\n   ========================================================================== */\n@media only screen and (max-width: 768px) {\n  .mkdf-custom-font-holder.mkdf-disable-title-break br {\n    display: none;\n  }\n}\n\n/* ==========================================================================\n   Custom Font shortcode responsive styles - end\n   ========================================================================== */\n/* ==========================================================================\n   Elements Holder shortcode responsive style - begin\n   ========================================================================== */\n@media only screen and (max-width: 1366px) {\n  .mkdf-elements-holder.mkdf-responsive-mode-1366.mkdf-two-columns .mkdf-eh-item {\n    width: 100%;\n    height: auto;\n    display: inline-block;\n  }\n  .mkdf-elements-holder.mkdf-responsive-mode-1366.mkdf-three-columns .mkdf-eh-item {\n    width: 100%;\n    height: auto;\n    display: inline-block;\n  }\n  .mkdf-elements-holder.mkdf-responsive-mode-1366.mkdf-four-columns .mkdf-eh-item {\n    width: 100%;\n    height: auto;\n    display: inline-block;\n  }\n  .mkdf-elements-holder.mkdf-responsive-mode-1366.mkdf-five-columns .mkdf-eh-item {\n    width: 100%;\n    height: auto;\n    display: inline-block;\n  }\n  .mkdf-elements-holder.mkdf-responsive-mode-1366.mkdf-six-columns .mkdf-eh-item {\n    width: 100%;\n    height: auto;\n    display: inline-block;\n  }\n  .mkdf-elements-holder.mkdf-responsive-mode-1366.mkdf-one-column-alignment-left .mkdf-eh-item .mkdf-eh-item-content {\n    text-align: left;\n  }\n  .mkdf-elements-holder.mkdf-responsive-mode-1366.mkdf-one-column-alignment-right .mkdf-eh-item .mkdf-eh-item-content {\n    text-align: right;\n  }\n  .mkdf-elements-holder.mkdf-responsive-mode-1366.mkdf-one-column-alignment-center .mkdf-eh-item .mkdf-eh-item-content {\n    text-align: center;\n  }\n}\n\n@media only screen and (max-width: 1024px) {\n  .mkdf-elements-holder.mkdf-responsive-mode-1024.mkdf-two-columns .mkdf-eh-item {\n    width: 100%;\n    height: auto;\n    display: inline-block;\n  }\n  .mkdf-elements-holder.mkdf-responsive-mode-1024.mkdf-three-columns .mkdf-eh-item {\n    width: 100%;\n    height: auto;\n    display: inline-block;\n  }\n  .mkdf-elements-holder.mkdf-responsive-mode-1024.mkdf-four-columns .mkdf-eh-item {\n    width: 100%;\n    height: auto;\n    display: inline-block;\n  }\n  .mkdf-elements-holder.mkdf-responsive-mode-1024.mkdf-five-columns .mkdf-eh-item {\n    width: 100%;\n    height: auto;\n    display: inline-block;\n  }\n  .mkdf-elements-holder.mkdf-responsive-mode-1024.mkdf-six-columns .mkdf-eh-item {\n    width: 100%;\n    height: auto;\n    display: inline-block;\n  }\n  .mkdf-elements-holder.mkdf-responsive-mode-1024.mkdf-one-column-alignment-left .mkdf-eh-item .mkdf-eh-item-content {\n    text-align: left;\n  }\n  .mkdf-elements-holder.mkdf-responsive-mode-1024.mkdf-one-column-alignment-right .mkdf-eh-item .mkdf-eh-item-content {\n    text-align: right;\n  }\n  .mkdf-elements-holder.mkdf-responsive-mode-1024.mkdf-one-column-alignment-center .mkdf-eh-item .mkdf-eh-item-content {\n    text-align: center;\n  }\n}\n\n@media only screen and (max-width: 768px) {\n  .mkdf-elements-holder.mkdf-responsive-mode-768.mkdf-two-columns .mkdf-eh-item {\n    width: 100%;\n    height: auto;\n    display: inline-block;\n  }\n  .mkdf-elements-holder.mkdf-responsive-mode-768.mkdf-three-columns .mkdf-eh-item {\n    width: 100%;\n    height: auto;\n    display: inline-block;\n  }\n  .mkdf-elements-holder.mkdf-responsive-mode-768.mkdf-four-columns .mkdf-eh-item {\n    width: 100%;\n    height: auto;\n    display: inline-block;\n  }\n  .mkdf-elements-holder.mkdf-responsive-mode-768.mkdf-five-columns .mkdf-eh-item {\n    width: 100%;\n    height: auto;\n    display: inline-block;\n  }\n  .mkdf-elements-holder.mkdf-responsive-mode-768.mkdf-six-columns .mkdf-eh-item {\n    width: 100%;\n    height: auto;\n    display: inline-block;\n  }\n  .mkdf-elements-holder.mkdf-responsive-mode-768.mkdf-one-column-alignment-left .mkdf-eh-item .mkdf-eh-item-content {\n    text-align: left;\n  }\n  .mkdf-elements-holder.mkdf-responsive-mode-768.mkdf-one-column-alignment-right .mkdf-eh-item .mkdf-eh-item-content {\n    text-align: right;\n  }\n  .mkdf-elements-holder.mkdf-responsive-mode-768.mkdf-one-column-alignment-center .mkdf-eh-item .mkdf-eh-item-content {\n    text-align: center;\n  }\n}\n\n@media only screen and (max-width: 680px) {\n  .mkdf-elements-holder.mkdf-responsive-mode-680.mkdf-two-columns .mkdf-eh-item {\n    width: 100%;\n    height: auto;\n    display: inline-block;\n  }\n  .mkdf-elements-holder.mkdf-responsive-mode-680.mkdf-three-columns .mkdf-eh-item {\n    width: 100%;\n    height: auto;\n    display: inline-block;\n  }\n  .mkdf-elements-holder.mkdf-responsive-mode-680.mkdf-four-columns .mkdf-eh-item {\n    width: 100%;\n    height: auto;\n    display: inline-block;\n  }\n  .mkdf-elements-holder.mkdf-responsive-mode-680.mkdf-five-columns .mkdf-eh-item {\n    width: 100%;\n    height: auto;\n    display: inline-block;\n  }\n  .mkdf-elements-holder.mkdf-responsive-mode-680.mkdf-six-columns .mkdf-eh-item {\n    width: 100%;\n    height: auto;\n    display: inline-block;\n  }\n  .mkdf-elements-holder.mkdf-responsive-mode-680.mkdf-one-column-alignment-left .mkdf-eh-item .mkdf-eh-item-content {\n    text-align: left;\n  }\n  .mkdf-elements-holder.mkdf-responsive-mode-680.mkdf-one-column-alignment-right .mkdf-eh-item .mkdf-eh-item-content {\n    text-align: right;\n  }\n  .mkdf-elements-holder.mkdf-responsive-mode-680.mkdf-one-column-alignment-center .mkdf-eh-item .mkdf-eh-item-content {\n    text-align: center;\n  }\n}\n\n@media only screen and (max-width: 480px) {\n  .mkdf-elements-holder.mkdf-responsive-mode-480.mkdf-two-columns .mkdf-eh-item {\n    width: 100%;\n    height: auto;\n    display: inline-block;\n  }\n  .mkdf-elements-holder.mkdf-responsive-mode-480.mkdf-three-columns .mkdf-eh-item {\n    width: 100%;\n    height: auto;\n    display: inline-block;\n  }\n  .mkdf-elements-holder.mkdf-responsive-mode-480.mkdf-four-columns .mkdf-eh-item {\n    width: 100%;\n    height: auto;\n    display: inline-block;\n  }\n  .mkdf-elements-holder.mkdf-responsive-mode-480.mkdf-five-columns .mkdf-eh-item {\n    width: 100%;\n    height: auto;\n    display: inline-block;\n  }\n  .mkdf-elements-holder.mkdf-responsive-mode-480.mkdf-six-columns .mkdf-eh-item {\n    width: 100%;\n    height: auto;\n    display: inline-block;\n  }\n  .mkdf-elements-holder.mkdf-responsive-mode-480.mkdf-one-column-alignment-left .mkdf-eh-item .mkdf-eh-item-content {\n    text-align: left;\n  }\n  .mkdf-elements-holder.mkdf-responsive-mode-480.mkdf-one-column-alignment-right .mkdf-eh-item .mkdf-eh-item-content {\n    text-align: right;\n  }\n  .mkdf-elements-holder.mkdf-responsive-mode-480.mkdf-one-column-alignment-center .mkdf-eh-item .mkdf-eh-item-content {\n    text-align: center;\n  }\n  .mkdf-elements-holder .mkdf-eh-item-content {\n    padding: 0 10px;\n  }\n}\n\n/* ==========================================================================\n   Elements Holder shortcode responsive style - end\n   ========================================================================== */\n/* ==========================================================================\n   Google Map shortcode responsive style - begin\n   ========================================================================== */\n@media only screen and (max-width: 1024px) {\n  .mkdf-google-map-overlay {\n    display: block;\n  }\n}\n\n/* ==========================================================================\n   Google Map shortcode responsive style - end\n   ========================================================================== */\n/* ==========================================================================\n   Process shortcode responsive style - begin\n   ========================================================================== */\n@media only screen and (max-width: 1366px) {\n  .mkdf-process-holder.mkdf-responsive-1366 .mkdf-mark-horizontal-holder {\n    display: none;\n  }\n  .mkdf-process-holder.mkdf-responsive-1366 .mkdf-mark-vertical-holder {\n    display: block;\n  }\n  .mkdf-process-holder.mkdf-responsive-1366 .mkdf-process-inner {\n    position: relative;\n    display: inline-block;\n    width: 100%;\n    vertical-align: middle;\n    padding: 0 0 0 76px;\n    margin: 0;\n    box-sizing: border-box;\n  }\n  .mkdf-process-holder.mkdf-responsive-1366 .mkdf-process-item {\n    width: 100%;\n    float: none;\n    padding: 0;\n    text-align: inherit;\n  }\n}\n\n@media only screen and (max-width: 1024px) {\n  .mkdf-process-holder.mkdf-responsive-1024 .mkdf-mark-horizontal-holder {\n    display: none;\n  }\n  .mkdf-process-holder.mkdf-responsive-1024 .mkdf-mark-vertical-holder {\n    display: block;\n  }\n  .mkdf-process-holder.mkdf-responsive-1024 .mkdf-process-inner {\n    position: relative;\n    display: inline-block;\n    width: 100%;\n    vertical-align: middle;\n    padding: 0 0 0 76px;\n    margin: 0;\n    box-sizing: border-box;\n  }\n  .mkdf-process-holder.mkdf-responsive-1024 .mkdf-process-item {\n    width: 100%;\n    float: none;\n    padding: 0;\n    text-align: inherit;\n  }\n}\n\n@media only screen and (max-width: 768px) {\n  .mkdf-process-holder.mkdf-responsive-768 .mkdf-mark-horizontal-holder {\n    display: none;\n  }\n  .mkdf-process-holder.mkdf-responsive-768 .mkdf-mark-vertical-holder {\n    display: block;\n  }\n  .mkdf-process-holder.mkdf-responsive-768 .mkdf-process-inner {\n    position: relative;\n    display: inline-block;\n    width: 100%;\n    vertical-align: middle;\n    padding: 0 0 0 76px;\n    margin: 0;\n    box-sizing: border-box;\n  }\n  .mkdf-process-holder.mkdf-responsive-768 .mkdf-process-item {\n    width: 100%;\n    float: none;\n    padding: 0;\n    text-align: inherit;\n  }\n}\n\n@media only screen and (max-width: 680px) {\n  .mkdf-process-holder.mkdf-responsive-680 .mkdf-mark-horizontal-holder {\n    display: none;\n  }\n  .mkdf-process-holder.mkdf-responsive-680 .mkdf-mark-vertical-holder {\n    display: block;\n  }\n  .mkdf-process-holder.mkdf-responsive-680 .mkdf-process-inner {\n    position: relative;\n    display: inline-block;\n    width: 100%;\n    vertical-align: middle;\n    padding: 0 0 0 76px;\n    margin: 0;\n    box-sizing: border-box;\n  }\n  .mkdf-process-holder.mkdf-responsive-680 .mkdf-process-item {\n    width: 100%;\n    float: none;\n    padding: 0;\n    text-align: inherit;\n  }\n}\n\n@media only screen and (max-width: 480px) {\n  .mkdf-process-holder.mkdf-responsive-480 .mkdf-mark-horizontal-holder {\n    display: none;\n  }\n  .mkdf-process-holder.mkdf-responsive-480 .mkdf-mark-vertical-holder {\n    display: block;\n  }\n  .mkdf-process-holder.mkdf-responsive-480 .mkdf-process-inner {\n    position: relative;\n    display: inline-block;\n    width: 100%;\n    vertical-align: middle;\n    padding: 0 0 0 76px;\n    margin: 0;\n    box-sizing: border-box;\n  }\n  .mkdf-process-holder.mkdf-responsive-480 .mkdf-process-item {\n    width: 100%;\n    float: none;\n    padding: 0;\n    text-align: inherit;\n  }\n}\n\n/* ==========================================================================\n   Process shortcode responsive style - end\n   ========================================================================== */\n/* ==========================================================================\n   Roadmap shortcode style - begin\n   ========================================================================== */\n@media only screen and (max-width: 680px) {\n  .mkdf-roadmap {\n    padding-top: 100px !important;\n  }\n  .mkdf-roadmap .mkdf-roadmap-item.mkdf-roadmap-item-above .mkdf-roadmap-item-stage-title-holder {\n    top: auto;\n    bottom: 35px;\n  }\n  .mkdf-roadmap .mkdf-roadmap-item.mkdf-roadmap-item-above .mkdf-roadmap-item-content-holder {\n    top: 75px;\n    bottom: auto;\n  }\n  .mkdf-roadmap .mkdf-roadmap-item.mkdf-roadmap-item-above .mkdf-roadmap-item-content-holder:after {\n    top: auto;\n    bottom: 100%;\n  }\n}\n\n/* ==========================================================================\n   Roadmap shortcode style - end\n   ========================================================================== */\n/* ==========================================================================\n   Section Title shortcode responsive styles - begin\n   ========================================================================== */\n@media only screen and (max-width: 1024px) {\n  .mkdf-section-title-holder.mkdf-st-two-columns {\n    padding: 0 !important;\n  }\n}\n\n@media only screen and (max-width: 768px) {\n  .mkdf-section-title-holder {\n    padding: 0 !important;\n  }\n  .mkdf-section-title-holder.mkdf-st-two-columns .mkdf-st-title,\n  .mkdf-section-title-holder.mkdf-st-two-columns .mkdf-st-text {\n    width: 100%;\n    float: none !important;\n    text-align: initial !important;\n  }\n  .mkdf-section-title-holder.mkdf-st-two-columns .mkdf-st-text {\n    margin: 14px 0 0;\n  }\n  .mkdf-section-title-holder.mkdf-st-disable-title-break .mkdf-st-title br {\n    display: none;\n  }\n}\n\n/* ==========================================================================\n   Section Title shortcode responsive styles - end\n   ========================================================================== */\n/* ==========================================================================\n   Tabs shortcode responsive style - begin\n   ========================================================================== */\n@media only screen and (max-width: 1024px) {\n  .mkdf-tabs.mkdf-tabs-standard .mkdf-tabs-nav li a {\n    padding: 7px 21px;\n  }\n  .mkdf-tabs.mkdf-tabs-boxed .mkdf-tabs-nav li {\n    margin: 0 8px 0 0;\n  }\n  .mkdf-tabs.mkdf-tabs-boxed .mkdf-tabs-nav li a {\n    padding: 7px 18px;\n  }\n  .mkdf-tabs.mkdf-tabs-simple .mkdf-tabs-nav li {\n    margin: 0 26px 0 0;\n  }\n  .mkdf-tabs.mkdf-tabs-vertical .mkdf-tabs-nav {\n    width: 180px;\n  }\n  .mkdf-tabs.mkdf-tabs-vertical .mkdf-tab-container {\n    width: calc(100% - 180px);\n    padding: 0 0 0 30px;\n  }\n}\n\n@media only screen and (max-width: 768px) {\n  .mkdf-tabs.mkdf-tabs-standard .mkdf-tabs-nav li {\n    display: block;\n    float: none;\n  }\n  .mkdf-tabs.mkdf-tabs-standard .mkdf-tabs-nav li a {\n    width: 100%;\n  }\n  .mkdf-tabs.mkdf-tabs-boxed .mkdf-tabs-nav li {\n    display: block;\n    float: none;\n    margin: 0 0 8px;\n  }\n  .mkdf-tabs.mkdf-tabs-boxed .mkdf-tabs-nav li a {\n    width: 100%;\n  }\n  .mkdf-tabs.mkdf-tabs-simple .mkdf-tabs-nav li {\n    margin: 0 20px 0 0;\n  }\n  .mkdf-tabs.mkdf-tabs-vertical .mkdf-tabs-nav,\n  .mkdf-tabs.mkdf-tabs-vertical .mkdf-tab-container {\n    display: inline-block;\n    width: 100%;\n    height: auto;\n  }\n  .mkdf-tabs.mkdf-tabs-vertical .mkdf-tabs-nav {\n    border-right: 0;\n  }\n  .mkdf-tabs.mkdf-tabs-vertical .mkdf-tabs-nav li {\n    float: left;\n    margin: 0 20px 0 0;\n  }\n  .mkdf-tabs.mkdf-tabs-vertical .mkdf-tab-container {\n    padding: 31px 0 0;\n  }\n  .mkdf-tabs .mkdf-tab-container img {\n    display: none;\n  }\n}\n\n@media only screen and (max-width: 680px) {\n  .mkdf-tabs.mkdf-tabs-simple .mkdf-tabs-nav {\n    padding: 0 0 20px;\n  }\n  .mkdf-tabs.mkdf-tabs-simple .mkdf-tabs-nav li {\n    display: block;\n    float: none;\n    margin: 0 0 20px;\n  }\n  .mkdf-tabs.mkdf-tabs-simple .mkdf-tabs-nav li a {\n    padding: 0;\n    width: 100%;\n  }\n  .mkdf-tabs.mkdf-tabs-vertical {\n    display: block;\n  }\n  .mkdf-tabs.mkdf-tabs-vertical .mkdf-tabs-nav li {\n    display: block;\n    float: none;\n    margin: 0 0 20px;\n  }\n  .mkdf-tabs.mkdf-tabs-vertical .mkdf-tabs-nav li a {\n    padding: 5px 10px 0 0;\n    width: 100%;\n    display: inline;\n  }\n}\n\n@media only screen and (max-width: 480px) {\n  .mkdf-tabs.mkdf-tabs-standard .mkdf-tabs-nav li a {\n    padding: 13px 21px 7px;\n  }\n}\n\n/* ==========================================================================\n   Tabs shortcode responsive style - end\n   ========================================================================== */\n@media only screen and (max-width: 768px) {\n  .mkdf-video-button-holder .mkdf-video-button-play {\n    font-size: 36px !important;\n  }\n  .mkdf-video-button-holder .mkdf-video-button-play span span {\n    padding: 4px;\n    width: 53px;\n    height: 53px;\n    line-height: 53px;\n  }\n  .mkdf-video-button-holder .mkdf-video-button-play span span .icon-basic-animation {\n    width: 53px;\n    height: 53px;\n    line-height: 53px;\n  }\n  .mkdf-video-button-holder .mkdf-video-button-play .mkdf-video-button-play-inner.top-right {\n    top: 9% !important;\n  }\n  .mkdf-video-button-holder .mkdf-video-button-text {\n    padding: 30px 30px 20px;\n  }\n  .mkdf-video-button-holder .mkdf-video-button-text .mkdf-video-button-title {\n    display: block;\n    width: 100%;\n  }\n  .mkdf-video-button-holder .mkdf-video-button-text p {\n    width: 100%;\n    display: block;\n  }\n  .mkdf-video-button-holder .mkdf-video-button-text-shadow-holder {\n    display: none;\n  }\n}\n\n@media only screen and (max-width: 480px) {\n  .mkdf-video-button-holder .mkdf-video-button-play {\n    font-size: 20px;\n  }\n  .mkdf-video-button-holder .mkdf-video-button-play .mkdf-video-button-play-inner.top-right {\n    top: 16% !important;\n  }\n}\n\n@media only screen and (max-width: 320px) {\n  .mkdf-video-button-holder .mkdf-video-button-play {\n    font-size: 24px !important;\n  }\n  .mkdf-video-button-holder .mkdf-video-button-play span span {\n    width: 20px;\n    height: 20px;\n    line-height: 20px;\n  }\n  .mkdf-video-button-holder .mkdf-video-button-play span span .icon-basic-animation {\n    width: 20px;\n    height: 20px;\n    line-height: 20px;\n  }\n  .mkdf-video-button-holder .mkdf-video-button-play .mkdf-video-button-play-inner.top-right {\n    top: 13% !important;\n    right: 4px !important;\n  }\n}\n\n@media only screen and (max-width: 1200px) {\n  .mkdf-workflow .mkdf-workflow-item {\n    max-width: 100%;\n  }\n  .mkdf-workflow .mkdf-workflow-item .mkdf-workflow-item-inner .mkdf-workflow-text {\n    padding: 0px 40px;\n  }\n  .mkdf-workflow .mkdf-workflow-item .mkdf-workflow-item-inner .mkdf-workflow-image {\n    padding: 0px 40px;\n  }\n}\n\n@media only screen and (max-width: 680px) {\n  .mkdf-workflow .main-line,\n  .mkdf-workflow .mkdf-workflow-item .line,\n  .mkdf-workflow .mkdf-workflow-item .mkdf-workflow-text .circle {\n    display: none !important;\n  }\n  .mkdf-workflow .mkdf-workflow-item {\n    text-align: left;\n  }\n  .mkdf-workflow .mkdf-workflow-item .mkdf-workflow-item-inner .mkdf-workflow-image {\n    width: 100%;\n    margin-bottom: 20px;\n    text-align: left;\n    padding: 0px 40px 0 0;\n  }\n  .mkdf-workflow .mkdf-workflow-item .mkdf-workflow-item-inner .mkdf-workflow-image.right {\n    text-align: left !important;\n  }\n  .mkdf-workflow .mkdf-workflow-item .mkdf-workflow-item-inner .mkdf-workflow-text {\n    width: 100% !important;\n    padding: 0px !important;\n    text-align: left !important;\n  }\n  .mkdf-workflow .mkdf-workflow-item:nth-of-type(2n) .mkdf-workflow-item-inner {\n    display: block !important;\n  }\n}\n", "////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////\n\n// layout mixins - start\n\n@mixin mkdfRelativeHolderLayout($vertical-align: middle) {\n    position: relative;\n    display: inline-block;\n    width: 100%;\n    vertical-align: $vertical-align;\n}\n\n@mixin mkdfAbsoluteHolderLayout() {\n    position: absolute;\n    display: block;\n    width: 100%;\n    height: 100%;\n    top: 0;\n    left: 0;\n}\n\n@mixin mkdfFlexBoxLayout($position: null, $align-items: null, $justify-content: null) {\n    @if ($position) {\n        position: $position;\n    }\n    \n    @include mkdfFlexLayout();\n    \n    @if ($align-items) {\n        @include mkdfFlexAlignItems($align-items);\n    }\n    \n    @if ($justify-content) {\n        @include mkdfFlexJustifyContent($justify-content);\n    }\n}\n\n@mixin mkdfFlexContainer($align-items: null, $justify-content: null, $flex-direction: null, $flex-wrap: null, $align-content: null) {\n\t@include mkdfFlexBoxLayout(null, $align-items, $justify-content);\n\t\n\t@if ($flex-direction) {\n\t\tflex-direction: $flex-direction;\n\t}\n\t\n\t@if ($flex-wrap) {\n\t\tflex-wrap: $flex-wrap;\n\t}\n\t\n\t@if ($align-content) {\n\t\talign-content: $align-content;\n\t}\n}\n\n@mixin mkdfFlexLayout() {\n    display: -webkit-box;\n    display: -webkit-flex;\n    display: -ms-flexbox;\n    display: flex;\n}\n\n@mixin mkdfInlineFlexLayout() {\n    display: -webkit-inline-flex;\n    display: -ms-inline-flexbox;\n    display: inline-flex;\n}\n\n@mixin mkdfFlexItem($order: 0, $flex-grow: 0, $flex-shrink: 1, $flex-basis: auto) {\n    order: $order;\n    flex-grow: $flex-grow;\n    flex-shrink: $flex-shrink;\n    flex-basis: $flex-basis;\n}\n\n@mixin mkdfFlexAlignItems($align-items) {\n    $older-align-items: $align-items;\n    \n    @if ($align-items == 'flex-start') {\n        $older-align-items: start;\n    } @else if ($align-items == 'flex-end') {\n        $older-align-items: end;\n    }\n    \n    -webkit-box-align: $older-align-items;\n    -webkit-align-items: $align-items;\n    -ms-flex-align: $older-align-items;\n    align-items: $align-items;\n}\n@mixin mkdfDefaultTransition($transition-param...) {\n    $transitions_each: ('-webkit-transition', '-moz-transition', 'transition');\n    $string: '';\n\n    @each $var in $transition-param{\n        @if $string == '' {\n            $string : $var $default-transition-duration $default-easing-function\n        } @else {\n            $string : $string, $var $default-transition-duration $default-easing-function\n        }\n    }\n\n\n    @each $transition in $transitions_each{\n        #{$transition}: $string;\n    }\n}\n@mixin mkdfFlexJustifyContent($justify-content) {\n    $older-justify-content: $justify-content;\n    \n    @if ($justify-content == 'flex-start') {\n        $older-justify-content: start;\n    } @else if ($justify-content == 'flex-end') {\n        $older-justify-content: end;\n    } @else if ($justify-content == 'space-between') {\n        $older-justify-content: justify;\n    }\n    \n    -webkit-box-pack: $older-justify-content;\n    -webkit-justify-content: $justify-content;\n    -ms-flex-pack: $older-justify-content;\n    justify-content: $justify-content;\n}\n\n@mixin mkdfTableLayout() {\n    position: relative;\n    display: table;\n    table-layout: fixed;\n    height: 100%;\n    width: 100%;\n}\n\n@mixin mkdfTableCellLayout($vertical-align: middle) {\n    position: relative;\n    display: table-cell;\n    height: 100%;\n    width: 100%;\n    vertical-align: $vertical-align;\n}\n\n@mixin mkdfTypographyLayout($important : null) {\n    color: inherit $important;\n    font-family: inherit $important;\n    font-size: inherit $important;\n    font-weight: inherit $important;\n    font-style: inherit $important;\n    line-height: inherit $important;\n    letter-spacing: inherit $important;\n    text-transform: inherit $important;\n}\n\n// layout mixins - end\n\n////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////\n\n// transition mixins - start\n\n@mixin mkdfTransition($transition-param...) {\n    -webkit-transition: $transition-param;\n    -moz-transition: $transition-param;\n    transition: $transition-param;\n}\n\n@mixin mkdfTransitionTransform($transition-param...) {\n    -webkit-transition: -webkit-transform $transition-param;\n    -moz-transition: -moz-transform $transition-param;\n    transition: transform $transition-param;\n}\n\n@mixin mkdfTransform($transform-param...) {\n    -webkit-transform: $transform-param;\n    -moz-transform: $transform-param;\n    transform: $transform-param;\n}\n\n@mixin mkdfAnimation($animation-param...) {\n    -webkit-animation: $animation-param;\n    -moz-animation: $animation-param;\n    animation: $animation-param;\n}\n\n@mixin mkdfTransformOrigin($animation-param...) {\n    -webkit-transform-origin: $animation-param;\n    -moz-transform-origin: $animation-param;\n    transform-origin: $animation-param;\n}\n@mixin mkdfPulse {\n\n}\n\n// transition mixins - end\n\n////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////\n\n// checkbox mixins - start\n\n$checkbox-size: 15px;\n$checkbox-border-width: 1px;\n\n%checkbox-style {\n    position: relative;\n    margin: 8px 0;\n    line-height: 1;\n\n    input[type=checkbox] {\n        width: $checkbox-size;\n        height: $checkbox-size;\n        max-height: $checkbox-size;\n        position: relative;\n        display: inline-block;\n        vertical-align: top;\n        top: 0;\n        left: 0;\n        margin: 0;\n    }\n\n    input[type=checkbox] + label {\n        position: absolute;\n        top: 0;\n        left: 0;\n        display: inline-block;\n        line-height: 0;\n        pointer-events: none;\n        cursor: pointer;\n    }\n\n    input[type=checkbox] + label span.mkdf-label-text {\n        display: inline-block;\n        padding-left: 10px;\n        line-height: $checkbox-size;\n        color: $default-heading-color;\n    }\n\n    input[type=checkbox] + label .mkdf-label-view {\n        display: inline-block;\n        vertical-align: top;\n        width: $checkbox-size;\n        height: $checkbox-size;\n        background-color: $default-background-color;\n        border: $checkbox-border-width solid $default-border-color;\n        border-radius: 2px;\n        cursor: pointer;\n        box-sizing: border-box;\n\n        &:hover {\n            cursor: pointer;\n        }\n    }\n\n    input[type=checkbox] + label .mkdf-label-view:after {\n        content: '';\n        position: absolute;\n        top: ($checkbox-size - ($checkbox-size / 2 - $checkbox-border-width)) / 2;\n        left: ($checkbox-size - ($checkbox-size / 2 - $checkbox-border-width)) / 2;\n        width: $checkbox-size / 2 - $checkbox-border-width;\n        height: $checkbox-size / 2 - $checkbox-border-width;\n        background-color: $first-main-color;\n        opacity: 0;\n        @include mkdfTransition(opacity 0.3s ease-in-out);\n    }\n\n    input[type=checkbox]:checked + label .mkdf-label-view:after {\n        opacity: 1;\n    }\n}\n\n// checkbox mixins - end\n\n////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////\n\n// common mixins - start\n\n@mixin mkdfBckImageStyle() {\n    background-size: cover;\n    background-repeat: no-repeat;\n    background-position: center center;\n}\n\n@mixin mkdfImageOverlayHoverStyle($with-hover: true) {\n    \n    @if ($with-hover) {\n        \n        &:hover {\n            \n            &:after {\n                opacity: 1;\n            }\n        }\n\n        &:after {\n            @include mkdfAbsoluteHolderLayout();\n            content: '';\n            background-color: rgba($first-main-color, .4);\n            opacity: 0;\n            @include mkdfTransition(opacity .2s ease-in-out);\n        }\n\n    } @else {\n        @include mkdfAbsoluteHolderLayout();\n        content: '';\n        background-color: rgba($first-main-color, .4);\n        opacity: 0;\n        @include mkdfTransition(opacity .2s ease-in-out);\n    }\n}\n\n@mixin mkdfStandardPaginationStyle($list_type: null) {\n    @include mkdfRelativeHolderLayout(top);\n    margin: 40px 0 0;\n    clear: both;\n\n    ul {\n        @include mkdfRelativeHolderLayout(top);\n        padding: 0;\n        margin: 0;\n        list-style: none;\n        text-align: center;\n\n        li {\n            position: relative;\n            display: inline-block;\n            vertical-align: top;\n            margin: 0 12px;\n\n            a {\n                position: relative;\n                display: inline-block;\n                vertical-align: top;\n                margin: 0;\n                padding: 0;\n            }\n\n            &.mkdf-pag-active {\n                \n                a {\n                    color: $first-main-color;\n                }\n            }\n\n            &.mkdf-pag-prev,\n            &.mkdf-pag-next,\n            &.mkdf-pag-first,\n            &.mkdf-pag-last {\n                margin: 0 2px;\n\n                a {\n                    font-size: 24px;\n\n                    span {\n                        display: block;\n                        line-height: inherit;\n\n                        &:before {\n                            display: block;\n                            line-height: inherit;\n                        }\n                    }\n                }\n            }\n\n            @if ($list_type == 'shortcode') {\n                \n                &.mkdf-pag-prev {\n                    \n                    a {\n                        opacity: 0;\n                    }\n                }\n\n                &.mkdf-pag-next {\n                    \n                    a {\n                        opacity: 1;\n                    }\n                }\n\n            } @else if ($list_type == 'shop') {\n                span {\n                    position: relative;\n                    display: inline-block;\n                    vertical-align: top;\n                    margin: 0;\n                    padding: 0;\n                    color: $first-main-color;\n                }\n\n                a {\n                    \n                    &.next,\n                    &.prev {\n                        font-size: 0;\n                        line-height: 0;\n\n                        &:before {\n                            display: block;\n                            font-family: 'ElegantIcons'; // same icon pack as in our templates for pagination\n                            font-size: 24px;\n                            line-height: 26px;\n                            -webkit-font-smoothing: antialiased;\n                            -moz-osx-font-smoothing: grayscale;\n                        }\n                    }\n\n                    &.prev {\n                        margin-right: -10px;\n\n                        &:before {\n                            content: \"\\34\";\n                        }\n                    }\n\n                    &.next {\n                        margin-left: -10px;\n\n                        &:before {\n                            content: \"\\35\";\n                        }\n                    }\n                }\n            }\n        }\n    }\n}\n\n@mixin mkdfButtonDefaultStyle() {\n    position: relative;\n    display: inline-block;\n    vertical-align: middle;\n    width: auto;\n    margin: 0;\n    font-family: $default-text-font;\n    font-size: 12px;\n    line-height: 2em;\n    letter-spacing: 0.16em;\n    font-weight: 700;\n    text-transform: uppercase;\n    outline: none;\n    box-sizing: border-box;\n    @include mkdfTransition(color .2s ease-in-out, background-color .2s ease-in-out, border-color .2s ease-in-out);\n}\n\n@mixin mkdfButtonSize($size: medium) {\n    \n    @if ($size == 'small') {\n        padding: 11px 24px;\n\n    } @else if ($size == 'medium') {\n        padding: 13px 34px 11px;\n\n\n    } @else if ($size == 'large') {\n        padding: 13px 43px 11px;\n\n    } @else if ($size == 'huge') {\n        padding: 21px 60px 16px;\n        font-size:14px;\n    }\n}\n\n@mixin mkdfButtonTransparentColor() {\n    color: $default-text-color;\n    background-color: transparent;\n}\n\n@mixin mkdfButtonSolidColor($important: null) {\n    color: #fff $important;\n    background-color: $first-main-color $important;\n    border: 1px solid transparent $important;\n}\n\n@mixin mkdfButtonSolidHoverColor($important: null) {\n    color: #fff $important;\n    background-color: $first-main-color $important;\n    box-shadow: none !important;\n}\n\n@mixin mkdfButtonOutlineColor($important: null) {\n    color: $first-main-color $important;\n    background-color: transparent $important;\n    border: 1px solid $first-main-color $important;\n}\n\n@mixin mkdfButtonOutlineHoverColor($important: null) {\n    color: #fff $important;\n    background-color: $first-main-color $important;\n    border-color: $first-main-color $important;\n}\n\n@mixin mkdfPlaceholder {\n    &::-webkit-input-placeholder {\n        @content\n    }\n\n    &:-moz-placeholder {\n        @content;\n\n        opacity: 1;\n    }\n    &::-moz-placeholder {\n        @content;\n\n        opacity: 1;\n    }\n    &:-ms-input-placeholder {\n        @content\n    }\n}\n\n\n @keyframes animate-btn-line {\n    0% {\n        @include mkdfTransformOrigin(0 50%);\n        @include mkdfTransform(scaleX(0));\n    }\n    100% {\n        @include mkdfTransformOrigin(0 50%);\n        @include mkdfTransform(scaleX(1));\n    }\n}\n@-webkit-keyframes mkdfPulsebig {\n   0% {\n    -moz-box-shadow: 0 0 0 0 currentColor;\n    box-shadow: 0 0 0 0 currentColor;\n  }\n  70% {\n      -moz-box-shadow: 0 0 0 20px rgba($first-main-color, 0.3);\n      box-shadow: 0 0 0 20px rgba($first-main-color, 0);\n  }\n  100% {\n      -moz-box-shadow: 0 0 0 0 rgba($first-main-color, 0);\n      box-shadow: 0 0 0 0 rgba($first-main-color, 0);\n  }\n}\n@keyframes mkdfPulsebig {\n  0% {\n    -moz-box-shadow: 0 0 0 0 currentColor;\n    box-shadow: 0 0 0 0 currentColor;\n  }\n  70% {\n      -moz-box-shadow: 0 0 0 20px rgba($first-main-color, 0.3);\n      box-shadow: 0 0 0 20px rgba($first-main-color, 0);\n  }\n  100% {\n      -moz-box-shadow: 0 0 0 0 rgba($first-main-color, 0);\n      box-shadow: 0 0 0 0 rgba($first-main-color, 0);\n  }\n}\n@-webkit-keyframes mkdfPulsesmall {\n  0% {\n    -webkit-box-shadow: 0 0 0 0 currentColor;\n  }\n  70% {\n      -webkit-box-shadow: 0 0 0 10px rgba($first-main-color, 0.15);\n  }\n  100% {\n      -webkit-box-shadow: 0 0 0 0 rgba($first-main-color, 0);\n  }\n}\n@keyframes mkdfPulsesmall {\n  0% {\n    -moz-box-shadow: 0 0 0 0 currentColor;\n    box-shadow: 0 0 0 0 currentColor;\n  }\n  70% {\n      -moz-box-shadow: 0 0 0 10px rgba($first-main-color, 0.15);\n      box-shadow: 0 0 0 10px rgba($first-main-color, 0);\n  }\n  100% {\n      -moz-box-shadow: 0 0 0 0 rgba($first-main-color, 0);\n      box-shadow: 0 0 0 0 rgba($first-main-color, 0);\n  }\n}\n@-webkit-keyframes mkdfPulsesmallfirst {\n  0% {\n    -webkit-box-shadow: 0 0 0 0 $first-main-color;\n  }\n  70% {\n      -webkit-box-shadow: 0 0 0 10px rgba($first-main-color, 0.15);\n  }\n  100% {\n      -webkit-box-shadow: 0 0 0 0 rgba($first-main-color, 0);\n  }\n}\n@keyframes mkdfPulsesmallfirst {\n  0% {\n    -moz-box-shadow: 0 0 0 0 $first-main-color;\n    box-shadow: 0 0 0 0 $first-main-color;\n  }\n  70% {\n      -moz-box-shadow: 0 0 0 10px rgba($first-main-color, 0.15);\n      box-shadow: 0 0 0 10px rgba($first-main-color, 0);\n  }\n  100% {\n      -moz-box-shadow: 0 0 0 0 rgba($first-main-color, 0);\n      box-shadow: 0 0 0 0 rgba($first-main-color, 0);\n  }\n}\n\n// common mixins - end\n\n////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////\n\n// mixins styles - start\n\n%input-style {\n    position: relative;\n    width: 100%;\n    margin: 0 0 $input-margin;\n    padding: $input-vertical-padding $input-horizontal-padding;\n    font-family: $default-text-font;\n    font-size: 16px;\n    font-weight: inherit;\n    color: #a2a3a3;\n    background-color: transparent;\n    border: 2px solid $default-border-color;\n    border-radius: 0;\n    outline: 0;\n    cursor: pointer;\n    -webkit-appearance: none;\n    box-sizing: border-box;\n    @include mkdfTransition(border-color 0.2s ease-in-out);\n\n    &:focus {\n        color: $default-heading-color;\n        \n    }\n\n    @include mkdfPlaceholder {\n        color: inherit;\n    }\n}\n\n// mixins styles - end\n\n////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////\n\n//media query mixins - start\n\n@mixin laptop-landscape-large {\n    @media only screen and (max-width: map-get($breakpoints, laptop-landscape-large)) {\n        @content;\n    }\n}\n\n@mixin laptop-landscape-mac {\n    @media only screen and (max-width: map-get($breakpoints, laptop-landscape-mac)) {\n        @content;\n    }\n}\n\n@mixin laptop-landscape-medium {\n    @media only screen and (max-width: map-get($breakpoints, laptop-landscape-medium)) {\n        @content;\n    }\n}\n\n@mixin laptop-landscape {\n    @media only screen and (max-width: map-get($breakpoints, laptop-landscape)) {\n        @content;\n    }\n}\n\n@mixin ipad-landscape {\n    @media only screen and (max-width: map-get($breakpoints, ipad-landscape)) {\n        @content;\n    }\n}\n\n@mixin ipad-portrait {\n    @media only screen and (max-width: map-get($breakpoints, ipad-portrait)) {\n        @content;\n    }\n}\n\n@mixin phone-landscape {\n    @media only screen and (max-width: map-get($breakpoints, phone-landscape)) {\n        @content;\n    }\n}\n\n@mixin phone-portrait {\n    @media only screen and (max-width: map-get($breakpoints, phone-portrait)) {\n        @content;\n    }\n}\n\n@mixin smaller-phone-portrait {\n    @media only screen and (max-width: map-get($breakpoints, smaller-phone-portrait)) {\n        @content;\n    }\n}\n\n// media query mixins - end\n\n// animation mixin - start\n\n@mixin keyframes($name) {\n    @-webkit-keyframes #{$name} {\n        @content;\n    }\n\n    @keyframes #{$name} {\n        @content;\n    }\n}\n\n@mixin animation($name, $duration, $repeat, $timing, $delay) {\n    -webkit-animation-name: $name;\n    -webkit-animation-duration: $duration;\n    -webkit-animation-iteration-count: $repeat;\n    -webkit-animation-timing-function: $timing;\n    -webkit-animation-delay: $delay;\n    -webkit-animation-fill-mode: forwards; // this prevents the animation from restarting!\n\n    animation-name: $name;\n    animation-duration: $duration;\n    animation-iteration-count: $repeat;\n    animation-timing-function: $timing;\n    animation-delay: $delay;\n    animation-fill-mode: forwards; // this prevents the animation from restarting!\n}\n\n// animation mixin - end\n\n////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////\n\n// heading mixins - start\n\n@mixin mkdfDefaultHeadingStyle() {\n    @include mkdfHeadingStyle();\n    font-weight: 600;\n    margin: 25px 0;\n    letter-spacing: -0.025em;\n    font-family: $default-text-font;\n    -ms-word-wrap: break-word;\n    word-wrap: break-word;\n    \n    a {\n        @include mkdfTypographyLayout();\n        \n        &:hover {\n            color: $first-main-color;\n        }\n    }\n}\n\n@mixin mkdfHeadingStyle($with-heading: null, $with-color: true) {\n    \n    @if ($with-color) {\n        color: $default-heading-color;\n    }\n    \n    @if ($with-heading == 'h1') {\n        @include mkdfH1();\n    } @else if ($with-heading == 'h2') {\n        @include mkdfH2();\n    } @else if ($with-heading == 'h3') {\n        @include mkdfH3();\n    } @else if ($with-heading == 'h4') {\n        @include mkdfH4();\n    } @else if ($with-heading == 'h5') {\n        @include mkdfH5();\n    } @else if ($with-heading == 'h6') {\n        @include mkdfH6();\n    }\n}\n\n@mixin mkdfBody() {\n    font-family: $additional-text-font;\n    font-size: 16px;\n    font-weight: 400;\n    line-height: 30px;\n    color: $default-text-color;\n    background-color: $default-background-color;\n    -webkit-font-smoothing: antialiased;\n}\n\n@mixin mkdfH1() {\n    font-size: 55px;\n    line-height: 1.23em;\n}\n\n@mixin mkdfH2() {\n    font-size: 40px;\n    line-height: 1.25em;\n}\n\n@mixin mkdfH3() {\n    font-size: 36px;\n    line-height: 1.16em;\n}\n\n@mixin mkdfH4() {\n    font-size: 30px;\n    line-height: 1.2em;\n}\n\n@mixin mkdfH5() {\n    font-size: 26px;\n    line-height: 1.23em;\n}\n\n@mixin mkdfH6() {\n    font-size: 20px;\n    line-height: 1.3em;\n    letter-spacing: 0;\n}\n\n// heading mixins - end\n\n////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////\n\n@mixin mkdfBlockquote($important : null) {\n    @include mkdfRelativeHolderLayout();\n    margin: 10px 0 $important;\n    padding: 20px 40px $important;\n    font-size: 18px $important;\n    line-height: 30px $important;\n    quotes: none;\n    box-sizing: border-box;\n    border: none $important;\n    color: $default-text-color $important;\n\n    > * {\n        @include mkdfTypographyLayout();\n        margin: 0;\n    }\n\n    &:after,\n    &:before{\n        content: '';\n    }\n\n    //&:before {\n    //    content: \"\\7b\";\n    //    font-family: \"ElegantIcons\";\n    //    font-size: 60px;\n    //    color: $first-main-color;\n    //    position: absolute;\n    //    top:50%;\n    //    @include mkdfTransform(translateY(-50%));\n    //}\n\n    cite,\n    .wp-block-quote__citation,\n    .wp-block-pullquote__citation,\n    footer {\n        display: block $important;\n        margin-top: 10px $important;\n        text-align: inherit $important;\n        font-size: 14px $important;\n        line-height: 1.3em $important;\n        letter-spacing: 0 $important;\n        font-style: normal  $important;\n        font-weight: 400 $important;\n        text-transform: none $important;\n    }\n\n    cite{\n        padding-left: 10%;\n        display: inline-block $important;\n    }\n\n}\n\n", "$breakpoints: (\n        laptop-landscape-large: 1440px,\n        laptop-landscape-mac: 1366px,\n        laptop-landscape-medium: 1280px,\n        laptop-landscape: 1200px,\n        ipad-landscape: 1024px,\n        ipad-portrait: 768px,\n        phone-landscape: 680px,\n        phone-portrait: 480px,\n        smaller-phone-portrait: 320px\n);\n\n$grid-width: 1100px;\n$grid-width-laptop-landscape: 950px;\n$grid-width-ipad-landscape: 768px;\n$grid-width-ipad-portrait: 600px;\n$grid-width-phone-landscape: 420px;\n$grid-width-phone-portrait: 300px;\n$grid-width-smaller-phone-portrait: 90%;\n\n$grid-width-boxed: 1150px;\n$grid-width-laptop-landscape-boxed: 1000px;\n$grid-width-ipad-landscape-boxed: 818px;\n$grid-width-ipad-portrait-boxed: 650px;\n$grid-width-phone-landscape-boxed: 470px;\n$grid-width-phone-portrait-boxed: 350px;\n$grid-width-smaller-phone-portrait-boxed: 92%;\n\n$grid-width-1300: 1300px;\n$grid-width-1200: 1200px;\n$grid-width-1000: 1000px;\n$grid-width-800: 800px;\n\n$default-text-font: '<PERSON><PERSON>', sans-serif;\n$additional-text-font: '<PERSON><PERSON><PERSON>', sans-serif;\n\n$first-main-color: #ea3d56;\n$first-main-color-dark-blue: #1b2c58;\n$first-main-color-medium-blue: #3745a5;\n$first-main-color-ligh-blue: #1f75ff;\n$first-main-color-yellow: #ffc40e;\n$first-main-color-green: #56c4c5;\n$default-heading-color: #1b2c58;\n$default-text-color: #868890;\n$shadow-color: inherit;\n\n\n$default-background-color: #fff;\n$additional-background-color: #f6f6f6;\n$default-border-color: rgba(#e1e1e1, 0.3);\n$default-border-radius: 4px;\n$default-box-shadow: 0 0 4.85px 0.15px rgba(#000, 0.09);\n\n$header-light-color: #fff;\n$header-light-hover-color: $first-main-color;\n$header-dark-color: $default-heading-color;\n$header-dark-hover-color: $first-main-color;\n\n// input elements\n$input-height: 50px;\n$sselect-input-height: $input-height;\n$input-vertical-padding: 22px;\n$input-horizontal-padding: 16px;\n$input-margin: 18px;\n\n// responsive breakpoints\n$laptop-landscape-large-plus-pixel: 1441px;\n$laptop-landscape-large: 1440px;\n$laptop-landscape-mac-plus-pixel: 1367px;\n$laptop-landscape-mac: 1366px;\n$laptop-landscape-medium-plus-pixel: 1281px;\n$laptop-landscape-medium: 1280px;\n$laptop-landscape-plus-pixel: 1201px;\n$laptop-landscape: 1200px;\n$ipad-landscape-plus-pixel: 1025px;\n$ipad-landscape: 1024px;\n$ipad-portrait-plus-pixel: 769px;\n$ipad-portrait: 768px;\n$phone-landscape-plus-pixel: 681px;\n$phone-landscape: 680px;\n$phone-portrait-plus-pixel: 481px;\n$phone-portrait: 480px;\n$smaller-phone-portrait-plus-pixel: 321px;\n$smaller-phone-portrait: 320px;\n\n$default-easing-function: ease;\n$default-transition-duration: .5s;", "/* ==========================================================================\n   Button shortcode responsive style - begin\n   ========================================================================== */\n\n@include ipad-landscape {\n\n\t.mkdf-btn {\n\n\t\t&.mkdf-btn-large {\n\t\t\t@include mkdfButtonSize();\n\t\t}\n\t}\n\t.mkdf-btn {\n\n\t\t\n\t\t&.mkdf-btn-huge {\n\t\t\t@include mkdfButtonSize(huge);\n\t\t}\n\t}\n}\n/* ==========================================================================\n   Button shortcode responsive style - end\n   ========================================================================== */", "/* ==========================================================================\n   Call To Action shortcode responsive style - begin\n   ========================================================================== */\n\n\n@media only screen and (min-width: $laptop-landscape-plus-pixel) and (max-width: 1300px) {\n\t\n\t.mkdf-call-to-action-holder {\n\t\t\n\t\t.mkdf-grid {\n\t\t\twidth: 1100px;\n\t\t}\n\t}\n}\n\n@include laptop-landscape {\n\t\n\t.mkdf-call-to-action-holder {\n\t\t\n\t\t&.mkdf-three-quarters-columns,\n\t\t&.mkdf-four-fifths-columns {\n\t\t\t\n\t\t\t.mkdf-cta-text-holder {\n\t\t\t\twidth: 66.66666666666667%;\n\t\t\t}\n\t\t\t\n\t\t\t.mkdf-cta-button-holder {\n\t\t\t\twidth: 33.33333333333333%;\n\t\t\t}\n\t\t}\n\t}\n}\n\n@include ipad-portrait {\n\t\n\t.mkdf-call-to-action-holder {\n\t\t\n\t\t&.mkdf-normal-layout {\n\t\t\t\n\t\t\t.mkdf-cta-inner,\n\t\t\t.mkdf-cta-text-holder,\n\t\t\t.mkdf-cta-button-holder {\n\t\t\t\tdisplay: block;\n\t\t\t}\n\t\t\t\n\t\t\t.mkdf-cta-button-holder {\n\t\t\t\tmargin: 28px 0 0;\n\t\t\t\ttext-align: initial;\n\t\t\t}\n\t\t}\n\t\t\n\t\t&.mkdf-two-halves-columns,\n\t\t&.mkdf-two-thirds-columns,\n\t\t&.mkdf-three-quarters-columns,\n\t\t&.mkdf-four-fifths-columns {\n\t\t\t\n\t\t\t.mkdf-cta-text-holder,\n\t\t\t.mkdf-cta-button-holder {\n\t\t\t\twidth: 100%;\n\t\t\t}\n\t\t}\n\t}\n}\n/* ==========================================================================\n   Call To Action shortcode responsive style - end\n   ========================================================================== */", "/* ==========================================================================\n   Countdown shortcode responsive style - begin\n   ========================================================================== */\n\n@include laptop-landscape {\n\t\n\t.mkdf-countdown {\n\t\t\n\t\t.countdown-row {\n\t\t\t\n\t\t\t.countdown-section {\n\t\t\t\t\n\t\t\t\t.countdown-amount {\n\t\t\t\t\tfont-size: 60px;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n}\n\n@include ipad-portrait {\n    \n    .mkdf-countdown {\n        \n        .countdown-row {\n\t        \n            &.countdown-show4,\n            &.countdown-show5,\n            &.countdown-show6 {\n\t\n\t            .countdown-section {\n\t\t            width: 33.33333333333333%;\n\t            }\n            }\n\t\n\t        .countdown-section {\n\t\t\n\t\t        .countdown-amount {\n\t\t\t        font-size: 50px;\n\t\t        }\n\t        }\n        }\n    }\n}\n\n@include phone-landscape {\n\t\n\t.mkdf-countdown {\n\t\t\n\t\t.countdown-row {\n\t\t\t\n\t\t\t.countdown-section {\n\t\t\t\t\n\t\t\t\t.countdown-amount {\n\t\t\t\t\tfont-size: 40px;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n}\n/* ==========================================================================\n   Countdown shortcode responsive style - end\n   ========================================================================== */", "/* ==========================================================================\n   Custom Font shortcode responsive styles - begin\n   ========================================================================== */\n\n@include ipad-portrait {\n\t\n\t.mkdf-custom-font-holder {\n\t\t\n\t\t&.mkdf-disable-title-break {\n\t\t\t\n\t\t\tbr {\n\t\t\t\tdisplay: none;\n\t\t\t}\n\t\t}\n\t}\n}\n/* ==========================================================================\n   Custom Font shortcode responsive styles - end\n   ========================================================================== */\n\n\n", "/* ==========================================================================\n   Elements Holder shortcode responsive style - begin\n   ========================================================================== */\n\n.mkdf-elements-holder {\n\t\n\t$columns_label: ('two', 'three', 'four', 'five', 'six');\n\t\n\t@include laptop-landscape-mac {\n\t\t\n\t\t&.mkdf-responsive-mode-1366 {\n\t\t\t\n\t\t\t@for $i from 0 to length($columns_label) {\n\t\t\t\t&.mkdf-#{nth($columns_label,$i+1)}-columns {\n\t\t\t\t\t\n\t\t\t\t\t.mkdf-eh-item {\n\t\t\t\t\t\twidth: 100%;\n\t\t\t\t\t\theight: auto;\n\t\t\t\t\t\tdisplay: inline-block;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\t\n\t\t\t&.mkdf-one-column-alignment-left {\n\t\t\t\t\n\t\t\t\t.mkdf-eh-item {\n\t\t\t\t\t\n\t\t\t\t\t.mkdf-eh-item-content {\n\t\t\t\t\t\ttext-align: left;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\t\n\t\t\t&.mkdf-one-column-alignment-right {\n\t\t\t\t\n\t\t\t\t.mkdf-eh-item {\n\t\t\t\t\t\n\t\t\t\t\t.mkdf-eh-item-content {\n\t\t\t\t\t\ttext-align: right;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\t\n\t\t\t&.mkdf-one-column-alignment-center {\n\t\t\t\t\n\t\t\t\t.mkdf-eh-item {\n\t\t\t\t\t\n\t\t\t\t\t.mkdf-eh-item-content {\n\t\t\t\t\t\ttext-align: center;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\t\n\t@include ipad-landscape {\n\t\t\n\t\t&.mkdf-responsive-mode-1024 {\n\t\t\t\n\t\t\t@for $i from 0 to length($columns_label) {\n\t\t\t\t&.mkdf-#{nth($columns_label,$i+1)}-columns {\n\t\t\t\t\t\n\t\t\t\t\t.mkdf-eh-item {\n\t\t\t\t\t\twidth: 100%;\n\t\t\t\t\t\theight: auto;\n\t\t\t\t\t\tdisplay: inline-block;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\t\n\t\t\t&.mkdf-one-column-alignment-left {\n\t\t\t\t\n\t\t\t\t.mkdf-eh-item {\n\t\t\t\t\t\n\t\t\t\t\t.mkdf-eh-item-content {\n\t\t\t\t\t\ttext-align: left;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\t\n\t\t\t&.mkdf-one-column-alignment-right {\n\t\t\t\t\n\t\t\t\t.mkdf-eh-item {\n\t\t\t\t\t\n\t\t\t\t\t.mkdf-eh-item-content {\n\t\t\t\t\t\ttext-align: right;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\t\n\t\t\t&.mkdf-one-column-alignment-center {\n\t\t\t\t\n\t\t\t\t.mkdf-eh-item {\n\t\t\t\t\t\n\t\t\t\t\t.mkdf-eh-item-content {\n\t\t\t\t\t\ttext-align: center;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\t\n\t@include ipad-portrait {\n\t\t\n\t\t&.mkdf-responsive-mode-768 {\n\t\t\t\n\t\t\t@for $i from 0 to length($columns_label) {\n\t\t\t\t&.mkdf-#{nth($columns_label,$i+1)}-columns {\n\t\t\t\t\t\n\t\t\t\t\t.mkdf-eh-item {\n\t\t\t\t\t\twidth: 100%;\n\t\t\t\t\t\theight: auto;\n\t\t\t\t\t\tdisplay: inline-block;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\t\n\t\t\t&.mkdf-one-column-alignment-left {\n\t\t\t\t\n\t\t\t\t.mkdf-eh-item {\n\t\t\t\t\t\n\t\t\t\t\t.mkdf-eh-item-content {\n\t\t\t\t\t\ttext-align: left;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\t\n\t\t\t&.mkdf-one-column-alignment-right {\n\t\t\t\t\n\t\t\t\t.mkdf-eh-item {\n\t\t\t\t\t\n\t\t\t\t\t.mkdf-eh-item-content {\n\t\t\t\t\t\ttext-align: right;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\t\n\t\t\t&.mkdf-one-column-alignment-center {\n\t\t\t\t\n\t\t\t\t.mkdf-eh-item {\n\t\t\t\t\t\n\t\t\t\t\t.mkdf-eh-item-content {\n\t\t\t\t\t\ttext-align: center;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\t\n\t@include phone-landscape {\n\t\t\n\t\t&.mkdf-responsive-mode-680 {\n\t\t\t\n\t\t\t@for $i from 0 to length($columns_label) {\n\t\t\t\t&.mkdf-#{nth($columns_label,$i+1)}-columns {\n\t\t\t\t\t\n\t\t\t\t\t.mkdf-eh-item {\n\t\t\t\t\t\twidth: 100%;\n\t\t\t\t\t\theight: auto;\n\t\t\t\t\t\tdisplay: inline-block;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\t\n\t\t\t&.mkdf-one-column-alignment-left {\n\t\t\t\t\n\t\t\t\t.mkdf-eh-item {\n\t\t\t\t\t\n\t\t\t\t\t.mkdf-eh-item-content {\n\t\t\t\t\t\ttext-align: left;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\t\n\t\t\t&.mkdf-one-column-alignment-right {\n\t\t\t\t\n\t\t\t\t.mkdf-eh-item {\n\t\t\t\t\t\n\t\t\t\t\t.mkdf-eh-item-content {\n\t\t\t\t\t\ttext-align: right;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\t\n\t\t\t&.mkdf-one-column-alignment-center {\n\t\t\t\t\n\t\t\t\t.mkdf-eh-item {\n\t\t\t\t\t\n\t\t\t\t\t.mkdf-eh-item-content {\n\t\t\t\t\t\ttext-align: center;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\t\n\t@include phone-portrait {\n\t\t\n\t\t&.mkdf-responsive-mode-480 {\n\t\t\t\n\t\t\t@for $i from 0 to length($columns_label) {\n\t\t\t\t&.mkdf-#{nth($columns_label,$i+1)}-columns {\n\t\t\t\t\t\n\t\t\t\t\t.mkdf-eh-item {\n\t\t\t\t\t\twidth: 100%;\n\t\t\t\t\t\theight: auto;\n\t\t\t\t\t\tdisplay: inline-block;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\t\n\t\t\t&.mkdf-one-column-alignment-left {\n\t\t\t\t\n\t\t\t\t.mkdf-eh-item {\n\t\t\t\t\t\n\t\t\t\t\t.mkdf-eh-item-content {\n\t\t\t\t\t\ttext-align: left;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\t\n\t\t\t&.mkdf-one-column-alignment-right {\n\t\t\t\t\n\t\t\t\t.mkdf-eh-item {\n\t\t\t\t\t\n\t\t\t\t\t.mkdf-eh-item-content {\n\t\t\t\t\t\ttext-align: right;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\t\n\t\t\t&.mkdf-one-column-alignment-center {\n\t\t\t\t\n\t\t\t\t.mkdf-eh-item {\n\t\t\t\t\t\n\t\t\t\t\t.mkdf-eh-item-content {\n\t\t\t\t\t\ttext-align: center;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\t\n\t\t.mkdf-eh-item-content{\n\t\t\tpadding: 0 10px;\n\t\t}\n\t}\n}\n/* ==========================================================================\n   Elements Holder shortcode responsive style - end\n   ========================================================================== */", "/* ==========================================================================\n   Google Map shortcode responsive style - begin\n   ========================================================================== */\n\n@include ipad-landscape {\n\n\t.mkdf-google-map-overlay {\n\t\tdisplay: block;\n\t}\n}\n/* ==========================================================================\n   Google Map shortcode responsive style - end\n   ========================================================================== */", "/* ==========================================================================\n   Process shortcode responsive style - begin\n   ========================================================================== */\n\n.mkdf-process-holder {\n\t\n\t@include laptop-landscape-mac {\n\t\t\n\t\t&.mkdf-responsive-1366 {\n\t\t\t\n\t\t\t.mkdf-mark-horizontal-holder {\n\t\t\t\tdisplay: none;\n\t\t\t}\n\t\t\t\n\t\t\t.mkdf-mark-vertical-holder {\n\t\t\t\tdisplay: block;\n\t\t\t}\n\t\t\t\n\t\t\t.mkdf-process-inner {\n\t\t\t\t@include mkdfRelativeHolderLayout();\n\t\t\t\tpadding: 0 0 0 76px;\n\t\t\t\tmargin: 0;\n\t\t\t\tbox-sizing: border-box;\n\t\t\t}\n\t\t\t\n\t\t\t.mkdf-process-item {\n\t\t\t\twidth: 100%;\n\t\t\t\tfloat: none;\n\t\t\t\tpadding: 0;\n\t\t\t\ttext-align: inherit;\n\t\t\t}\n\t\t}\n\t}\n\t\n\t@include ipad-landscape {\n\t\t\n\t\t&.mkdf-responsive-1024 {\n\t\t\t\n\t\t\t.mkdf-mark-horizontal-holder {\n\t\t\t\tdisplay: none;\n\t\t\t}\n\t\t\t\n\t\t\t.mkdf-mark-vertical-holder {\n\t\t\t\tdisplay: block;\n\t\t\t}\n\t\t\t\n\t\t\t.mkdf-process-inner {\n\t\t\t\t@include mkdfRelativeHolderLayout();\n\t\t\t\tpadding: 0 0 0 76px;\n\t\t\t\tmargin: 0;\n\t\t\t\tbox-sizing: border-box;\n\t\t\t}\n\t\t\t\n\t\t\t.mkdf-process-item {\n\t\t\t\twidth: 100%;\n\t\t\t\tfloat: none;\n\t\t\t\tpadding: 0;\n\t\t\t\ttext-align: inherit;\n\t\t\t}\n\t\t}\n\t}\n\t\n\t@include ipad-portrait {\n\t\t\n\t\t&.mkdf-responsive-768 {\n\t\t\t\n\t\t\t.mkdf-mark-horizontal-holder {\n\t\t\t\tdisplay: none;\n\t\t\t}\n\t\t\t\n\t\t\t.mkdf-mark-vertical-holder {\n\t\t\t\tdisplay: block;\n\t\t\t}\n\t\t\t\n\t\t\t.mkdf-process-inner {\n\t\t\t\t@include mkdfRelativeHolderLayout();\n\t\t\t\tpadding: 0 0 0 76px;\n\t\t\t\tmargin: 0;\n\t\t\t\tbox-sizing: border-box;\n\t\t\t}\n\t\t\t\n\t\t\t.mkdf-process-item {\n\t\t\t\twidth: 100%;\n\t\t\t\tfloat: none;\n\t\t\t\tpadding: 0;\n\t\t\t\ttext-align: inherit;\n\t\t\t}\n\t\t}\n\t}\n\t\n\t@include phone-landscape {\n\t\t\n\t\t&.mkdf-responsive-680 {\n\t\t\t\n\t\t\t.mkdf-mark-horizontal-holder {\n\t\t\t\tdisplay: none;\n\t\t\t}\n\t\t\t\n\t\t\t.mkdf-mark-vertical-holder {\n\t\t\t\tdisplay: block;\n\t\t\t}\n\t\t\t\n\t\t\t.mkdf-process-inner {\n\t\t\t\t@include mkdfRelativeHolderLayout();\n\t\t\t\tpadding: 0 0 0 76px;\n\t\t\t\tmargin: 0;\n\t\t\t\tbox-sizing: border-box;\n\t\t\t}\n\t\t\t\n\t\t\t.mkdf-process-item {\n\t\t\t\twidth: 100%;\n\t\t\t\tfloat: none;\n\t\t\t\tpadding: 0;\n\t\t\t\ttext-align: inherit;\n\t\t\t}\n\t\t}\n\t}\n\t\n\t@include phone-portrait {\n\t\t\n\t\t&.mkdf-responsive-480 {\n\t\t\t\n\t\t\t.mkdf-mark-horizontal-holder {\n\t\t\t\tdisplay: none;\n\t\t\t}\n\t\t\t\n\t\t\t.mkdf-mark-vertical-holder {\n\t\t\t\tdisplay: block;\n\t\t\t}\n\t\t\t\n\t\t\t.mkdf-process-inner {\n\t\t\t\t@include mkdfRelativeHolderLayout();\n\t\t\t\tpadding: 0 0 0 76px;\n\t\t\t\tmargin: 0;\n\t\t\t\tbox-sizing: border-box;\n\t\t\t}\n\t\t\t\n\t\t\t.mkdf-process-item {\n\t\t\t\twidth: 100%;\n\t\t\t\tfloat: none;\n\t\t\t\tpadding: 0;\n\t\t\t\ttext-align: inherit;\n\t\t\t}\n\t\t}\n\t}\n}\n/* ==========================================================================\n   Process shortcode responsive style - end\n   ========================================================================== */", "/* ==========================================================================\n   Roadmap shortcode style - begin\n   ========================================================================== */\n\n.mkdf-roadmap {\n\t@include phone-landscape{\n\t\tpadding-top: 100px !important; //important because od the inline style\n\n\t\t.mkdf-roadmap-item.mkdf-roadmap-item-above{\n\t\t\t.mkdf-roadmap-item-stage-title-holder{\n\t\t\t\ttop: auto;\n\t\t\t\tbottom: 35px;\n\t\t\t}\n\n\t\t\t.mkdf-roadmap-item-content-holder{\n\t\t\t\ttop: 75px;\n\t\t\t\tbottom: auto;\n\n\t\t\t\t&:after{\n\t\t\t\t\ttop: auto;\n\t\t\t\t\tbottom: 100%;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n}\n/* ==========================================================================\n   Roadmap shortcode style - end\n   ========================================================================== */", "/* ==========================================================================\n   Section Title shortcode responsive styles - begin\n   ========================================================================== */\n\n@include ipad-landscape {\n\t\n\t.mkdf-section-title-holder {\n\t\t\n\t\t&.mkdf-st-two-columns {\n\t\t\tpadding: 0 !important; // it can be set inline in shortcode options\n\t\t}\n\t}\n}\n\n@include ipad-portrait {\n\t\n\t.mkdf-section-title-holder {\n\t\tpadding: 0 !important; // it can be set inline in shortcode options\n\t\t\n\t\t&.mkdf-st-two-columns {\n\t\t\t\n\t\t\t.mkdf-st-title,\n\t\t\t.mkdf-st-text {\n\t\t\t\twidth: 100%;\n\t\t\t\tfloat: none !important;\n\t\t\t\ttext-align: initial !important;\n\t\t\t}\n\t\t\t\n\t\t\t.mkdf-st-text {\n\t\t\t\tmargin: 14px 0 0;\n\t\t\t}\n\t\t}\n\t\t\n\t\t&.mkdf-st-disable-title-break {\n\t\t\t\n\t\t\t.mkdf-st-title {\n\t\t\t\t\n\t\t\t\tbr {\n\t\t\t\t\tdisplay: none;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n}\n/* ==========================================================================\n   Section Title shortcode responsive styles - end\n   ========================================================================== */\n\n\n", "/* ==========================================================================\n   Tabs shortcode responsive style - begin\n   ========================================================================== */\n\n@include ipad-landscape {\n\t\n\t.mkdf-tabs {\n\t\t\n\t\t&.mkdf-tabs-standard {\n\t\t\t\n\t\t\t.mkdf-tabs-nav {\n\t\t\t\t\n\t\t\t\tli {\n\t\t\t\t\t\n\t\t\t\t\ta {\n\t\t\t\t\t\tpadding: 7px 21px;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\t\n\t\t&.mkdf-tabs-boxed {\n\t\t\t\n\t\t\t.mkdf-tabs-nav {\n\t\t\t\t\n\t\t\t\tli {\n\t\t\t\t\tmargin: 0 8px 0 0;\n\t\t\t\t\t\n\t\t\t\t\ta {\n\t\t\t\t\t\tpadding: 7px 18px;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\t\n\t\t&.mkdf-tabs-simple {\n\t\t\t\n\t\t\t.mkdf-tabs-nav {\n\t\t\t\t\n\t\t\t\tli {\n\t\t\t\t\tmargin: 0 26px 0 0;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\t\n\t\t&.mkdf-tabs-vertical {\n\t\t\t\n\t\t\t.mkdf-tabs-nav {\n\t\t\t\twidth: 180px;\n\t\t\t}\n\t\t\t\n\t\t\t.mkdf-tab-container {\n\t\t\t\twidth: calc(100% - 180px);\n\t\t\t\tpadding: 0 0 0 30px;\n\t\t\t}\n\t\t}\n\t}\n}\n\n@include ipad-portrait {\n\t\n\t.mkdf-tabs {\n\t\t\n\t\t&.mkdf-tabs-standard {\n\t\t\t\n\t\t\t.mkdf-tabs-nav {\n\t\t\t\t\n\t\t\t\tli {\n\t\t\t\t\tdisplay: block;\n\t\t\t\t\tfloat: none;\n\t\t\t\t\t\n\t\t\t\t\ta {\n\t\t\t\t\t\twidth: 100%;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\t\n\t\t&.mkdf-tabs-boxed {\n\t\t\t\n\t\t\t.mkdf-tabs-nav {\n\t\t\t\t\n\t\t\t\tli {\n\t\t\t\t\tdisplay: block;\n\t\t\t\t\tfloat: none;\n\t\t\t\t\tmargin: 0 0 8px;\n\t\t\t\t\t\n\t\t\t\t\ta {\n\t\t\t\t\t\twidth: 100%;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\t\n\t\t&.mkdf-tabs-simple {\n\t\t\t\n\t\t\t.mkdf-tabs-nav {\n\t\t\t\t\n\t\t\t\tli {\n\t\t\t\t\tmargin: 0 20px 0 0;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\t\n\t\t&.mkdf-tabs-vertical {\n\t\t\t\n\t\t\t.mkdf-tabs-nav,\n\t\t\t.mkdf-tab-container {\n\t\t\t\tdisplay: inline-block;\n\t\t\t\twidth: 100%;\n\t\t\t\theight: auto;\n\t\t\t}\n\t\t\t\n\t\t\t.mkdf-tabs-nav {\n\t\t\t\tborder-right: 0;\n\t\t\t\t\n\t\t\t\tli {\n\t\t\t\t\tfloat: left;\n\t\t\t\t\tmargin: 0 20px 0 0;\n\t\t\t\t}\n\t\t\t}\n\t\t\t\n\t\t\t.mkdf-tab-container {\n\t\t\t\tpadding: 31px 0 0;\n\t\t\t}\n\t\t}\n\t\t.mkdf-tab-container {\n\n\t\t\timg {\n\t\t\t\tdisplay: none;\n\t\t\t}\n\t\t}\n\t}\n}\n\n@include phone-landscape {\n\t\n\t.mkdf-tabs {\n\t\t\n\t\t&.mkdf-tabs-simple {\n\t\t\t\n\t\t\t.mkdf-tabs-nav {\n\t\t\t\tpadding: 0 0 20px;\n\t\t\t\t\n\t\t\t\tli {\n\t\t\t\t\tdisplay: block;\n\t\t\t\t\tfloat: none;\n\t\t\t\t\tmargin: 0 0 20px;\n\t\t\t\t\t\n\t\t\t\t\ta {\n\t\t\t\t\t\tpadding: 0;\n\t\t\t\t\t\twidth: 100%;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\t\n\t\t&.mkdf-tabs-vertical {\n\n\t\t\tdisplay: block;\n\t\t\t\n\t\t\t.mkdf-tabs-nav {\n\t\t\t\t\n\t\t\t\tli {\n\t\t\t\t\tdisplay: block;\n\t\t\t\t\tfloat: none;\n\t\t\t\t\tmargin: 0 0 20px;\n\t\t\t\t\t\n\t\t\t\t\ta {\n\t\t\t\t\t\tpadding: 5px 10px 0 0;\n\t\t\t\t\t\twidth: 100%;\n\t\t\t\t\t\tdisplay: inline;\n\t\t\t\t\t\t\n\t\t\t\t\t\t\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n}\n\n@include phone-portrait {\n\n\t.mkdf-tabs {\n\n\t\t&.mkdf-tabs-standard {\n\n\t\t\t.mkdf-tabs-nav {\n\n\t\t\t\tli {\n\n\t\t\t\t\ta {\n\t\t\t\t\t\tpadding: 13px 21px 7px;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n}\n/* ==========================================================================\n   Tabs shortcode responsive style - end\n   ========================================================================== */", "@include ipad-portrait {\n\n\t.mkdf-video-button-holder {\n\n\t\t.mkdf-video-button-play {\n\t\t\tfont-size: 36px !important;\n\n\t\t\tspan {\n\t\t\t\tspan {\n\t\t\t\t\tpadding: 4px;\n\t\t\t\t\twidth: 53px;\n    \t\t\t\theight: 53px;\n    \t\t\t\tline-height: 53px;\n\n    \t\t\t\t.icon-basic-animation {\n\t\t\t\t\t    width: 53px;\n\t    \t\t\t\theight: 53px;\n\t    \t\t\t\tline-height: 53px;\n    \t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\t.mkdf-video-button-play-inner {\n\n\t\t\t\t&.top-right {\n\t\t\t\t\ttop: 9% !important;\n    \t\t\t\t\n\t\t\t\t}\n\n\t\t\t}\n\t\t}\n\n\t\t.mkdf-video-button-text {\n\t\t\tpadding: 30px 30px 20px;\n\n\t\t\t.mkdf-video-button-title {\n\t\t\t\tdisplay:block;\n\t\t\t\twidth:100%;\n\t\t\t}\n\n\t\t\tp {\n\t\t\t\twidth:100%;\n\t\t\t\tdisplay:block;\n\t\t\t}\n\t\t}\n\n\t\t.mkdf-video-button-text-shadow-holder {\n\t\t\tdisplay: none;\n\t\t}\n\t}\n}\n\n@include phone-portrait {\n\n\t.mkdf-video-button-holder {\n\n\t\t.mkdf-video-button-play {\n\t\t\tfont-size: 20px;\n\n\t\t\t.mkdf-video-button-play-inner {\n\n\t\t\t\t&.top-right {\n\t\t\t\t\ttop: 16% !important;\n    \t\t\t\t\n\t\t\t\t}\n\n\t\t\t}\n\t\t}\n\t}\n}\n\n@include smaller-phone-portrait {\n\n\t.mkdf-video-button-holder {\n\n\t\t.mkdf-video-button-play {\n\t\t\tfont-size: 24px !important;\n\n\t\t\tspan {\n\t\t\t\tspan {\n\t\t\t\t\t\n\t\t\t\t\twidth: 20px;\n    \t\t\t\theight: 20px;\n    \t\t\t\tline-height: 20px;\n\n    \t\t\t\t.icon-basic-animation {\n\t\t\t\t\t    width: 20px;\n\t    \t\t\t\theight: 20px;\n\t    \t\t\t\tline-height: 20px;\n    \t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\t.mkdf-video-button-play-inner {\n\n\t\t\t\t&.top-right {\n\t\t\t\t\ttop: 13% !important;\n\t\t\t\t\tright: 4px !important;\n    \t\t\t\t\n\t\t\t\t}\n\n\t\t\t}\n\t\t}\n\n\t\t\n\t}\n}\n", "@include laptop-landscape {\r\n\t.mkdf-workflow {\r\n\t\t.mkdf-workflow-item {\r\n\t\t\tmax-width: 100%;\r\n\r\n\t\t\t.mkdf-workflow-item-inner .mkdf-workflow-text {\r\n\t\t\t\tpadding: 0px 40px;\r\n\t\t\t}\r\n\r\n\t\t\t .mkdf-workflow-item-inner .mkdf-workflow-image {\r\n\t\t\t\t padding: 0px 40px;\r\n\t\t\t }\r\n\t\t}\r\n\t}\r\n}\r\n\r\n@include ipad-portrait {\r\n\r\n}\r\n\r\n@include phone-landscape {\r\n\r\n\t.mkdf-workflow {\r\n\r\n\t\t.main-line,\r\n\t\t.mkdf-workflow-item .line,\r\n\t\t.mkdf-workflow-item .mkdf-workflow-text .circle {\r\n\t\t\tdisplay: none !important;\r\n\t\t}\r\n\r\n\t\t.mkdf-workflow-item {\r\n\t\t\ttext-align: left;\r\n\r\n\t\t\t.mkdf-workflow-item-inner {\r\n\r\n\t\t\t\t.mkdf-workflow-image {\r\n\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\tmargin-bottom: 20px;\r\n\t\t\t\t\ttext-align: left;\r\n\t\t\t\t\tpadding: 0px 40px 0 0;\r\n\r\n\t\t\t\t\t&.right {\r\n\t\t\t\t\t\ttext-align: left !important;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t.mkdf-workflow-text {\r\n\t\t\t\t\twidth: 100% !important;\r\n\t\t\t\t\tpadding: 0px !important;\r\n\t\t\t\t\ttext-align: left !important;\r\n\t\t\t\t}\r\n\r\n\t\t\t}\r\n\r\n\r\n\t\t\t\r\n\t\t}\r\n\t\t.mkdf-workflow-item:nth-of-type(2n)  {\r\n\r\n\t\t\t.mkdf-workflow-item-inner {\r\n\t\t\t\tdisplay: block !important;\r\n\t\t\t}\r\n\r\n\t\t}\r\n\r\n\t}\r\n\t\r\n}"]}