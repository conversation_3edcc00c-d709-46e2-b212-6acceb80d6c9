@import "../../../../assets/css/lvca-lib";

.lvca-team-members {
  .lvca-team-member {
    .lvca-social-list {
      margin-top: 20px;
      .lvca-social-list-item {
        display: inline;
        margin: 0 15px 0 0;
        }
      }
    .lvca-team-member-details {
      @include lvca-body-font();
      }
    .lvca-team-member-text {
      .lvca-title {
        @include lvca-heading-style();
        }
      .lvca-team-member-position {
        font-size: 15px;
        line-height: 24px;
        font-style: italic;
        color: #888;
        margin-bottom: 10px;
        }
      }
    }

  }
/*-------- Style 1 ----------------*/

.lvca-team-members.lvca-style1 {
  .lvca-team-member-wrapper {
    float: left;
    padding: 10px;
    }
  .lvca-team-member {
    max-width: 320px;
    margin: 0 auto 40px;
    .lvca-image-wrapper {
      text-align: center;
      position: relative;
      img {
        max-width: 100%;
        margin: 0 auto 30px;
        border-radius: 50%;
        transition: all .3s ease-in-out 0s;
        }
      .lvca-social-list {
        position: absolute;
        height: 100%;
        width: 100%;
        top: 40%;
        z-index: 2;
        @include respond-to-max(767) {
          position: relative;
          top: 0;
          }
        i {
          font-size: 26px;
          color: #fff;
          opacity: 0;
          transition: all .3s ease-in-out 0s;
          &:hover {
            color: #ccc;
            }
          @include respond-to-max(767) {
            color: inherit;
            opacity: 1;
            &:hover {
              color: inherit;
              }
            }
          }
        }
      }
    &:hover {
      .lvca-image-wrapper {
        img {
          filter: brightness(50%);
          @include respond-to-max(767) {
            filter: brightness(80%);
            }
          }
        .lvca-social-list i {
          opacity: 1;
          }
        }
      }
    .lvca-team-member-text {
      text-align: center;
      max-width: 650px;
      .lvca-title {
        margin-bottom: 10px;
        }
      }
    .lvca-social-list {
      margin: 10px auto;
      }
    }
  }

/*-------- Style 2 ----------------*/

.lvca-team-members.lvca-style2 {
  position: relative;
  max-width: 960px;
  .lvca-team-member-wrapper {
    clear: both;
    margin-top: 100px;
    &:first-child {
      margin-top: 0;
      }
    /* Make that flip-flop possible */
    .lvca-image-wrapper {
      float: left;
      position: relative;
      img {
        max-width: 320px;
        border-radius: 50%;
        transition: all .3s ease-in-out 0s;
        }
      }
    .lvca-team-member-text {
      margin: 10px 0 0;
      vertical-align: middle;
      padding-top: 20px;
      .lvca-title {
        margin-bottom: 5px;
        }
      .lvca-team-member-details {
        margin: 10px 0 10px;
        }
      .lvca-social-list {
        i {
          font-size: 24px;
          }
        }
      }
    &:hover {
      .lvca-image-wrapper img {
        filter: brightness(80%);
        }
      }
    }
  .lvca-team-member-wrapper:nth-child(odd) {
    .lvca-image-wrapper {
      margin-right: 50px;
      }
    }
  .lvca-team-member-wrapper:nth-child(even) {
    .lvca-image-wrapper {
      float: right;
      margin-left: 50px;
      }
    .lvca-team-member-text {
      .lvca-title, .lvca-team-member-position, .lvca-team-member-details, .lvca-social-list {
        text-align: right;
        }
      }
    }
  }

@include respond-to-max(767) {
  .lvca-team-members.lvca-style2 {
    .lvca-team-member-wrapper {
      margin-top: 75px;
      }
    .lvca-team-member {
      .lvca-image-wrapper, .lvca-team-member-text {
        width: 100%;
        float: none;
        }
      .lvca-image-wrapper {
        text-align: center;
        img {
          margin: 0 auto 20px;
          }
        }
      .lvca-team-member-text {
        max-width: 400px;
        margin: 0 auto;
        padding-top: 0;
        .lvca-title, .lvca-team-member-position, .lvca-team-member-details, .lvca-social-list {
          text-align: center !important;
          }
        }
      }
    }
  }

.lvca-dark-bg .lvca-team-members {
  .lvca-team-member {
    .lvca-team-member-details {
      color: #909090;
      }
    .lvca-team-member-text {
      .lvca-title {
        color: #e5e5e5;
        }
      .lvca-team-member-position {
        color: #505050;
        }
      }
    }

  }