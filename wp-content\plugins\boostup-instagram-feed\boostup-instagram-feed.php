<?php
/*
Plugin Name: BoostUp Instagram Feed
Description: Plugin that adds Instagram feed functionality to our theme
Author: Mikado Themes
Version: 2.1
*/
define('BOOSTUP_INSTAGRAM_FEED_VERSION', '2.1');
define('BOOSTUP_INSTAGRAM_ABS_PATH', dirname(__FILE__));
define('BOOSTUP_INSTAGRAM_REL_PATH', dirname(plugin_basename(__FILE__ )));
define( 'BOOSTUP_INSTAGRAM_URL_PATH', plugin_dir_url( __FILE__ ) );
define( 'BOOSTUP_INSTAGRAM_ASSETS_PATH', BOOSTUP_INSTAGRAM_ABS_PATH . '/assets' );
define( 'BOOSTUP_INSTAGRAM_ASSETS_URL_PATH', BOOSTUP_INSTAGRAM_URL_PATH . 'assets' );
define( 'BOOSTUP_INSTAGRAM_SHORTCODES_PATH', BO<PERSON>TUP_INSTAGRAM_ABS_PATH . '/shortcodes' );
define( 'BOOSTUP_INSTAGRAM_SHORTCODES_URL_PATH', BOOSTUP_INSTAGRAM_URL_PATH . 'shortcodes' );

include_once 'load.php';

if ( ! function_exists( 'boostup_instagram_theme_installed' ) ) {
    /**
     * Checks whether theme is installed or not
     * @return bool
     */
    function boostup_instagram_theme_installed() {
        return defined( 'MIKADO_ROOT' );
    }
}

if ( ! function_exists( 'boostup_instagram_feed_text_domain' ) ) {
	/**
	 * Loads plugin text domain so it can be used in translation
	 */
	function boostup_instagram_feed_text_domain() {
		load_plugin_textdomain( 'boostup-instagram-feed', false, BOOSTUP_INSTAGRAM_REL_PATH . '/languages' );
	}
	
	add_action( 'plugins_loaded', 'boostup_instagram_feed_text_domain' );
}