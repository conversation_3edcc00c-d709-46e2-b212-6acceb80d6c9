/* ==========================================================================
   Global partials
   ========================================================================== */
@-webkit-keyframes animate-btn-line {
  0% {
    -webkit-transform-origin: 0 50%;
    transform-origin: 0 50%;
    -webkit-transform: scaleX(0);
    transform: scaleX(0);
  }
  100% {
    -webkit-transform-origin: 0 50%;
    transform-origin: 0 50%;
    -webkit-transform: scaleX(1);
    transform: scaleX(1);
  }
}
@keyframes animate-btn-line {
  0% {
    -webkit-transform-origin: 0 50%;
    transform-origin: 0 50%;
    -webkit-transform: scaleX(0);
    transform: scaleX(0);
  }
  100% {
    -webkit-transform-origin: 0 50%;
    transform-origin: 0 50%;
    -webkit-transform: scaleX(1);
    transform: scaleX(1);
  }
}

@-webkit-keyframes mkdfPulsebig {
  0% {
    -webkit-box-shadow: 0 0 0 0 currentColor;
    box-shadow: 0 0 0 0 currentColor;
  }
  70% {
    -webkit-box-shadow: 0 0 0 20px rgba(234, 61, 86, 0);
    box-shadow: 0 0 0 20px rgba(234, 61, 86, 0);
  }
  100% {
    -webkit-box-shadow: 0 0 0 0 rgba(234, 61, 86, 0);
    box-shadow: 0 0 0 0 rgba(234, 61, 86, 0);
  }
}

@keyframes mkdfPulsebig {
  0% {
    -webkit-box-shadow: 0 0 0 0 currentColor;
    box-shadow: 0 0 0 0 currentColor;
  }
  70% {
    -webkit-box-shadow: 0 0 0 20px rgba(234, 61, 86, 0);
    box-shadow: 0 0 0 20px rgba(234, 61, 86, 0);
  }
  100% {
    -webkit-box-shadow: 0 0 0 0 rgba(234, 61, 86, 0);
    box-shadow: 0 0 0 0 rgba(234, 61, 86, 0);
  }
}

@-webkit-keyframes mkdfPulsesmall {
  0% {
    -webkit-box-shadow: 0 0 0 0 currentColor;
  }
  70% {
    -webkit-box-shadow: 0 0 0 10px rgba(234, 61, 86, 0.15);
  }
  100% {
    -webkit-box-shadow: 0 0 0 0 rgba(234, 61, 86, 0);
  }
}

@keyframes mkdfPulsesmall {
  0% {
    -webkit-box-shadow: 0 0 0 0 currentColor;
    box-shadow: 0 0 0 0 currentColor;
  }
  70% {
    -webkit-box-shadow: 0 0 0 10px rgba(234, 61, 86, 0);
    box-shadow: 0 0 0 10px rgba(234, 61, 86, 0);
  }
  100% {
    -webkit-box-shadow: 0 0 0 0 rgba(234, 61, 86, 0);
    box-shadow: 0 0 0 0 rgba(234, 61, 86, 0);
  }
}

@-webkit-keyframes mkdfPulsesmallfirst {
  0% {
    -webkit-box-shadow: 0 0 0 0 #ea3d56;
  }
  70% {
    -webkit-box-shadow: 0 0 0 10px rgba(234, 61, 86, 0.15);
  }
  100% {
    -webkit-box-shadow: 0 0 0 0 rgba(234, 61, 86, 0);
  }
}

@keyframes mkdfPulsesmallfirst {
  0% {
    -webkit-box-shadow: 0 0 0 0 #ea3d56;
    box-shadow: 0 0 0 0 #ea3d56;
  }
  70% {
    -webkit-box-shadow: 0 0 0 10px rgba(234, 61, 86, 0);
    box-shadow: 0 0 0 10px rgba(234, 61, 86, 0);
  }
  100% {
    -webkit-box-shadow: 0 0 0 0 rgba(234, 61, 86, 0);
    box-shadow: 0 0 0 0 rgba(234, 61, 86, 0);
  }
}

/* ==========================================================================
   Shortcodes responsive styles
   ========================================================================== */

/*# sourceMappingURL=../../../../plugins/boostup-twitter-feed/assets/css/shortcodes-map-responsive.css.map */
