/* ==========================================================================
   Instagram widget style - begin
   ========================================================================== */

aside.mkdf-sidebar,
.wpb_widgetised_column {
	
	.widget {
		
		&.widget_mkdf_instagram_widget {
			
			.mkdf-widget-title {
				margin: 0 0 25px;
			}
		}
	}
}

.mkdf-instagram-feed {
	list-style: none;
	padding: 0;
	margin: 0;
	
	li {
		float: left;
		box-sizing: border-box;
		border: none !important;
		
		a {
			position: relative;
			display: block;
			overflow: hidden;
			@include mkdfImageOverlayHoverStyle();
			
			.mkdf-instagram-icon {
				position: absolute;
				top: 50%;
				left: 50%;
				color: $default-heading-color;
				opacity: 0;
				z-index: 1;
				@include mkdfTransition(opacity .2s ease-in-out);
				@include mkdfTransform(translate(-50%, -50%));
			}
			
			&:hover {
				
				.mkdf-instagram-icon {
					opacity: 1;
				}
			}
		}
		
		img {
			width: 100%;
			display: block;
		}
	}
	
	&.mkdf-instagram-gallery {
		$gallery_space_label: ('no', 'tiny', 'small', 'normal');
		$gallery_space_width: (0, 5, 10, 15);
		
		@for $i from 0 to length($gallery_space_label) {
			&.mkdf-#{nth($gallery_space_label,$i+1)}-space {
				$column_width: nth($gallery_space_width, $i+1);
				$column_width_margin: $column_width * 2;
				
				@if ($column_width == 0) {
					margin: 0;
				} @else {
					margin: 0 -#{$column_width}px -#{$column_width_margin}px;
				}
				
				li {
					padding: 0 #{$column_width}px;
					margin: 0 0 #{$column_width_margin}px;
				}
			}
		}
		
		$gallery_columns: ('2', '3', '4', '6', '9');
		@for $i from 0 to length($gallery_columns) {
			&.mkdf-col-#{nth($gallery_columns, $i+1)} {
				$column_width: 100%/($i+2);
				
				@if ($i == 3) {
					$column_width: 100%/6;
				} @else if ($i == 4) {
					$column_width: 100%/9;
				}
				
				li {
					width: $column_width;
				}
			}
		}
	}
	
	&.mkdf-instagram-carousel {
		
		li {
			position: relative;
			width: 100%;
			margin: 0;
			padding: 0;
			
			a {
				position: relative;
				display: block;
				height: 100%;
			}
		}
	}
}

/* ==========================================================================
   Instagram widget style - end
   ========================================================================== */