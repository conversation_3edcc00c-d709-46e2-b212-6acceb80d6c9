<?php

if ( ! function_exists( 'boostup_core_map_testimonials_meta' ) ) {
	function boostup_core_map_testimonials_meta() {
		$testimonial_meta_box = boostup_mikado_create_meta_box(
			array(
				'scope' => array( 'testimonials' ),
				'title' => esc_html__( 'Testimonial', 'boostup-core' ),
				'name'  => 'testimonial_meta'
			)
		);
		
		boostup_mikado_create_meta_box_field(
			array(
				'name'        => 'mkdf_testimonial_title',
				'type'        => 'text',
				'label'       => esc_html__( 'Title', 'boostup-core' ),
				'description' => esc_html__( 'Enter testimonial title', 'boostup-core' ),
				'parent'      => $testimonial_meta_box,
			)
		);
		
		boostup_mikado_create_meta_box_field(
			array(
				'name'        => 'mkdf_testimonial_text',
				'type'        => 'text',
				'label'       => esc_html__( 'Text', 'boostup-core' ),
				'description' => esc_html__( 'Enter testimonial text', 'boostup-core' ),
				'parent'      => $testimonial_meta_box,
			)
		);
		
		boostup_mikado_create_meta_box_field(
			array(
				'name'        => 'mkdf_testimonial_author',
				'type'        => 'text',
				'label'       => esc_html__( 'Author', 'boostup-core' ),
				'description' => esc_html__( 'Enter author name', 'boostup-core' ),
				'parent'      => $testimonial_meta_box,
			)
		);
		
		boostup_mikado_create_meta_box_field(
			array(
				'name'        => 'mkdf_testimonial_author_position',
				'type'        => 'text',
				'label'       => esc_html__( 'Author Position', 'boostup-core' ),
				'description' => esc_html__( 'Enter author job position', 'boostup-core' ),
				'parent'      => $testimonial_meta_box,
			)
		);
	}
	
	add_action( 'boostup_mikado_action_meta_boxes_map', 'boostup_core_map_testimonials_meta', 95 );
}