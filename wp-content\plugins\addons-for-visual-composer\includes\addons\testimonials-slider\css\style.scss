@import "../../../../assets/css/lvca-lib";

.lvca-testimonials-slider {
  position: relative;
  &.lvca-container {
    max-width: 900px;
    margin: 0 auto;
  }
  .lvca-testimonial-text {
    text-align: center;
    max-width: 750px;
    margin: 0 auto 40px;
    font-size: 18px;
    line-height: 32px;
    font-style: italic;
    color: #666;
    .lvca-dark-bg & {
      color: #ccc;
    }
    i {
      color: #ccc;
      font-size: 32px;
      display: block;
      margin-bottom: 35px;
      background: none;
      width: auto;
      height: auto;
      .lvca-dark-bg & {
        color: #ddd;
      }
    }
    }
  .lvca-testimonial-user {
    display: table;
    margin: 0 auto;
    .lvca-image-wrapper {
      display: table-cell;
      img {
        max-width: 64px;
        border-radius: 50%;
        margin-right: 15px;
        }
      }
    .lvca-text {
      display: table-cell;
      vertical-align: middle;
      color: #888;
      .lvca-dark-bg & {
        color: #909090;
      }
      .lvca-author-name {
        @include lvca-heading-style();
        font-size: 15px;
        line-height: 24px;
        margin-bottom: 5px;
        .lvca-dark-bg & {
          color: #e5e5e5;
        }        
        }
      }
    }
  }