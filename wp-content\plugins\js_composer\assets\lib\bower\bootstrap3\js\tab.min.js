/*!
 * WPBakery Page Builder v6.0.0 (https://wpbakery.com)
 * Copyright 2011-2023 <PERSON>, WPBakery
 * License: Commercial. More details: http://go.wpbakery.com/licensing
 */

// jscs:disable
// jshint ignore: start

!function($){"use strict";function Tab(element){this.element=$(element)}function Plugin(option){return this.each(function(){var $this=$(this),data=$this.data("bs.tab");data||$this.data("bs.tab",data=new Tab(this)),"string"==typeof option&&data[option]()})}Tab.VERSION="3.1.1",Tab.prototype.show=function(){var previous,e,$this=this.element,$ul=$this.closest("ul:not(.dropdown-menu)"),selector=(selector=$this.data("target"))||(selector=$this.attr("href"))&&selector.replace(/.*(?=#[^\s]*$)/,"");$this.parent("li").hasClass("active")||(previous=$ul.find(".active:last a")[0],e=$.Event("show.bs.tab",{relatedTarget:previous}),$this.trigger(e),e.isDefaultPrevented())||(e=$(selector),this.activate($this.closest("li"),$ul),this.activate(e,e.parent(),function(){$this.trigger({type:"shown.bs.tab",relatedTarget:previous})}))},Tab.prototype.activate=function(element,container,callback){var $active=container.find("> .active"),transition=callback&&$.support.transition&&$active.hasClass("fade");function next(){$active.removeClass("active").find("> .dropdown-menu > .active").removeClass("active"),element.addClass("active"),transition?(element[0].offsetWidth,element.addClass("in")):element.removeClass("fade"),element.parent(".dropdown-menu")&&element.closest("li.dropdown").addClass("active"),callback&&callback()}transition?$active.one($.support.transition.end,next).emulateTransitionEnd(150):next(),$active.removeClass("in")};var old=$.fn.tab;$.fn.tab=Plugin,$.fn.tab.Constructor=Tab,$.fn.tab.noConflict=function(){return $.fn.tab=old,this},$(document).on("click.bs.tab.data-api",'[data-toggle="tab"], [data-toggle="pill"]',function(e){e.preventDefault(),Plugin.call($(this),"show")})}(jQuery);