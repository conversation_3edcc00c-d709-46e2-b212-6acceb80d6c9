/* ==========================================================================
   Banner shortcode style - begin
   ========================================================================== */

.mkdf-banner-holder {
    @include mkdfRelativeHolderLayout();
	
	.touch & {
		cursor: pointer;
	}
	
	&.mkdf-visible-on-hover {
		
		&:hover {
			
			.mkdf-banner-text-holder {
				opacity: 1;
			}
		}
		
		.mkdf-banner-text-holder {
			opacity: 0;
			@include mkdfTransition(opacity .2s ease-out);
		}
	}
	
	&.mkdf-disabled {
		
		.mkdf-banner-text-holder {
			display: none;
		}
	}
	
	&.mkdf-banner-info-centered {
		
		.mkdf-banner-text-holder {
			padding: 70px 20px;
			text-align: center;
		}
	}
	
	.mkdf-banner-image {
		@include mkdfRelativeHolderLayout();
		
		img {
			display: block;
		}
	}
	
	.mkdf-banner-text-holder {
		@include mkdfAbsoluteHolderLayout();
		padding: 35px;
		background-color: rgba($default-heading-color, .4);
		box-sizing: border-box;
		
		@include ipad-portrait {
			padding: 25px;
		}
	}
	
	.mkdf-banner-text-outer {
		@include mkdfTableLayout();
	}
	
	.mkdf-banner-text-inner {
		position: relative;
		display: table-cell;
		height: 100%;
		width: 100%;
		vertical-align: bottom;
	}
	
	.mkdf-banner-subtitle {
		margin: 0 0 4px;
		color: #fff;
	}
	
	.mkdf-banner-title {
		margin: 0;
		color: #fff;
		
		.mkdf-banner-title-light {
			font-weight: 300;
		}
	}
	
	.mkdf-banner-link-text {
		position: relative;
		display: inline-block;
		vertical-align: top;
		margin: 11px 0 0;
		color: #fff;
		line-height: 1em;
		z-index: 2;
		@include mkdfTransform(translateZ(0));
		
		&:hover {
			
			.mkdf-banner-link-hover {
				width: 100%;
			}
		}
		
		.mkdf-banner-link-original {
			position: relative;
			display: inline-block;
			vertical-align: top;
			width: 100%;
			
			span {
				color: inherit;
			}
		}
		
		.mkdf-banner-link-hover {
			position: absolute;
			top: 0;
			left: 0;
			width: 0.1%;
			height: 100%;
			display: inline-block;
			vertical-align: top;
			white-space: nowrap;
			overflow: hidden;
			@include mkdfTransition(width .4s ease-in-out);
			
			span {
				color: $first-main-color;
			}
		}
		
		.mkdf-banner-link-icon,
		.mkdf-banner-link-label {
			position: relative;
			display: inline-block;
			vertical-align: top;
		}
		
		.mkdf-banner-link-icon {
			margin: 0 2px 0 0;
			font-size: 15px;
		}
		
		.mkdf-banner-link-label {
			font-size: 14px;
			line-height: inherit;
		}
	}

	.mkdf-banner-link {
		@include mkdfAbsoluteHolderLayout();
		z-index: 1;
	}
}
/* ==========================================================================
   Banner shortcode style - end
   ========================================================================== */