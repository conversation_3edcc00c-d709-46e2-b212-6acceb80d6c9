/* ==========================================================================
   Team shortcode style - begin
   ========================================================================== */

.mkdf-team-holder {
    @include mkdfRelativeHolderLayout();
	
	&.mkdf-team-info-on-image {
		
		.touch & {
			cursor: pointer;
		}

		&:hover {
			
			.mkdf-team-social-wrapper {
				opacity: 1;
			}

			.mkdf-team-social-inner {
				@include mkdfTransitionTransform(.45s cubic-bezier(0.64, 0.01, 0.15, 1.16), opacity .5s);
				@include mkdfTransform(translate3d(0,0,0));
			}
		}
		
		.mkdf-team-social-holder {
			margin: 7px 0 0;
		}
	}

	.mkdf-team-inner {
		text-align: center;
		border: 1px solid rgba(225,225,225,.2);
		@include mkdfTransition(all .3s ease-out);

		&:hover {
			border: 1px solid rgba(225,225,225,.4);
		}
	}

    .mkdf-team-image {
        @include mkdfRelativeHolderLayout();
		width: auto;
		margin-top: 38px;

        img {
            display: block;
			width: 200px;
			height: 200px;
			object-fit: cover;
			border-radius: 50%;
			border: 3px solid #fff;
        }
    }
	
	.mkdf-team-info {
		@include mkdfRelativeHolderLayout();
		margin: 7px 0 57px;
		text-align: center;
	}

    .mkdf-team-name {
        margin: 0;
    }

    .mkdf-team-position {
        margin: 12px 0 0;
		font-size: 14px;
		opacity: 0.4;
		text-transform: uppercase;
		letter-spacing: 0.01em;
		font-weight: 700;
		line-height: 26px;
    }
	
	.mkdf-team-text {
		margin: 10px 0 0;
	}
	
	.mkdf-team-social-wrapper {
		@include mkdfAbsoluteHolderLayout();
		background-color: rgba(#fff, .85);
		z-index: 1;
		opacity: 0;
		@include mkdfTransform(translateZ(0));
		@include mkdfTransition(opacity .3s);
	}
	
	.mkdf-team-social-outer {
		@include mkdfTableLayout();
	}
	
	.mkdf-team-social-inner {
		position: relative;
		display: table-cell;
		height: 100%;
		width: 100%;
		padding: 20px 40px 33px;
		vertical-align: bottom;
		@include mkdfTransitionTransform(.2s ease);
		@include mkdfTransform(translate3d(0,40px,0));
	}
	
	.mkdf-team-social-holder {
		@include mkdfRelativeHolderLayout();
		
		.mkdf-team-icon {
			font-size: 14px;
			margin: 0;

			&:first-child {
				font-size: 24px;
				position: absolute;
				right: 5px;
				bottom: 24px;
			}

			&:nth-child(2) {
				font-size: 16px;
				position: absolute;
				right: -9px;
				bottom: 69px;
			}

			&:nth-child(3) {
				font-size: 14px;
				position: absolute;
				right: -13px;
				bottom: 103px;
			}

			&:nth-child(4) {
				font-size: 12px;
				position: absolute;
				right: -8px;
				bottom: 132px;
			}

			&:nth-child(5) {
				font-size: 10px;
				position: absolute;
				right: 4px;
				bottom: 155px;
			}
			
			.mkdf-icon-element {
				font-size: inherit;
				@include mkdfTransition(none);
				color: #fff;
			}
		}
	}
}
/* ==========================================================================
   Team shortcode style - end
   ========================================================================== */