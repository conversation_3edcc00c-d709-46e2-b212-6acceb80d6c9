{"version": 3, "sources": ["shortcodes-map-responsive.scss", "shortcodes-map-responsive.css", "../../../../../themes/boostup/assets/css/scss/_mixins.scss", "../../../../../themes/boostup/assets/css/scss/_variables.scss"], "names": [], "mappings": "AAAA;;+ECE+E;ACuf9E;EACG;IAxUA,+BAyUsC;IAvUtC,uBAuUsC;IArVtC,4BAsVoC;IApVpC,oBAoVoC;EDjftC;ECmfE;IA5UA,+BA6UsC;IA3UtC,uBA2UsC;IAzVtC,4BA0VoC;IAxVpC,oBAwVoC;ED7etC;AACF;ACqeC;EACG;IAxUA,+BAyUsC;IAvUtC,uBAuUsC;IArVtC,4BAsVoC;IApVpC,oBAoVoC;EDjftC;ECmfE;IA5UA,+BA6UsC;IA3UtC,uBA2UsC;IAzVtC,4BA0VoC;IAxVpC,oBAwVoC;ED7etC;AACF;;AC+eA;EACG;IAEC,wCAAgC;IAAhC,gCAAgC;ED5elC;EC8eA;IAEI,mDCteoB;IDsepB,2CCteoB;EFNxB;EC8eA;IAEI,gDC1eoB;ID0epB,wCC1eoB;EFFxB;AACF;;AC8eA;EACE;IAEE,wCAAgC;IAAhC,gCAAgC;ED3elC;EC6eA;IAEI,mDCpfoB;IDofpB,2CCpfoB;EFSxB;EC6eA;IAEI,gDCxfoB;IDwfpB,wCCxfoB;EFaxB;AACF;;AC6eA;EACE;IACE,wCAAwC;ED1e1C;EC4eA;IACI,sDChgBoB;EFsBxB;EC4eA;IACI,gDCngBoB;EFyBxB;AACF;;AC4eA;EACE;IAEE,wCAAgC;IAAhC,gCAAgC;EDzelC;EC2eA;IAEI,mDC7gBoB;ID6gBpB,2CC7gBoB;EFoCxB;EC2eA;IAEI,gDCjhBoB;IDihBpB,wCCjhBoB;EFwCxB;AACF;;AC2eA;EACE;IACE,mCCthBsB;EF8CxB;EC0eA;IACI,sDCzhBoB;EFiDxB;EC0eA;IACI,gDC5hBoB;EFoDxB;AACF;;AC0eA;EACE;IAEE,mCCliBsB;IDkiBtB,2BCliBsB;EF2DxB;ECyeA;IAEI,mDCtiBoB;IDsiBpB,2CCtiBoB;EF+DxB;ECyeA;IAEI,gDC1iBoB;ID0iBpB,wCC1iBoB;EFmExB;AACF;;ADlGA;;+ECsG+E", "file": "../../../../boostup-twitter-feed/assets/css/scss/shortcodes-map-responsive.css", "sourcesContent": ["/* ==========================================================================\n   Global partials\n   ========================================================================== */\n@import '../../../../../themes/boostup/assets/css/scss/variables';\n@import '../../../../../themes/boostup/assets/css/scss/mixins';\n\n/* ==========================================================================\n   Shortcodes responsive styles\n   ========================================================================== */\n", "/* ==========================================================================\n   Global partials\n   ========================================================================== */\n@keyframes animate-btn-line {\n  0% {\n    -webkit-transform-origin: 0 50%;\n    -moz-transform-origin: 0 50%;\n    transform-origin: 0 50%;\n    -webkit-transform: scaleX(0);\n    -moz-transform: scaleX(0);\n    transform: scaleX(0);\n  }\n  100% {\n    -webkit-transform-origin: 0 50%;\n    -moz-transform-origin: 0 50%;\n    transform-origin: 0 50%;\n    -webkit-transform: scaleX(1);\n    -moz-transform: scaleX(1);\n    transform: scaleX(1);\n  }\n}\n\n@-webkit-keyframes mkdfPulsebig {\n  0% {\n    -moz-box-shadow: 0 0 0 0 currentColor;\n    box-shadow: 0 0 0 0 currentColor;\n  }\n  70% {\n    -moz-box-shadow: 0 0 0 20px rgba(234, 61, 86, 0.3);\n    box-shadow: 0 0 0 20px rgba(234, 61, 86, 0);\n  }\n  100% {\n    -moz-box-shadow: 0 0 0 0 rgba(234, 61, 86, 0);\n    box-shadow: 0 0 0 0 rgba(234, 61, 86, 0);\n  }\n}\n\n@keyframes mkdfPulsebig {\n  0% {\n    -moz-box-shadow: 0 0 0 0 currentColor;\n    box-shadow: 0 0 0 0 currentColor;\n  }\n  70% {\n    -moz-box-shadow: 0 0 0 20px rgba(234, 61, 86, 0.3);\n    box-shadow: 0 0 0 20px rgba(234, 61, 86, 0);\n  }\n  100% {\n    -moz-box-shadow: 0 0 0 0 rgba(234, 61, 86, 0);\n    box-shadow: 0 0 0 0 rgba(234, 61, 86, 0);\n  }\n}\n\n@-webkit-keyframes mkdfPulsesmall {\n  0% {\n    -webkit-box-shadow: 0 0 0 0 currentColor;\n  }\n  70% {\n    -webkit-box-shadow: 0 0 0 10px rgba(234, 61, 86, 0.15);\n  }\n  100% {\n    -webkit-box-shadow: 0 0 0 0 rgba(234, 61, 86, 0);\n  }\n}\n\n@keyframes mkdfPulsesmall {\n  0% {\n    -moz-box-shadow: 0 0 0 0 currentColor;\n    box-shadow: 0 0 0 0 currentColor;\n  }\n  70% {\n    -moz-box-shadow: 0 0 0 10px rgba(234, 61, 86, 0.15);\n    box-shadow: 0 0 0 10px rgba(234, 61, 86, 0);\n  }\n  100% {\n    -moz-box-shadow: 0 0 0 0 rgba(234, 61, 86, 0);\n    box-shadow: 0 0 0 0 rgba(234, 61, 86, 0);\n  }\n}\n\n@-webkit-keyframes mkdfPulsesmallfirst {\n  0% {\n    -webkit-box-shadow: 0 0 0 0 #ea3d56;\n  }\n  70% {\n    -webkit-box-shadow: 0 0 0 10px rgba(234, 61, 86, 0.15);\n  }\n  100% {\n    -webkit-box-shadow: 0 0 0 0 rgba(234, 61, 86, 0);\n  }\n}\n\n@keyframes mkdfPulsesmallfirst {\n  0% {\n    -moz-box-shadow: 0 0 0 0 #ea3d56;\n    box-shadow: 0 0 0 0 #ea3d56;\n  }\n  70% {\n    -moz-box-shadow: 0 0 0 10px rgba(234, 61, 86, 0.15);\n    box-shadow: 0 0 0 10px rgba(234, 61, 86, 0);\n  }\n  100% {\n    -moz-box-shadow: 0 0 0 0 rgba(234, 61, 86, 0);\n    box-shadow: 0 0 0 0 rgba(234, 61, 86, 0);\n  }\n}\n\n/* ==========================================================================\n   Shortcodes responsive styles\n   ========================================================================== */\n", "////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////\n\n// layout mixins - start\n\n@mixin mkdfRelativeHolderLayout($vertical-align: middle) {\n    position: relative;\n    display: inline-block;\n    width: 100%;\n    vertical-align: $vertical-align;\n}\n\n@mixin mkdfAbsoluteHolderLayout() {\n    position: absolute;\n    display: block;\n    width: 100%;\n    height: 100%;\n    top: 0;\n    left: 0;\n}\n\n@mixin mkdfFlexBoxLayout($position: null, $align-items: null, $justify-content: null) {\n    @if ($position) {\n        position: $position;\n    }\n    \n    @include mkdfFlexLayout();\n    \n    @if ($align-items) {\n        @include mkdfFlexAlignItems($align-items);\n    }\n    \n    @if ($justify-content) {\n        @include mkdfFlexJustifyContent($justify-content);\n    }\n}\n\n@mixin mkdfFlexContainer($align-items: null, $justify-content: null, $flex-direction: null, $flex-wrap: null, $align-content: null) {\n\t@include mkdfFlexBoxLayout(null, $align-items, $justify-content);\n\t\n\t@if ($flex-direction) {\n\t\tflex-direction: $flex-direction;\n\t}\n\t\n\t@if ($flex-wrap) {\n\t\tflex-wrap: $flex-wrap;\n\t}\n\t\n\t@if ($align-content) {\n\t\talign-content: $align-content;\n\t}\n}\n\n@mixin mkdfFlexLayout() {\n    display: -webkit-box;\n    display: -webkit-flex;\n    display: -ms-flexbox;\n    display: flex;\n}\n\n@mixin mkdfInlineFlexLayout() {\n    display: -webkit-inline-flex;\n    display: -ms-inline-flexbox;\n    display: inline-flex;\n}\n\n@mixin mkdfFlexItem($order: 0, $flex-grow: 0, $flex-shrink: 1, $flex-basis: auto) {\n    order: $order;\n    flex-grow: $flex-grow;\n    flex-shrink: $flex-shrink;\n    flex-basis: $flex-basis;\n}\n\n@mixin mkdfFlexAlignItems($align-items) {\n    $older-align-items: $align-items;\n    \n    @if ($align-items == 'flex-start') {\n        $older-align-items: start;\n    } @else if ($align-items == 'flex-end') {\n        $older-align-items: end;\n    }\n    \n    -webkit-box-align: $older-align-items;\n    -webkit-align-items: $align-items;\n    -ms-flex-align: $older-align-items;\n    align-items: $align-items;\n}\n@mixin mkdfDefaultTransition($transition-param...) {\n    $transitions_each: ('-webkit-transition', '-moz-transition', 'transition');\n    $string: '';\n\n    @each $var in $transition-param{\n        @if $string == '' {\n            $string : $var $default-transition-duration $default-easing-function\n        } @else {\n            $string : $string, $var $default-transition-duration $default-easing-function\n        }\n    }\n\n\n    @each $transition in $transitions_each{\n        #{$transition}: $string;\n    }\n}\n@mixin mkdfFlexJustifyContent($justify-content) {\n    $older-justify-content: $justify-content;\n    \n    @if ($justify-content == 'flex-start') {\n        $older-justify-content: start;\n    } @else if ($justify-content == 'flex-end') {\n        $older-justify-content: end;\n    } @else if ($justify-content == 'space-between') {\n        $older-justify-content: justify;\n    }\n    \n    -webkit-box-pack: $older-justify-content;\n    -webkit-justify-content: $justify-content;\n    -ms-flex-pack: $older-justify-content;\n    justify-content: $justify-content;\n}\n\n@mixin mkdfTableLayout() {\n    position: relative;\n    display: table;\n    table-layout: fixed;\n    height: 100%;\n    width: 100%;\n}\n\n@mixin mkdfTableCellLayout($vertical-align: middle) {\n    position: relative;\n    display: table-cell;\n    height: 100%;\n    width: 100%;\n    vertical-align: $vertical-align;\n}\n\n@mixin mkdfTypographyLayout($important : null) {\n    color: inherit $important;\n    font-family: inherit $important;\n    font-size: inherit $important;\n    font-weight: inherit $important;\n    font-style: inherit $important;\n    line-height: inherit $important;\n    letter-spacing: inherit $important;\n    text-transform: inherit $important;\n}\n\n// layout mixins - end\n\n////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////\n\n// transition mixins - start\n\n@mixin mkdfTransition($transition-param...) {\n    -webkit-transition: $transition-param;\n    -moz-transition: $transition-param;\n    transition: $transition-param;\n}\n\n@mixin mkdfTransitionTransform($transition-param...) {\n    -webkit-transition: -webkit-transform $transition-param;\n    -moz-transition: -moz-transform $transition-param;\n    transition: transform $transition-param;\n}\n\n@mixin mkdfTransform($transform-param...) {\n    -webkit-transform: $transform-param;\n    -moz-transform: $transform-param;\n    transform: $transform-param;\n}\n\n@mixin mkdfAnimation($animation-param...) {\n    -webkit-animation: $animation-param;\n    -moz-animation: $animation-param;\n    animation: $animation-param;\n}\n\n@mixin mkdfTransformOrigin($animation-param...) {\n    -webkit-transform-origin: $animation-param;\n    -moz-transform-origin: $animation-param;\n    transform-origin: $animation-param;\n}\n@mixin mkdfPulse {\n\n}\n\n// transition mixins - end\n\n////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////\n\n// checkbox mixins - start\n\n$checkbox-size: 15px;\n$checkbox-border-width: 1px;\n\n%checkbox-style {\n    position: relative;\n    margin: 8px 0;\n    line-height: 1;\n\n    input[type=checkbox] {\n        width: $checkbox-size;\n        height: $checkbox-size;\n        max-height: $checkbox-size;\n        position: relative;\n        display: inline-block;\n        vertical-align: top;\n        top: 0;\n        left: 0;\n        margin: 0;\n    }\n\n    input[type=checkbox] + label {\n        position: absolute;\n        top: 0;\n        left: 0;\n        display: inline-block;\n        line-height: 0;\n        pointer-events: none;\n        cursor: pointer;\n    }\n\n    input[type=checkbox] + label span.mkdf-label-text {\n        display: inline-block;\n        padding-left: 10px;\n        line-height: $checkbox-size;\n        color: $default-heading-color;\n    }\n\n    input[type=checkbox] + label .mkdf-label-view {\n        display: inline-block;\n        vertical-align: top;\n        width: $checkbox-size;\n        height: $checkbox-size;\n        background-color: $default-background-color;\n        border: $checkbox-border-width solid $default-border-color;\n        border-radius: 2px;\n        cursor: pointer;\n        box-sizing: border-box;\n\n        &:hover {\n            cursor: pointer;\n        }\n    }\n\n    input[type=checkbox] + label .mkdf-label-view:after {\n        content: '';\n        position: absolute;\n        top: ($checkbox-size - ($checkbox-size / 2 - $checkbox-border-width)) / 2;\n        left: ($checkbox-size - ($checkbox-size / 2 - $checkbox-border-width)) / 2;\n        width: $checkbox-size / 2 - $checkbox-border-width;\n        height: $checkbox-size / 2 - $checkbox-border-width;\n        background-color: $first-main-color;\n        opacity: 0;\n        @include mkdfTransition(opacity 0.3s ease-in-out);\n    }\n\n    input[type=checkbox]:checked + label .mkdf-label-view:after {\n        opacity: 1;\n    }\n}\n\n// checkbox mixins - end\n\n////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////\n\n// common mixins - start\n\n@mixin mkdfBckImageStyle() {\n    background-size: cover;\n    background-repeat: no-repeat;\n    background-position: center center;\n}\n\n@mixin mkdfImageOverlayHoverStyle($with-hover: true) {\n    \n    @if ($with-hover) {\n        \n        &:hover {\n            \n            &:after {\n                opacity: 1;\n            }\n        }\n\n        &:after {\n            @include mkdfAbsoluteHolderLayout();\n            content: '';\n            background-color: rgba($first-main-color, .4);\n            opacity: 0;\n            @include mkdfTransition(opacity .2s ease-in-out);\n        }\n\n    } @else {\n        @include mkdfAbsoluteHolderLayout();\n        content: '';\n        background-color: rgba($first-main-color, .4);\n        opacity: 0;\n        @include mkdfTransition(opacity .2s ease-in-out);\n    }\n}\n\n@mixin mkdfStandardPaginationStyle($list_type: null) {\n    @include mkdfRelativeHolderLayout(top);\n    margin: 40px 0 0;\n    clear: both;\n\n    ul {\n        @include mkdfRelativeHolderLayout(top);\n        padding: 0;\n        margin: 0;\n        list-style: none;\n        text-align: center;\n\n        li {\n            position: relative;\n            display: inline-block;\n            vertical-align: top;\n            margin: 0 12px;\n\n            a {\n                position: relative;\n                display: inline-block;\n                vertical-align: top;\n                margin: 0;\n                padding: 0;\n            }\n\n            &.mkdf-pag-active {\n                \n                a {\n                    color: $first-main-color;\n                }\n            }\n\n            &.mkdf-pag-prev,\n            &.mkdf-pag-next,\n            &.mkdf-pag-first,\n            &.mkdf-pag-last {\n                margin: 0 2px;\n\n                a {\n                    font-size: 24px;\n\n                    span {\n                        display: block;\n                        line-height: inherit;\n\n                        &:before {\n                            display: block;\n                            line-height: inherit;\n                        }\n                    }\n                }\n            }\n\n            @if ($list_type == 'shortcode') {\n                \n                &.mkdf-pag-prev {\n                    \n                    a {\n                        opacity: 0;\n                    }\n                }\n\n                &.mkdf-pag-next {\n                    \n                    a {\n                        opacity: 1;\n                    }\n                }\n\n            } @else if ($list_type == 'shop') {\n                span {\n                    position: relative;\n                    display: inline-block;\n                    vertical-align: top;\n                    margin: 0;\n                    padding: 0;\n                    color: $first-main-color;\n                }\n\n                a {\n                    \n                    &.next,\n                    &.prev {\n                        font-size: 0;\n                        line-height: 0;\n\n                        &:before {\n                            display: block;\n                            font-family: 'ElegantIcons'; // same icon pack as in our templates for pagination\n                            font-size: 24px;\n                            line-height: 26px;\n                            -webkit-font-smoothing: antialiased;\n                            -moz-osx-font-smoothing: grayscale;\n                        }\n                    }\n\n                    &.prev {\n                        margin-right: -10px;\n\n                        &:before {\n                            content: \"\\34\";\n                        }\n                    }\n\n                    &.next {\n                        margin-left: -10px;\n\n                        &:before {\n                            content: \"\\35\";\n                        }\n                    }\n                }\n            }\n        }\n    }\n}\n\n@mixin mkdfButtonDefaultStyle() {\n    position: relative;\n    display: inline-block;\n    vertical-align: middle;\n    width: auto;\n    margin: 0;\n    font-family: $default-text-font;\n    font-size: 12px;\n    line-height: 2em;\n    letter-spacing: 0.16em;\n    font-weight: 700;\n    text-transform: uppercase;\n    outline: none;\n    box-sizing: border-box;\n    @include mkdfTransition(color .2s ease-in-out, background-color .2s ease-in-out, border-color .2s ease-in-out);\n}\n\n@mixin mkdfButtonSize($size: medium) {\n    \n    @if ($size == 'small') {\n        padding: 11px 24px;\n\n    } @else if ($size == 'medium') {\n        padding: 13px 34px 11px;\n\n\n    } @else if ($size == 'large') {\n        padding: 13px 43px 11px;\n\n    } @else if ($size == 'huge') {\n        padding: 21px 60px 16px;\n        font-size:14px;\n    }\n}\n\n@mixin mkdfButtonTransparentColor() {\n    color: $default-text-color;\n    background-color: transparent;\n}\n\n@mixin mkdfButtonSolidColor($important: null) {\n    color: #fff $important;\n    background-color: $first-main-color $important;\n    border: 1px solid transparent $important;\n}\n\n@mixin mkdfButtonSolidHoverColor($important: null) {\n    color: #fff $important;\n    background-color: $first-main-color $important;\n    box-shadow: none !important;\n}\n\n@mixin mkdfButtonOutlineColor($important: null) {\n    color: $first-main-color $important;\n    background-color: transparent $important;\n    border: 1px solid $first-main-color $important;\n}\n\n@mixin mkdfButtonOutlineHoverColor($important: null) {\n    color: #fff $important;\n    background-color: $first-main-color $important;\n    border-color: $first-main-color $important;\n}\n\n@mixin mkdfPlaceholder {\n    &::-webkit-input-placeholder {\n        @content\n    }\n\n    &:-moz-placeholder {\n        @content;\n\n        opacity: 1;\n    }\n    &::-moz-placeholder {\n        @content;\n\n        opacity: 1;\n    }\n    &:-ms-input-placeholder {\n        @content\n    }\n}\n\n\n @keyframes animate-btn-line {\n    0% {\n        @include mkdfTransformOrigin(0 50%);\n        @include mkdfTransform(scaleX(0));\n    }\n    100% {\n        @include mkdfTransformOrigin(0 50%);\n        @include mkdfTransform(scaleX(1));\n    }\n}\n@-webkit-keyframes mkdfPulsebig {\n   0% {\n    -moz-box-shadow: 0 0 0 0 currentColor;\n    box-shadow: 0 0 0 0 currentColor;\n  }\n  70% {\n      -moz-box-shadow: 0 0 0 20px rgba($first-main-color, 0.3);\n      box-shadow: 0 0 0 20px rgba($first-main-color, 0);\n  }\n  100% {\n      -moz-box-shadow: 0 0 0 0 rgba($first-main-color, 0);\n      box-shadow: 0 0 0 0 rgba($first-main-color, 0);\n  }\n}\n@keyframes mkdfPulsebig {\n  0% {\n    -moz-box-shadow: 0 0 0 0 currentColor;\n    box-shadow: 0 0 0 0 currentColor;\n  }\n  70% {\n      -moz-box-shadow: 0 0 0 20px rgba($first-main-color, 0.3);\n      box-shadow: 0 0 0 20px rgba($first-main-color, 0);\n  }\n  100% {\n      -moz-box-shadow: 0 0 0 0 rgba($first-main-color, 0);\n      box-shadow: 0 0 0 0 rgba($first-main-color, 0);\n  }\n}\n@-webkit-keyframes mkdfPulsesmall {\n  0% {\n    -webkit-box-shadow: 0 0 0 0 currentColor;\n  }\n  70% {\n      -webkit-box-shadow: 0 0 0 10px rgba($first-main-color, 0.15);\n  }\n  100% {\n      -webkit-box-shadow: 0 0 0 0 rgba($first-main-color, 0);\n  }\n}\n@keyframes mkdfPulsesmall {\n  0% {\n    -moz-box-shadow: 0 0 0 0 currentColor;\n    box-shadow: 0 0 0 0 currentColor;\n  }\n  70% {\n      -moz-box-shadow: 0 0 0 10px rgba($first-main-color, 0.15);\n      box-shadow: 0 0 0 10px rgba($first-main-color, 0);\n  }\n  100% {\n      -moz-box-shadow: 0 0 0 0 rgba($first-main-color, 0);\n      box-shadow: 0 0 0 0 rgba($first-main-color, 0);\n  }\n}\n@-webkit-keyframes mkdfPulsesmallfirst {\n  0% {\n    -webkit-box-shadow: 0 0 0 0 $first-main-color;\n  }\n  70% {\n      -webkit-box-shadow: 0 0 0 10px rgba($first-main-color, 0.15);\n  }\n  100% {\n      -webkit-box-shadow: 0 0 0 0 rgba($first-main-color, 0);\n  }\n}\n@keyframes mkdfPulsesmallfirst {\n  0% {\n    -moz-box-shadow: 0 0 0 0 $first-main-color;\n    box-shadow: 0 0 0 0 $first-main-color;\n  }\n  70% {\n      -moz-box-shadow: 0 0 0 10px rgba($first-main-color, 0.15);\n      box-shadow: 0 0 0 10px rgba($first-main-color, 0);\n  }\n  100% {\n      -moz-box-shadow: 0 0 0 0 rgba($first-main-color, 0);\n      box-shadow: 0 0 0 0 rgba($first-main-color, 0);\n  }\n}\n\n// common mixins - end\n\n////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////\n\n// mixins styles - start\n\n%input-style {\n    position: relative;\n    width: 100%;\n    margin: 0 0 $input-margin;\n    padding: $input-vertical-padding $input-horizontal-padding;\n    font-family: $default-text-font;\n    font-size: 16px;\n    font-weight: inherit;\n    color: #a2a3a3;\n    background-color: transparent;\n    border: 2px solid $default-border-color;\n    border-radius: 0;\n    outline: 0;\n    cursor: pointer;\n    -webkit-appearance: none;\n    box-sizing: border-box;\n    @include mkdfTransition(border-color 0.2s ease-in-out);\n\n    &:focus {\n        color: $default-heading-color;\n        \n    }\n\n    @include mkdfPlaceholder {\n        color: inherit;\n    }\n}\n\n// mixins styles - end\n\n////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////\n\n//media query mixins - start\n\n@mixin laptop-landscape-large {\n    @media only screen and (max-width: map-get($breakpoints, laptop-landscape-large)) {\n        @content;\n    }\n}\n\n@mixin laptop-landscape-mac {\n    @media only screen and (max-width: map-get($breakpoints, laptop-landscape-mac)) {\n        @content;\n    }\n}\n\n@mixin laptop-landscape-medium {\n    @media only screen and (max-width: map-get($breakpoints, laptop-landscape-medium)) {\n        @content;\n    }\n}\n\n@mixin laptop-landscape {\n    @media only screen and (max-width: map-get($breakpoints, laptop-landscape)) {\n        @content;\n    }\n}\n\n@mixin ipad-landscape {\n    @media only screen and (max-width: map-get($breakpoints, ipad-landscape)) {\n        @content;\n    }\n}\n\n@mixin ipad-portrait {\n    @media only screen and (max-width: map-get($breakpoints, ipad-portrait)) {\n        @content;\n    }\n}\n\n@mixin phone-landscape {\n    @media only screen and (max-width: map-get($breakpoints, phone-landscape)) {\n        @content;\n    }\n}\n\n@mixin phone-portrait {\n    @media only screen and (max-width: map-get($breakpoints, phone-portrait)) {\n        @content;\n    }\n}\n\n@mixin smaller-phone-portrait {\n    @media only screen and (max-width: map-get($breakpoints, smaller-phone-portrait)) {\n        @content;\n    }\n}\n\n// media query mixins - end\n\n// animation mixin - start\n\n@mixin keyframes($name) {\n    @-webkit-keyframes #{$name} {\n        @content;\n    }\n\n    @keyframes #{$name} {\n        @content;\n    }\n}\n\n@mixin animation($name, $duration, $repeat, $timing, $delay) {\n    -webkit-animation-name: $name;\n    -webkit-animation-duration: $duration;\n    -webkit-animation-iteration-count: $repeat;\n    -webkit-animation-timing-function: $timing;\n    -webkit-animation-delay: $delay;\n    -webkit-animation-fill-mode: forwards; // this prevents the animation from restarting!\n\n    animation-name: $name;\n    animation-duration: $duration;\n    animation-iteration-count: $repeat;\n    animation-timing-function: $timing;\n    animation-delay: $delay;\n    animation-fill-mode: forwards; // this prevents the animation from restarting!\n}\n\n// animation mixin - end\n\n////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////\n\n// heading mixins - start\n\n@mixin mkdfDefaultHeadingStyle() {\n    @include mkdfHeadingStyle();\n    font-weight: 600;\n    margin: 25px 0;\n    letter-spacing: -0.025em;\n    font-family: $default-text-font;\n    -ms-word-wrap: break-word;\n    word-wrap: break-word;\n    \n    a {\n        @include mkdfTypographyLayout();\n        \n        &:hover {\n            color: $first-main-color;\n        }\n    }\n}\n\n@mixin mkdfHeadingStyle($with-heading: null, $with-color: true) {\n    \n    @if ($with-color) {\n        color: $default-heading-color;\n    }\n    \n    @if ($with-heading == 'h1') {\n        @include mkdfH1();\n    } @else if ($with-heading == 'h2') {\n        @include mkdfH2();\n    } @else if ($with-heading == 'h3') {\n        @include mkdfH3();\n    } @else if ($with-heading == 'h4') {\n        @include mkdfH4();\n    } @else if ($with-heading == 'h5') {\n        @include mkdfH5();\n    } @else if ($with-heading == 'h6') {\n        @include mkdfH6();\n    }\n}\n\n@mixin mkdfBody() {\n    font-family: $additional-text-font;\n    font-size: 16px;\n    font-weight: 400;\n    line-height: 30px;\n    color: $default-text-color;\n    background-color: $default-background-color;\n    -webkit-font-smoothing: antialiased;\n}\n\n@mixin mkdfH1() {\n    font-size: 55px;\n    line-height: 1.23em;\n}\n\n@mixin mkdfH2() {\n    font-size: 40px;\n    line-height: 1.25em;\n}\n\n@mixin mkdfH3() {\n    font-size: 36px;\n    line-height: 1.16em;\n}\n\n@mixin mkdfH4() {\n    font-size: 30px;\n    line-height: 1.2em;\n}\n\n@mixin mkdfH5() {\n    font-size: 26px;\n    line-height: 1.23em;\n}\n\n@mixin mkdfH6() {\n    font-size: 20px;\n    line-height: 1.3em;\n    letter-spacing: 0;\n}\n\n// heading mixins - end\n\n////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////\n\n@mixin mkdfBlockquote($important : null) {\n    @include mkdfRelativeHolderLayout();\n    margin: 10px 0 $important;\n    padding: 20px 40px $important;\n    font-size: 18px $important;\n    line-height: 30px $important;\n    quotes: none;\n    box-sizing: border-box;\n    border: none $important;\n    color: $default-text-color $important;\n\n    > * {\n        @include mkdfTypographyLayout();\n        margin: 0;\n    }\n\n    &:after,\n    &:before{\n        content: '';\n    }\n\n    //&:before {\n    //    content: \"\\7b\";\n    //    font-family: \"ElegantIcons\";\n    //    font-size: 60px;\n    //    color: $first-main-color;\n    //    position: absolute;\n    //    top:50%;\n    //    @include mkdfTransform(translateY(-50%));\n    //}\n\n    cite,\n    .wp-block-quote__citation,\n    .wp-block-pullquote__citation,\n    footer {\n        display: block $important;\n        margin-top: 10px $important;\n        text-align: inherit $important;\n        font-size: 14px $important;\n        line-height: 1.3em $important;\n        letter-spacing: 0 $important;\n        font-style: normal  $important;\n        font-weight: 400 $important;\n        text-transform: none $important;\n    }\n\n    cite{\n        padding-left: 10%;\n        display: inline-block $important;\n    }\n\n}\n\n", "$breakpoints: (\n        laptop-landscape-large: 1440px,\n        laptop-landscape-mac: 1366px,\n        laptop-landscape-medium: 1280px,\n        laptop-landscape: 1200px,\n        ipad-landscape: 1024px,\n        ipad-portrait: 768px,\n        phone-landscape: 680px,\n        phone-portrait: 480px,\n        smaller-phone-portrait: 320px\n);\n\n$grid-width: 1100px;\n$grid-width-laptop-landscape: 950px;\n$grid-width-ipad-landscape: 768px;\n$grid-width-ipad-portrait: 600px;\n$grid-width-phone-landscape: 420px;\n$grid-width-phone-portrait: 300px;\n$grid-width-smaller-phone-portrait: 90%;\n\n$grid-width-boxed: 1150px;\n$grid-width-laptop-landscape-boxed: 1000px;\n$grid-width-ipad-landscape-boxed: 818px;\n$grid-width-ipad-portrait-boxed: 650px;\n$grid-width-phone-landscape-boxed: 470px;\n$grid-width-phone-portrait-boxed: 350px;\n$grid-width-smaller-phone-portrait-boxed: 92%;\n\n$grid-width-1300: 1300px;\n$grid-width-1200: 1200px;\n$grid-width-1000: 1000px;\n$grid-width-800: 800px;\n\n$default-text-font: '<PERSON><PERSON>', sans-serif;\n$additional-text-font: '<PERSON><PERSON><PERSON>', sans-serif;\n\n$first-main-color: #ea3d56;\n$first-main-color-dark-blue: #1b2c58;\n$first-main-color-medium-blue: #3745a5;\n$first-main-color-ligh-blue: #1f75ff;\n$first-main-color-yellow: #ffc40e;\n$first-main-color-green: #56c4c5;\n$default-heading-color: #1b2c58;\n$default-text-color: #868890;\n$shadow-color: inherit;\n\n\n$default-background-color: #fff;\n$additional-background-color: #f6f6f6;\n$default-border-color: rgba(#e1e1e1, 0.3);\n$default-border-radius: 4px;\n$default-box-shadow: 0 0 4.85px 0.15px rgba(#000, 0.09);\n\n$header-light-color: #fff;\n$header-light-hover-color: $first-main-color;\n$header-dark-color: $default-heading-color;\n$header-dark-hover-color: $first-main-color;\n\n// input elements\n$input-height: 50px;\n$sselect-input-height: $input-height;\n$input-vertical-padding: 22px;\n$input-horizontal-padding: 16px;\n$input-margin: 18px;\n\n// responsive breakpoints\n$laptop-landscape-large-plus-pixel: 1441px;\n$laptop-landscape-large: 1440px;\n$laptop-landscape-mac-plus-pixel: 1367px;\n$laptop-landscape-mac: 1366px;\n$laptop-landscape-medium-plus-pixel: 1281px;\n$laptop-landscape-medium: 1280px;\n$laptop-landscape-plus-pixel: 1201px;\n$laptop-landscape: 1200px;\n$ipad-landscape-plus-pixel: 1025px;\n$ipad-landscape: 1024px;\n$ipad-portrait-plus-pixel: 769px;\n$ipad-portrait: 768px;\n$phone-landscape-plus-pixel: 681px;\n$phone-landscape: 680px;\n$phone-portrait-plus-pixel: 481px;\n$phone-portrait: 480px;\n$smaller-phone-portrait-plus-pixel: 321px;\n$smaller-phone-portrait: 320px;\n\n$default-easing-function: ease;\n$default-transition-duration: .5s;"]}