/* ==========================================================================
   Portfolio shortcode style - begin
   ========================================================================== */

.mkdf-portfolio-list-holder {
    @include mkdfRelativeHolderLayout();

	/***** Article Global Style - begin *****/

	&.mkdf-diagonal-layout.mkdf-five-columns{

		article{

			&:nth-child(6){
				margin-top: 66px;

				@media screen and (max-width: 1024px){
					margin-top: 0;
				}
			}

			&:nth-child(5){
				margin-top: 132px;

				@media screen and (max-width: 768px){
					margin-top: 0;
				}
			}

			&:nth-child(4){
				margin-top: 198px;

				@media screen and (max-width: 680px){
					margin-top: 0;
				}
			}

			&:nth-child(3){
				margin-top: 264px;

				@media screen and (max-width: 680px){
					margin-top: 0;
				}
			}
		}
		.mkdf-pl-inner {

			&.mkdf-masonry-list-wrapper {
					
					opacity: 0;
					-webkit-transition: opacity .22s cubic-bezier(0.23, 1, 0.32, 1), -webkit-transform 0s cubic-bezier(0.23, 1, 0.32, 1);
					transition: opacity .22s cubic-bezier(0.23, 1, 0.32, 1), transform 0s cubic-bezier(0.23, 1, 0.32, 1);
					
					
					.mkdf-pl-item  {

							.mkdf-pl-item-inner {

								opacity: 0;
								@include mkdfTransform(scale(.6));
								-webkit-transition: -webkit-transform .6s cubic-bezier(0.18, 0.89, 0.32, 1), opacity .2s ease-out;
								transition: transform .4s cubic-bezier(0.18, 0.89, 0.32, 1), opacity .2s ease-out;

							}
									

							

								.mkdf-pl-item-inner {

									&.mkdf-appeared {

										opacity: 1;
										@include mkdfTransform(scale(1));

									}
									
								}
								

							
					}
					
				}
		}
	}

    article {

	    .touch & {
		    cursor: pointer;
	    }
		
	    .mkdf-pl-item-inner {
		    @include mkdfRelativeHolderLayout();
	    }
	    
	    .mkdf-pli-image {
		    @include mkdfRelativeHolderLayout();
		    
		    img {
			    display: block;
			    width: 100%;
		    }

			.mkdf-pli-image-hover {
				@include mkdfAbsoluteHolderLayout();
				padding: 20px;
				opacity: 0;
				text-align: center;
				box-sizing: border-box;
				color: #fff;
				font-size: 72px;
				@include mkdfTransition(opacity .2s ease-in-out);

				.mkdf-pli-image-hover-table{
					display: table;
					height: 100%;
					width: 100%;

					i{
						display: table-cell;
						vertical-align: middle;
						@include mkdfTransition(all .4s ease-in-out);
					}
				}
			}
	    }

		&:hover{

			.mkdf-pli-image{

				.mkdf-pli-image-hover{
					opacity: 1;

					i {
						transform: rotate(90deg);
					}
				}
			}
		}

	    .mkdf-pli-link {
		    @include mkdfAbsoluteHolderLayout();
	    }

	    .mkdf-pli-text-wrapper {
	        @include mkdfTableLayout();
	    }

	    .mkdf-pli-text {
		    @include mkdfTableCellLayout();

		    .mkdf-pli-title {
				margin: 0;
				line-height: 1.5em;
		    }

		    .mkdf-pli-category-holder {
			    position: relative;
			    display: block;
			   

			    a {
				    position: relative;
				    display: inline-block;
				    vertical-align: middle;
				    padding: 0 6px 0 0;
				    margin: 0 3px 0 0;
					color: #0c2c5866;
				    z-index: 8;
					font-size: 12px;
					font-family: $default-text-font;
					font-weight: 700;
					text-transform: uppercase;
					letter-spacing: 0.1em;

				    
				    &:after {
					    position: absolute;
					    top: 0;
					    right: -4px;
					    content: '/';
					    color: $first-main-color-dark-blue;
						opacity: 0.4;
					    font-size: 14px;
					    line-height: inherit;
				    }

				    &:last-child {
					    margin: 0;
					    padding: 0;
					    
					    &:after {
						    display: none;
					    }
				    }

					&:hover{
						color: $first-main-color;

							&:after {
						    color: $first-main-color-dark-blue;
							opacity: 0.4;
						    
					    }
					}
			    }
		    }

		    .mkdf-pli-excerpt {
			    margin: 10px 0 0;
		    }
	    }
    }

	/***** Article Global Style - end *****/
	
	/***** Specific Global Style - begin *****/
	
	&.mkdf-pl-has-shadow {
		
		article {
			
			.mkdf-pli-image {
				box-shadow: 0 16px 46px 0 rgba(0,0,0,.3);
			}
		}
	}
	
	&.mkdf-pl-has-filter {
		
		.mkdf-pl-inner {
			overflow: hidden;
		}
	}
	
	&.mkdf-pl-no-content {
		
		.mkdf-pli-text-holder {
			display: none;
		}
	}
	/***** Specific Global Style - end *****/
	
	&.mkdf-pl-masonry {

        &.mkdf-fixed-masonry-items {
	        
            article {
	            
                .mkdf-pl-item-inner,
                .mkdf-pli-image {
		            height: 100%;
	            }
            }
        }
	}

	/***** Portfolio Types - end *****/

	/***** Additional Features - begin *****/

	&.mkdf-pl-has-animation {

		article {
			opacity: 0;
			@include mkdfTransform(translateY(80px));
			@include mkdfTransition(opacity .8s cubic-bezier(0.34, 0.52, 0.57, 1.04), transform .8s cubic-bezier(0.34, 0.52, 0.57, 1.04));

			&.mkdf-item-show {
				opacity: 1;
				@include mkdfTransform(translateY(0));

				&.mkdf-item-shown {
					@include mkdfTransition(none);
				}
			}
			
			.touch & {
				opacity: 1;
				@include mkdfTransform(translateY(0));
			}
		}
	}

	/***** Additional Features - end *****/
}

/* ==========================================================================
   Portfolio shortcode style - end
   ========================================================================== */

/* ==========================================================================
   Portfolio filter style - begin
   ========================================================================== */

.mkdf-pl-filter-holder {
    @include mkdfRelativeHolderLayout();
    margin: 0 0 30px;
    text-align: center;

    ul {
        position: relative;
        display: inline-block;
        vertical-align: middle;
        margin: 0;
        padding: 12px 29px;
        list-style: none;
		border: 2px solid #edeeef;

        li {
            position: relative;
            display: inline-block;
            vertical-align: middle;
            margin: 0;
	        padding: 0 15px;
            cursor: pointer;

	        @include ipad-landscape {
		        padding: 0 10px;
	        }

            span {
                position: relative;
                display: inline-block;
                vertical-align: middle;
                color: $default-heading-color;
				font-family: $default-text-font;
                font-size: 20px;
                line-height: 22px;
                white-space: nowrap;
                @include mkdfTransition(color .2s ease-out);
            }

            &.mkdf-pl-current,
            &:hover {

                span {
                    color: $first-main-color;
                }
            }
        }
    }
}
/* ==========================================================================
   Portfolio filter style - end
   ========================================================================== */

/* ==========================================================================
   Portfolio standard pagination style - begin
   ========================================================================== */

.mkdf-portfolio-list-holder {
	
	&.mkdf-pl-pag-standard {
		
		.mkdf-pl-inner {
			opacity: 1;
			@include mkdfTransition(opacity .2s ease-out);
		}
		
		&.mkdf-pl-pag-standard-animate {
			
			.mkdf-pl-inner {
				opacity: 0;
			}
		}
	}
}

.mkdf-pl-standard-pagination {
	@include mkdfStandardPaginationStyle('shortcode');
}
/* ==========================================================================
   Portfolio standard pagination style - end
   ========================================================================== */

/* ==========================================================================
   Portfolio load more pagination style - begin
   ========================================================================== */

.mkdf-pl-load-more-holder {
	@include mkdfRelativeHolderLayout();

    .mkdf-pl-load-more {
	    margin: 32px 0 0;
	    text-align: center;
    }
}
/* ==========================================================================
   Portfolio load more pagination style - end
   ========================================================================== */

/* ==========================================================================
   Portfolio loading element style - begin
   ========================================================================== */

.mkdf-pl-loading {
	position: relative;
	display: none;
	width: 100%;
	margin: 40px 0 20px;
	color: $default-heading-color;
	text-align: center;

	&.mkdf-filter-trigger {
		position: absolute;
		top: 250px;
		left: 0;
	}
	
    &.mkdf-standard-pag-trigger {
		position: absolute;
		top: 50px;
		left: 0;
	
	    .mkdf-pl-has-filter & {
		    top: 150px;
	    }
    }

	&.mkdf-showing {
		display: block;
	}

	> div {
		position: relative;
		display: inline-block;
		vertical-align: middle;
		width: 14px;
		height: 14px;
		margin: 0 3px;
		background-color: $default-heading-color;
		border-radius: 100%;
		@include mkdfAnimation(sk-bouncedelay 1.4s infinite ease-in-out both);
	}

	.mkdf-pl-loading-bounce1 {
		-webkit-animation-delay: -0.32s;
		-moz-animation-delay: -0.32s;
		animation-delay: -0.32s;
	}

	.mkdf-pl-loading-bounce2 {
		-webkit-animation-delay: -0.16s;
		-moz-animation-delay: -0.16s;
		animation-delay: -0.16s;
	}
}

@-webkit-keyframes sk-bouncedelay {
	0%, 80%, 100% {
		-webkit-transform: scale(0);
	}
	40% {
		-webkit-transform: scale(1.0);
	}
}

@-moz-keyframes sk-bouncedelay {
	0%, 80%, 100% {
		-moz-transform: scale(0);
	}
	40% {
		-moz-transform: scale(1.0);
	}
}

@keyframes sk-bouncedelay {
	0%, 80%, 100% {
		-webkit-transform: scale(0);
		transform: scale(0);
	}
	40% {
		-webkit-transform: scale(1.0);
		transform: scale(1.0);
	}
}

/* ==========================================================================
   Portfolio list responsive - start
   ========================================================================== */

@media (min-width: 768px) and (max-width: 1024px) {

	.mkdf-portfolio-list-holder.mkdf-grid-list.mkdf-three-columns .mkdf-item-space, 
	.mkdf-portfolio-list-holder.mkdf-grid-list.mkdf-three-columns .mkdf-masonry-grid-sizer {

	    width: 50% !important;
	    
	
	}
	.mkdf-portfolio-list-holder.mkdf-grid-list.mkdf-three-columns .mkdf-item-space:nth-child(2n+1) {
		clear: both !important;
	}
	
		
}

/* ==========================================================================
   Portfolio list responsive - end
   ========================================================================== */
   
/* ==========================================================================
   Portfolio loading element style - end
   ========================================================================== */