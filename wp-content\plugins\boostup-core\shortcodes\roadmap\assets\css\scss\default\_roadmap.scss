/* ==========================================================================
   Roadmap shortcode style - begin
   ========================================================================== */

.mkdf-roadmap {
    @include mkdfRelativeHolderLayout();
	padding: 200px 0; //3px due to arrow point
	overflow: hidden;

	.mkdf-roadmap-holder{
		overflow: hidden;
	}

	.mkdf-roadmap-line{
		position: relative;
		width: 0%;
		height: 3px;
		background-color: $additional-background-color;

		.mkdf-rl-arrow-left,
		.mkdf-rl-arrow-right{
			position: absolute;
			top: 50%;
			font-size: 30px;
			color: $first-main-color;
			cursor: pointer;
			@include mkdfTransform(translateY(-50%));
			z-index: 50;
		}

		.mkdf-rl-arrow-left{
			left: -2px;
			padding: 10px 10px 10px 0; //to enlarge click area

			svg{
				@include mkdfTransform(rotate(180deg));
			}
		}

		.mkdf-rl-arrow-right{
			right: -2px;
			padding: 10px 0 10px 10px; //to enlarge click area
		}
	}

	.mkdf-roadmap-inner-holder{
		@include mkdfTransition(all .2s ease-in-out);
	}

	.mkdf-roadmap-item{
		position: relative;
		float: left;
		text-align: center;
		@include mkdfTransform(translateY(-17px)); //2px due to line height/2

		.mkdf-roadmap-item-circle-holder{
			font-size: 0;
		}

		.mkdf-roadmap-item-before-circle,
		.mkdf-roadmap-item-after-circle{
			display: inline-block;
			vertical-align: middle;
			width: 0;
			height: 3px;
			background-color: #dfdfdf;
			position: absolute;
			top: 14px;
		}
		.mkdf-roadmap-item-before-circle {
		    left: 0;
		}
		.mkdf-roadmap-item-after-circle {
		    left: calc(50% - -11px);
		}

		.mkdf-roadmap-item-circle {
			display: inline-block;
			vertical-align: middle;
			width: 20px;
			height: 20px;
			border-radius: 50%;
			background-color: $additional-background-color;
			box-shadow: inset 0px 0px 0px 6px $first-main-color;
		}

		.mkdf-roadmap-item-stage-title-holder {
			position: absolute;
			left: 0;
			width: 100%;

			.mkdf-ris-title{
				color: $default-heading-color;
				font-size: 20px;
				font-family: $default-text-font;
				font-weight: 600;
			}
		}

		.mkdf-roadmap-item-content-holder {
			position: absolute;
			left: 4%;
			width: 92%;
			text-align: left;
			padding: 30px 36px;
			box-sizing: border-box;
			box-shadow: 0px 5px 31px 0px rgba(0, 0, 0, 0.2);
			background-color: #fff;
			border-radius: 5px;
			z-index: -1;

			@media screen and (max-width: 1440px) and (min-width: 1280px) {
				padding: 20px;
			}

			.mkdf-ric-title{
				margin: 0 0 14px;
				@include phone-portrait {
					text-align: center;
				}
			}


			.mkdf-ric-content {
				@include phone-portrait {
					text-align: center;
				}
			}

			&:after{
				content: '';
				position: absolute;
				left: 50%;
				width: 3px;
				height: 70px;
				background-color: #dfdfdf;
				@include mkdfTransform(translateX(-50%));
				z-index: -1;
			}
		}

		&.mkdf-roadmap-item-above{
			.mkdf-roadmap-item-stage-title-holder{
				top: 35px;
			}

			.mkdf-roadmap-item-content-holder{
				bottom: 75px;

				&:after{
					top: 100%;
				}
			}
		}

		&.mkdf-roadmap-item-below{
			.mkdf-roadmap-item-stage-title-holder{
				bottom: 32px;
			}

			.mkdf-roadmap-item-content-holder{
				top: 75px;

				&:after{
					bottom: 100%;
				}
			}
		}

		&.mkdf-roadmap-reached-item{
			.mkdf-roadmap-item-before-circle{
				background-color: $first-main-color;
				left: 0;
			}
		}

		&.mkdf-roadmap-passed-item{
			.mkdf-roadmap-item-before-circle,
			.mkdf-roadmap-item-after-circle{
				background-color: $first-main-color;
			}
		}
	}

	&.mkdf-roadmap-skin-dark{
		.mkdf-roadmap-line,
		.mkdf-roadmap-item-before-circle,
		.mkdf-roadmap-item-after-circle,
		.mkdf-roadmap-item-circle,
		.mkdf-roadmap-item-content-holder:after{
		}

		.mkdf-roadmap-item-stage-title-holder .mkdf-ris-title{
		}

		.mkdf-ric-title{
		}

		.mkdf-roadmap-item-content-holder {
			background-color: #fff;
		}
	}

	&.mkdf-roadmap-skin-firstmain {

		.mkdf-roadmap-item {

			.mkdf-roadmap-item-content-holder {
				background-color: $first-main-color;

				&:after{
					background-color: $first-main-color;
				}

				.mkdf-ric-title {
					color: #fff;
				}

				.mkdf-ric-content {
					color: #fff;
				}
			}

			.mkdf-roadmap-item-before-circle,
			.mkdf-roadmap-item-after-circle{
				background-color: rgba($first-main-color,0.3);
			}

			.mkdf-roadmap-item-circle {
				background-color: #fff;
				box-shadow: inset 0px 0px 0px 6px $first-main-color;
			}


			&.mkdf-roadmap-reached-item{
				.mkdf-roadmap-item-before-circle{
					background-color: $first-main-color;
				}
			}

			&.mkdf-roadmap-passed-item{
				.mkdf-roadmap-item-before-circle,
				.mkdf-roadmap-item-after-circle{
					background-color: $first-main-color;
				}
			}
		}

		.mkdf-roadmap-item-stage-title-holder .mkdf-ris-title{
			color: $first-main-color;
		}

		.mkdf-roadmap-line {
			background-color: rgba($first-main-color,0.3);

			.mkdf-rl-arrow-left,
			.mkdf-rl-arrow-right{
				color: $first-main-color;
			}
		}

	}

	&.mkdf-roadmap-skin-light {
		
		.mkdf-roadmap-item {

			.mkdf-roadmap-item-content-holder {
				background-color: #fff;

				&:after{
					background-color: #fff;
				}
			}

			.mkdf-roadmap-item-before-circle,
			.mkdf-roadmap-item-after-circle{
				display: inline-block;
				vertical-align: middle;
				width: 0;
				height: 3px;
				background-color: rgba(255,255,255,0.3);
			}

			.mkdf-roadmap-item-circle {
				background-color: $first-main-color-ligh-blue;
				box-shadow: inset 0px 0px 0px 6px #fff;
			}


			&.mkdf-roadmap-reached-item{
				.mkdf-roadmap-item-before-circle{
					background-color: #fff;
				}
			}

			&.mkdf-roadmap-passed-item{
				.mkdf-roadmap-item-before-circle,
				.mkdf-roadmap-item-after-circle{
					background-color: #fff;
				}
			}
		}

		.mkdf-roadmap-item-stage-title-holder .mkdf-ris-title{
			color: #fff;
		}

		.mkdf-roadmap-line {
			background-color: rgba(255,255,255,0.3);

			.mkdf-rl-arrow-left,
			.mkdf-rl-arrow-right{
				color: #fff;
			}
		}

	}

}
/* ==========================================================================
   Roadmap shortcode style - end
   ========================================================================== */