<?php
/**
 * Diagnostic script to identify admin URL issues on GoDaddy servers
 * This will help us understand what's happening when you click "Edit Page"
 */

// Load WordPress
require_once('wp-config.php');
require_once('wp-load.php');

// Check if user is logged in and has admin privileges
if (!is_user_logged_in() || !current_user_can('manage_options')) {
    die('Access denied. You must be logged in as an administrator.');
}

echo "<h2>WordPress Admin URL Diagnostic</h2>";

// Server information
echo "<h3>Server Information:</h3>";
echo "<ul>";
echo "<li><strong>HTTP Host:</strong> " . ($_SERVER['HTTP_HOST'] ?? 'Not set') . "</li>";
echo "<li><strong>Server Name:</strong> " . ($_SERVER['SERVER_NAME'] ?? 'Not set') . "</li>";
echo "<li><strong>Request URI:</strong> " . ($_SERVER['REQUEST_URI'] ?? 'Not set') . "</li>";
echo "<li><strong>Document Root:</strong> " . ($_SERVER['DOCUMENT_ROOT'] ?? 'Not set') . "</li>";
echo "<li><strong>WordPress URL:</strong> " . get_option('siteurl') . "</li>";
echo "<li><strong>Home URL:</strong> " . get_option('home') . "</li>";
echo "</ul>";

// WordPress configuration
echo "<h3>WordPress Configuration:</h3>";
echo "<ul>";
echo "<li><strong>WP_DEBUG:</strong> " . (defined('WP_DEBUG') && WP_DEBUG ? 'Enabled' : 'Disabled') . "</li>";
echo "<li><strong>Permalink Structure:</strong> " . get_option('permalink_structure') . "</li>";
echo "<li><strong>Admin URL:</strong> " . admin_url() . "</li>";
echo "<li><strong>Site URL:</strong> " . site_url() . "</li>";
echo "</ul>";

// Test URLs
echo "<h3>Test URLs:</h3>";
$test_post_id = 1; // Assuming post ID 1 exists
$edit_url = admin_url("post.php?post={$test_post_id}&action=edit");
$admin_url = admin_url('admin.php');
$dashboard_url = admin_url('index.php');

echo "<ul>";
echo "<li><strong>Dashboard:</strong> <a href='" . esc_url($dashboard_url) . "' target='_blank'>" . htmlspecialchars($dashboard_url) . "</a></li>";
echo "<li><strong>Admin:</strong> <a href='" . esc_url($admin_url) . "' target='_blank'>" . htmlspecialchars($admin_url) . "</a></li>";
echo "<li><strong>Edit Post (ID 1):</strong> <a href='" . esc_url($edit_url) . "' target='_blank'>" . htmlspecialchars($edit_url) . "</a></li>";
echo "</ul>";

// Plugin status
echo "<h3>Plugin Status:</h3>";
$active_plugins = get_option('active_plugins');
$boostup_core_active = in_array('boostup-core/main.php', $active_plugins);
echo "<ul>";
echo "<li><strong>BoostUp Core Active:</strong> " . ($boostup_core_active ? 'Yes' : 'No') . "</li>";
echo "<li><strong>Total Active Plugins:</strong> " . count($active_plugins) . "</li>";
echo "</ul>";

// Rewrite rules
echo "<h3>Rewrite Rules Status:</h3>";
$rules = get_option('rewrite_rules');
echo "<ul>";
echo "<li><strong>Rules Exist:</strong> " . (is_array($rules) ? 'Yes' : 'No') . "</li>";
echo "<li><strong>Number of Rules:</strong> " . (is_array($rules) ? count($rules) : 0) . "</li>";
echo "<li><strong>Last Flush:</strong> " . (get_option('boostup_core_last_rewrite_flush', 'Never')) . "</li>";
echo "</ul>";

// Check for specific issues
echo "<h3>Potential Issues:</h3>";
echo "<ul>";

// Check if mod_rewrite is available
if (function_exists('apache_get_modules')) {
    $modules = apache_get_modules();
    $mod_rewrite = in_array('mod_rewrite', $modules);
    echo "<li><strong>mod_rewrite:</strong> " . ($mod_rewrite ? 'Available' : 'Not available') . "</li>";
} else {
    echo "<li><strong>mod_rewrite:</strong> Cannot detect (function not available)</li>";
}

// Check .htaccess
$htaccess_path = ABSPATH . '.htaccess';
$htaccess_exists = file_exists($htaccess_path);
echo "<li><strong>.htaccess exists:</strong> " . ($htaccess_exists ? 'Yes' : 'No') . "</li>";

if ($htaccess_exists) {
    $htaccess_writable = is_writable($htaccess_path);
    echo "<li><strong>.htaccess writable:</strong> " . ($htaccess_writable ? 'Yes' : 'No') . "</li>";
}

// Check for conflicting rewrite rules
if (is_array($rules)) {
    $conflicting_rules = 0;
    foreach ($rules as $pattern => $rewrite) {
        if (strpos($pattern, 'admin') !== false && strpos($rewrite, 'admin') === false) {
            $conflicting_rules++;
        }
    }
    echo "<li><strong>Potentially conflicting rules:</strong> " . $conflicting_rules . "</li>";
}

echo "</ul>";

// Test a simple admin AJAX call
echo "<h3>Admin AJAX Test:</h3>";
$ajax_url = admin_url('admin-ajax.php');
echo "<p>Testing AJAX URL: <a href='" . esc_url($ajax_url . '?action=heartbeat') . "' target='_blank'>" . htmlspecialchars($ajax_url) . "</a></p>";

echo "<h3>Recommendations:</h3>";
echo "<ol>";
if (!$boostup_core_active) {
    echo "<li>BoostUp Core plugin is not active. Activate it to test.</li>";
}
if (!is_array($rules) || count($rules) === 0) {
    echo "<li>Rewrite rules are missing. Run the flush script.</li>";
}
if (!$htaccess_exists) {
    echo "<li>.htaccess file is missing. Go to Settings > Permalinks and save to regenerate.</li>";
}
echo "<li>Try clicking the test URLs above to see which ones work.</li>";
echo "<li>If edit URLs return 404, the issue is confirmed.</li>";
echo "<li>Run the flush-rewrite-rules.php script after this diagnosis.</li>";
echo "</ol>";

echo "<p><em>Delete this file after diagnosis is complete.</em></p>";
?>
