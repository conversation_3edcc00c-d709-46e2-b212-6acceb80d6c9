/* ==========================================================================
   Portfolio Item Layout - Standard Shader style - begin
   ========================================================================== */

.mkdf-portfolio-list-holder {
	
	&.mkdf-pl-boxed {

		&.mkdf-light-skin {

			.mkdf-pli-excerpt,
			.mkdf-pli-title {
				color: #fff;
			}
		}

		.mkdf-pl-item-inner{
			border: 2px solid rgba(225,225,225,.2);
			padding: 20px;
			box-sizing: border-box;
			@include mkdfTransition(all .3s ease-out);

			&:hover {
				border: 2px solid rgba(225,225,225,.4);
			}
		}

		.mkdf-pli-text-holder {
			@include mkdfRelativeHolderLayout();
			margin: 29px 0 0;
		}

		article {

			.mkdf-pli-text {

				.mkdf-pli-category-holder {
					margin: 20px 0 0;
					display: inline-block;
					width: 60%;

					a {
						color: #0c2c5866;
						font-weight: 700;
						font-size: 12px;
						letter-spacing: 0.01em;

						&:after {
							font-size: 12px;
						    color: #0c2c5866;
					    }

						&:hover {
							color: $first-main-color;
						}
					}
				}

				.mkdf-pli-social-share {
					display: inline-block;
					width: 40%;
					text-align: right;
					color: #fff;
					float: right;
					margin: 20px 0 0;

					ul {
						li {
							a {
								color: rgba(255,255,255, 0.4);
								z-index: 8;
								position: relative;

								&:hover{
									color: rgba(255,255,255, 1);
								}
							}
						}
					}
				}
			}
		}

	}
}
/* ==========================================================================
   Portfolio Item Layout - Standard Shader style - end
   ========================================================================== */