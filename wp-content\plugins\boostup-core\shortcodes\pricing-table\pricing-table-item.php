<?php
namespace BoostUpCore\CPT\Shortcodes\PricingTable;

use BoostUpCore\Lib;

class PricingTableItem implements Lib\ShortcodeInterface {
	private $base;
	
	function __construct() {
		$this->base = 'mkdf_pricing_table_item';
		add_action( 'vc_before_init', array( $this, 'vcMap' ) );
	}
	
	public function getBase() {
		return $this->base;
	}
	
	public function vcMap() {
		if ( function_exists( 'vc_map' ) ) {
			vc_map(
				array(
					'name'                      => esc_html__( 'Pricing Table Item', 'boostup-core' ),
					'base'                      => $this->base,
					'icon'                      => 'icon-wpb-pricing-table-item extended-custom-icon',
					'category'                  => esc_html__( 'by BOOSTUP', 'boostup-core' ),
					'allowed_container_element' => 'vc_row',
					'as_child'                  => array( 'only' => 'mkdf_pricing_table' ),
					'params'                    => array(
						array(
							'type'        => 'textfield',
							'param_name'  => 'custom_class',
							'heading'     => esc_html__( 'Custom CSS Class', 'boostup-core' ),
							'description' => esc_html__( 'Style particular content element differently - add a class name and refer to it in custom CSS', 'boostup-core' )
						),
						array(
							'type'        => 'dropdown',
							'param_name'  => 'set_active_item',
							'heading'     => esc_html__( 'Set Item As Active', 'boostup-core' ),
							'value'       => array_flip( boostup_mikado_get_yes_no_select_array( false ) ),
							'save_always' => true
						),
						array(
							'type'       => 'colorpicker',
							'param_name' => 'content_background_color',
							'heading'    => esc_html__( 'Active Content Background Color', 'boostup-core' ),
                            'dependency' => array( 'element' => 'set_active_item', 'value' => array( 'yes' ) )
						),
						array(
							'type'       => 'colorpicker',
							'param_name' => 'content_shadow_color',
							'heading'    => esc_html__( 'Active Content Shadow Color', 'boostup-core' ),
                            'dependency' => array( 'element' => 'set_active_item', 'value' => array( 'yes' ) )
						),
						array(
							'type'        => 'textfield',
							'param_name'  => 'active_title',
							'heading'     => esc_html__( 'Active Title', 'boostup-core' ),
							'value'       => esc_html__( 'Best Choice', 'boostup-core' ),
							'save_always' => true,
							'dependency' => array( 'element' => 'set_active_item', 'value' => array( 'yes' ) )
						),
						array(
							'type'       => 'colorpicker',
							'param_name' => 'active_title_color',
							'heading'    => esc_html__( 'Active Title Color', 'boostup-core' ),
							'dependency' => array( 'element' => 'active_title', 'not_empty' => true )
						),
						array(
							'type'        => 'textfield',
							'param_name'  => 'title',
							'heading'     => esc_html__( 'Title', 'boostup-core' ),
							'value'       => esc_html__( 'Basic Plan', 'boostup-core' ),
							'save_always' => true
						),
						array(
							'type'       => 'colorpicker',
							'param_name' => 'title_color',
							'heading'    => esc_html__( 'Title Color', 'boostup-core' ),
							'dependency' => array( 'element' => 'title', 'not_empty' => true )
						),
						array(
							'type'       => 'attach_image',
							'param_name' => 'custom_icon',
							'heading'    => esc_html__( 'Custom Icon', 'boostup-core' )
						),
						array(
							'type'       => 'textfield',
							'param_name' => 'price',
							'heading'    => esc_html__( 'Price', 'boostup-core' )
						),
						array(
							'type'       => 'colorpicker',
							'param_name' => 'price_color',
							'heading'    => esc_html__( 'Price Color', 'boostup-core' ),
						),
						array(
							'type'        => 'textfield',
							'param_name'  => 'currency',
							'heading'     => esc_html__( 'Currency', 'boostup-core' ),
							'description' => esc_html__( 'Default mark is $', 'boostup-core' )
						),
						array(
							'type'       => 'colorpicker',
							'param_name' => 'currency_color',
							'heading'    => esc_html__( 'Currency Color', 'boostup-core' ),
						),
						array(
							'type'        => 'textfield',
							'param_name'  => 'price_period',
							'heading'     => esc_html__( 'Price Period', 'boostup-core' ),
							'description' => esc_html__( 'Default label is: "/PER MONTH"', 'boostup-core' )
						),
						array(
							'type'       => 'colorpicker',
							'param_name' => 'price_period_color',
							'heading'    => esc_html__( 'Price Period Color', 'boostup-core' ),
						),
						array(
							'type'        => 'textfield',
							'param_name'  => 'button_text',
							'heading'     => esc_html__( 'Button Text', 'boostup-core' ),
							'value'       => esc_html__( 'PURCHASE', 'boostup-core' ),
							'save_always' => true
						),
						array(
							'type'       => 'textfield',
							'param_name' => 'link',
							'heading'    => esc_html__( 'Button Link', 'boostup-core' ),
							'dependency' => array( 'element' => 'button_text', 'not_empty' => true )
						),
						array(
							'type'       => 'colorpicker',
							'param_name' => 'button_color',
							'heading'    => esc_html__( 'Color', 'boostup-core' ),
							'group'      => esc_html__( 'Button Style', 'boostup-core' )
						),
						array(
							'type'       => 'colorpicker',
							'param_name' => 'button_hover_color',
							'heading'    => esc_html__( 'Hover Color', 'boostup-core' ),
							'group'      => esc_html__( 'Button Style', 'boostup-core' )
						),
						array(
							'type'       => 'colorpicker',
							'param_name' => 'button_background_color',
							'heading'    => esc_html__( 'Background Color', 'boostup-core' ),
                            'dependency' => array( 'element' => 'set_active_item', 'value' => array( 'yes' ) ),
							'group'      => esc_html__( 'Button Style', 'boostup-core' )
						),
						array(
							'type'       => 'colorpicker',
							'param_name' => 'button_hover_background_color',
							'heading'    => esc_html__( 'Hover Background Color', 'boostup-core' ),
							'group'      => esc_html__( 'Button Style', 'boostup-core' )
						),
						array(
							'type'       => 'textarea_html',
							'param_name' => 'content',
							'heading'    => esc_html__( 'Content', 'boostup-core' ),
							'value'      => '<li>content content content</li><li>content content content</li><li>content content content</li>'
						)
					)
				)
			);
		}
	}
	
	public function render( $atts, $content = null ) {
		$args   = array(
			'custom_class'             => '',
			'custom_icon'              => '',
			'set_active_item'          => 'no',
			'content_background_color' => '#fff',
			'content_shadow_color' 	   => 'rgba(130,29,45,0.1)',
			'active_title'             => '',
			'active_title_color'       => '',
			'title'                    => '',
			'title_color'              => '',
			'price'                    => '100',
			'price_color'              => '',
			'currency'                 => '$',
			'currency_color'           => '',
			'price_period'             => '/PER MONTH',
			'price_period_color'       => '',
			'button_text'              => '',
			'link'                     => '',
			'button_color'             => '',
			'button_hover_color'       => '',
			'button_background_color'       => '',
			'button_hover_background_color'       => '',

		);
		$params = shortcode_atts( $args, $atts );
		
		$params['content']             = preg_replace( '#^<\/p>|<p>$#', '', $content ); // delete p tag before and after content
		$params['holder_classes']      = $this->getHolderClasses( $params );
		$params['holder_styles']       = $this->getHolderStyles( $params );
		$params['title_styles']        = $this->getTitleStyles( $params );
		$params['active_title_styles'] = $this->getActiveTitleStyles( $params );
		$params['price_styles']        = $this->getPriceStyles( $params );
		$params['currency_styles']     = $this->getCurrencyStyles( $params );
		$params['price_period_styles'] = $this->getPricePeriodStyles( $params );
		
		$html = boostup_core_get_shortcode_module_template_part( 'templates/pricing-table-template', 'pricing-table', '', $params );
		
		return $html;
	}
	
	private function getHolderClasses( $params ) {
		$holderClasses = array();
		
		$holderClasses[] = ! empty( $params['custom_class'] ) ? esc_attr( $params['custom_class'] ) : '';
		$holderClasses[] = $params['set_active_item'] === 'yes' ? 'mkdf-pt-active-item' : '';
		
		return implode( ' ', $holderClasses );
	}
	
	private function getHolderStyles( $params ) {
		$itemStyle = array();
		
		if ( $params['set_active_item'] == 'yes' && ! empty( $params['content_background_color'] ) ) {
			$itemStyle[] = 'background-color: ' . $params['content_background_color'];
		}

		if ( $params['set_active_item'] == 'yes' && ! empty( $params['content_shadow_color'] ) ) {
			$itemStyle[] = 'box-shadow: 0 0 52.38px 1.62px ' . $params['content_shadow_color'];
		}
		
		return implode( ';', $itemStyle );
	}

	private function getActiveTitleStyles( $params ) {
		$itemStyle = array();

		if ( ! empty( $params['active_title_color'] ) ) {
			$itemStyle[] = 'color: ' . $params['active_title_color'];
		}

		return implode( ';', $itemStyle );
	}
	
	private function getTitleStyles( $params ) {
		$itemStyle = array();
		
		if ( ! empty( $params['title_color'] ) ) {
			$itemStyle[] = 'color: ' . $params['title_color'];
		}
		
		return implode( ';', $itemStyle );
	}
	
	private function getPriceStyles( $params ) {
		$itemStyle = array();
		
		if ( ! empty( $params['price_color'] ) ) {
			$itemStyle[] = 'color: ' . $params['price_color'];
		}
		
		return implode( ';', $itemStyle );
	}
	
	private function getCurrencyStyles( $params ) {
		$itemStyle = array();
		
		if ( ! empty( $params['currency_color'] ) ) {
			$itemStyle[] = 'color: ' . $params['currency_color'];
		}
		
		return implode( ';', $itemStyle );
	}
	
	private function getPricePeriodStyles( $params ) {
		$itemStyle = array();
		
		if ( ! empty( $params['price_period_color'] ) ) {
			$itemStyle[] = 'color: ' . $params['price_period_color'];
		}
		
		return implode( ';', $itemStyle );
	}
}