<?php

if ( ! function_exists( 'boostup_core_import_object' ) ) {
	function boostup_core_import_object() {
		$boostup_core_import_object = new BoostUpCoreImport();
	}
	
	add_action( 'init', 'boostup_core_import_object' );
}

if ( ! function_exists( 'boostup_core_data_import' ) ) {
	function boostup_core_data_import() {
		$importObject = BoostUpCoreImport::getInstance();
		
		if ( $_POST['import_attachments'] == 1 ) {
			$importObject->attachments = true;
		} else {
			$importObject->attachments = false;
		}
		
		$folder = "boostup/";
		if ( ! empty( $_POST['example'] ) ) {
			$folder = $_POST['example'] . "/";
		}
		
		$importObject->import_content( $folder . $_POST['xml'] );
		
		die();
	}
	
	add_action( 'wp_ajax_boostup_core_data_import', 'boostup_core_data_import' );
}

if ( ! function_exists( 'boostup_core_widgets_import' ) ) {
	function boostup_core_widgets_import() {
		$importObject = BoostUpCoreImport::getInstance();
		
		$folder = "boostup/";
		if ( ! empty( $_POST['example'] ) ) {
			$folder = $_POST['example'] . "/";
		}
		
		$importObject->import_widgets( $folder . 'widgets.txt', $folder . 'custom_sidebars.txt' );
		
		die();
	}
	
	add_action( 'wp_ajax_boostup_core_widgets_import', 'boostup_core_widgets_import' );
}

if ( ! function_exists( 'boostup_core_options_import' ) ) {
	function boostup_core_options_import() {
		$importObject = BoostUpCoreImport::getInstance();
		
		$folder = "boostup/";
		if ( ! empty( $_POST['example'] ) ) {
			$folder = $_POST['example'] . "/";
		}
		
		$importObject->import_options( $folder . 'options.txt' );
		
		die();
	}
	
	add_action( 'wp_ajax_boostup_core_options_import', 'boostup_core_options_import' );
}

if ( ! function_exists( 'boostup_core_other_import' ) ) {
	function boostup_core_other_import() {
		$importObject = BoostUpCoreImport::getInstance();
		
		$folder = "boostup/";
		if ( ! empty( $_POST['example'] ) ) {
			$folder = $_POST['example'] . "/";
		}
		
		$importObject->import_options( $folder . 'options.txt' );
		$importObject->import_widgets( $folder . 'widgets.txt', $folder . 'custom_sidebars.txt' );
		$importObject->import_menus( $folder . 'menus.txt' );
		$importObject->import_settings_pages( $folder . 'settingpages.txt' );

		$importObject->mkdf_update_meta_fields_after_import($folder);
		$importObject->mkdf_update_options_after_import($folder);

		if ( boostup_core_is_revolution_slider_installed() ) {
			$importObject->rev_slider_import( $folder );
		}
		
		die();
	}
	
	add_action( 'wp_ajax_boostup_core_other_import', 'boostup_core_other_import' );
}