(function($) {
    'use strict';
	
	var teambordercoloronhover = {};
	mkdf.modules.teambordercoloronhover = teambordercoloronhover;

    teambordercoloronhover.mkdfTeamBorderColorOnHover = mkdfTeamBorderColorOnHover;

    teambordercoloronhover.mkdfOnDocumentReady = mkdfOnDocumentReady;
	
	$(document).ready(mkdfOnDocumentReady);
	
	/*
	 All functions to be called on $(document).ready() should be in this function
	 */
	function mkdfOnDocumentReady() {
		mkdfTeamBorderColorOnHover();
	}
	
	/**
	 * Init accordions shortcode
	 */
	function mkdfTeamBorderColorOnHover(){

		var hoverTeamElement;

        hoverTeamElement = $('.mkdf-team-holder .mkdf-team-inner');
		
		hoverTeamElement.on('mouseenter', function() {
			var thisTeam = $(this);
			
			if(thisTeam.attr("data-border-hover-color")) {
				thisTeam.css("border", "1px solid " + thisTeam.attr("data-border-hover-color"));
			}
		});
		hoverTeamElement.on('mouseleave', function() {
			var thisTeam = $(this);
			
			if(thisTeam.attr("data-border-color")) {
				thisTeam.css("border", "1px solid " + thisTeam.attr("data-border-color"));
			}
		});
	}

})(jQuery);