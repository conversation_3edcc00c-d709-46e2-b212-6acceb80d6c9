*** WPBakery Page Builder changelog ***

2023-05-24 - version 6.13.0
  - Fix: XSS vulnerability fix for elements
  - Fix: XSS vulnerability fix for the user roles

2023-04-18 - version 6.11.0
  - New: API for wordpress.com created to support integration
  - Update: Access to templates is in compliance with the licensing terms
  - Fix: "Add to Cart" of WooCommerce accepts multiple products
  - Fix: The editor works properly with Flex Slider
  - Fix: Row and column edit options are displayed properly
  - Fix: The Custom Heading element works properly
  - Fix: Changing element output does not add an empty script tag
  - Fix: WPBakery works properly with the Gutenberg content
  - Fix: The Custom Heading element works properly
  - Fix: Boxed and full-width layouts work properly with all major themes
  - Fix: Drag and drop for rows works properly in the Frontend editor

2022-10-13 - version 6.10.0
  - New: Introduce local Google Fonts
  - New: Compatibility for WooCommerce Tab Manager
  - Update: Pie chart legend positions
  - Update: Multiple translations improvements
  - Fix: Rows full width logic
  - Fix: Remove all notices in FE editor
  - Fix: Controls sizing in FE editor
  - Fix: Translate keys in WooCommerce Posts Grid
  - Fix: Flexslider overflow in image gallery
  - Fix: Round chard legend position, allow to specify position
  - Fix: WooCommerce Shop page css
  - Fix: Remove dead css prefixes
  - Fix: Prevent issues with addPageCustomCss when passing a custom ID. Specifically the templatera shortcode passes a custom ID and so if the templatera shortcode is on the front page/homepage the template ID is not correctly passed
  - Fix: Custom accent color not working with the Separator with Text element shadow style
  - Fix: Youtube Video BG Auto Play Issues
  - Fix: License check logic
  - Fix: Edit form loading for async scripts
  - Fix: Warning on the removed default template
  - Fix: Automapper removing elements work properly
  - Fix: get_the_excerpt filter arguments support WordPress 5.5
  - Fix: Admin access in multisite works properly
  - Fix: Edit-form initialization works properly

2022-04-07 - version 6.9.0
  - New: Filter to disable full width row JS
  - Update: Option to set custom color for the round chart element
  - Update: WPML compatibility improved with filters for images
  - Fix: Raw HTML element works properly for WPML Translation Editor
  - Fix: Auto-mapper deprecation notice works properly
  - Fix: Template preview works properly
  - Fix: Stretched row works properly in WordPress default themes
  - Fix: Post grid works properly

2021-12-20 - version 6.8.0
  - Update: Deprecated jQuery removed from the plugin
  - Fix: Button animation works properly on Safari
  - Fix: Image gallery works properly on WordPress 5.8
  - Fix: "Load more" button works properly to load content with Ajax
  - Fix: WPBakery works properly with Layer Slider
  - Fix: Role management for users works properly for the Super Admin role
  - Fix: WPBakery works properly with WPML
  - Fix: WPBakery works properly with ACF
  - Fix: Post grid loading works properly
  - Fix: Post grid categories work properly on mobile
  - Fix: Post grid autoplay works properly
  - Fix: Post grid works properly with ACF
  - Fix: Post grid images full sizes works properly
  - Fix: Scrollbar works properly for Raw HTML
  - Fix: Post grid pagination works properly
  - Fix: Charts work properly in the editor
  - Fix: Update filters for vc_shortcode_content_filter
  - Fix: Empty ACF values are not displayed
  - Fix: Editor loads properly for all user roles
  - Deprecated: Class WPBakeryVisualComposerAbstract will be removed in 6.9.0

2021-07-06 - version 6.7.0
  - New: Custom breakpoint controls to adjust stacking behavior
  - New: Option to disable animations in Masonry grid added
  - New: Filter for wpb map element settings added
  - New: Filter for changing heading tag added
  - Compatibility: WooCommerce does not generate warnings
  - Fix: Lightbox image filenames are correct
  - Fix: Jump for “vclink” removed
  - Fix: Owl carousel does not mix content
  - Fix: Initial value on attributes work properly
  - Fix: URLs updated to https
  - Fix: Case sensitive single image size updated

2021-02-22 - version 6.6.0
  - New: prettyPhoto removed and Lightbox2 added to the plugin (prettyPhoto will stay for backward compatibility)
  - New: Call to Action element allows to align titles and select tags
  - New: FlexSlider updated to 2.7.2 version and RTL support improved in WooCommerce products
  - Update: $post variable added to the grid items filter ‘vc_gitem_post_data_get_link_real_title’
  - Update: Catalan translation added
  - Fix: Frontend editor initialization works properly
  - Fix: Drag and drop for section element works properly in the backend editor
  - Fix: WPBakery button in the Gutenberg editor works properly
  - Fix: Request element parameters are passed properly in the edit form

2020-12-16 - version 6.5.0
  - Update: Compatibility with WordPress 5.6
  - Update: Compatibility with PHP8
  - Fix: Template saving works properly
  - Fix: Super admin editor access in multisite works properly
  - Fix: setPost() method do_action_ref_array passes arguments correctly
  - Fix: Modify with selected grid template loads properly
  - Fix: Edit with WPBakery in Gutenberg control appears correctly
  - Fix: Edit form loading spinner displayed correctly

2020-11-17 - version 6.4.2
  - Fix: WooCommerce 4.7 compatibility issues resolved

2020-09-24 - version 6.4.1
  - Fix: Role manager unfiltered html access improved
  - Fix: Role manager Frontend editor access improved
  - Update: German translation updated

2020-09-11 - version 6.4.0
  - New: Rank Math SEO compatibility introduced
  - New: Option to exclude categories in the loop parameters
  - New: Option to ignore sticky posts in the loop parameters
  - Update: jquery updated to jquery-core
  - Update: show_in_rest is checked for Gutenberg compatibility
  - Update: setTitle allows HTML entities
  - Update: Role manager access improved for WordPress user roles
  - Fix: Multisite setup works properly for super admin user role
  - Fix: Yoast SEO latest version is fully compatible with the editor
  - Fix: Autocomplete works properly

2020-08-21 - version 6.3.0
  - Added: Role manager for unfiltered_html available
  - Update: Filter for screen sizes in the navigation bar
  - Update: Trigger event to prepare tab content
  - Update: Description for the limit in WooCommerce shortcodes
  - Update: LiteSpeed cache compatibility updated
  - Update: Flickr element render API
  - Update: HTML for image captions enabled
  - Fix: Posts grid elements with "load more" option animates properly
  - Fix: Dependency for not_empty in vc_link attribute type
  - Fix: Target blank link selector
  - Fix: Entity characters in french language
  - Fix: Class vc_tta-o-non-responsive on parent nodes
  - Fix: Undo/redo content disappearing with edit-only access

2020-04-20 - version 6.2.0
  - Update: Performance improvements for the editor
  - Update: Filter for ajax grid rendering added
  - Update: Filters for post_custom_css and shortcodes_custom_css added
  - Update: NL translation added for the plugin
  - Update: BR translation added for the plugin
  - Update: Standards and code styles updated
  - Update: Classic and Gutenberg editor toggle updated
  - Update: Portrait video support added
  - Compatibility: Rank Math SEO compatibility improved
  - Fix: Ajax add to cart works properly
  - Fix: Plugin update works properly with multi-language version
  - Fix: Sorted_list attribute works properly
  - Fix: Shop Manager role works properly with the plugin
  - Fix: Post id works properly in the preview mode
  - Fix: Loading configuration works properly
  - Fix: Order by post works properly
  - Fix: oEmbed works properly in text blocks
  - Fix: addShortcodesCustomCss works properly

2019-12-13 - version 6.1.0
  - Added: Catalan, Ukrainian, Hungarian translations
  - Update: WordPress 5.3 Compatibility
  - Update: FontAwesome update to 5.10.2
  - Update: loop attribute not found rows and order by relevance
  - Update: PrettyPhoto https support
  - Fix: RTL controls on backend
  - Fix: RTL column reverse
  - Fix: Disabled post revisions desing options
  - Fix: Save row/section as template
  - Fix: HoverBox improvements
  - Remove: IE9 support

2019-07-17 - version 6.0.5
  - Fix: LESS vendor folder available
  - Fix: All Media in Text Block works properly

2019-07-15 - version 6.0.4
  - Fix: Element API works properly for 3rd party elements
  - Fix: Post grid filter displayed properly
  - Fix: Custom CSS editor does not remove "\"
  - Fix: Directory name typo for Instagram filter files corrected
  - Fix: Templates import API does work correctly
  - Fix: Row design options settings for elements in templates works properly
  - Fix: WooCommerce cart element works properly
  - Fix: Option "Save as a template" for a row is available
  - Fix: Elements edit form displayed correctly
  - Fix: Warning message for PHP function `htmlspecialchars`
  - Fix: Custom post types taxonomies work in Post Grid element
  - Fix: Revolution Slider output content properly

2019-05-31 - version 6.0.3
  - Update: Init action priority is changed from 9 to 11
  - Update: Allow return in shortcode templates
  - Update: Coding standards improvements
  - Fix: Param Group clone
  - Fix: Checkbox value display in edit form
  - Fix: Loop attribute tax_query render
  - Fix: Attach image typo
  - Fix: Removed notice when shortcode template file missing
  - Fix: Map meta cap warning on custom post types

2019-05-17 - version 6.0.2
  - Fix: Container elements controls in Backend Editor

2019-05-15 - version 6.0.1
  - Fix: Tour element class error

2019-05-15 - version 6.0
  - Update: Compliance with coding standards
  - Update: jQuery Waypoints update to the latest version
  - Update: Fallback for previews added
  - Fix: Pageable container autoplay
  - Fix: Hover box does not flip on iOS devices
  - Fix: Responsive media queries work properly
  - Fix: WooCommerce products pages in preview mode
  - Fix: Data source for Post Grid works with correct taxonomies
  - Fix: Design options for elements work in preview mode
  - Fix: The Editor got broken after adding many images
  - Fix: The posts source got broken in Post Grid after importing data

2019-02-13 - version 5.7
  - Update: Compatibility with PHP 7.3
  - Fix: WooCommerce corrected editable role
  - Fix: Page preview styles independent from view page

2018-11-27 - version 5.6
  - Update: Compatibility with Wordpress 5.0

2018-10-11 - version 5.5.5
  - Fix: SSL issues for fonts
  - Fix: Gutenberg improvements

2018-09-08 - version 5.5.4
  - Fix: Link selector works properly in Backend editor

2018-09-07 - version 5.5.3
  - Added: RTL control toggle added for columns
  - Fix: Accordion control styles display properly in Role Manager settings
  - Fix: Plugin update notice works properly
  - Fix: RTL works properly with full screen rows
  - Fix: Single Image elements are counted into Yoast SEO sitemap
  - Fix: Post grid id applied properly to reduce number of records in wp_postmeta
  - Fix: Exception works properly in Post Grid
  - Fix: SSL timeout on activation
  - Fix: Link selector works properly in Frontend and Backend editors
  - Fix: Simplified controls in Design Options save border color
  - Fix: Empty space in class name removed for inner column
  - Fix: Post grid display proper number of posts specified

2018-06-27 - version 5.5.2
   - Fix: "WPBMap Uncaught Exception" for modifyParam API method

2018-06-22 - version 5.5.1
   - Fix: "WPBMap Uncaught Exception" for dropParam and addParam API methods

2018-06-15 - version 5.5
   - Added: Undo/Redo operations available for the editor actions
   - Added: Gutenberg compatibility elements to insert Gutenberg blocks in the WPBakery layout
   - Added: 5 columns grid layout for rows
   - Added: RTL support for the full width rows
   - Added: Settings to enable/disable Gutenberg plugin
   - API Added: vc_shortcode_prepare_atts to update prepared shortcode attributes
   - API Added: vc_map_get_attributes to add new, modify all compiled attributes
   - API Added: vc_basic_grid_filter_query_filters to modify custom query in posts grid.
   - API Added: vc_is_valid_post_type_be to disable/enable Backend Editor for several post type
   - API Added: vc_show_button_fe to show Edit With WPB in Frontend Editor
   - API Update: vc_shortcode_output added shortcode tag in attributes
   - Update: Order by "include" added to WooCommerce
   - Update: WooCommerce elements default order/orderby synchronized
   - Update: Isotope/Masonry library version update
   - Update: Performance improvements for the Frontend Editor loading
   - Update: Performance improvements for the Editor Save
   - Update: Performance improvements for the Posts Grid loading
   - Update: WPML compatibility for posts/front pages
   - Update: RevSlider compatibility improvements
   - Update: Ninja Forms compatibility improvements
   - Update: ChartJS library update
   - Update: vc_wpautop, vc_wpnop versions updated
   - Fix: PHP 7.2 compatibility improvements
   - Fix: Post Slider custom links work properly
   - Fix: Extra p tags in grid removed
   - Fix: Blog page design options work properly
   - Fix: WPML single image translation available
   - Fix: HoverBox element on iOS and Firefox improvements
   - Fix: Builder.parse in frontend
   - Fix: Custom OnClick action in buttons for Posts Grids
   - Fix: Shortcode attributes update by reference inside filter vc_shortcode_output

2018-03-08 - version 5.4.7
   - Fix: Custom shortcode mapping

2018-03-07 - version 5.4.6
   - Update: Compatibility with PHP 7.2 added
   - Update: Compatibility filters for WP Toolset added
   - Update: French translation updated
   - Update: Persian translation updated

2017-11-24 - version 5.4.5
   - Fix: Firefox tinymce and vc_link issue
   - Fix: WooCommerce vendor categories list

2017-11-16 - version 5.4.4
   - Fix: TinyMCE Saves linebreaks

2017-11-15 - version 5.4.3
   - Added: Shortcode support in raw_html
   - Added: More filters for vc_btn shortcode
   - Update: Shortcode automapper admin content escaping
   - Fix: Improve WordPress 4.9 tinyMCE support
   - Compatibility: with WordPress 4.9
   - Compatibility: with WP Tool Set plugin

2017-10-13 - version 5.4.2
   - Fix: Updater URL typo

2017-10-12 - version 5.4.1
   - Fix: Plugin zip size

2017-10-12 - version 5.4
   - Update: WPBakery Page Builder logo update
   - Update: Navigation bar color schema in Backend and Grid Builder
   - Update: TweetMe text update with WPBakery data
   - Update: FontAwesome library update
   - Update: PO/MO files with new strings
   - Fix: Param saving dependant on checkbox
   - Fix: Extra javascript loading with Yoast SEO activated
   - Fix: Double shortcode parsing in WPB field
   - Fix: Checkbox param value repeat inside param group
   - Fix: Fixed encodeUriComponent
   - Fix: Device icons for column responsiveness
   - Fix: WPML vendor
   - Fix: ZigZag separator display on Firefox
   - Fix: Element presets category merge

2017-09-15 - version 5.3
   - Update: WordPress native color interface
   - Update: New interface icons
   - Update: New SVG element icons
   - Update: FontAwesome library is updated
   - Update: With new color scheme comes new name WPBakery Page Builder

2017-08-03 - version 5.2.1
   - Update: Function add_shortcode_param returned
   - Update: Function get_row_css_class returned
   - Update: Function wpb_map returned
   - Fix: std fixed for attribute types nested in a param group
   - Fix: vc_js error on frontend editor
   - Fix: Font dropdown in TinyMCE for WordPress 4.8.1

2017-06-29 - version 5.2.
   - Added: RTL support
   - Added: HoverBox content element
   - Added: ZigZag separator content element
   - Added: Parallax effect added to columns
   - Added: video background added to columns
   - Update: element presets work as My Elements in Add Element window
   - Update: drag and drop control in Role Manager
   - Update: deprecated functions removed
   - Update: Arabic translation
   - Update: Russian translation
   - Update: Spanish translation
   - Fix: TinyMCE performance in Backend editor
   - Fix: remove obsolete function designOptions
   - Fix: Layer Slider compatibility issues
   - Fix: templates user access
   - Fix: shortcode saving in fe adds 0=""
   - Fix: duplicate icons when adding custom icon font
   - Fix: subscriber see WPBakery Page Builder welcome page
   - Fix: WPML vendor issue
   - Fix: page returns 0 when clicking add to cart from grid template
   - Fix: shortcode regexp for dash in shortcode name
   - Fix: preg_replace result, replacement and extra space
   - Fix: filesystem error when saving design options (css) in settings
   - Fix: woocommerce product id in grid api
   - Fix: tabs/tours/accordions role access

2017-04-11 - version 5.1.1.1.
   - Update: Performance improvements for elements
   - Fix: Element removing warnings

2017-04-04 - version 5.1.1
   - Added: extra security check for grid ajax calls to harden security

2017-03-09 - version 5.1
   - Added: Element Id for all elements
   - Added: Image size control for Grid Builder media elements
   - Update: Grid Builder Backend editor style user interface
   - Update: List of available Google Fonts
   - Fix: getJSON backward compatibility for jQuery
   - Fix: vcIcon font family
   - Fix: postDeactivate redirect url
   - Fix: Extra isset check in case if vc_remove_param
   - Fix: Not all child categories are displayed in the drop-down
   - Fix: Autocomplete param sorting
   - Fix: Shortcode mapper and shortcodes with dashes
   - Fix: Can not read property of 'IndexOf'
   - Fix: Custom CSS textarea height
   - Fix: Missing scrollbar for view preset
   - Fix: Fix vc_gitem_template_attribute_post_image_url warning for array_merge
   - Fix: Box-shadow and borders for twenty-seventeen
   - Fix: Media query for added css files
   - Fix: Increase gap in edit form for google fonts
   - Fix: Hiding all "Tabs" in tab module still displays active item
   - Fix: php 7.1 compatibility issues
   - Fix: Section save as template option

2016-11-17 - version 5.0.1
   - Improved: Auto update process on hostings with wrong chmod on /tmp directory
   - Fixed: CSS Animation "none" works differently on save
   - Fixed: Prettyphoto jumps up on closing in firefox
   - Fixed: Section in template breaks
   - Fixed: Warning for array_merge in vc_gitem_template_attribute_post_image_url

2016-11-09 - version 5.0
   - Added: WPBakery Page Builder Template Library with downloadable templates
   - Added: Section element to combine rows
   - Added: Material icon set to icon libraries
   - Added: Animate CSS animations
   - Added: template saving for section and row
   - Update: all elements has CSS animation attribute
   - Update: UI of element preset saving
   - Update: editor controls with retina friendly icon set
   - Update: IE8-9 support files removed
   - Update: compatibility with Ninja Forms
   - Update: less.js version update for better performance
   - Update: activation/deactivation mechanism
   - Update: iconpicker performance
   - Fix: php notice for default post type
   - Fix: php notices for post save
   - Fix: autocomplete element removing indexes
   - Fix: warning foreach for acf grid shortcodes
   - Fix: activation state on multisite
   - Fix: custom heading google fonts loading
   - Deprecated: class license methods in favor of 4.8
   - Deprecated: droppable and draggable methods in javascript for rows

2016-09-07 - version 4.12.1
   - Fixed: backend editor loading - getContent in case if #content doesn't exists
   - Fixed: Grid item background image URL quoting
   - Fixed: Entypo icons
   - Fixed: Templates preview element icons
   - Fixed: Remove extra next/prev buttons for prettyPhoto
   - Fixed: Lightbox in firefox
   - Fixed: Empty content welcome block for latest woocommerce
   - Fixed: Hook into the YoastSEO:ready event directly.
   - Fixed: Disable scrollTo when autoplay enabled
   - Fixed: PrettyPhoto is-single hover controls
   - Fixed: Grid Item ajax response rendering when no posts found
   - Updated: Make old names for deprecated elements
   - Updated: Old buttons and CTA removed from add element list
   - Updated: FontAwesome to 4.6.3

2016-06-08 - version 4.12
   - Added: Set default templates for post types
   - Added: Option to disable/hide row
   - Added: ’nofollow’ attribute option for links
   - Added: onclick action option for links
   - Improved: CSS file loading
   - Improved: Custom Heading added to Toggle(Faq)
   - Improved: Compatibility with qTranslate X and Polylang
   - Improved: French translation files
   - Fixed: White line in grid on mac chrome
   - Fixed: vc_vendor for Yoast
   - Fixed: Post grid and post masonry grid settings
   - Fixed: Lightbox prettyPhoto JS
   - Fixed: Shortcode mapper default value
   - Fixed: Server resource control
   - Fixed: textarea_raw_html in param_group
   - Fixed: Image doubling in masonry grid
   - Fixed: Locale file for Japanese
   - Fixed: Contact Form 7 search by title
   - Fixed: Version display on Welcome page
   - Fixed: Equal Height on IE11
   - Fixed: ‘js_composer_front.min.js and Masonry component
   - Fixed: Ability to disable accordion scroll
   - Fixed: ‘Add Template’ and WPML conflict
   - Fixed: Autocomplete field
   - Fixed: PrettyPhoto navigation in Image Carousel
   - Fixed: Loop builder in grid
   - Fixed: Woo Commerce product settings

2016-04-15 - version ********
   - # is properly escaped for updated jQuery

2016-04-07 - version 4.11.2
   - Added: Compatibility with WP 4.5
   - Added: Filter for database query limits
   - Improved: Input Sanitization
   - Improved: TGMPA compatibility
   - Improved: Pageable Container Back button behavior
   - Improved: vc_siteAttachedImages now uses get_posts instead of direct query
   - Improved: Ordering by meta key in grid elements
   - Improved: loop param work with multiple taxonomies selection
   - Fixed: Google maps scroll behavior
   - Fixed: Initial loading for Masonry Media Grid
   - Fixed: Advanced Custom Fields vendor initialization
   - Fixed: Post Grid filter taxonomy names collapsing

2016-03-16 - version 4.11.1
   - Fixed: WPML + Image selection is working again
   - Fixed: vc_remove_param() + Custom WP Theme Front-end editor broke Text block

2016-03-10 - version 4.11
   - Added: New predefined templates added
   - Added: Mono Social icons added to icon library list
   - Added: 'All' text in grid element filters can be modified in param window
   - Added: Gradient button style for buttons
   - Added: Grid element initial loading controls (including 'Disable' option)
   - Added: TweetMe Button content element params description added
   - Update: WordPress 4.5 compatibility added
   - Update: Additional element description in Backend editor
   - Update: Video background autoplay on mobile devices
   - Update: Refactor grid params array system
   - Update: In some cases relative path to admin-ajax.php returned 404, replaced with full path
   - Fixed: Russian language adds new content tab
   - Fixed: Content display on video background in IE10
   - Fixed: wp_get_attachment_url work with arg
   - Fixed: php7 array slice indexes
   - Fixed: '_self' attribute fix in CTA and button
   - Fixed: get_currentuserinfo() updated
   - Fixed: Full height row and iteration issue
   - Fixed: ACF foreach warning fixed
   - Fixed: Link stripping from images added from "Add Media"

5.02.2016 - version 4.10
	- Added: Compatibility with option to add ACF to page
	- Added: Video width and alignment controls
	- Added: Parallax speed parameter added
	- Added: Category and Author elements added to Grid Builder
	- Added: Link to author in Grid Builder
	- Update: License tab available with 'set as theme’
	- Update: TweetMe button updated to support new styles
	- Update: Fontawesome library updated
	- Update: Grid Builder templates include user templates
	- Fixed: Add Media image sorting in WYSIWYG
	- Fixed: Single image preview in Grid Builder
	- Fixed: WP Theme 2016 underline fix
	- Fixed: Tabs outline style fix
	- Fixed: Iconpicker icons rendering on category change
	- Fixed: ACF fields rendering in Grid Builder

2016-01-12 - version 4.9.2
   - Fixed: "Editing an existing Post Grid, page IDs are displayed instead of titles."
   - Fixed: Extra CSS class name for single image in post grid
   - Fixed: Third elements not working in templates
   - Fixed: WPBakery Page Builder button is now added even if psot doesn't support title
   - Fixed: Yoast + single image without default value
   - Fixed: Grid builder on multisite
   - Fixed: 404 error on WP multisite
   - Improved: Tab activation through menu (links on same page)
   - Improved: PHP7 compatibility

2015-12-23 - version 4.9.1
   - Improved: VC save action is fired on wp save_post instead of edit_post
   - Improved: Some unneded files removed from package
   - Fixed: Element is calling save method only if changes were made
   - Fixed: svg image selection in single image element
   - Fixed: image style in post grid is now applied correctly
   - Fixed: Equal height in Safari

7.12.2015 - version 4.9
   - Improved: Now WPBakery Page Builder is faster at least for 30%
   - Added: Equal height columns
   - Added: Vertical alignment of element within a column
   - Added: Vertical aligment of column within a row
   - Added: Fullscreen editing mode in backend editor
   - Improved: Single click License activation
   - Improved: Removed margins from row and now applied to content elements
   - Improved: All loaders are unified in VC interface
   - Removed: Guides on/off removed from backend editor
   - Fixed: ACF repeater field
   - Fixed: Presets for columns
   - Fixed: Ninja Forms
   - Fixed: WPML taxonomies (thanks to dev guys from WPML)
   - Fixed: Social button breaks ssl
   - Fixed: Template search
   - Fixed: Rem units in Design options
   - Fixed: Advanced TinyMCE support
   - Fixed: Icon display for nested shortcodes
   - Other minor improvements and fixes

6.11.2015 - version 4.8.1
   - Added: Support for Advanced TinyMCE
   - Improved: Envato API changed to new version 2.0
   - Improved: ABSPATH check added to all php files
   - Improved: Filter in getParamTabsList method of the TTA added
   - Fixed: Post grid loads in http when using https
   - Fixed: Responsiveness in full height row
   - Fixed: Responsive Column Behavior
   - Fixed: Numbers of line chart displayed properly
   - Fixed: Text display on grid buttons
   - Fixed: Error 404 on vc-general on WP multi site install
   - Fixed: wp multisite hardcoded 'js_composer/js_composer.php', so impossible to use other plugin name
   - Fixed: Backend display proper column width
   - Fixed: Column offset resets after update
   - Fixed: Full image is broken on Firefox
   - Fixed: Error for non-logged in users
   - Fixed: Image Carousel display correctly
   - Fixed: Text element with animation is properly displayed in TTA
   - Fixed: Grid Element display price prefix $ per WooCommerce Settings

2015-10-22 - version 4.8
   - New: Advanced Role manager for WPBakery Page Builder. Now you can fine tune access to WPBakery Page Builder functionality based on user roles
   - New: New interface with template preview option in Default Templates and My Templates sections
   - New: No need for license activation on localhost
   - New: "vc_register_settings_preset" action is added. Theme authors can add elements presets through the inner API
   - New: New design styles for Separator element
   - New: Option to add icon to Separator element
   - Improved: Single image element markup revises and improved
   - Improved: UX improvements for "Save template" under the Templates section
   - Improved: Load more button in grid now use latest VC button version
   - Improved: Lighter and cleaner welcome screen on empty page
   - Fixed: Protocol is replaced with // in css import (google fonts)
   - Fixed: Loading of Google subsets
   - Fixed: Display of icons for nested shortcodes
   - Fixed: Post grid display if loaded from Templatera element
   - Fixed: Fast switching between tabs
   - Fixed: Autocomplete of categories (woocommerce)
   - Fixed: Separator text moves to the next line when no space available

2.10.2015 - version 4.7.4
   - Fixed: Bug in grid builder presets
   - Added extra ajax check

2015-09-29 - version 4.7.3
   - Re: Backwards compatibility for images with custom link
   - Added border for single image with external source
   - Width of vc icon in "Add element" window fixed
   - Presets saving mechanism optimized

2015-09-22 - version 4.7.2
   - "Undefined index" notice in tabs, tour, accordion removed (for cases when vc_remove_param() was called)
   - Bottom border in image gallery fixed
   - <br> now works in mobile view tabs
   - Backwards compatibility for images with custom link
   - Minor css improvements and fixes

2015-09-16 - version 4.7.1.1
   - Fixed: CSS conflict with Ninja Forms

2015-09-15 - version 4.7.1
   - Improved: Dependency are now called whe preset is selected for element
   - Improved: Old Tabs, Accordions and Tours removed from default templates
   - Fixed: Direct link to tabs (tour or accordion)
   - Fixed: Raw HTML (Cannot read property 'trim' of undefined)
   - Fixed: Nested elements saving in template
   - Fixed: Remove params function call and Notice: Undefined variable
   - Fixed: Html tags entered in text block now are decoded and not converted to actual tags
   - Fixed: LESS regeneration after plugin version increment

2015-08-28 - version 4.7
   - Compatibility with WP 4.3
   - New: Element Preset mechanism added (Save, Set as Default)
   - New: More predefined layouts added
   - New: Param Window and other window update
   - New: Param Window is resizeable
   - New: Pageable Content Element based on Tabs added
   - New: Single Image Zoom option added to On Click Action
   - Improved: Pie Chart element has new WPBakery Page Builder colors
   - Improved: Separator width values added
   - Improved: Custom Heading has H1 tag as an option
   - Improved: Single Image and Image Gallery Source for External Image Added
   - Improved: Single Image element uses caption from Media Library or input (for External Images)
   - Improved: Single Image and Image gallery has same options under On Click Action
   - Improved: Design Options added to all elements
   - Improved: Border radius added to Design Options
   - Improved: Progress Bar uses param group for values
   - Fixed: Limit for acceptance of ACF field in Grid removed
   - Delay of CSS animations reduced
   - Minor bugfixes and improvements

2015-07-23 - version 4.6.2
   - Custom CSS class added for Tour, Tabs, Accordions
   - Syntax in js file changed for better minifying support
   - ACF limit in grid removed
   - Single image target initialization in edit screen fixed

2015-07-11 - version 4.6.1
   - Fixed: Cannot redeclare hex2rgb()
   - Icons per page set to 200 in message box element
   - Changed syntax in js_composer_front.js - now W3 Total Cache should not have problems with minifying
   - Compatibility with PHP 5.2.4 added
   - Fixed: Woo Rating count in grid
   - Fixed: Post image element in grid
   - Fixed: Pinterest button height

2015-07-02 - version 4.6
   - New: Full screen row height
   - New: Chart elements (Pie, Doughnut, Line, Bar)
   - New: YouTube background for row
   - New: Tabs element
   - New: Tour element
   - New: Accordion element
   - New: Image overlay filters - enhance your images with awesome color overlays
   - Improved: New cleaner shortcodes (default values are not stored in the [shortcode])
   - Improved: UI for param group
   - Improved: Alt tags for images
   - Improved: Responsive options added to inner columns
   - Improved: Custom heading element. Cleaner markup, option to choose font from theme
   - Improved: Icon element now accepts custom color
   - Improved: Outline button now accepts custom color for border
   - Improved: Shortcode mapping process speed improvements
   - Fixed: Removed taxonomy db call from map.php (speed improvements)
   - Fixed: WP SEO + VC "Page slug changes if a shortcode with custom WP_Query used"
   - Fixed: Loading js files in vc-gallery.php
   - Fixed: Design options work when loaded from Templatera

2015-06-16 - version 4.5.3
   - prettyPhoto updated to 3.1.6 - latest stable and secure version

2015-05-18 - version 4.5.2
   - Performance: Removed call to get taxonomies from vc_map and now it is called via ajax only when it is needed
   - Fixed: slug changing when Yoast SEO plugin activated
   - Fixed: Unwanted gap in Grid when full width row is used
   - Fixed: Design Options now are loaded from templates too
   - Fixed: Custom inline styles from WYSIWYG editor now can be saved in templates
   - Fixed: All product categories now are accessible in VC
   - Fixed: Path for loading "imagesloaded.pkgd.min.js"
   - Resolved: Conflict with Owl Carousel

2015-04-30 - version 4.5.1
   - Improvements with stretch row logic
   - Grid pagination isn't hiding "add to column" control in Fronend editor
   - Grid filter logic improved
   - JS script logic for templates improved
   - Bug with wpLink fixed
   - WPBakery Page Builder settings link in multisite fixed

2015-04-27 - version 4.5
   - New: Parallax option for row added
   - New: IDs for rows
   - New: Param type "Hidden" available in shortcode mapper. Now you can preset shortcodes value and it will be not editable in VC interface (hardcoded)
   - New: Button element rebuilt from scratch. Extremely flexible with many options (Old buttons are still available but you will see deprecated message)
   - New: Call to action element rebuilt from scratch
   - New: Design Options section is powered by LESS technology (it actually compile less on save). This gives us proper tools to take Design options to the next level in the next updates
   - New: Guide tour for user who start using VC for the first time
   - New: VC Welcome page which is shown on plugin activation/update
   - Link attribute works with WP 4.2
   - Yoast compatibility improvements
   - Shortcode mapper user experience and ux improvements
   - VC Settings moved to the top level in WP Dashboard
   - License activation improvements now you can activate VC on the same domain (same url) again
   - Add to cart option is added to the VC Grid Builder
   - Traditional Chinese translation added. Thanks to ming xuan li
   - Various bug fixes and small improvements
   - All javascript and php files now share same coding standards (formatting)

2015-04-21 - version 4.4.4
   - Compatibility with WordPress 4.2 added

2015-03-12 - version 4.4.3
   - Color picker z-index increased
   - Proper image gallery re-initialization after dragging
   - Negative values can be saved in Design options now
   - Single image now has circle and round styles
   - "Author" role is added in the user group settings
   - "$" sign can be used in title in masonry grid
   - Shortcode mapper parsing shortcodes correctly now
   - Quotes (") can be used in the Custom heading element
   - Row stretching works as expected now
   - Japanese translation added. Thanks to evange
   - Position after cloning element is correct now
   - Auto rotate tabs works now
   - "h3 element converted to p tag if template is saved in frontend editor" fixed
   - "1/3 + 2/3" layout is added in grid builder
   - 3D shadow look improved for single image element
   - Yoast now is parsing content of VC
   - vc_load_default_templates_action returned
   - Custom query works properly in grid builder now

2015-01-28 - version 4.4.2
   - Extra CSS class can be used in grid elements now
   - Single image styles fixed
   - added action "vc_load_default_templates_action"
   - vc_add_default_templates() fixed
   - Added compatibility for older Layer slider
   - Background dissapearing in row on child element deleting fixed
   - Message box backwards compatibility added
   - Grid builder ajax stability improved
   - Css tpl for Design options regenerated
   - Custom query in Grid builder handles correctly ampersant and quote marks
   - Compatibility with jwplayer improved
   - Compatibility with ACF improved

2015-01-19 - version 4.4.1
   - Posts Grid (and other grids) are working on homepage
   - NextGen gallery working
   - Shareaholic plugin working
   - Related posts plugin working
   - Revolution slider working
   - Removed br tags that were inserted inside the div tag by third party filter
   - vc_is_inline() working as expexted again


2015-01-16 - version 4.4
   - Bulgarian translation added. Thanks to: Valentin Val
   - New elements added: Grid, Masonry Grid, Media Grid, Media Masonry Grid
   - Grid Element builder added
   - Element replaced: Toggle element replaced with new version
   - Element replaced: Message box replaced with new version
   - Tab (groups) are fixed in content element edit
   - New filter added: vc_wpb_getimagesize
   - Improvements for German translation - .mo, .po files updated
   - Separator thickness option and alignment added
   - Predefault template list updated
   - Blank page layout updated
   - Button 2.0 has alignment option "centre"
   - Compatibility and automapping with Ninja Forms introduced
   - Icon Element: Icon Element
   - Woo Commerce shortcodes are automapped
   - Autocomplete param added
   - custom_markup param added
   - Filter available: vc_custom_markup_render_filter
   - Drag icon for column added
   - Accordion title replacement bug fixed
   - Row can be stretched: full width, full width with content, full width with content (no paddings) Note: This won't work with overflow: hidden
   - EasyTables is fully compatible with WPBakery Page Builder Frontend
   - Compatibility with Yoast SEO
   - Multiple fields param group added
   - Undefined offset warning removed if single image with incorrect id
   - Shortcode automapper regexp improved for better handling shortcodes without params
   - Welcome screen typography following WPBakery Page Builder design
   - Saving post with custom Query overriding title and slug bug fixed
   - Tour element autorotate option fixed
   - Mixed content errors in https mode removed
   - custom.css file will be loaded only if it has content
   - Nested Shortcode "as_child" parameter when set to "except" fixed
   - ... and more

2014-09-10 - version 4.3.4
   - WP 4.0 improvements
   - Cloning row with tabs fixed
   - Cloning single tab in frontend editor fixed
   - Untranslated strings found and now outputed with __() and _e()
   - Custom CSS in Settings page converted > to special char fixed
   - IE8 compatibility improved. vc-ie8.css added. Thanks to AJ Clarke
   - Using front-end editor turns off the comments fixed
   - Persian translation added. Thanks to: Ali Mohammadi | www.bestit.co

2014-08-19 - version 4.3.3
   - Edit elements in Accordion fixed
   - Design Options: border params are not saved if you don't enter all 4 border widths
   - Inner row margins updated for better UI
   - Fixed layout highlighting setting with the same mask (case 1/3 + 2/3 and 2/3 + 1/3)
   - Advanced Custom Fields + VC now can work together (without js error)
   - ACE Editor and Option tree now work together (without js error)
   - pt_BR locale updated
   - de_DE locale file updated

2014-08-04 - version 4.3.2
   - Force custom css regeneration upon plugin update. This will fix problem for users who used custom Design options and experienced "broken layout"

2014-07-31 - version 4.3.1
   - Obsolete vc_spanX classes removed (caused problems for some users)
   - Fixed: "llegal offset type in ... shortcodes.php"
   - French translation updated

2014-07-29 - version 4.3
   - UX improvements in Backend editor
   - New: Offset option is added for columns
   - New: Now you can add any number of columns inside row (it will be placed to the next line if there is not enough of space)
   - New: Clone for columns
   - New: Control your layout accross multiple devices. Now you can set different column width for mobile and desktop layouts
   - New: Hide Columns on particular device
   - New: Added support for mqTranslate (qTranslate fork which works with WP 3.9+)
   - New: Row can be minimized in backend (to save space)
   - New: Empty space content element
   - New: Custom Heading content element
   - New: Predefined layouts
   - New: CSS syntax highlight added for Custom CSS option
   - Fully reworked css for frontend and backend editor. Modular and divided across multiple less files
   - CSS loading is moved to the wp_head();
   - Other small fixes and improvements


2014-06-12 - version 4.2.3
   - Isotope library updated
   - Flexslider next/prev arrows fixed (font added)
   - Single image now can accept relative urls
   - Improvements with third party plugins


2014-06-03 - version 4.2.2
   - Tabs: Auto generated IDs are back
   - Localizations are loaded correctly now
   - Pie chart element js error fixed
   - Notifier about unsaved changes added to the Custom CSS box
   - Custom CSS in Settings->WPBakery Page Builder->Custom CSS fixed
   - CSS editor accept css3 units
   - Removed "Activation notice" if vc_set_as_theme() called
   - front_enqueue_css and front_enqueue_js added in vc_map() https://kb.wpbakery.com/index.php?title=Vc_map
   - edit_post_link() caused problems in some themes - fixed
   - Template has the html tags for rich text formatting double encoded fixed
   - std param in maps improved logic
   - Single image: reveal target if link is set

2014-05-27 - version 4.2.1
   - vc_map() preloader fix. Thanks to Chris R for help in debugging

2014-05-21 - version 4.2
   - Performance improvements
   - Code structure changed, now everything is organized in folders
   - Code revised and phpdoc comments added (soon generated documentation will be available)
   - CSS icon will be shown at the "Gear" icon if there's custom css code
   - WP autoupdater now works in WP Dashboard->Updates section too
   - Default image changed for single image and image galleries
   - New Tour slide now title changed from "Tab" to "Slide"
   - Small fixes and improvements

2014-05-12 - version 4.1.3.1
   - Typo in js code fixed, which prevented rows from cloning in backend editor

2014-05-09 - version 4.1.3
   - Checkbox dependencies works now
   - Dependencies logic is executed upon edit screen opening
   - Textarea HTML is added in th shortcode mapper for use with content attribute
   - z-index for modals is lowered
   - Post grid: link to post works by default
   - Auto suggestion in query builder now gives hint after 2 chars
   - Message box and Warning box correctly displayed in the backend editor
   - vc_add_param()/vc_remove_element() works again

2014-04-22 - version 4.1.2
   WP 3.9 compatibilty update
   - php warning/notices in mapper.php
   - Read more text in post grid is now translable
   - Autocomplete z-index in loop param increased
   - Tiny MCE get active editor logic improved
   - vc_add_param() and other helpers api functions are back to normal
   - WP 3.9 select link in WYSIWYG editor works now
   - When default text is removed from wysiwyg editor it will not be back on next edit

2014-04-17 - version 4.1.1
   - z-index value for add element modal box increased
   - Improved element stacking on mopbile devices
   - Links now work in WP Text element
   - WP Menus element is populated on init event now
   - Removed console.log in js file
   - IE10 fix for TinyMCE

2014-04-16 - version 4.1
   - New: Shortcode Mapper, map any third party shortcode to WPBakery Page Builder with an easy to use interface
   - New: Design options for Row, Columns, Text block, Single image and Video block elements. Add baackground color/image, paddings, border and margins. Tons of variations. You are a designer now!
   - New: Now you can group your params in tabs
   - White color for button added
   - Colors are now visible as actual colors in the dropdowns instead of "plain names"
   - Color picker have alpha chanel now
   - .po/.mo files updated

2014-04-09 - version 4.0.5
   - Compatibility with new TinyMCE version in WordPress 3.9 added
   - Logic behind license activation button changed. Clicking activate button will save all fields
   - Image carousel popup next/prev links show correct images
   - Mobile screen width setting in design options updated to new less/css
   - "Show only WPBakery Page Builder" in "User groups access rules" fixed
   - Extra CSS class appended to the separator element

2014-03-27 - version 4.0.4
   Bug fix round
   - Post grid categories: z-index fixed so autosuggestions now visible
   - Custom row layout input field in frontend mode works now

2014-03-20 - version 4.0.3
   Bug fix round
   - Cloning elements in frontend is handled better
   - Enable/Disable Respinsivness in the backend from settings page
   - "Edit with WPBakery Page Builder" button in the admin bar now shows for correct user roles (who have access rights)
   - vc_disable_frontend() function enhanced
   - "TypeError: $(...).wpColorPicker" error illuminated
   - VC button in the backend reeived a facelift
   - WPBakery Page Builder suppor for child themes improved
   - Element dependencies tweaked to work with multiple values

2014-03-11 - version 4.0.2
   - Google maps element updated to support new google maps version
   - Child theme support added
   - Disable responsivness is back
   - h1 css rules, normalize.css and print.css removed fron final js_composer_front.css removed
   - Workaround for inline scripts added (for devs who output js code inline)
   - Revolution slider support added
   - Nested shortcodes: except param can accept comma separated values now
   - CSS code added in front end is mirrored in backend interface
   - Sprite for predefined row laoyouts added
   - "Close" modal button in backend position tweak added
   - Updated .po/.mo files

2014-03-03 - version 4.0.1
   - Optimizations for WPEngine
   - Plugin search is back
   - User access rules logic improved (for publish/save posts in inline mode)
   - Nested shortcodes can now accept except param

2014-03-02 - version 4.0
   - Major update. All new inline (front end) editing mode. Now you can see what you are doing, no more trials and errors
   - Based on Bootstrap 3
   - New content elements (even more to come...)
   - New styles for Separators, Single images
   - And much-much more! 3 months of hard development invested in this update

2014-01-08 - ver. 3.7.4
   - NL locale added
   - Custom layout fixed (all fractional sizes work now on front end)
   - 100 sliders limit value for Revolutions sliders increased
   - Color picker css improved for Win users

2013-12-13 - ver. 3.7.3
   - TinyMCE "Text" mode buttons size fixed
   - Small WordPress 3.8 adjustments

2013-12-08 - version 3.7.2
   - "Add Element" window received a "facelift"
   - Alignment for Single Image fixed
   - Custom CSS: now is outputted only in single page view
   - Scroll problem in edit element view in Chrome fixed
   - .po/.mo files updated
   - Raw js block: removed bottom margin
   - Posts grid column count fixed

2013-11-27 - version 3.7.1
   - vc_remove_element() is back

2013-11-26 - version 3.7
   - New: Posts Grid element with much more control and visual query builder
   - New: Custom teaser, control what content should be used in Posts grid and Carousel
   - New: Image carousel
   - New: Now you can set color for row background
   - New: Now you can set text color for row
   - New: Fine tune margins and paddings within a row
   - New: Single image alignment added
   - New: Custom CSS for singular pages. Add Custom CSS code to particular pages only
   - New: Now third party developers can easily create nested shortcodes Eg: [items][single_item][single_item][/items] More: https://kb.wpbakery.com/index.php?title=Nested_Shortcodes_%28container%29
   - New: Weight attribute for content elements added
   - Conflict with Gantry framework fixed
   - Conflict with QuForm plugin fixed
   - Single image url fixed
   - Third party shortcodes wraps in the text block when switching to WPBakery Page Builder mode
   - .po/.mo files updated

2013-10-07 - version 3.6.14.1
   - Re: URL for single image improved
   - Dependency improvements

2013-10-07 - version 3.6.14
   - IT locale added
   - Empty text block works correct now
   - Isotope css added for print page version
   - URL for single image improved
   - Site admins in Multisite have access to VC Settings now

2013-09-09 - version 3.6.13
   - 7/12 + 5/12 layout now works on the front end
   - FR locale updated
   - Dependency setting now works for single image element

2013-08-23 - version 3.6.12
   - fix for dissapearing paragraph and br tags

2013-08-15 - version 3.6.11
   - Carousel is back
   - "isHidden" javascript error which was affecting some users fixed
   - WP Custom Menu logic improved

2013-08-09 - version 3.6.10
   - New: Pie chart content element
   - Updated code to support new jQuery UI version (Accordion spacing, Tour next/prev links working again, Image grid in 2nd+ tab working again, Tour sorting/cloning fixed)
   - pt_BR locale updated

2013-08-05 - version 3.6.9
   - New: Auto updater. Enter your API key/Purchase code/Envato username and plugin will be updated automaticaly
   - New: Add multiple categories to your content elements (when register new element with vc_map())
   - New: "If you close this window all shortcode settings will be lost. Close this window?" message now is showed only when there are unsaved changes
   - Javascript code tested with JShint
   - Better "bullet proof" logic for custom size images

2013-07-15 - version 3.6.8
   - More row layouts added
   - Custom row layout option added, now you can enter exact size you want/need
   - Row layout switcher changed
   - Title removed for Text block in edit screen (for space savings)
   - vc_remove_element() typo fixed
   - Quotes properly escaped now in Template names
   - Isotope library updated
   - Images from same galleries can be cycled in prettyPhoto lightbox
   - Colorpicker attribute now works for all user roles

2013-07-03 - version 3.6.7
   - Navigation for Accordion added, now you can link to specific section directly
   - Inner API extended. More details https://kb.wpbakery.com/index.php?title=Inner_API
   - PHP code formatted for PHP Strict standards
   - default.po and .mo files updated

2013-06-25 - version 3.6.6
   - New: Accordion now can be collapsed by default
   - Quotes in Accordion/Tour/Tabs now works
   - Category filter works now after wpb_remove() function call
   - Some users had "unexpected T_VAR in loop templates.html" error - fixed
   - "Show only WPBakery Page Builder" permission fixed

2013-06-17 - version 3.6.5
   - New: Shortcode Template logic. More here: https://kb.wpbakery.com/index.php?title=Shortcode_Templates_(markup)
   - New: Link param type
   - New: Updates notifier now "hooks" into the WP Updates system
   - New helper function added for easier param adding to existing content elements wpb_add_param($shortcode, $param)
   - Colorpicker param type now is using WordPress default color picker
   - Modal window fix for Avada theme
   - Google maps: New setting to hide info balloon
   - TinyMCE editor works correct now, for users with non US locale
   - Tab duplicating fixed
   - FR locale updated
   - Textdomains added in few strings. .po and .mo files updated
   - Accordion "Add new accordion section" text changed
   - Plugin activation improved
   - Text typos in Google maps and Progress bar fixed
   - Twitter widget removed. Due to the api change tweets can't be loaded "the old" way anymore. As workaround, login to your twitter account and create new widget (https://twitter.com/settings/widgets) then use raw HTML block to place generated code to your page

2013-06-07 - version *******
   - update for the those who have javascript error "jQuery(...).waypoint is not a function"

2013-06-01 - version 3.6.4
   - New feature: Added CSS animations for Single image, Message box, Text Block & Toggle. Which runs when element enters browsers viewport.
   - Progress bar animation now starts when it enters into the viewport. Added filter for class names.

2013-05-27 - version 3.6.3
   - tinyMCE initialization improved
   - #tb_window z-index increased

2013-05-24 - version 3.6.2
   - Safe switching added. If you had content before switching to WPBakery Page Builder mode, it will be wrapped in Text Block now
   - CSS conflict resulting in cutting "Add element" and element edit pages fixed
   - Some localization strings fixed

2013-05-21 - version 3.6.1
   - custom.css and js_composer_front_custom.css storing location changed from plugin's folder to wp-content/uploads/js_composer/*
   - German translation updated. Thanks to Toni (kratos85)
   - French translation added. Thanks to sailor1978.
   - Spanish translation updated. Thanks to elpuntografico
   - Russian translation added.

2013-05-13 - version 3.6
   - Fully rewritten backend. Now powered by backbone.js library - enourmous speed improvements
   - Elements drag and drop logic improved
   - New settings page. Now you can change default colors, CSS grid settings, Content elements bottom spacing. You should see it!
   - New search filter in "Add element" window. Content elements are filtered while you type
   - New content elements: Progress bar (animated), Gravity Form
   - Default WP widgets now can be inserted directly to your page
   - Single image block improved (added option for auto linking to the 'large' image)
   - Accordion block improved (added "collapsible" option)
   - Posts Slider block improved (added option to add titles)
   - Button block improved (add prettyphoto extra class and link will open in lightbox)
   - Custom CSS section added into the settings page. Now pasting your own css code is a lot easier
   - New param type for developers (checkbox)
   - prettyPhoto updated (3.1.5)
   - .po & .mo files updated
   - Overal files cleanup (plugin file size reduced)
   - And a lot more small thigs that will make your experience with WPBakery Page Builder better

2013-02-15 - version 3.5.3
   - Image selection i18nLocale fixed
   - .po and .mo files updated

2013-02-07 - version 3.5.2
   - Activation error fixed (cases when Contact form7 was installed)
   - User rights updated (Row in Row now works)

2013-01-25 - version 3.5.1
   - WP native image selector added (for single image and galleries)
   - delete icon in tabs
   - add section for accordion fixed
   - row extra class output on front end fixed
   - IE images stretching removed
   - "convert to new version" added support for 3rd party shortcodes

2013-01-22 - version 3.5
   - Major update, read more about update: http://bit.ly/vc_34_to_35
   - Fully rewriten backend UI, with better row->column logic
   - Overall speed improvements
   - Highly customizable for third party developers
   - Due to large amount of css conflicts, original bootstrap was removed and forked "prefixed" one is used now
   - Improved element's responsiveness
   - .po & .mo files updated
   - And many, many more...

2012-12-18 - version 3.4.12
   - Edit tab titles fixed

2012-12-13 - version 3.4.11
   - var_dump() removed from shortcodes.php file
   - pressing cancel button works correctly now
   - depreceted function image_resize replaced with wp_get_image_editor()
   - .po and .mo files updated

2012-12-12 - version 3.4.10
   - Accordion "freezing" fixed
   - WP 3.5 compatibility fixes added

2012-12-29 - version 3.4.9
   - Introduced developers methods more info https://kb.wpbakery.com/
   - Exceprts now are working for posts/pages created with WPBakery Page Builder
   - Nivo slider updated (ver. 3.1)
   - Responsive css can be turned off from WPBakery Page Builder settings page

2012-11-15 - version 3.4.8
   - fix for qTranslate (after recent update qTranslate was hiding WPBakery Page Builder button)
   - new colorpicker param type added
   - Template system updated

2012-11-09 - version 3.4.7
   - Updates notifier added (beta)
   - Toggling between WPBakery Page Builder/Classic view now works as it should be (content is updating correctly)

2012-11-05 - version 3.4.6
   - Raw HTML and Raw js content blocks updated

2012-10-31 - version 3.4.5
   - Fixed shortcodes initialization ([vc_column_text width="1/1" el_position="first last"] ...)

2012-10-23 - version 3.4.4
   - wamp environment fix

2012-10-16 - version 3.4.3
   - fixed "Empty visual editor"

2012-10-11 - version 3.4.2
   - fixed "Call to undefined function get_currentuserinfo()"

2012-10-09 - version 3.4.1
   - Activation error fix
   - Few small addons

2012-10-05 - version 3.4
   - New facelifted backend interface
   - New content block selection menu
   - Call to action block in IE fixed
   - Single image block: now image can be removed after upload
   - VC initializing fixed (for users who changed default wp-content/plugins/ path)
   - Raq html/Raw js content blocks non latin characters fixed
   - New method for developers. Now you can completely override outputted html on frontend
   - Google maps positioning in 2nd, 3rd, ..., tab fixed
   - .po & .mo files updated

2012-09-21 - version 3.3.3
   - image max-width set to 100%;
   - Fixed image uploading layout (now it uses full width)
   - locale string added in few __() and _e() functions
   - .wpb_flickr_widget & .wpb_twitter_widget bottom margin added in LESS
   - css background, changed to background-color where possible

2012-08-21 - version 3.3.2
   - second attempt to fix "Featured image"

2001-08-212 - version 3.3.1
   - add_them_support('post-thumbnails'); extends post thumbnails defined by theme

2012-08-16 - version 3.3
   - This version main focus is on CSS files, they are improved. Content elements now share similar "default grey" style across all elements
   - Bootstrap now comes with LESS files
   - Bootstrap css rules stripped to prevent css conflicts with 3rd party themes/plugins
   - js_composer_front.css: re-done. Now compiles from LESS files.

2012-08-14 - version 3.2.4
   - Video in tabs fixed
   - Container extra class name applied to inner text blocks bug fixed

2012-08-09 - version 3.2.3
   - Settings page enhancments

2012-07-27 - version 3.2.2
   - jQuery UI CSS facelifted, now should look great in every design.
   - LESS files for jQuery UI now available in assets/css/ui-custom-theme/less

2012-07-24 - version 3.2.1
   - jQuery Tabs switching fixed

2012-07-23 - version 3.2.0
   - Teaser grid: link target added
   - Image grid placed in tabs bug fixed
   - FlexSlider updated
   - Masonry Layout for Teaser grid added
   - Sorting in Teaser grid added
   - Extra class name for containers added
   - .js files outputs in footer
   - If user switch WPBakery Page Builder mode when there's text already, then this text is wrapped in text block.
   - Administrator now can set user roles related settings in WPBakery Page Builder->Settings
   - Administrator now can set WPBakery Page Builder as default editor (per user role)
   - Image gallery: link target added
   - Single image element: link option added
   - Posts slider: removed string "No featured image set" if no image is set
   - Fixed problem with advanced custom fields
   - .po file updated

2012-06-04 - version 3.1.0
   - Major release
   - Rewritten core. Now OOP driven
   - Updated documentation and Advanced documentation
   - Updated locale files
   - 1/5 and 5/6 columns sizes added
   - UI enhanced
   - Single image element added
   - Raw HTML element added
   - Raw JS element added
   - Drag & Drop works better now
   - Adding images in galleries is easier now (WP native way)
   - Bootstrap updated to 2.0.4

2012-05-10 - version 3.0.4
   - HTML Comments removed from generated html code. Some themes wrapped them in paragraph tag :(

2012-04-27 - version 3.0.3
   - Alert box icons on front end added
   - Image gallery. Now images can be shuffled/randomized
   - Tested with Advanced TinyMCE plugin. Works OK
   - Bootstrap updated to 2.0.3

2012-04-15 - version 3.0.2
   - bootstrap.css cleaned, unused classes removed.

2012-04-14 - version 3.0.1
   - Attached images field, now checks for deleted images and if ID doesn't exist - ID is removed.
   - Cloning tabs, accordion and tour fixed.
   - google+ added
   - pinterest added
   - German translation added. Contributed by aleccs
   - Polish translation added. Contributed by Bartosz Arendt, Digital Factory
   - apostrophe \' fixed
   - Accordion droppable fixed, now it doesn't accept tabs, tour and accordion in it.
   - .po file updated
   - .container class name comment out from bootstrap.css file. Because it isn't used withing the plugin, but conflicts with 3rd part themes


2012-04-11 - version 3.0 - Major release - Fully rewritten
   - New content blocks added (Accordion, Posts slider, Google maps, Video widget);
   - Old content blocks extended (FAQ Toggle: default state option added, Image gallery: ability to link each slide individually added, new layout (simple grid) added, now you can enter size for your image WP native way. thumbnail, media, large;
   - Tabs, Tours and Accordion content block are fully revamped: now they can accept other content elements inside them, as a result you can columns or image gallery right in tabs;
   - Teaser (posts) grid: new filtering and sorting options added;

2012-02-09 - version 2.3.3
   - prettyPhoto updated and few js improvements.

2012-02-07 - version 2.3.2
   - js and css files now included with version number.

2012-01-27 - version 2.3.1
   - Added additional "if" check to prevent error message when image can't be found or resized

2011-12-20 - version 2.3.0
   - "no link" in VC teaser grid
   - "Read more" button removed & image wrapped in span; WPBakery Page Builder now switching default WP content area to Visual mode automatically

2011-12-12 - version 2.2.9
   - WP 3.3 compatibility;
   - .clear class renamed to .vc_clear;
   - Now tabs and tour slides have classname, as a result they can be targeted with css;
   - If WPBakery Page Builder settings are empty, then columns are percent based.

2011-09-22 - version 2.2.8
   - New content block added - "Text separator".

2011-09-15 - version 2.2.7
   - .wrapper class renamed to .wpb_wrapper;
   - CSS file cleaned and tweaked;
   - Posts grid -> Categories (narrowing by custom taxonomies) now works with custom post types and regular posts;
   - New content element added - "Tour section";
   - Fixed bug with image inserting into tabs;
   - Checked that js_composer works great with YOAST SEO, some user reported that they don't work together.

2011-08-01 - version 2.2.6
   - CSS improvements.

2011-06-21 - version 2.2.5
   - Nested tabs bug fixed. Thanks to Justin.

2011-06-15 - version 2.2.4
   - Scheduled update. Overall improvements.

2011-06-13 - version 2.2.3
   - Now you can generate grid for multiple templates. Eg: For full width page, page with sidebar and so on.

2011-06-08 - version 2.2.2
   - New content block "Call to action box"

2011-06-07 - version 2.2.1
   - Few tweaks to the backend design, posts grid now can show posts in Carousel

2011-06-04 - version 2.2.0
   - Redesigned backend, new content module "Button"

2011-05-31 - version 2.1.9
   - Fully rewritten engine, as a result you can add your own shortcodes. Note: ver 2.0 isn't compatible with 1.2 plugin version, please delete old plugin first.

2011-05-24 - version 2.1.8
   - New content block "Widgetised Sidebar", few bugfixes.
